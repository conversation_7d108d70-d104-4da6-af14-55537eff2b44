var Ts=Object.defineProperty;var Es=(t,r,e)=>r in t?Ts(t,r,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[r]=e;var Yr=(t,r,e)=>Es(t,typeof r!="symbol"?r+"":r,e);import{d as Re,r as R,f as Ke,C as Ar,c as Q,o as G,b as S,y as ui,N as Tr,t as it,i as se,A as Xn,_ as Te,W as Kn,X as Is,Y as di,v as rr,n as _e,j as pe,F as Ce,k as De,x as Gn,h as kt,l as Ua,H as Pe,a as ue,Z as As,g as Zr,w as ur,$ as Ds,B as Un,e as Qn,z as Ps,m as Vi,L as Fs,I as Je,J as Ne,a0 as Rs,G as qn,a1 as Wa}from"./main-BiUkPfWp.js";import{u as ta,q as sr,r as zi,N as Jn,l as Oe,t as Bs,v as Ns,o as Os,G as Vs,a as zs,b as $s,c as Ws,d as Ys,e as Zs,f as Hs,g as js,h as Xs,i as Ks,j as Gs,k as Us,m as Qs,n as qs,T as Js,w as tl,L as to,s as el}from"./index-Dpwnm-Tx.js";import{r as rl}from"./index-Bc-UAyzM.js";/**
     * @license
     * KLineChart v10.0.1
     * Copyright (c) 2019 lihu.
     * Licensed under Apache License 2.0 https://www.apache.org/licenses/LICENSE-2.0
     */function he(t,r){if(!(!We(t)&&!We(r))){for(var e in r)if(Object.prototype.hasOwnProperty.call(r,e)){var a=t[e],i=r[e];We(i)&&We(a)?he(a,i):P(r[e])&&(t[e]=Er(r[e]))}}}function Er(t){if(!We(t))return t;var r=null;He(t)?r=[]:r={};for(var e in t)if(Object.prototype.hasOwnProperty.call(t,e)){var a=t[e];We(a)?r[e]=Er(a):r[e]=a}return r}function He(t){return Object.prototype.toString.call(t)==="[object Array]"}function Me(t){return typeof t=="function"}function We(t){return typeof t=="object"&&P(t)}function vt(t){return typeof t=="number"&&Number.isFinite(t)}function P(t){return t!=null}function Qr(t){return typeof t=="boolean"}function Gt(t){return typeof t=="string"}var al=/\\(\\)?/g,il=RegExp(`[^.[\\]]+|\\[(?:([^"'][^[]*)|(["'])((?:(?!\\2)[^\\\\]|\\\\.)*?)\\2)\\]|(?=(?:\\.|\\[\\])(?:\\.|\\[\\]|$))`,"g");function ge(t,r,e){if(P(t)){var a=[];r.replace(il,function(s){for(var l=[],c=1;c<arguments.length;c++)l[c-1]=arguments[c];var u=s;return P(l[1])?u=l[2].replace(al,"$1"):P(l[0])&&(u=l[0].trim()),a.push(u),""});for(var i=t,n=0,o=a.length;P(i)&&n<o;)i=i==null?void 0:i[a[n++]];return P(i)?i:e??"--"}return e??"--"}function nl(t,r){var e={};return t.formatToParts(new Date(r)).forEach(function(a){var i=a.type,n=a.value;switch(i){case"year":{e.YYYY=n;break}case"month":{e.MM=n;break}case"day":{e.DD=n;break}case"hour":{e.HH=n==="24"?"00":n;break}case"minute":{e.mm=n;break}case"second":{e.ss=n;break}}}),e}function eo(t,r,e){var a=nl(t,r);return e.replace(/YYYY|MM|DD|HH|mm|ss/g,function(i){return a[i]})}function xe(t,r){var e=+t;return vt(e)?e.toFixed(r??2):"".concat(t)}function ro(t){var r=+t;if(vt(r)){if(r>1e9)return"".concat(+(r/1e9).toFixed(3),"B");if(r>1e6)return"".concat(+(r/1e6).toFixed(3),"M");if(r>1e3)return"".concat(+(r/1e3).toFixed(3),"K")}return"".concat(t)}function ao(t,r){var e="".concat(t);if(r.length===0)return e;if(e.includes(".")){var a=e.split(".");return"".concat(a[0].replace(/(\d)(?=(\d{3})+$)/g,function(i){return"".concat(i).concat(r)}),".").concat(a[1])}return e.replace(/(\d)(?=(\d{3})+$)/g,function(i){return"".concat(i).concat(r)})}function io(t,r){var e="".concat(t),a=new RegExp("\\.0{"+r+",}[1-9][0-9]*$");if(a.test(e)){var i=e.split("."),n=i.length-1,o=i[n],s=/0*/.exec(o);if(P(s)){var l=s[0].length;return i[n]=o.replace(/0*/,"0{".concat(l,"}")),i.join(".")}}return e}function $i(t,r){return t.replace(/\{(\w+)\}/g,function(e,a){var i=r[a];return P(i)?i:"{".concat(a,"}")})}var Hr=null;function hr(t){var r,e;return(e=(r=t.ownerDocument.defaultView)===null||r===void 0?void 0:r.devicePixelRatio)!==null&&e!==void 0?e:1}function _r(t,r,e){return"".concat(r??"normal"," ").concat(t??12,"px ").concat(e??"Helvetica Neue")}function Ye(t,r,e,a){if(!P(Hr)){var i=document.createElement("canvas"),n=hr(i);Hr=i.getContext("2d"),Hr.scale(n,n)}return Hr.font=_r(r,e,a),Math.round(Hr.measureText(t).width)}var Qa=function(t,r){return Qa=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,a){e.__proto__=a}||function(e,a){for(var i in a)Object.prototype.hasOwnProperty.call(a,i)&&(e[i]=a[i])},Qa(t,r)};function St(t,r){if(typeof r!="function"&&r!==null)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");Qa(t,r);function e(){this.constructor=t}t.prototype=r===null?Object.create(r):(e.prototype=r.prototype,new e)}var U=function(){return U=Object.assign||function(r){for(var e,a=1,i=arguments.length;a<i;a++){e=arguments[a];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(r[n]=e[n])}return r},U.apply(this,arguments)};function ga(t,r){var e={};for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&r.indexOf(a)<0&&(e[a]=t[a]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,a=Object.getOwnPropertySymbols(t);i<a.length;i++)r.indexOf(a[i])<0&&Object.prototype.propertyIsEnumerable.call(t,a[i])&&(e[a[i]]=t[a[i]]);return e}function no(t,r,e,a){function i(n){return n instanceof e?n:new e(function(o){o(n)})}return new(e||(e=Promise))(function(n,o){function s(u){try{c(a.next(u))}catch(d){o(d)}}function l(u){try{c(a.throw(u))}catch(d){o(d)}}function c(u){u.done?n(u.value):i(u.value).then(s,l)}c((a=a.apply(t,[])).next())})}function oo(t,r){var e={label:0,sent:function(){if(n[0]&1)throw n[1];return n[1]},trys:[],ops:[]},a,i,n,o=Object.create((typeof Iterator=="function"?Iterator:Object).prototype);return o.next=s(0),o.throw=s(1),o.return=s(2),typeof Symbol=="function"&&(o[Symbol.iterator]=function(){return this}),o;function s(c){return function(u){return l([c,u])}}function l(c){if(a)throw new TypeError("Generator is already executing.");for(;o&&(o=0,c[0]&&(e=0)),e;)try{if(a=1,i&&(n=c[0]&2?i.return:c[0]?i.throw||((n=i.return)&&n.call(i),0):i.next)&&!(n=n.call(i,c[1])).done)return n;switch(i=0,n&&(c=[c[0]&2,n.value]),c[0]){case 0:case 1:n=c;break;case 4:return e.label++,{value:c[1],done:!1};case 5:e.label++,i=c[1],c=[0];continue;case 7:c=e.ops.pop(),e.trys.pop();continue;default:if(n=e.trys,!(n=n.length>0&&n[n.length-1])&&(c[0]===6||c[0]===2)){e=0;continue}if(c[0]===3&&(!n||c[1]>n[0]&&c[1]<n[3])){e.label=c[1];break}if(c[0]===6&&e.label<n[1]){e.label=n[1],n=c;break}if(n&&e.label<n[2]){e.label=n[2],e.ops.push(c);break}n[2]&&e.ops.pop(),e.trys.pop();continue}c=r.call(t,e)}catch(u){c=[6,u],i=0}finally{a=n=0}if(c[0]&5)throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}}function Ze(t){var r=typeof Symbol=="function"&&Symbol.iterator,e=r&&t[r],a=0;if(e)return e.call(t);if(t&&typeof t.length=="number")return{next:function(){return t&&a>=t.length&&(t=void 0),{value:t&&t[a++],done:!t}}};throw new TypeError(r?"Object is not iterable.":"Symbol.iterator is not defined.")}function ar(t,r){var e=typeof Symbol=="function"&&t[Symbol.iterator];if(!e)return t;var a=e.call(t),i,n=[],o;try{for(;(r===void 0||r-- >0)&&!(i=a.next()).done;)n.push(i.value)}catch(s){o={error:s}}finally{try{i&&!i.done&&(e=a.return)&&e.call(a)}finally{if(o)throw o.error}}return n}function dr(t,r,e){if(arguments.length===2)for(var a=0,i=r.length,n;a<i;a++)(n||!(a in r))&&(n||(n=Array.prototype.slice.call(r,0,a)),n[a]=r[a]);return t.concat(n||Array.prototype.slice.call(r))}function ma(t){var r={width:0,height:0,left:0,right:0,top:0,bottom:0};return P(t)&&he(r,t),r}var Mr=-1;function ya(t){return Me(window.requestAnimationFrame)?window.requestAnimationFrame(t):window.setTimeout(t,20)}function Wi(t){Me(window.cancelAnimationFrame)?window.cancelAnimationFrame(t):window.clearTimeout(t)}function Yi(t){if(Me(window.requestIdleCallback))return window.requestIdleCallback(t);var r=performance.now();return window.setTimeout(function(){t({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(performance.now()-r))}})},1)}function ol(t){Me(window.cancelIdleCallback)?window.cancelIdleCallback(t):window.clearTimeout(t)}var qa=function(){function t(r){this._options={duration:500,iterationCount:1},this._currentIterationCount=0,this._running=!1,this._time=0,he(this._options,r)}return t.prototype._loop=function(){var r=this;this._running=!0;var e=function(){var a;if(r._running){var i=new Date().getTime()-r._time;i<r._options.duration?((a=r._doFrameCallback)===null||a===void 0||a.call(r,i),ya(e)):(r.stop(),r._currentIterationCount++,r._currentIterationCount<r._options.iterationCount&&r.start())}};ya(e)},t.prototype.doFrame=function(r){return this._doFrameCallback=r,this},t.prototype.setDuration=function(r){return this._options.duration=r,this},t.prototype.setIterationCount=function(r){return this._options.iterationCount=r,this},t.prototype.start=function(){this._running||(this._time=new Date().getTime(),this._loop())},t.prototype.stop=function(){var r;this._running&&((r=this._doFrameCallback)===null||r===void 0||r.call(this,this._options.duration)),this._running=!1},t}(),Ya=1,Zi=new Date().getTime();function ua(t){var r=new Date().getTime();return r===Zi?++Ya:Ya=1,Zi=r,"".concat(t??"").concat(r,"_").concat(Ya)}function nr(t,r){var e,a=document.createElement(t),i=r??{};for(var n in i)a.style[n]=(e=i[n])!==null&&e!==void 0?e:"";return a}function Ja(t,r,e){var a=0,i=0;for(i=t.length-1;a!==i;){var n=Math.floor((i+a)/2),o=i-a,s=t[n][r];if(e===t[a][r])return a;if(e===t[i][r])return i;if(e===s)return n;if(e>s?a=n:i=n,o<=2)break}return a}function sl(t){var r=Math.floor(qe(t)),e=yr(r),a=t/e,i=0;return a<1.5?i=1:a<2.5?i=2:a<3.5?i=3:a<4.5?i=4:a<5.5?i=5:a<6.5?i=6:i=8,t=i*e,+t.toFixed(Math.abs(r))}function Hi(t,r){r=Math.max(0,r??0);var e=Math.pow(10,r);return Math.round(t*e)/e}function ll(t){var r=t.toString(),e=r.indexOf("e");if(e>0){var a=+r.slice(e+1);return a<0?-a:0}var i=r.indexOf(".");return i<0?0:r.length-1-i}function so(t,r,e){for(var a,i,n=[Number.MIN_SAFE_INTEGER,Number.MAX_SAFE_INTEGER],o=t.length,s=0;s<o;){var l=t[s];n[0]=Math.max((a=l[r])!==null&&a!==void 0?a:Number.MIN_SAFE_INTEGER,n[0]),n[1]=Math.min((i=l[e])!==null&&i!==void 0?i:Number.MAX_SAFE_INTEGER,n[1]),++s}return n}function qe(t){return t===0?0:Math.log10(t)}function yr(t){return Math.pow(10,t)}function ji(){return{from:0,to:0,realFrom:0,realTo:0}}function Xi(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return t.join("_")}var cl=function(){function t(r){this._requestIdleCallbackId=Mr,this._tasks=r??[],this._operateTasks()}return t.prototype._operateTasks=function(r){var e=this;this._requestIdleCallbackId!==Mr&&(ol(this._requestIdleCallbackId),this._requestIdleCallbackId=Mr),r==null||r(),this._requestIdleCallbackId=Yi(function(a){e._runTasks(a)})},t.prototype._runTasks=function(r){for(var e=this;r.timeRemaining()>0&&this._tasks.length>0;){var a=this._tasks.shift();a==null||a.handler()}this._tasks.length>0&&(this._requestIdleCallbackId=Yi(function(i){e._runTasks(i)}))},t.prototype.addTask=function(r){var e=this;return this._operateTasks(function(){var a=e._tasks.findIndex(function(i){return i.id===r.id});a>-1?e._tasks[a]=r:e._tasks.push(r)}),this},t.prototype.removeTask=function(r){var e=this;return P(r)&&this._operateTasks(function(){if(P(r)){var a=e._tasks.findIndex(function(i){return i.id===r});a>-1&&e._tasks.splice(a,1)}else e._tasks=[]}),this},t}(),ul=function(){function t(){this._callbacks=[]}return t.prototype.subscribe=function(r){var e=this._callbacks.indexOf(r);e<0&&this._callbacks.push(r)},t.prototype.unsubscribe=function(r){if(Me(r)){var e=this._callbacks.indexOf(r);e>-1&&this._callbacks.splice(e,1)}else this._callbacks=[]},t.prototype.execute=function(r){this._callbacks.forEach(function(e){e(r)})},t.prototype.isEmpty=function(){return this._callbacks.length===0},t}();function Ir(t){return t==="transparent"||t==="none"||/^[rR][gG][Bb][Aa]\(([\s]*(2[0-4][0-9]|25[0-5]|[01]?[0-9][0-9]?)[\s]*,){3}[\s]*0[\s]*\)$/.test(t)||/^[hH][Ss][Ll][Aa]\(([\s]*(360｜3[0-5][0-9]|[012]?[0-9][0-9]?)[\s]*,)([\s]*((100|[0-9][0-9]?)%|0)[\s]*,){2}([\s]*0[\s]*)\)$/.test(t)}function vr(t,r){var e=t.replace(/^#/,""),a=parseInt(e,16),i=a>>16&255,n=a>>8&255,o=a&255;return"rgba(".concat(i,", ").concat(n,", ").concat(o,", ").concat(r??1,")")}var pt={RED:"#F92855",GREEN:"#2DC08E",WHITE:"#FFFFFF",GREY:"#76808F",BLUE:"#1677FF"};function dl(){return{show:!0,horizontal:{show:!0,size:1,color:"#EDEDED",style:"dashed",dashedValue:[2,2]},vertical:{show:!0,size:1,color:"#EDEDED",style:"dashed",dashedValue:[2,2]}}}function hl(){var t={show:!0,color:pt.GREY,textOffset:5,textSize:10,textFamily:"Helvetica Neue",textWeight:"normal"};return{type:"candle_solid",bar:{compareRule:"current_open",upColor:pt.RED,downColor:pt.GREEN,noChangeColor:pt.GREY,upBorderColor:pt.RED,downBorderColor:pt.GREEN,noChangeBorderColor:pt.GREY,upWickColor:pt.RED,downWickColor:pt.GREEN,noChangeWickColor:pt.GREY},area:{lineSize:2,lineColor:pt.BLUE,smooth:!1,value:"close",backgroundColor:[{offset:0,color:vr(pt.BLUE,.01)},{offset:1,color:vr(pt.BLUE,.2)}],point:{show:!0,color:pt.BLUE,radius:4,rippleColor:vr(pt.BLUE,.3),rippleRadius:8,animation:!0,animationDuration:1e3}},priceMark:{show:!0,high:U({},t),low:U({},t),last:{show:!0,compareRule:"current_open",upColor:pt.RED,downColor:pt.GREEN,noChangeColor:pt.GREY,line:{show:!0,style:"dashed",dashedValue:[4,4],size:1},text:{show:!0,style:"fill",size:12,paddingLeft:4,paddingTop:4,paddingRight:4,paddingBottom:4,borderColor:"transparent",borderStyle:"solid",borderSize:0,borderDashedValue:[2,2],color:pt.WHITE,family:"Helvetica Neue",weight:"normal",borderRadius:2},extendTexts:[]}},tooltip:{offsetLeft:4,offsetTop:6,offsetRight:4,offsetBottom:6,showRule:"always",showType:"standard",rect:{position:"fixed",paddingLeft:4,paddingRight:4,paddingTop:4,paddingBottom:4,offsetLeft:4,offsetTop:4,offsetRight:4,offsetBottom:4,borderRadius:4,borderSize:1,borderColor:"#F2F3F5",color:"#FEFEFE"},title:{show:!0,size:14,family:"Helvetica Neue",weight:"normal",color:pt.GREY,marginLeft:8,marginTop:4,marginRight:8,marginBottom:4,template:"{ticker} · {period}"},legend:{size:12,family:"Helvetica Neue",weight:"normal",color:pt.GREY,marginLeft:8,marginTop:4,marginRight:8,marginBottom:4,defaultValue:"n/a",custom:[{title:"time",value:"{time}"},{title:"open",value:"{open}"},{title:"high",value:"{high}"},{title:"low",value:"{low}"},{title:"close",value:"{close}"},{title:"volume",value:"{volume}"}]},features:[]}}}function vl(){var t=vr(pt.RED,.7),r=vr(pt.GREEN,.7);return{ohlc:{compareRule:"current_open",upColor:t,downColor:r,noChangeColor:pt.GREY},bars:[{style:"fill",borderStyle:"solid",borderSize:1,borderDashedValue:[2,2],upColor:t,downColor:r,noChangeColor:pt.GREY}],lines:["#FF9600","#935EBD",pt.BLUE,"#E11D74","#01C5C4"].map(function(e){return{style:"solid",smooth:!1,size:1,dashedValue:[2,2],color:e}}),circles:[{style:"fill",borderStyle:"solid",borderSize:1,borderDashedValue:[2,2],upColor:t,downColor:r,noChangeColor:pt.GREY}],lastValueMark:{show:!1,text:{show:!1,style:"fill",color:pt.WHITE,size:12,family:"Helvetica Neue",weight:"normal",borderStyle:"solid",borderColor:"transparent",borderSize:0,borderDashedValue:[2,2],paddingLeft:4,paddingTop:4,paddingRight:4,paddingBottom:4,borderRadius:2}},tooltip:{offsetLeft:4,offsetTop:6,offsetRight:4,offsetBottom:6,showRule:"always",showType:"standard",title:{show:!0,showName:!0,showParams:!0,size:12,family:"Helvetica Neue",weight:"normal",color:pt.GREY,marginLeft:8,marginTop:4,marginRight:8,marginBottom:4},legend:{size:12,family:"Helvetica Neue",weight:"normal",color:pt.GREY,marginLeft:8,marginTop:4,marginRight:8,marginBottom:4,defaultValue:"n/a"},features:[]}}}function Ki(){return{show:!0,size:"auto",axisLine:{show:!0,color:"#DDDDDD",size:1},tickText:{show:!0,color:pt.GREY,size:12,family:"Helvetica Neue",weight:"normal",marginStart:4,marginEnd:6},tickLine:{show:!0,size:1,length:3,color:"#DDDDDD"}}}function fl(){return{show:!0,horizontal:{show:!0,line:{show:!0,style:"dashed",dashedValue:[4,2],size:1,color:pt.GREY},text:{show:!0,style:"fill",color:pt.WHITE,size:12,family:"Helvetica Neue",weight:"normal",borderStyle:"solid",borderDashedValue:[2,2],borderSize:1,borderColor:pt.GREY,borderRadius:2,paddingLeft:4,paddingRight:4,paddingTop:4,paddingBottom:4,backgroundColor:pt.GREY},features:[]},vertical:{show:!0,line:{show:!0,style:"dashed",dashedValue:[4,2],size:1,color:pt.GREY},text:{show:!0,style:"fill",color:pt.WHITE,size:12,family:"Helvetica Neue",weight:"normal",borderStyle:"solid",borderDashedValue:[2,2],borderSize:1,borderColor:pt.GREY,borderRadius:2,paddingLeft:4,paddingRight:4,paddingTop:4,paddingBottom:4,backgroundColor:pt.GREY}}}}function pl(){var t=vr(pt.BLUE,.35),r=vr(pt.BLUE,.25);function e(){return{style:"fill",color:pt.WHITE,size:12,family:"Helvetica Neue",weight:"normal",borderStyle:"solid",borderDashedValue:[2,2],borderSize:1,borderRadius:2,borderColor:pt.BLUE,paddingLeft:4,paddingRight:4,paddingTop:4,paddingBottom:4,backgroundColor:pt.BLUE}}return{point:{color:pt.BLUE,borderColor:t,borderSize:1,radius:5,activeColor:pt.BLUE,activeBorderColor:t,activeBorderSize:3,activeRadius:5},line:{style:"solid",smooth:!1,color:pt.BLUE,size:1,dashedValue:[2,2]},rect:{style:"fill",color:r,borderColor:pt.BLUE,borderSize:1,borderRadius:0,borderStyle:"solid",borderDashedValue:[2,2]},polygon:{style:"fill",color:pt.BLUE,borderColor:pt.BLUE,borderSize:1,borderStyle:"solid",borderDashedValue:[2,2]},circle:{style:"fill",color:r,borderColor:pt.BLUE,borderSize:1,borderStyle:"solid",borderDashedValue:[2,2]},arc:{style:"solid",color:pt.BLUE,size:1,dashedValue:[2,2]},text:e()}}function gl(){return{size:1,color:"#DDDDDD",fill:!0,activeBackgroundColor:vr(pt.BLUE,.08)}}function ml(){return{grid:dl(),candle:hl(),indicator:vl(),xAxis:Ki(),yAxis:Ki(),separator:gl(),crosshair:fl(),overlay:pl()}}function hi(t,r,e,a){var i=t.result,n=t.figures,o=t.styles,s=ge(o,"circles",e.circles),l=s.length,c=ge(o,"bars",e.bars),u=c.length,d=ge(o,"lines",e.lines),v=d.length,h=0,f=0,p=0,g,m=0;n.forEach(function(b){var x;switch(b.type){case"circle":{m=h;var L=s[h%l];g=U(U({},L),{color:L.noChangeColor}),h++;break}case"bar":{m=f;var _=c[f%u];g=U(U({},_),{color:_.noChangeColor}),f++;break}case"line":{m=p,g=d[p%v],p++;break}}if(P(b.type)){var M=(x=b.styles)===null||x===void 0?void 0:x.call(b,{data:{prev:i[r-1],current:i[r],next:i[r+1]},indicator:t,defaultStyles:e});a(b,U(U({},g),M),m)}})}var lo=function(){function t(r){this.precision=4,this.calcParams=[],this.shouldOhlc=!1,this.shouldFormatBigNumber=!1,this.visible=!0,this.zLevel=0,this.series="normal",this.figures=[],this.minValue=null,this.maxValue=null,this.styles=null,this.shouldUpdate=function(e,a){var i=JSON.stringify(e.calcParams)!==JSON.stringify(a.calcParams)||e.figures!==a.figures||e.calc!==a.calc,n=i||e.shortName!==a.shortName||e.series!==a.series||e.minValue!==a.minValue||e.maxValue!==a.maxValue||e.precision!==a.precision||e.shouldOhlc!==a.shouldOhlc||e.shouldFormatBigNumber!==a.shouldFormatBigNumber||e.visible!==a.visible||e.zLevel!==a.zLevel||e.extendData!==a.extendData||e.regenerateFigures!==a.regenerateFigures||e.createTooltipDataSource!==a.createTooltipDataSource||e.draw!==a.draw;return{calc:i,draw:n}},this.calc=function(){return[]},this.regenerateFigures=null,this.createTooltipDataSource=null,this.draw=null,this.onDataStateChange=null,this.result=[],this._lockSeriesPrecision=!1,this.override(r),this._lockSeriesPrecision=!1}return t.prototype.override=function(r){var e,a,i=this,n=i.result,o=ga(i,["result"]);this._prevIndicator=U(U({},Er(o)),{result:n});var s=r.id,l=r.name,c=r.shortName,u=r.precision,d=r.styles,v=r.figures,h=r.calcParams,f=ga(r,["id","name","shortName","precision","styles","figures","calcParams"]);!Gt(this.id)&&Gt(s)&&(this.id=s),Gt(this.name)||(this.name=l??""),this.shortName=(e=c??this.shortName)!==null&&e!==void 0?e:this.name,vt(u)&&(this.precision=u,this._lockSeriesPrecision=!0),P(d)&&((a=this.styles)!==null&&a!==void 0||(this.styles={}),he(this.styles,d)),he(this,f),P(h)&&(this.calcParams=h,Me(this.regenerateFigures)&&(this.figures=this.regenerateFigures(this.calcParams))),this.figures=v??this.figures},t.prototype.setSeriesPrecision=function(r){this._lockSeriesPrecision||(this.precision=r)},t.prototype.shouldUpdateImp=function(){var r=this._prevIndicator.zLevel!==this.zLevel,e=this.shouldUpdate(this._prevIndicator,this);return Qr(e)?{calc:e,draw:e,sort:r}:U(U({},e),{sort:r})},t.prototype.calcImp=function(r){return no(this,void 0,void 0,function(){var e;return oo(this,function(a){switch(a.label){case 0:return a.trys.push([0,2,,3]),[4,this.calc(r,this)];case 1:return e=a.sent(),this.result=e,[2,!0];case 2:return a.sent(),[2,!1];case 3:return[2]}})})},t.extend=function(r){var e=function(a){St(i,a);function i(){return a.call(this,r)||this}return i}(t);return e},t}(),yl={name:"AVP",shortName:"AVP",series:"price",precision:2,figures:[{key:"avp",title:"AVP: ",type:"line"}],calc:function(t){var r=0,e=0;return t.map(function(a){var i,n,o={},s=(i=a.turnover)!==null&&i!==void 0?i:0,l=(n=a.volume)!==null&&n!==void 0?n:0;return r+=s,e+=l,e!==0&&(o.avp=r/e),o})}},_l={name:"AO",shortName:"AO",calcParams:[5,34],figures:[{key:"ao",title:"AO: ",type:"bar",baseValue:0,styles:function(t){var r,e,a=t.data,i=t.indicator,n=t.defaultStyles,o=a.prev,s=a.current,l=(r=o==null?void 0:o.ao)!==null&&r!==void 0?r:Number.MIN_SAFE_INTEGER,c=(e=s==null?void 0:s.ao)!==null&&e!==void 0?e:Number.MIN_SAFE_INTEGER,u="";c>l?u=ge(i.styles,"bars[0].upColor",n.bars[0].upColor):u=ge(i.styles,"bars[0].downColor",n.bars[0].downColor);var d=c>l?"stroke":"fill";return{color:u,style:d,borderColor:u}}}],calc:function(t,r){var e=r.calcParams,a=Math.max(e[0],e[1]),i=0,n=0,o=0,s=0;return t.map(function(l,c){var u={},d=(l.low+l.high)/2;if(i+=d,n+=d,c>=e[0]-1){o=i/e[0];var v=t[c-(e[0]-1)];i-=(v.low+v.high)/2}if(c>=e[1]-1){s=n/e[1];var v=t[c-(e[1]-1)];n-=(v.low+v.high)/2}return c>=a-1&&(u.ao=o-s),u})}},Cl={name:"BIAS",shortName:"BIAS",calcParams:[6,12,24],figures:[{key:"bias1",title:"BIAS6: ",type:"line"},{key:"bias2",title:"BIAS12: ",type:"line"},{key:"bias3",title:"BIAS24: ",type:"line"}],regenerateFigures:function(t){return t.map(function(r,e){return{key:"bias".concat(e+1),title:"BIAS".concat(r,": "),type:"line"}})},calc:function(t,r){var e=r.calcParams,a=r.figures,i=[];return t.map(function(n,o){var s={},l=n.close;return e.forEach(function(c,u){var d;if(i[u]=((d=i[u])!==null&&d!==void 0?d:0)+l,o>=c-1){var v=i[u]/e[u];s[a[u].key]=(l-v)/v*100,i[u]-=t[o-(c-1)].close}}),s})}};function bl(t,r){var e=t.length,a=0;return t.forEach(function(i){var n=i.close-r;a+=n*n}),a=Math.abs(a),Math.sqrt(a/e)}var xl={name:"BOLL",shortName:"BOLL",series:"price",calcParams:[20,2],precision:2,shouldOhlc:!0,figures:[{key:"up",title:"UP: ",type:"line"},{key:"mid",title:"MID: ",type:"line"},{key:"dn",title:"DN: ",type:"line"}],calc:function(t,r){var e=r.calcParams,a=e[0]-1,i=0;return t.map(function(n,o){var s=n.close,l={};if(i+=s,o>=a){l.mid=i/e[0];var c=bl(t.slice(o-a,o+1),l.mid);l.up=l.mid+e[1]*c,l.dn=l.mid-e[1]*c,i-=t[o-a].close}return l})}},wl={name:"BRAR",shortName:"BRAR",calcParams:[26],figures:[{key:"br",title:"BR: ",type:"line"},{key:"ar",title:"AR: ",type:"line"}],calc:function(t,r){var e=r.calcParams,a=0,i=0,n=0,o=0;return t.map(function(s,l){var c,u,d={},v=s.high,h=s.low,f=s.open,p=((c=t[l-1])!==null&&c!==void 0?c:s).close;if(n+=v-f,o+=f-h,a+=v-p,i+=p-h,l>=e[0]-1){o!==0?d.ar=n/o*100:d.ar=0,i!==0?d.br=a/i*100:d.br=0;var g=t[l-(e[0]-1)],m=g.high,b=g.low,x=g.open,L=((u=t[l-e[0]])!==null&&u!==void 0?u:t[l-(e[0]-1)]).close;a-=m-L,i-=L-b,n-=m-x,o-=x-b}return d})}},Ll={name:"BBI",shortName:"BBI",series:"price",precision:2,calcParams:[3,6,12,24],shouldOhlc:!0,figures:[{key:"bbi",title:"BBI: ",type:"line"}],calc:function(t,r){var e=r.calcParams,a=Math.max.apply(Math,dr([],ar(e),!1)),i=[],n=[];return t.map(function(o,s){var l={},c=o.close;if(e.forEach(function(d,v){var h;i[v]=((h=i[v])!==null&&h!==void 0?h:0)+c,s>=d-1&&(n[v]=i[v]/d,i[v]-=t[s-(d-1)].close)}),s>=a-1){var u=0;n.forEach(function(d){u+=d}),l.bbi=u/4}return l})}},kl={name:"CCI",shortName:"CCI",calcParams:[20],figures:[{key:"cci",title:"CCI: ",type:"line"}],calc:function(t,r){var e=r.calcParams,a=e[0]-1,i=0,n=[];return t.map(function(o,s){var l={},c=(o.high+o.low+o.close)/3;if(i+=c,n.push(c),s>=a){var u=i/e[0],d=n.slice(s-a,s+1),v=0;d.forEach(function(p){v+=Math.abs(p-u)});var h=v/e[0];l.cci=h!==0?(c-u)/h/.015:0;var f=(t[s-a].high+t[s-a].low+t[s-a].close)/3;i-=f}return l})}},Sl={name:"CR",shortName:"CR",calcParams:[26,10,20,40,60],figures:[{key:"cr",title:"CR: ",type:"line"},{key:"ma1",title:"MA1: ",type:"line"},{key:"ma2",title:"MA2: ",type:"line"},{key:"ma3",title:"MA3: ",type:"line"},{key:"ma4",title:"MA4: ",type:"line"}],calc:function(t,r){var e=r.calcParams,a=Math.ceil(e[1]/2.5+1),i=Math.ceil(e[2]/2.5+1),n=Math.ceil(e[3]/2.5+1),o=Math.ceil(e[4]/2.5+1),s=0,l=[],c=0,u=[],d=0,v=[],h=0,f=[],p=[];return t.forEach(function(g,m){var b,x,L,_,M,w={},k=(b=t[m-1])!==null&&b!==void 0?b:g,C=(k.high+k.close+k.low+k.open)/4,y=Math.max(0,g.high-C),T=Math.max(0,C-g.low);m>=e[0]-1&&(T!==0?w.cr=y/T*100:w.cr=0,s+=w.cr,c+=w.cr,d+=w.cr,h+=w.cr,m>=e[0]+e[1]-2&&(l.push(s/e[1]),m>=e[0]+e[1]+a-3&&(w.ma1=l[l.length-1-a]),s-=(x=p[m-(e[1]-1)].cr)!==null&&x!==void 0?x:0),m>=e[0]+e[2]-2&&(u.push(c/e[2]),m>=e[0]+e[2]+i-3&&(w.ma2=u[u.length-1-i]),c-=(L=p[m-(e[2]-1)].cr)!==null&&L!==void 0?L:0),m>=e[0]+e[3]-2&&(v.push(d/e[3]),m>=e[0]+e[3]+n-3&&(w.ma3=v[v.length-1-n]),d-=(_=p[m-(e[3]-1)].cr)!==null&&_!==void 0?_:0),m>=e[0]+e[4]-2&&(f.push(h/e[4]),m>=e[0]+e[4]+o-3&&(w.ma4=f[f.length-1-o]),h-=(M=p[m-(e[4]-1)].cr)!==null&&M!==void 0?M:0)),p.push(w)}),p}},Ml={name:"DMA",shortName:"DMA",calcParams:[10,50,10],figures:[{key:"dma",title:"DMA: ",type:"line"},{key:"ama",title:"AMA: ",type:"line"}],calc:function(t,r){var e=r.calcParams,a=Math.max(e[0],e[1]),i=0,n=0,o=0,s=[];return t.forEach(function(l,c){var u,d={},v=l.close;i+=v,n+=v;var h=0,f=0;if(c>=e[0]-1&&(h=i/e[0],i-=t[c-(e[0]-1)].close),c>=e[1]-1&&(f=n/e[1],n-=t[c-(e[1]-1)].close),c>=a-1){var p=h-f;d.dma=p,o+=p,c>=a+e[2]-2&&(d.ama=o/e[2],o-=(u=s[c-(e[2]-1)].dma)!==null&&u!==void 0?u:0)}s.push(d)}),s}},Tl={name:"DMI",shortName:"DMI",calcParams:[14,6],figures:[{key:"pdi",title:"PDI: ",type:"line"},{key:"mdi",title:"MDI: ",type:"line"},{key:"adx",title:"ADX: ",type:"line"},{key:"adxr",title:"ADXR: ",type:"line"}],calc:function(t,r){var e=r.calcParams,a=0,i=0,n=0,o=0,s=0,l=0,c=0,u=0,d=[];return t.forEach(function(v,h){var f,p,g={},m=(f=t[h-1])!==null&&f!==void 0?f:v,b=m.close,x=v.high,L=v.low,_=x-L,M=Math.abs(x-b),w=Math.abs(b-L),k=x-m.high,C=m.low-L,y=Math.max(Math.max(_,M),w),T=k>0&&k>C?k:0,I=C>0&&C>k?C:0;if(a+=y,i+=T,n+=I,h>=e[0]-1){h>e[0]-1?(o=o-o/e[0]+y,s=s-s/e[0]+T,l=l-l/e[0]+I):(o=a,s=i,l=n);var E=0,A=0;o!==0&&(E=s*100/o,A=l*100/o),g.pdi=E,g.mdi=A;var F=0;A+E!==0&&(F=Math.abs(A-E)/(A+E)*100),c+=F,h>=e[0]*2-2&&(h>e[0]*2-2?u=(u*(e[0]-1)+F)/e[0]:u=c/e[0],g.adx=u,h>=e[0]*2+e[1]-3&&(g.adxr=(((p=d[h-(e[1]-1)].adx)!==null&&p!==void 0?p:0)+u)/2))}d.push(g)}),d}},El={name:"EMV",shortName:"EMV",calcParams:[14,9],figures:[{key:"emv",title:"EMV: ",type:"line"},{key:"maEmv",title:"MAEMV: ",type:"line"}],calc:function(t,r){var e=r.calcParams,a=0,i=[];return t.map(function(n,o){var s,l={};if(o>0){var c=t[o-1],u=n.high,d=n.low,v=(s=n.volume)!==null&&s!==void 0?s:0,h=(u+d)/2-(c.high+c.low)/2;if(v===0||u-d===0)l.emv=0;else{var f=v/1e8/(u-d);l.emv=h/f}a+=l.emv,i.push(l.emv),o>=e[0]&&(l.maEmv=a/e[0],a-=i[o-e[0]])}return l})}},Il={name:"EMA",shortName:"EMA",series:"price",calcParams:[6,12,20],precision:2,shouldOhlc:!0,figures:[{key:"ema1",title:"EMA6: ",type:"line"},{key:"ema2",title:"EMA12: ",type:"line"},{key:"ema3",title:"EMA20: ",type:"line"}],regenerateFigures:function(t){return t.map(function(r,e){return{key:"ema".concat(e+1),title:"EMA".concat(r,": "),type:"line"}})},calc:function(t,r){var e=r.calcParams,a=r.figures,i=0,n=[];return t.map(function(o,s){var l={},c=o.close;return i+=c,e.forEach(function(u,d){s>=u-1&&(s>u-1?n[d]=(2*c+(u-1)*n[d])/(u+1):n[d]=i/u,l[a[d].key]=n[d])}),l})}},Al={name:"MTM",shortName:"MTM",calcParams:[12,6],figures:[{key:"mtm",title:"MTM: ",type:"line"},{key:"maMtm",title:"MAMTM: ",type:"line"}],calc:function(t,r){var e=r.calcParams,a=0,i=[];return t.forEach(function(n,o){var s,l={};if(o>=e[0]){var c=n.close,u=t[o-e[0]].close;l.mtm=c-u,a+=l.mtm,o>=e[0]+e[1]-1&&(l.maMtm=a/e[1],a-=(s=i[o-(e[1]-1)].mtm)!==null&&s!==void 0?s:0)}i.push(l)}),i}},Dl={name:"MA",shortName:"MA",series:"price",calcParams:[5,10,30,60],precision:2,shouldOhlc:!0,figures:[{key:"ma1",title:"MA5: ",type:"line"},{key:"ma2",title:"MA10: ",type:"line"},{key:"ma3",title:"MA30: ",type:"line"},{key:"ma4",title:"MA60: ",type:"line"}],regenerateFigures:function(t){return t.map(function(r,e){return{key:"ma".concat(e+1),title:"MA".concat(r,": "),type:"line"}})},calc:function(t,r){var e=r.calcParams,a=r.figures,i=[];return t.map(function(n,o){var s={},l=n.close;return e.forEach(function(c,u){var d;i[u]=((d=i[u])!==null&&d!==void 0?d:0)+l,o>=c-1&&(s[a[u].key]=i[u]/c,i[u]-=t[o-(c-1)].close)}),s})}},Pl={name:"MACD",shortName:"MACD",calcParams:[12,26,9],figures:[{key:"dif",title:"DIF: ",type:"line"},{key:"dea",title:"DEA: ",type:"line"},{key:"macd",title:"MACD: ",type:"bar",baseValue:0,styles:function(t){var r,e,a=t.data,i=t.indicator,n=t.defaultStyles,o=a.prev,s=a.current,l=(r=o==null?void 0:o.macd)!==null&&r!==void 0?r:Number.MIN_SAFE_INTEGER,c=(e=s==null?void 0:s.macd)!==null&&e!==void 0?e:Number.MIN_SAFE_INTEGER,u="";c>0?u=ge(i.styles,"bars[0].upColor",n.bars[0].upColor):c<0?u=ge(i.styles,"bars[0].downColor",n.bars[0].downColor):u=ge(i.styles,"bars[0].noChangeColor",n.bars[0].noChangeColor);var d=l<c?"stroke":"fill";return{style:d,color:u,borderColor:u}}}],calc:function(t,r){var e=r.calcParams,a=0,i=0,n=0,o=0,s=0,l=0,c=Math.max(e[0],e[1]);return t.map(function(u,d){var v={},h=u.close;return a+=h,d>=e[0]-1&&(d>e[0]-1?i=(2*h+(e[0]-1)*i)/(e[0]+1):i=a/e[0]),d>=e[1]-1&&(d>e[1]-1?n=(2*h+(e[1]-1)*n)/(e[1]+1):n=a/e[1]),d>=c-1&&(o=i-n,v.dif=o,s+=o,d>=c+e[2]-2&&(d>c+e[2]-2?l=(o*2+l*(e[2]-1))/(e[2]+1):l=s/e[2],v.macd=(o-l)*2,v.dea=l)),v})}},Fl={name:"OBV",shortName:"OBV",calcParams:[30],figures:[{key:"obv",title:"OBV: ",type:"line"},{key:"maObv",title:"MAOBV: ",type:"line"}],calc:function(t,r){var e=r.calcParams,a=0,i=0,n=[];return t.forEach(function(o,s){var l,c,u,d,v=(l=t[s-1])!==null&&l!==void 0?l:o;o.close<v.close?i-=(c=o.volume)!==null&&c!==void 0?c:0:o.close>v.close&&(i+=(u=o.volume)!==null&&u!==void 0?u:0);var h={obv:i};a+=i,s>=e[0]-1&&(h.maObv=a/e[0],a-=(d=n[s-(e[0]-1)].obv)!==null&&d!==void 0?d:0),n.push(h)}),n}},Rl={name:"PVT",shortName:"PVT",figures:[{key:"pvt",title:"PVT: ",type:"line"}],calc:function(t){var r=0;return t.map(function(e,a){var i,n,o={},s=e.close,l=(i=e.volume)!==null&&i!==void 0?i:1,c=((n=t[a-1])!==null&&n!==void 0?n:e).close,u=0,d=c*l;return d!==0&&(u=(s-c)/d),r+=u,o.pvt=r,o})}},Bl={name:"PSY",shortName:"PSY",calcParams:[12,6],figures:[{key:"psy",title:"PSY: ",type:"line"},{key:"maPsy",title:"MAPSY: ",type:"line"}],calc:function(t,r){var e=r.calcParams,a=0,i=0,n=[],o=[];return t.forEach(function(s,l){var c,u,d={},v=((c=t[l-1])!==null&&c!==void 0?c:s).close,h=s.close-v>0?1:0;n.push(h),a+=h,l>=e[0]-1&&(d.psy=a/e[0]*100,i+=d.psy,l>=e[0]+e[1]-2&&(d.maPsy=i/e[1],i-=(u=o[l-(e[1]-1)].psy)!==null&&u!==void 0?u:0),a-=n[l-(e[0]-1)]),o.push(d)}),o}},Nl={name:"ROC",shortName:"ROC",calcParams:[12,6],figures:[{key:"roc",title:"ROC: ",type:"line"},{key:"maRoc",title:"MAROC: ",type:"line"}],calc:function(t,r){var e=r.calcParams,a=[],i=0;return t.forEach(function(n,o){var s,l,c={};if(o>=e[0]-1){var u=n.close,d=((s=t[o-e[0]])!==null&&s!==void 0?s:t[o-(e[0]-1)]).close;d!==0?c.roc=(u-d)/d*100:c.roc=0,i+=c.roc,o>=e[0]-1+e[1]-1&&(c.maRoc=i/e[1],i-=(l=a[o-(e[1]-1)].roc)!==null&&l!==void 0?l:0)}a.push(c)}),a}},Ol={name:"RSI",shortName:"RSI",calcParams:[6,12,24],figures:[{key:"rsi1",title:"RSI1: ",type:"line"},{key:"rsi2",title:"RSI2: ",type:"line"},{key:"rsi3",title:"RSI3: ",type:"line"}],regenerateFigures:function(t){return t.map(function(r,e){var a=e+1;return{key:"rsi".concat(a),title:"RSI".concat(a,": "),type:"line"}})},calc:function(t,r){var e=r.calcParams,a=r.figures,i=[],n=[];return t.map(function(o,s){var l,c={},u=((l=t[s-1])!==null&&l!==void 0?l:o).close,d=o.close-u;return e.forEach(function(v,h){var f,p,g;if(d>0?i[h]=((f=i[h])!==null&&f!==void 0?f:0)+d:n[h]=((p=n[h])!==null&&p!==void 0?p:0)+Math.abs(d),s>=v-1){n[h]!==0?c[a[h].key]=100-100/(1+i[h]/n[h]):c[a[h].key]=0;var m=t[s-(v-1)],b=(g=t[s-v])!==null&&g!==void 0?g:m,x=m.close-b.close;x>0?i[h]-=x:n[h]-=Math.abs(x)}}),c})}},Vl={name:"SMA",shortName:"SMA",series:"price",calcParams:[12,2],precision:2,figures:[{key:"sma",title:"SMA: ",type:"line"}],shouldOhlc:!0,calc:function(t,r){var e=r.calcParams,a=0,i=0;return t.map(function(n,o){var s={},l=n.close;return a+=l,o>=e[0]-1&&(o>e[0]-1?i=(l*e[1]+i*(e[0]-e[1]+1))/(e[0]+1):i=a/e[0],s.sma=i),s})}},zl={name:"KDJ",shortName:"KDJ",calcParams:[9,3,3],figures:[{key:"k",title:"K: ",type:"line"},{key:"d",title:"D: ",type:"line"},{key:"j",title:"J: ",type:"line"}],calc:function(t,r){var e=r.calcParams,a=[];return t.forEach(function(i,n){var o,s,l,c,u={},d=i.close;if(n>=e[0]-1){var v=so(t.slice(n-(e[0]-1),n+1),"high","low"),h=v[0],f=v[1],p=h-f,g=(d-f)/(p===0?1:p)*100;u.k=((e[1]-1)*((s=(o=a[n-1])===null||o===void 0?void 0:o.k)!==null&&s!==void 0?s:50)+g)/e[1],u.d=((e[2]-1)*((c=(l=a[n-1])===null||l===void 0?void 0:l.d)!==null&&c!==void 0?c:50)+u.k)/e[2],u.j=3*u.k-2*u.d}a.push(u)}),a}},$l={name:"SAR",shortName:"SAR",series:"price",calcParams:[2,2,20],precision:2,shouldOhlc:!0,figures:[{key:"sar",title:"SAR: ",type:"circle",styles:function(t){var r,e,a,i=t.data,n=t.indicator,o=t.defaultStyles,s=i.current,l=(r=s==null?void 0:s.sar)!==null&&r!==void 0?r:Number.MIN_SAFE_INTEGER,c=(((e=s==null?void 0:s.high)!==null&&e!==void 0?e:0)+((a=s==null?void 0:s.low)!==null&&a!==void 0?a:0))/2,u=l<c?ge(n.styles,"circles[0].upColor",o.circles[0].upColor):ge(n.styles,"circles[0].downColor",o.circles[0].downColor);return{color:u}}}],calc:function(t,r){var e=r.calcParams,a=e[0]/100,i=e[1]/100,n=e[2]/100,o=a,s=-100,l=!1,c=0;return t.map(function(u,d){var v=c,h=u.high,f=u.low;if(l){(s===-100||s<h)&&(s=h,o=Math.min(o+i,n)),c=v+o*(s-v);var p=Math.min(t[Math.max(1,d)-1].low,f);c>u.low?(c=s,o=a,s=-100,l=!l):c>p&&(c=p)}else{(s===-100||s>f)&&(s=f,o=Math.min(o+i,n)),c=v+o*(s-v);var g=Math.max(t[Math.max(1,d)-1].high,h);c<u.high?(c=s,o=0,s=-100,l=!l):c<g&&(c=g)}return{high:h,low:f,sar:c}})}},Wl={name:"TRIX",shortName:"TRIX",calcParams:[12,9],figures:[{key:"trix",title:"TRIX: ",type:"line"},{key:"maTrix",title:"MATRIX: ",type:"line"}],calc:function(t,r){var e=r.calcParams,a=0,i=0,n=0,o=0,s=0,l=0,c=0,u=[];return t.forEach(function(d,v){var h,f={},p=d.close;if(a+=p,v>=e[0]-1&&(v>e[0]-1?i=(2*p+(e[0]-1)*i)/(e[0]+1):i=a/e[0],s+=i,v>=e[0]*2-2&&(v>e[0]*2-2?n=(2*i+(e[0]-1)*n)/(e[0]+1):n=s/e[0],l+=n,v>=e[0]*3-3))){var g=0,m=0;v>e[0]*3-3?(g=(2*n+(e[0]-1)*o)/(e[0]+1),m=(g-o)/o*100):g=l/e[0],o=g,f.trix=m,c+=m,v>=e[0]*3+e[1]-4&&(f.maTrix=c/e[1],c-=(h=u[v-(e[1]-1)].trix)!==null&&h!==void 0?h:0)}u.push(f)}),u}};function Gi(){return{key:"volume",title:"VOLUME: ",type:"bar",baseValue:0,styles:function(t){var r=t.data,e=t.indicator,a=t.defaultStyles,i=r.current,n=ge(e.styles,"bars[0].noChangeColor",a.bars[0].noChangeColor);return P(i)&&(i.close>i.open?n=ge(e.styles,"bars[0].upColor",a.bars[0].upColor):i.close<i.open&&(n=ge(e.styles,"bars[0].downColor",a.bars[0].downColor))),{color:n}}}}var Yl={name:"VOL",shortName:"VOL",series:"volume",calcParams:[5,10,20],shouldFormatBigNumber:!0,precision:0,minValue:0,figures:[{key:"ma1",title:"MA5: ",type:"line"},{key:"ma2",title:"MA10: ",type:"line"},{key:"ma3",title:"MA20: ",type:"line"},Gi()],regenerateFigures:function(t){var r=t.map(function(e,a){return{key:"ma".concat(a+1),title:"MA".concat(e,": "),type:"line"}});return r.push(Gi()),r},calc:function(t,r){var e=r.calcParams,a=r.figures,i=[];return t.map(function(n,o){var s,l=(s=n.volume)!==null&&s!==void 0?s:0,c={volume:l,open:n.open,close:n.close};return e.forEach(function(u,d){var v,h;i[d]=((v=i[d])!==null&&v!==void 0?v:0)+l,o>=u-1&&(c[a[d].key]=i[d]/u,i[d]-=(h=t[o-(u-1)].volume)!==null&&h!==void 0?h:0)}),c})}},Zl={name:"VR",shortName:"VR",calcParams:[26,6],figures:[{key:"vr",title:"VR: ",type:"line"},{key:"maVr",title:"MAVR: ",type:"line"}],calc:function(t,r){var e=r.calcParams,a=0,i=0,n=0,o=0,s=[];return t.forEach(function(l,c){var u,d,v,h,f,p={},g=l.close,m=((u=t[c-1])!==null&&u!==void 0?u:l).close,b=(d=l.volume)!==null&&d!==void 0?d:0;if(g>m?a+=b:g<m?i+=b:n+=b,c>=e[0]-1){var x=n/2;i+x===0?p.vr=0:p.vr=(a+x)/(i+x)*100,o+=p.vr,c>=e[0]+e[1]-2&&(p.maVr=o/e[1],o-=(v=s[c-(e[1]-1)].vr)!==null&&v!==void 0?v:0);var L=t[c-(e[0]-1)],_=(h=t[c-e[0]])!==null&&h!==void 0?h:L,M=L.close,w=(f=L.volume)!==null&&f!==void 0?f:0;M>_.close?a-=w:M<_.close?i-=w:n-=w}s.push(p)}),s}},Hl={name:"WR",shortName:"WR",calcParams:[6,10,14],figures:[{key:"wr1",title:"WR1: ",type:"line"},{key:"wr2",title:"WR2: ",type:"line"},{key:"wr3",title:"WR3: ",type:"line"}],regenerateFigures:function(t){return t.map(function(r,e){return{key:"wr".concat(e+1),title:"WR".concat(e+1,": "),type:"line"}})},calc:function(t,r){var e=r.calcParams,a=r.figures;return t.map(function(i,n){var o={},s=i.close;return e.forEach(function(l,c){var u=l-1;if(n>=u){var d=so(t.slice(n-u,n+1),"high","low"),v=d[0],h=d[1],f=v-h;o[a[c].key]=f===0?0:(s-v)/f*100}}),o})}},vi={},jl=[yl,_l,Cl,xl,wl,Ll,kl,Sl,Ml,Tl,El,Il,Al,Dl,Pl,Fl,Rl,Bl,Nl,Ol,Vl,zl,$l,Wl,Yl,Zl,Hl];jl.forEach(function(t){vi[t.name]=lo.extend(t)});function Xl(t){vi[t.name]=lo.extend(t)}function co(t){var r;return(r=vi[t])!==null&&r!==void 0?r:null}function $e(t,r){var e,a=(e=r==null?void 0:r.ignoreEvent)!==null&&e!==void 0?e:!1;return Qr(a)?!a:!a.includes(t)}var Ui=1,jr=-1,Kl="overlay_",Lr="overlay_figure_",uo=function(){function t(r){this.groupId="",this.totalStep=1,this.currentStep=Ui,this.lock=!1,this.visible=!0,this.zLevel=0,this.needDefaultPointFigure=!1,this.needDefaultXAxisFigure=!1,this.needDefaultYAxisFigure=!1,this.mode="normal",this.modeSensitivity=8,this.points=[],this.styles=null,this.createPointFigures=null,this.createXAxisFigures=null,this.createYAxisFigures=null,this.performEventPressedMove=null,this.performEventMoveForDrawing=null,this.onDrawStart=null,this.onDrawing=null,this.onDrawEnd=null,this.onClick=null,this.onDoubleClick=null,this.onRightClick=null,this.onPressedMoveStart=null,this.onPressedMoving=null,this.onPressedMoveEnd=null,this.onMouseMove=null,this.onMouseEnter=null,this.onMouseLeave=null,this.onRemoved=null,this.onSelected=null,this.onDeselected=null,this._prevZLevel=0,this._prevPressedPoint=null,this._prevPressedPoints=[],this.override(r)}return t.prototype.override=function(r){var e,a;this._prevOverlay=Er(this);var i=r.id,n=r.name;r.currentStep;var o=r.points,s=r.styles,l=ga(r,["id","name","currentStep","points","styles"]);if(he(this,l),Gt(this.name)||(this.name=n??""),!Gt(this.id)&&Gt(i)&&(this.id=i),P(s)&&((e=this.styles)!==null&&e!==void 0||(this.styles={}),he(this.styles,s)),He(o)&&o.length>0){var c=0;if(this.points=dr([],ar(o),!1),o.length>=this.totalStep-1?(this.currentStep=jr,c=this.totalStep-1):(this.currentStep=o.length+1,c=o.length),Me(this.performEventMoveForDrawing))for(var u=0;u<c;u++)this.performEventMoveForDrawing({currentStep:u+2,mode:this.mode,points:this.points,performPointIndex:u,performPoint:this.points[u]});this.currentStep===jr&&((a=this.performEventPressedMove)===null||a===void 0||a.call(this,{currentStep:this.currentStep,mode:this.mode,points:this.points,performPointIndex:this.points.length-1,performPoint:this.points[this.points.length-1]}))}},t.prototype.getPrevZLevel=function(){return this._prevZLevel},t.prototype.setPrevZLevel=function(r){this._prevZLevel=r},t.prototype.shouldUpdate=function(){var r=this._prevOverlay.zLevel!==this.zLevel,e=r||JSON.stringify(this._prevOverlay.points)!==JSON.stringify(this.points)||this._prevOverlay.visible!==this.visible||this._prevOverlay.extendData!==this.extendData||this._prevOverlay.styles!==this.styles;return{sort:r,draw:e}},t.prototype.nextStep=function(){this.currentStep===this.totalStep-1?this.currentStep=jr:this.currentStep++},t.prototype.forceComplete=function(){this.currentStep=jr},t.prototype.isDrawing=function(){return this.currentStep!==jr},t.prototype.isStart=function(){return this.currentStep===Ui},t.prototype.eventMoveForDrawing=function(r){var e,a=this.currentStep-1,i={};vt(r.timestamp)&&(i.timestamp=r.timestamp),vt(r.dataIndex)&&(i.dataIndex=r.dataIndex),vt(r.value)&&(i.value=r.value),this.points[a]=i,(e=this.performEventMoveForDrawing)===null||e===void 0||e.call(this,{currentStep:this.currentStep,mode:this.mode,points:this.points,performPointIndex:a,performPoint:i})},t.prototype.eventPressedPointMove=function(r,e){var a;this.points[e].timestamp=r.timestamp,vt(r.value)&&(this.points[e].value=r.value),(a=this.performEventPressedMove)===null||a===void 0||a.call(this,{currentStep:this.currentStep,points:this.points,mode:this.mode,performPointIndex:e,performPoint:this.points[e]})},t.prototype.startPressedMove=function(r){this._prevPressedPoint=U({},r),this._prevPressedPoints=Er(this.points)},t.prototype.eventPressedOtherMove=function(r,e){if(this._prevPressedPoint!==null){var a=null;vt(r.dataIndex)&&vt(this._prevPressedPoint.dataIndex)&&(a=r.dataIndex-this._prevPressedPoint.dataIndex);var i=null;vt(r.value)&&vt(this._prevPressedPoint.value)&&(i=r.value-this._prevPressedPoint.value),this.points=this._prevPressedPoints.map(function(n){var o;vt(n.timestamp)&&(n.dataIndex=e.timestampToDataIndex(n.timestamp));var s=U({},n);return vt(a)&&vt(n.dataIndex)&&(s.dataIndex=n.dataIndex+a,s.timestamp=(o=e.dataIndexToTimestamp(s.dataIndex))!==null&&o!==void 0?o:void 0),vt(i)&&vt(n.value)&&(s.value=n.value+i),s})}},t.extend=function(r){var e=function(a){St(i,a);function i(){return a.call(this,r)||this}return i}(t);return e},t}(),Gl={name:"fibonacciLine",totalStep:3,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:function(t){var r,e,a,i=t.chart,n=t.coordinates,o=t.bounding,s=t.overlay,l=t.yAxis,c=s.points;if(n.length>0){var u=0;if(!((r=l==null?void 0:l.isInCandle())!==null&&r!==void 0)||r)u=(a=(e=i.getSymbol())===null||e===void 0?void 0:e.pricePrecision)!==null&&a!==void 0?a:2;else{var d=i.getIndicators({paneId:s.paneId});d.forEach(function(x){u=Math.max(u,x.precision)})}var v=[],h=[],f=0,p=o.width;if(n.length>1&&vt(c[0].value)&&vt(c[1].value)){var g=[1,.786,.618,.5,.382,.236,0],m=n[0].y-n[1].y,b=c[0].value-c[1].value;g.forEach(function(x){var L,_=n[1].y+m*x,M=i.getDecimalFold().format(i.getThousandsSeparator().format((((L=c[1].value)!==null&&L!==void 0?L:0)+b*x).toFixed(u)));v.push({coordinates:[{x:f,y:_},{x:p,y:_}]}),h.push({x:f,y:_,text:"".concat(M," (").concat((x*100).toFixed(1),"%)"),baseline:"bottom"})})}return[{type:"line",attrs:v},{type:"text",isCheckEvent:!1,attrs:h}]}return[]}},Ul={name:"horizontalRayLine",totalStep:3,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:function(t){var r=t.coordinates,e=t.bounding,a={x:0,y:r[0].y};return P(r[1])&&r[0].x<r[1].x&&(a.x=e.width),[{type:"line",attrs:{coordinates:[r[0],a]}}]},performEventPressedMove:function(t){var r=t.points,e=t.performPoint;r[0].value=e.value,r[1].value=e.value},performEventMoveForDrawing:function(t){var r=t.currentStep,e=t.points,a=t.performPoint;r===2&&(e[0].value=a.value)}},Ql={name:"horizontalSegment",totalStep:3,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:function(t){var r=t.coordinates,e=[];return r.length===2&&e.push({coordinates:r}),[{type:"line",attrs:e}]},performEventPressedMove:function(t){var r=t.points,e=t.performPoint;r[0].value=e.value,r[1].value=e.value},performEventMoveForDrawing:function(t){var r=t.currentStep,e=t.points,a=t.performPoint;r===2&&(e[0].value=a.value)}},ql={name:"horizontalStraightLine",totalStep:2,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:function(t){var r=t.coordinates,e=t.bounding;return[{type:"line",attrs:{coordinates:[{x:0,y:r[0].y},{x:e.width,y:r[0].y}]}}]}},fi=function(){function t(){this._children=[],this._callbacks=new Map}return t.prototype.registerEvent=function(r,e){return this._callbacks.set(r,e),this},t.prototype.onEvent=function(r,e){var a=this._callbacks.get(r);return P(a)&&this.checkEventOn(e)?a(e):!1},t.prototype.dispatchEventToChildren=function(r,e){var a=this._children.length-1;if(a>-1){for(var i=a;i>-1;i--)if(this._children[i].dispatchEvent(r,e))return!0}return!1},t.prototype.dispatchEvent=function(r,e){return this.dispatchEventToChildren(r,e)?!0:this.onEvent(r,e)},t.prototype.addChild=function(r){return this._children.push(r),this},t.prototype.clear=function(){this._children=[]},t}(),ye=2,ho=function(t){St(r,t);function r(e){var a=t.call(this)||this;return a.attrs=e.attrs,a.styles=e.styles,a}return r.prototype.checkEventOn=function(e){return this.checkEventOnImp(e,this.attrs,this.styles)},r.prototype.setAttrs=function(e){return this.attrs=e,this},r.prototype.setStyles=function(e){return this.styles=e,this},r.prototype.draw=function(e){this.drawImp(e,this.attrs,this.styles)},r.extend=function(e){var a=function(i){St(n,i);function n(){return i!==null&&i.apply(this,arguments)||this}return n.prototype.checkEventOnImp=function(o,s,l){return e.checkEventOn(o,s,l)},n.prototype.drawImp=function(o,s,l){e.draw(o,s,l)},n}(r);return a},r}(fi);function vo(t,r){var e,a,i=[];i=i.concat(r);try{for(var n=Ze(i),o=n.next();!o.done;o=n.next()){var s=o.value,l=s.coordinates;if(l.length>1)for(var c=1;c<l.length;c++){var u=l[c-1],d=l[c];if(u.x===d.x){if(Math.abs(u.y-t.y)+Math.abs(d.y-t.y)-Math.abs(u.y-d.y)<ye+ye&&Math.abs(t.x-u.x)<ye)return!0}else{var v=Da(u,d),h=pi(v,t),f=Math.abs(h-t.y);if(Math.abs(u.x-t.x)+Math.abs(d.x-t.x)-Math.abs(u.x-d.x)<ye+ye&&f*f/(v[0]*v[0]+1)<ye*ye)return!0}}}}catch(p){e={error:p}}finally{try{o&&!o.done&&(a=n.return)&&a.call(n)}finally{if(e)throw e.error}}return!1}function pi(t,r){return t!==null?r.x*t[0]+t[1]:r.y}function qr(t,r,e){var a=Da(t,r);return pi(a,e)}function Da(t,r){var e=t.x-r.x;if(e!==0){var a=(t.y-r.y)/e,i=t.y-a*t.x;return[a,i]}return null}function fo(t,r,e){var a=r.length,i=vt(e)?e>0&&e<1?e:0:e?.5:0;if(i>0&&a>2){for(var n=r[0].x,o=r[0].y,s=1;s<a-1;s++){var l=r[s-1],c=r[s],u=r[s+1],d=c.x-l.x,v=c.y-l.y,h=u.x-c.x,f=u.y-c.y,p=u.x-l.x,g=u.y-l.y,m=Math.sqrt(d*d+v*v),b=Math.sqrt(h*h+f*f),x=b/(b+m),L=c.x+p*i*x,_=c.y+g*i*x;L=Math.min(L,Math.max(u.x,c.x)),_=Math.min(_,Math.max(u.y,c.y)),L=Math.max(L,Math.min(u.x,c.x)),_=Math.max(_,Math.min(u.y,c.y)),p=L-c.x,g=_-c.y;var M=c.x-p*m/b,w=c.y-g*m/b;M=Math.min(M,Math.max(l.x,c.x)),w=Math.min(w,Math.max(l.y,c.y)),M=Math.max(M,Math.min(l.x,c.x)),w=Math.max(w,Math.min(l.y,c.y)),p=c.x-M,g=c.y-w,L=c.x+p*b/m,_=c.y+g*b/m,t.bezierCurveTo(n,o,M,w,c.x,c.y),n=L,o=_}var k=r[a-1];t.bezierCurveTo(n,o,k.x,k.y,k.x,k.y)}else for(var s=1;s<a;s++)t.lineTo(r[s].x,r[s].y)}function Jl(t,r,e){var a=[];a=a.concat(r);var i=e.style,n=i===void 0?"solid":i,o=e.smooth,s=o===void 0?!1:o,l=e.size,c=l===void 0?1:l,u=e.color,d=u===void 0?"currentColor":u,v=e.dashedValue,h=v===void 0?[2,2]:v;t.lineWidth=c,t.strokeStyle=d,n==="dashed"?t.setLineDash(h):t.setLineDash([]);var f=c%2===1?.5:0;a.forEach(function(p){var g=p.coordinates;g.length>1&&(g.length===2&&(g[0].x===g[1].x||g[0].y===g[1].y)?(t.beginPath(),g[0].x===g[1].x?(t.moveTo(g[0].x+f,g[0].y),t.lineTo(g[1].x+f,g[1].y)):(t.moveTo(g[0].x,g[0].y+f),t.lineTo(g[1].x,g[1].y+f)),t.stroke(),t.closePath()):(t.save(),c%2===1&&t.translate(.5,.5),t.beginPath(),t.moveTo(g[0].x,g[0].y),fo(t,g,s),t.stroke(),t.closePath(),t.restore()))})}var t1={name:"line",checkEventOn:vo,draw:function(t,r,e){Jl(t,r,e)}};function po(t,r,e){var a=e??0,i=[];if(t.length>1)if(t[0].x===t[1].x){var n=0,o=r.height;if(i.push({coordinates:[{x:t[0].x,y:n},{x:t[0].x,y:o}]}),t.length>2){i.push({coordinates:[{x:t[2].x,y:n},{x:t[2].x,y:o}]});for(var s=t[0].x-t[2].x,l=0;l<a;l++){var c=s*(l+1);i.push({coordinates:[{x:t[0].x+c,y:n},{x:t[0].x+c,y:o}]})}}}else{var u=0,d=r.width,v=Da(t[0],t[1]),h=v[0],f=v[1];if(i.push({coordinates:[{x:u,y:u*h+f},{x:d,y:d*h+f}]}),t.length>2){var p=t[2].y-h*t[2].x;i.push({coordinates:[{x:u,y:u*h+p},{x:d,y:d*h+p}]});for(var s=f-p,l=0;l<a;l++){var g=f+s*(l+1);i.push({coordinates:[{x:u,y:u*h+g},{x:d,y:d*h+g}]})}}}return i}var e1={name:"parallelStraightLine",totalStep:4,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:function(t){var r=t.coordinates,e=t.bounding;return[{type:"line",attrs:po(r,e)}]}},r1={name:"priceChannelLine",totalStep:4,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:function(t){var r=t.coordinates,e=t.bounding;return[{type:"line",attrs:po(r,e,1)}]}},a1={name:"priceLine",totalStep:2,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:function(t){var r,e,a,i=t.chart,n=t.coordinates,o=t.bounding,s=t.overlay,l=t.yAxis,c=0;if(!((r=l==null?void 0:l.isInCandle())!==null&&r!==void 0)||r)c=(a=(e=i.getSymbol())===null||e===void 0?void 0:e.pricePrecision)!==null&&a!==void 0?a:2;else{var u=i.getIndicators({paneId:s.paneId});u.forEach(function(h){c=Math.max(c,h.precision)})}var d=s.points[0].value,v=d===void 0?0:d;return[{type:"line",attrs:{coordinates:[n[0],{x:o.width,y:n[0].y}]}},{type:"text",ignoreEvent:!0,attrs:{x:n[0].x,y:n[0].y,text:i.getDecimalFold().format(i.getThousandsSeparator().format(v.toFixed(c))),baseline:"bottom"}}]}};function i1(t,r){if(t.length>1){var e={x:0,y:0};return t[0].x===t[1].x&&t[0].y!==t[1].y?t[0].y<t[1].y?e={x:t[0].x,y:r.height}:e={x:t[0].x,y:0}:t[0].x>t[1].x?e={x:0,y:qr(t[0],t[1],{x:0,y:t[0].y})}:e={x:r.width,y:qr(t[0],t[1],{x:r.width,y:t[0].y})},{coordinates:[t[0],e]}}return[]}var n1={name:"rayLine",totalStep:3,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:function(t){var r=t.coordinates,e=t.bounding;return[{type:"line",attrs:i1(r,e)}]}},o1={name:"segment",totalStep:3,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:function(t){var r=t.coordinates;return r.length===2?[{type:"line",attrs:{coordinates:r}}]:[]}},s1={name:"straightLine",totalStep:3,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:function(t){var r=t.coordinates,e=t.bounding;return r.length===2?r[0].x===r[1].x?[{type:"line",attrs:{coordinates:[{x:r[0].x,y:0},{x:r[0].x,y:e.height}]}}]:[{type:"line",attrs:{coordinates:[{x:0,y:qr(r[0],r[1],{x:0,y:r[0].y})},{x:e.width,y:qr(r[0],r[1],{x:e.width,y:r[0].y})}]}}]:[]}},l1={name:"verticalRayLine",totalStep:3,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:function(t){var r=t.coordinates,e=t.bounding;if(r.length===2){var a={x:r[0].x,y:0};return r[0].y<r[1].y&&(a.y=e.height),[{type:"line",attrs:{coordinates:[r[0],a]}}]}return[]},performEventPressedMove:function(t){var r=t.points,e=t.performPoint;r[0].timestamp=e.timestamp,r[0].dataIndex=e.dataIndex,r[1].timestamp=e.timestamp,r[1].dataIndex=e.dataIndex},performEventMoveForDrawing:function(t){var r=t.currentStep,e=t.points,a=t.performPoint;r===2&&(e[0].timestamp=a.timestamp,e[0].dataIndex=a.dataIndex)}},c1={name:"verticalSegment",totalStep:3,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:function(t){var r=t.coordinates;return r.length===2?[{type:"line",attrs:{coordinates:r}}]:[]},performEventPressedMove:function(t){var r=t.points,e=t.performPoint;r[0].timestamp=e.timestamp,r[0].dataIndex=e.dataIndex,r[1].timestamp=e.timestamp,r[1].dataIndex=e.dataIndex},performEventMoveForDrawing:function(t){var r=t.currentStep,e=t.points,a=t.performPoint;r===2&&(e[0].timestamp=a.timestamp,e[0].dataIndex=a.dataIndex)}},u1={name:"verticalStraightLine",totalStep:2,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:function(t){var r=t.coordinates,e=t.bounding;return[{type:"line",attrs:{coordinates:[{x:r[0].x,y:0},{x:r[0].x,y:e.height}]}}]}},d1={name:"simpleAnnotation",totalStep:2,styles:{line:{style:"dashed"}},createPointFigures:function(t){var r,e=t.overlay,a=t.coordinates,i="";P(e.extendData)&&(Me(e.extendData)?i=e.extendData(e):i=(r=e.extendData)!==null&&r!==void 0?r:"");var n=a[0].x,o=a[0].y-6,s=o-50,l=s-5;return[{type:"line",attrs:{coordinates:[{x:n,y:o},{x:n,y:s}]},ignoreEvent:!0},{type:"polygon",attrs:{coordinates:[{x:n,y:s},{x:n-4,y:l},{x:n+4,y:l}]},ignoreEvent:!0},{type:"text",attrs:{x:n,y:l,text:i,align:"center",baseline:"bottom"},ignoreEvent:!0}]}},h1={name:"simpleTag",totalStep:2,styles:{line:{style:"dashed"}},createPointFigures:function(t){var r=t.bounding,e=t.coordinates;return{type:"line",attrs:{coordinates:[{x:0,y:e[0].y},{x:r.width,y:e[0].y}]},ignoreEvent:!0}},createYAxisFigures:function(t){var r,e,a,i,n=t.chart,o=t.overlay,s=t.coordinates,l=t.bounding,c=t.yAxis,u=(r=c==null?void 0:c.isFromZero())!==null&&r!==void 0?r:!1,d="left",v=0;u?(d="left",v=0):(d="right",v=l.width);var h="";return P(o.extendData)&&(Me(o.extendData)?h=o.extendData(o):h=(e=o.extendData)!==null&&e!==void 0?e:""),!P(h)&&vt(o.points[0].value)&&(h=xe(o.points[0].value,(i=(a=n.getSymbol())===null||a===void 0?void 0:a.pricePrecision)!==null&&i!==void 0?i:2)),{type:"text",attrs:{x:v,y:s[0].y,text:h,align:d,baseline:"middle"}}}},gi={},v1=[Gl,Ul,Ql,ql,e1,r1,a1,n1,o1,s1,l1,c1,u1,d1,h1];v1.forEach(function(t){gi[t.name]=uo.extend(t)});function _a(t){gi[t.name]=uo.extend(t)}function f1(t){var r;return(r=gi[t])!==null&&r!==void 0?r:null}var p1={grid:{horizontal:{color:"#EDEDED"},vertical:{color:"#EDEDED"}},candle:{priceMark:{high:{color:"#76808F"},low:{color:"#76808F"}},tooltip:{rect:{color:"#FEFEFE",borderColor:"#F2F3F5"},title:{color:"#76808F"},legend:{color:"#76808F"}}},indicator:{tooltip:{title:{color:"#76808F"},legend:{color:"#76808F"}}},xAxis:{axisLine:{color:"#DDDDDD"},tickText:{color:"#76808F"},tickLine:{color:"#DDDDDD"}},yAxis:{axisLine:{color:"#DDDDDD"},tickText:{color:"#76808F"},tickLine:{color:"#DDDDDD"}},separator:{color:"#DDDDDD"},crosshair:{horizontal:{line:{color:"#76808F"},text:{borderColor:"#686D76",backgroundColor:"#686D76"}},vertical:{line:{color:"#76808F"},text:{borderColor:"#686D76",backgroundColor:"#686D76"}}}},g1={grid:{horizontal:{color:"#292929"},vertical:{color:"#292929"}},candle:{priceMark:{high:{color:"#929AA5"},low:{color:"#929AA5"}},tooltip:{rect:{color:"rgba(10, 10, 10, .6)",borderColor:"rgba(10, 10, 10, .6)"},title:{color:"#929AA5"},legend:{color:"#929AA5"}}},indicator:{tooltip:{title:{color:"#929AA5"},legend:{color:"#929AA5"}}},xAxis:{axisLine:{color:"#333333"},tickText:{color:"#929AA5"},tickLine:{color:"#333333"}},yAxis:{axisLine:{color:"#333333"},tickText:{color:"#929AA5"},tickLine:{color:"#333333"}},separator:{color:"#333333"},crosshair:{horizontal:{line:{color:"#929AA5"},text:{borderColor:"#373a40",backgroundColor:"#373a40"}},vertical:{line:{color:"#929AA5"},text:{borderColor:"#373a40",backgroundColor:"#373a40"}}}},m1={light:p1,dark:g1};function y1(t){var r;return(r=m1[t])!==null&&r!==void 0?r:null}var da=30,go=100,wt={CANDLE:"candle_pane",INDICATOR:"indicator_pane_",X_AXIS:"x_axis_pane"},Qi={MIN:1,MAX:125},_1=10,C1=80,ti=10,b1=function(){function t(r,e){var a=this;this._styles=ml(),this._formatter={formatDate:function(d){var v=d.dateTimeFormat,h=d.timestamp,f=d.template;return eo(v,h,f)},formatBigNumber:ro,formatExtendText:function(d){return""}},this._innerFormatter={formatDate:function(d,v,h){return a._formatter.formatDate({dateTimeFormat:a._dateTimeFormat,timestamp:d,template:v,type:h})},formatBigNumber:function(d){return a._formatter.formatBigNumber(d)},formatExtendText:function(d){return a._formatter.formatExtendText(d)}},this._locale="en-US",this._thousandsSeparator={sign:",",format:function(d){return ao(d,a._thousandsSeparator.sign)}},this._decimalFold={threshold:3,format:function(d){return io(d,a._decimalFold.threshold)}},this._symbol=null,this._period=null,this._dataList=[],this._orderBookDataList=[],this._dataLoader=null,this._loading=!1,this._dataLoadMore={forward:!1,backward:!1},this._zoomEnabled=!0,this._scrollEnabled=!0,this._candleVisible=!0,this._totalBarSpace=0,this._barSpace=_1,this._offsetRightDistance=C1,this._startLastBarRightSideDiffBarCount=0,this._scrollLimitRole="bar_count",this._minVisibleBarCount={left:2,right:2},this._maxOffsetDistance={left:50,right:50},this._visibleRange=ji(),this._visibleRangeDataList=[],this._visibleRangeHighLowPrice=[{x:0,price:Number.MIN_SAFE_INTEGER},{x:0,price:Number.MAX_SAFE_INTEGER}],this._crosshair={},this._actions=new Map,this._indicators=new Map,this._taskScheduler=new cl,this._overlays=new Map,this._progressOverlayInfo=null,this._lastPriceMarkExtendTextUpdateTimers=[],this._pressedOverlayInfo={paneId:"",overlay:null,figureType:"none",figureIndex:-1,figure:null},this._hoverOverlayInfo={paneId:"",overlay:null,figureType:"none",figureIndex:-1,figure:null},this._clickOverlayInfo={paneId:"",overlay:null,figureType:"none",figureIndex:-1,figure:null},this._chart=r,this._calcOptimalBarSpace(),this._lastBarRightSideDiffBarCount=this._offsetRightDistance/this._barSpace;var i=e??{},n=i.styles,o=i.locale,s=i.timezone,l=i.formatter,c=i.thousandsSeparator,u=i.decimalFold;P(n)&&this.setStyles(n),Gt(o)&&this.setLocale(o),this.setTimezone(s??""),P(l)&&this.setFormatter(l),P(c)&&this.setThousandsSeparator(c),P(u)&&this.setDecimalFold(u)}return t.prototype.setStyles=function(r){var e=this,a,i,n,o,s,l,c=null;if(Gt(r)?c=y1(r):c=r,he(this._styles,c),He((n=(i=(a=c==null?void 0:c.candle)===null||a===void 0?void 0:a.tooltip)===null||i===void 0?void 0:i.legend)===null||n===void 0?void 0:n.custom)&&(this._styles.candle.tooltip.legend.custom=c.candle.tooltip.legend.custom),P((l=(s=(o=c==null?void 0:c.candle)===null||o===void 0?void 0:o.priceMark)===null||s===void 0?void 0:s.last)===null||l===void 0?void 0:l.extendTexts)){this._clearLastPriceMarkExtendTextUpdateTimer();var u=[];this._styles.candle.priceMark.last.extendTexts.forEach(function(d){var v=d.updateInterval;if(d.show&&v>0&&!u.includes(v)){u.push(v);var h=setInterval(function(){e._chart.updatePane(0,wt.CANDLE)},v);e._lastPriceMarkExtendTextUpdateTimers.push(h)}})}},t.prototype.getStyles=function(){return this._styles},t.prototype.setFormatter=function(r){he(this._formatter,r)},t.prototype.getFormatter=function(){return this._formatter},t.prototype.getInnerFormatter=function(){return this._innerFormatter},t.prototype.setLocale=function(r){this._locale=r},t.prototype.getLocale=function(){return this._locale},t.prototype.setTimezone=function(r){if(!P(this._dateTimeFormat)||this.getTimezone()!==r){var e={hour12:!1,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"};r.length>0&&(e.timeZone=r);var a=null;try{a=new Intl.DateTimeFormat("en",e)}catch{}a!==null&&(this._dateTimeFormat=a)}},t.prototype.getTimezone=function(){return this._dateTimeFormat.resolvedOptions().timeZone},t.prototype.getDateTimeFormat=function(){return this._dateTimeFormat},t.prototype.setThousandsSeparator=function(r){he(this._thousandsSeparator,r)},t.prototype.getThousandsSeparator=function(){return this._thousandsSeparator},t.prototype.setDecimalFold=function(r){he(this._decimalFold,r)},t.prototype.getDecimalFold=function(){return this._decimalFold},t.prototype.setSymbol=function(r){this._processDataUnsubscribe(),this._symbol=r,this._synchronizeIndicatorSeriesPrecision(),this.resetData()},t.prototype.getSymbol=function(){return this._symbol},t.prototype.setPeriod=function(r){this._processDataUnsubscribe(),this._period=r,this.resetData()},t.prototype.getPeriod=function(){return this._period},t.prototype.getDataList=function(){return this._dataList},t.prototype.getVisibleRangeDataList=function(){return this._visibleRangeDataList},t.prototype.getVisibleRangeHighLowPrice=function(){return this._visibleRangeHighLowPrice},t.prototype._addData=function(r,e,a){var i=this,n,o,s=!1,l=!1,c=0;if(He(r)){var u={backward:!1,forward:!1};switch(Qr(a)?(u.backward=a,u.forward=a):(u.backward=(n=a==null?void 0:a.backward)!==null&&n!==void 0?n:!1,u.forward=(o=a==null?void 0:a.forward)!==null&&o!==void 0?o:!1),c=r.length,e){case"init":{this._clearData(),this._dataList=r,this._dataLoadMore.backward=u.backward,this._dataLoadMore.forward=u.forward,this.setOffsetRightDistance(this._offsetRightDistance),l=!0;break}case"backward":{this._dataList=this._dataList.concat(r),this._dataLoadMore.backward=u.backward,l=c>0;break}case"forward":{this._dataList=r.concat(this._dataList),this._dataLoadMore.forward=u.forward,l=c>0;break}}s=!0}else{var d=this._dataList.length,v=r.timestamp,h=ge(this._dataList[d-1],"timestamp",0);if(v>h){this._dataList.push(r);var f=this.getLastBarRightSideDiffBarCount();f<0&&this.setLastBarRightSideDiffBarCount(--f),c=1,s=!0,l=!0}else v===h&&(this._dataList[d-1]=r,s=!0,l=!0)}if(s&&l){this._adjustVisibleRange(),this.setCrosshair(this._crosshair,{notInvalidate:!0});var p=this.getIndicatorsByFilter({});p.forEach(function(g){i._addIndicatorCalcTask(g,e)}),this._chart.layout({measureWidth:!0,update:!0,buildYAxisTick:!0,cacheYAxisWidth:e!=="init"})}},t.prototype.setDataLoader=function(r){this._dataLoader=r,this.resetData()},t.prototype._calcOptimalBarSpace=function(){this._gapBarSpace=Math.max(2,Math.min(this._barSpace*.8,this._barSpace-2))},t.prototype._adjustVisibleRange=function(){var r,e,a=this._dataList.length,i=this._totalBarSpace/this._barSpace,n=0,o=0;this._scrollLimitRole==="distance"?(n=(this._totalBarSpace-this._maxOffsetDistance.right)/this._barSpace,o=(this._totalBarSpace-this._maxOffsetDistance.left)/this._barSpace):(n=this._minVisibleBarCount.left,o=this._minVisibleBarCount.right),n=Math.max(0,n),o=Math.max(0,o);var s=i-Math.min(n,a);this._lastBarRightSideDiffBarCount>s&&(this._lastBarRightSideDiffBarCount=s);var l=-a+Math.min(o,a);this._lastBarRightSideDiffBarCount<l&&(this._lastBarRightSideDiffBarCount=l);var c=Math.round(this._lastBarRightSideDiffBarCount+a+.5),u=c,d=c;d>a&&(d=a);var v=Math.round(d-i)-1,h=v;h<0&&(h=0);var f=this._lastBarRightSideDiffBarCount>0?Math.round(a+this._lastBarRightSideDiffBarCount-i)-1:h;this._visibleRange={from:h,to:d,realFrom:f,realTo:u},this.executeAction("onVisibleRangeChange",this._visibleRange),this._visibleRangeDataList=[],this._visibleRangeHighLowPrice=[{x:0,price:Number.MIN_SAFE_INTEGER},{x:0,price:Number.MAX_SAFE_INTEGER}];for(var p=f;p<u;p++){var g=this._dataList[p],m=this.dataIndexToCoordinate(p);this._visibleRangeDataList.push({dataIndex:p,x:m,data:{prev:(r=this._dataList[p-1])!==null&&r!==void 0?r:g,current:g,next:(e=this._dataList[p+1])!==null&&e!==void 0?e:g}}),P(g)&&(this._visibleRangeHighLowPrice[0].price<g.high&&(this._visibleRangeHighLowPrice[0].price=g.high,this._visibleRangeHighLowPrice[0].x=m),this._visibleRangeHighLowPrice[1].price>g.low&&(this._visibleRangeHighLowPrice[1].price=g.low,this._visibleRangeHighLowPrice[1].x=m))}!this._loading&&P(this._dataLoader)&&P(this._symbol)&&P(this._period)&&(h===0?this._dataLoadMore.forward&&this._processDataLoad("forward"):d===a&&this._dataLoadMore.backward&&this._processDataLoad("backward"))},t.prototype._processDataLoad=function(r){var e=this,a,i,n,o;if(!this._loading&&P(this._dataLoader)&&P(this._symbol)&&P(this._period)){this._loading=!0;var s={type:r,symbol:this._symbol,period:this._period,timestamp:null,callback:function(l,c){var u,d;e._loading=!1,e._addData(l,r,c),r==="init"&&((d=(u=e._dataLoader)===null||u===void 0?void 0:u.subscribeBar)===null||d===void 0||d.call(u,{symbol:e._symbol,period:e._period,callback:function(v){e._addData(v,"update")}}))}};switch(r){case"backward":{s.timestamp=(i=(a=this._dataList[this._dataList.length-1])===null||a===void 0?void 0:a.timestamp)!==null&&i!==void 0?i:null;break}case"forward":{s.timestamp=(o=(n=this._dataList[0])===null||n===void 0?void 0:n.timestamp)!==null&&o!==void 0?o:null;break}}this._dataLoader.getBars(s)}},t.prototype._processDataUnsubscribe=function(){var r,e;P(this._dataLoader)&&P(this._symbol)&&P(this._period)&&((e=(r=this._dataLoader).unsubscribeBar)===null||e===void 0||e.call(r,{symbol:this._symbol,period:this._period}))},t.prototype.resetData=function(){this._loading=!1,this._processDataLoad("init")},t.prototype.getBarSpace=function(){return{bar:this._barSpace,halfBar:this._barSpace/2,gapBar:this._gapBarSpace,halfGapBar:Math.floor(this._gapBarSpace/2)}},t.prototype.setBarSpace=function(r,e){r<Qi.MIN||r>Qi.MAX||this._barSpace===r||(this._barSpace=r,this._calcOptimalBarSpace(),e==null||e(),this._adjustVisibleRange(),this.setCrosshair(this._crosshair,{notInvalidate:!0}),this._chart.layout({measureWidth:!0,update:!0,buildYAxisTick:!0,cacheYAxisWidth:!0}))},t.prototype.setTotalBarSpace=function(r){this._totalBarSpace!==r&&(this._totalBarSpace=r,this._adjustVisibleRange(),this.setCrosshair(this._crosshair,{notInvalidate:!0}))},t.prototype.setOffsetRightDistance=function(r,e){return this._offsetRightDistance=this._scrollLimitRole==="distance"?Math.min(this._maxOffsetDistance.right,r):r,this._lastBarRightSideDiffBarCount=this._offsetRightDistance/this._barSpace,(e??!1)&&(this._adjustVisibleRange(),this.setCrosshair(this._crosshair,{notInvalidate:!0}),this._chart.layout({measureWidth:!0,update:!0,buildYAxisTick:!0,cacheYAxisWidth:!0})),this},t.prototype.getInitialOffsetRightDistance=function(){return this._offsetRightDistance},t.prototype.getOffsetRightDistance=function(){return Math.max(0,this._lastBarRightSideDiffBarCount*this._barSpace)},t.prototype.getLastBarRightSideDiffBarCount=function(){return this._lastBarRightSideDiffBarCount},t.prototype.setLastBarRightSideDiffBarCount=function(r){this._lastBarRightSideDiffBarCount=r},t.prototype.setMaxOffsetLeftDistance=function(r){this._scrollLimitRole="distance",this._maxOffsetDistance.left=r},t.prototype.setMaxOffsetRightDistance=function(r){this._scrollLimitRole="distance",this._maxOffsetDistance.right=r},t.prototype.setLeftMinVisibleBarCount=function(r){this._scrollLimitRole="bar_count",this._minVisibleBarCount.left=r},t.prototype.setRightMinVisibleBarCount=function(r){this._scrollLimitRole="bar_count",this._minVisibleBarCount.right=r},t.prototype.getVisibleRange=function(){return this._visibleRange},t.prototype.startScroll=function(){this._startLastBarRightSideDiffBarCount=this._lastBarRightSideDiffBarCount},t.prototype.scroll=function(r){if(this._scrollEnabled){var e=r/this._barSpace,a=this._lastBarRightSideDiffBarCount*this._barSpace;this._lastBarRightSideDiffBarCount=this._startLastBarRightSideDiffBarCount-e,this._adjustVisibleRange(),this.setCrosshair(this._crosshair,{notInvalidate:!0}),this._chart.layout({measureWidth:!0,update:!0,buildYAxisTick:!0,cacheYAxisWidth:!0});var i=Math.round(a-this._lastBarRightSideDiffBarCount*this._barSpace);i!==0&&this.executeAction("onScroll",{distance:i})}},t.prototype.getDataByDataIndex=function(r){var e;return(e=this._dataList[r])!==null&&e!==void 0?e:null},t.prototype.coordinateToFloatIndex=function(r){var e=this._dataList.length,a=(this._totalBarSpace-r)/this._barSpace,i=e+this._lastBarRightSideDiffBarCount-a;return Math.round(i*1e6)/1e6},t.prototype.dataIndexToTimestamp=function(r){var e=this._dataList.length;if(e===0)return null;var a=this.getDataByDataIndex(r);if(P(a))return a.timestamp;if(P(this._period)){var i=e-1,n=null,o=0;if(r>i?(n=this._dataList[i].timestamp,o=r-i):r<0&&(n=this._dataList[0].timestamp,o=r),vt(n)){var s=this._period,l=s.type,c=s.span;switch(l){case"second":return n+c*1e3*o;case"minute":return n+c*60*1e3*o;case"hour":return n+c*60*60*1e3*o;case"day":return n+c*24*60*60*1e3*o;case"week":return n+c*7*24*60*60*1e3*o;case"month":{var u=new Date(n),d=u.getDate(),v=u.getMonth()+c*o;u.setMonth(v);var h=new Date(u.getFullYear(),u.getMonth()+1,0).getDate();return u.setDate(Math.min(d,h)),u.getTime()}case"year":{var u=new Date(n);return u.setFullYear(u.getFullYear()+c*o),u.getTime()}}}}return null},t.prototype.timestampToDataIndex=function(r){var e=this._dataList.length;if(e===0)return 0;if(P(this._period)){var a=null,i=0,n=e-1,o=this._dataList[n].timestamp;r>o&&(a=o,i=n);var s=this._dataList[0].timestamp;if(r<s&&(a=s,i=0),vt(a)){var l=this._period,c=l.type,u=l.span;switch(c){case"second":return i+Math.floor((r-a)/(u*1e3));case"minute":return i+Math.floor((r-a)/(u*60*1e3));case"hour":return i+Math.floor((r-a)/(u*60*60*1e3));case"day":return i+Math.floor((r-a)/(u*24*60*60*1e3));case"week":return i+Math.floor((r-a)/(u*7*24*60*60*1e3));case"month":{var d=new Date(a),v=new Date(r),h=d.getFullYear(),f=v.getFullYear(),p=d.getMonth(),g=v.getMonth();return i+Math.floor((f-h)*12+(g-p)/u)}case"year":{var h=new Date(a).getFullYear(),f=new Date(r).getFullYear();return i+Math.floor((f-h)/u)}}}}return Ja(this._dataList,"timestamp",r)},t.prototype.dataIndexToCoordinate=function(r){var e=this._dataList.length,a=e+this._lastBarRightSideDiffBarCount-r;return Math.floor(this._totalBarSpace-(a-.5)*this._barSpace+.5)},t.prototype.coordinateToDataIndex=function(r){return Math.ceil(this.coordinateToFloatIndex(r))-1},t.prototype.zoom=function(r,e){var a=this,i;if(this._zoomEnabled){var n=e??null;vt(n==null?void 0:n.x)||(n={x:(i=this._crosshair.x)!==null&&i!==void 0?i:this._totalBarSpace/2});var o=n.x,s=this.coordinateToFloatIndex(o),l=this._barSpace,c=this._barSpace+r*(this._barSpace/ti);this.setBarSpace(c,function(){a._lastBarRightSideDiffBarCount+=s-a.coordinateToFloatIndex(o)});var u=this._barSpace/l;u!==1&&this.executeAction("onZoom",{scale:u})}},t.prototype.setZoomEnabled=function(r){this._zoomEnabled=r},t.prototype.isZoomEnabled=function(){return this._zoomEnabled},t.prototype.setScrollEnabled=function(r){this._scrollEnabled=r},t.prototype.isScrollEnabled=function(){return this._scrollEnabled},t.prototype.setCandleVisible=function(r){this._candleVisible=r},t.prototype.isCandleVisible=function(){return this._candleVisible},t.prototype.setCrosshair=function(r,e){var a,i=e??{},n=i.notInvalidate,o=i.notExecuteAction,s=i.forceInvalidate,l=r??{},c=0,u=0;vt(l.x)?(c=this.coordinateToDataIndex(l.x),c<0?u=0:c>this._dataList.length-1?u=this._dataList.length-1:u=c):(c=this._dataList.length-1,u=c);var d=this._dataList[u],v=this.dataIndexToCoordinate(c),h={x:this._crosshair.x,y:this._crosshair.y,paneId:this._crosshair.paneId};this._crosshair=U(U({},l),{realX:v,kLineData:d,realDataIndex:c,dataIndex:u,timestamp:(a=this.dataIndexToTimestamp(c))!==null&&a!==void 0?a:void 0}),(h.x!==l.x||h.y!==l.y||h.paneId!==l.paneId||(s??!1))&&(P(d)&&!(o??!1)&&this._chart.crosshairChange(this._crosshair),(n??!1)||this._chart.updatePane(1))},t.prototype.getCrosshair=function(){return this._crosshair},t.prototype.executeAction=function(r,e){var a;(a=this._actions.get(r))===null||a===void 0||a.execute(e)},t.prototype.subscribeAction=function(r,e){var a;this._actions.has(r)||this._actions.set(r,new ul),(a=this._actions.get(r))===null||a===void 0||a.subscribe(e)},t.prototype.unsubscribeAction=function(r,e){var a=this._actions.get(r);P(a)&&(a.unsubscribe(e),a.isEmpty()&&this._actions.delete(r))},t.prototype.hasAction=function(r){var e=this._actions.get(r);return P(e)&&!e.isEmpty()},t.prototype._sortIndicators=function(r){var e;Gt(r)?(e=this._indicators.get(r))===null||e===void 0||e.sort(function(a,i){return a.zLevel-i.zLevel}):this._indicators.forEach(function(a){a.sort(function(i,n){return i.zLevel-n.zLevel})})},t.prototype._addIndicatorCalcTask=function(r,e){var a=this;this._taskScheduler.addTask({id:Xi(r.id),handler:function(){var i;(i=r.onDataStateChange)===null||i===void 0||i.call(r,{state:"loading",type:e,indicator:r}),r.calcImp(a._dataList).then(function(n){var o;n&&(a._chart.layout({measureWidth:!0,update:!0,buildYAxisTick:!0,cacheYAxisWidth:e!=="init"}),(o=r.onDataStateChange)===null||o===void 0||o.call(r,{state:"ready",type:e,indicator:r}))}).catch(function(){var n;(n=r.onDataStateChange)===null||n===void 0||n.call(r,{state:"error",type:e,indicator:r})})}})},t.prototype.addIndicator=function(r,e,a){var i=r.name,n=this.getIndicatorsByFilter(r);if(n.length>0)return!1;var o=this.getIndicatorsByPaneId(e),s=co(i),l=new s;return this._synchronizeIndicatorSeriesPrecision(l),l.paneId=e,l.override(r),a||(this.removeIndicator({paneId:e}),o=[]),o.push(l),this._indicators.set(e,o),this._sortIndicators(e),this._addIndicatorCalcTask(l,"init"),!0},t.prototype.getIndicatorsByPaneId=function(r){var e;return(e=this._indicators.get(r))!==null&&e!==void 0?e:[]},t.prototype.getIndicatorsByFilter=function(r){var e=r.paneId,a=r.name,i=r.id,n=function(s){return P(i)?s.id===i:!P(a)||s.name===a},o=[];return P(e)?o=o.concat(this.getIndicatorsByPaneId(e).filter(n)):this._indicators.forEach(function(s){o=o.concat(s.filter(n))}),o},t.prototype.removeIndicator=function(r){var e=this,a=!1,i=this.getIndicatorsByFilter(r);return i.forEach(function(n){var o=e.getIndicatorsByPaneId(n.paneId),s=o.findIndex(function(l){return l.id===n.id});s>-1&&(e._taskScheduler.removeTask(Xi(n.id)),o.splice(s,1),a=!0),o.length===0&&e._indicators.delete(n.paneId)}),a},t.prototype.hasIndicators=function(r){return this._indicators.has(r)},t.prototype._synchronizeIndicatorSeriesPrecision=function(r){if(P(this._symbol)){var e=this._symbol,a=e.pricePrecision,i=a===void 0?2:a,n=e.volumePrecision,o=n===void 0?0:n,s=function(l){switch(l.series){case"price":{l.setSeriesPrecision(i);break}case"volume":{l.setSeriesPrecision(o);break}}};P(r)?s(r):this._indicators.forEach(function(l){l.forEach(function(c){s(c)})})}},t.prototype.overrideIndicator=function(r){var e=this,a=!1,i=!1,n=this.getIndicatorsByFilter(r);return n.forEach(function(o){o.override(r);var s=o.shouldUpdateImp(),l=s.calc,c=s.draw,u=s.sort;u&&(i=!0),l?e._addIndicatorCalcTask(o,"update"):c&&(a=!0)}),i&&this._sortIndicators(),a?(this._chart.layout({update:!0}),!0):!1},t.prototype.getOverlaysByFilter=function(r){var e,a=r.id,i=r.groupId,n=r.paneId,o=r.name,s=function(u){return P(a)?u.id===a:P(i)?u.groupId===i&&(!P(o)||u.name===o):!P(o)||u.name===o},l=[];P(n)?l=l.concat(this.getOverlaysByPaneId(n).filter(s)):this._overlays.forEach(function(u){l=l.concat(u.filter(s))});var c=(e=this._progressOverlayInfo)===null||e===void 0?void 0:e.overlay;return P(c)&&s(c)&&l.push(c),l},t.prototype.getOverlaysByPaneId=function(r){var e;if(!Gt(r)){var a=[];return this._overlays.forEach(function(i){a=a.concat(i)}),a}return(e=this._overlays.get(r))!==null&&e!==void 0?e:[]},t.prototype._sortOverlays=function(r){var e;Gt(r)?(e=this._overlays.get(r))===null||e===void 0||e.sort(function(a,i){return a.zLevel-i.zLevel}):this._overlays.forEach(function(a){a.sort(function(i,n){return i.zLevel-n.zLevel})})},t.prototype.addOverlays=function(r,e){var a=this,i=[],n=r.map(function(o,s){var l,c,u,d,v,h,f,p;if(P(o.id)){var g=null;try{for(var m=Ze(a._overlays),b=m.next();!b.done;b=m.next()){var x=b.value,L=x[1],_=L.find(function(y){return y.id===o.id});if(P(_)){g=_;break}}}catch(y){l={error:y}}finally{try{b&&!b.done&&(c=m.return)&&c.call(m)}finally{if(l)throw l.error}}if(P(g))return o.id}var M=f1(o.name);if(P(M)){var w=(u=o.id)!==null&&u!==void 0?u:ua(Kl),_=new M,k=(d=o.paneId)!==null&&d!==void 0?d:wt.CANDLE;o.id=w,(v=o.groupId)!==null&&v!==void 0||(o.groupId=w);var C=a.getOverlaysByPaneId(k).length;return(h=o.zLevel)!==null&&h!==void 0||(o.zLevel=C),_.override(o),i.includes(k)||i.push(k),_.isDrawing()?a._progressOverlayInfo={paneId:k,overlay:_,appointPaneFlag:e[s]}:(a._overlays.has(k)||a._overlays.set(k,[]),(f=a._overlays.get(k))===null||f===void 0||f.push(_)),_.isStart()&&((p=_.onDrawStart)===null||p===void 0||p.call(_,{overlay:_,chart:a._chart})),w}return null});return i.length>0&&(this._sortOverlays(),i.forEach(function(o){a._chart.updatePane(1,o)}),this._chart.updatePane(1,wt.X_AXIS)),n},t.prototype.getProgressOverlayInfo=function(){return this._progressOverlayInfo},t.prototype.progressOverlayComplete=function(){var r;if(this._progressOverlayInfo!==null){var e=this._progressOverlayInfo,a=e.overlay,i=e.paneId;a.isDrawing()||(this._overlays.has(i)||this._overlays.set(i,[]),(r=this._overlays.get(i))===null||r===void 0||r.push(a),this._sortOverlays(i),this._progressOverlayInfo=null)}},t.prototype.updateProgressOverlayInfo=function(r,e){this._progressOverlayInfo!==null&&(Qr(e)&&e&&(this._progressOverlayInfo.appointPaneFlag=e),this._progressOverlayInfo.paneId=r,this._progressOverlayInfo.overlay.override({paneId:r}))},t.prototype.overrideOverlay=function(r){var e=this,a=!1,i=[],n=this.getOverlaysByFilter(r);return n.forEach(function(o){o.override(r);var s=o.shouldUpdate(),l=s.sort,c=s.draw;l&&(a=!0),(l||c)&&(i.includes(o.paneId)||i.push(o.paneId))}),a&&this._sortOverlays(),i.length>0?(i.forEach(function(o){e._chart.updatePane(1,o)}),this._chart.updatePane(1,wt.X_AXIS),!0):!1},t.prototype.removeOverlay=function(r){var e=this,a=[],i=this.getOverlaysByFilter(r);return i.forEach(function(n){var o,s=n.paneId,l=e.getOverlaysByPaneId(n.paneId);if((o=n.onRemoved)===null||o===void 0||o.call(n,{overlay:n,chart:e._chart}),a.includes(s)||a.push(s),n.isDrawing())e._progressOverlayInfo=null;else{var c=l.findIndex(function(u){return u.id===n.id});c>-1&&l.splice(c,1)}l.length===0&&e._overlays.delete(s)}),a.length>0?(a.forEach(function(n){e._chart.updatePane(1,n)}),this._chart.updatePane(1,wt.X_AXIS),!0):!1},t.prototype.setPressedOverlayInfo=function(r){this._pressedOverlayInfo=r},t.prototype.getPressedOverlayInfo=function(){return this._pressedOverlayInfo},t.prototype.setHoverOverlayInfo=function(r,e,a){var i=this._hoverOverlayInfo,n=i.overlay,o=i.figureType,s=i.figureIndex,l=i.figure,c=r.overlay;if(((n==null?void 0:n.id)!==(c==null?void 0:c.id)||o!==r.figureType||s!==r.figureIndex)&&(this._hoverOverlayInfo=r,(n==null?void 0:n.id)!==(c==null?void 0:c.id))){var u=!1,d=!1;n!==null&&(n.override({zLevel:n.getPrevZLevel()}),d=!0,a(n,l)&&(u=!0)),c!==null&&(c.setPrevZLevel(c.zLevel),c.override({zLevel:Number.MAX_SAFE_INTEGER}),d=!0,e(c,r.figure)&&(u=!0)),d&&this._sortOverlays(),u||this._chart.updatePane(1)}},t.prototype.getHoverOverlayInfo=function(){return this._hoverOverlayInfo},t.prototype.setClickOverlayInfo=function(r,e,a){var i=this._clickOverlayInfo,n=i.paneId,o=i.overlay,s=i.figureType,l=i.figure,c=i.figureIndex,u=r.overlay;((o==null?void 0:o.id)!==(u==null?void 0:u.id)||s!==r.figureType||c!==r.figureIndex)&&(this._clickOverlayInfo=r,(o==null?void 0:o.id)!==(u==null?void 0:u.id)&&(P(o)&&a(o,l),P(u)&&e(u,r.figure),this._chart.updatePane(1,r.paneId),n!==r.paneId&&this._chart.updatePane(1,n),this._chart.updatePane(1,wt.X_AXIS)))},t.prototype.getClickOverlayInfo=function(){return this._clickOverlayInfo},t.prototype.isOverlayEmpty=function(){return this._overlays.size===0&&this._progressOverlayInfo===null},t.prototype.isOverlayDrawing=function(){var r,e;return(e=(r=this._progressOverlayInfo)===null||r===void 0?void 0:r.overlay.isDrawing())!==null&&e!==void 0?e:!1},t.prototype._clearLastPriceMarkExtendTextUpdateTimer=function(){this._lastPriceMarkExtendTextUpdateTimers.forEach(function(r){clearInterval(r)}),this._lastPriceMarkExtendTextUpdateTimers=[]},t.prototype._clearData=function(){this._dataLoadMore.backward=!1,this._dataLoadMore.forward=!1,this._loading=!1,this._dataList=[],this._visibleRangeDataList=[],this._visibleRangeHighLowPrice=[{x:0,price:Number.MIN_SAFE_INTEGER},{x:0,price:Number.MAX_SAFE_INTEGER}],this._visibleRange=ji(),this._crosshair={}},t.prototype.getChart=function(){return this._chart},t.prototype.destroy=function(){this._clearData(),this._clearLastPriceMarkExtendTextUpdateTimer(),this._taskScheduler.removeTask(),this._overlays.clear(),this._indicators.clear(),this._actions.clear()},t.prototype.setOrderFlowData=function(r){this._orderBookDataList=r;var e=this._chart.getDrawPaneById(wt.CANDLE);if(P(e)){var a=e.getMainWidget();P(a)&&"setOrderFlowData"in a&&typeof a.setOrderFlowData=="function"&&(a.setOrderFlowData(r),this._chart.updatePane(3,wt.CANDLE))}},t.prototype.getOrderFlowData=function(){return this._orderBookDataList},t.prototype.applyNewData=function(r,e){this._addData(r,"init",e)},t.prototype.updateData=function(r){this._addData(r,"update")},t}(),Vt={MAIN:"main",X_AXIS:"xAxis",Y_AXIS:"yAxis",SEPARATOR:"separator"},Gr=7;function x1(){return no(this,void 0,void 0,function(){return oo(this,function(t){switch(t.label){case 0:return[4,new Promise(function(r){var e=new ResizeObserver(function(a){r(a.every(function(i){return"devicePixelContentBoxSize"in i})),e.disconnect()});e.observe(document.body,{box:"device-pixel-content-box"})}).catch(function(){return!1})];case 1:return[2,t.sent()]}})})}var qi=function(){function t(r,e){var a=this;this._supportedDevicePixelContentBox=!1,this._width=0,this._height=0,this._pixelWidth=0,this._pixelHeight=0,this._nextPixelWidth=0,this._nextPixelHeight=0,this._requestAnimationId=Mr,this._mediaQueryListener=function(){var i=hr(a._element);a._nextPixelWidth=Math.round(a._element.clientWidth*i),a._nextPixelHeight=Math.round(a._element.clientHeight*i),a._resetPixelRatio()},this._listener=e,this._element=nr("canvas",r),this._ctx=this._element.getContext("2d"),x1().then(function(i){a._supportedDevicePixelContentBox=i,i?(a._resizeObserver=new ResizeObserver(function(n){var o=n.find(function(l){return l.target===a._element}),s=o==null?void 0:o.devicePixelContentBoxSize[0];P(s)&&(a._nextPixelWidth=s.inlineSize,a._nextPixelHeight=s.blockSize,(a._pixelWidth!==a._nextPixelWidth||a._pixelHeight!==a._nextPixelHeight)&&a._resetPixelRatio())}),a._resizeObserver.observe(a._element,{box:"device-pixel-content-box"})):(a._mediaQueryList=window.matchMedia("(resolution: ".concat(hr(a._element),"dppx)")),a._mediaQueryList.addListener(a._mediaQueryListener))}).catch(function(i){return!1})}return t.prototype._resetPixelRatio=function(){var r=this;this._executeListener(function(){var e=r._element.clientWidth,a=r._element.clientHeight;r._width=e,r._height=a,r._pixelWidth=r._nextPixelWidth,r._pixelHeight=r._nextPixelHeight,r._element.width=r._nextPixelWidth,r._element.height=r._nextPixelHeight;var i=r._nextPixelWidth/e,n=r._nextPixelHeight/a;r._ctx.scale(i,n)})},t.prototype._executeListener=function(r){var e=this;this._requestAnimationId===Mr&&(this._requestAnimationId=ya(function(){e._ctx.clearRect(0,0,e._width,e._height),r==null||r(),e._listener(),e._requestAnimationId=Mr}))},t.prototype.update=function(r,e){if(this._width!==r||this._height!==e){if(this._element.style.width="".concat(r,"px"),this._element.style.height="".concat(e,"px"),!this._supportedDevicePixelContentBox){var a=hr(this._element);this._nextPixelWidth=Math.round(r*a),this._nextPixelHeight=Math.round(e*a),this._resetPixelRatio()}}else this._executeListener()},t.prototype.getElement=function(){return this._element},t.prototype.getContext=function(){return this._ctx},t.prototype.destroy=function(){P(this._resizeObserver)&&this._resizeObserver.unobserve(this._element),P(this._mediaQueryList)&&this._mediaQueryList.removeListener(this._mediaQueryListener)},t}(),mo=function(t){St(r,t);function r(e,a){var i=t.call(this)||this;return i._bounding=ma(),i._cursor="crosshair",i._forceCursor=null,i._pane=a,i._rootContainer=e,i._container=i.createContainer(),e.appendChild(i._container),i}return r.prototype.setBounding=function(e){return he(this._bounding,e),this},r.prototype.getContainer=function(){return this._container},r.prototype.getBounding=function(){return this._bounding},r.prototype.getPane=function(){return this._pane},r.prototype.checkEventOn=function(e){return!0},r.prototype.setCursor=function(e){Gt(this._forceCursor)||e!==this._cursor&&(this._cursor=e,this._container.style.cursor=this._cursor)},r.prototype.setForceCursor=function(e){var a;e!==this._forceCursor&&(this._forceCursor=e,this._container.style.cursor=(a=this._forceCursor)!==null&&a!==void 0?a:this._cursor)},r.prototype.getForceCursor=function(){return this._forceCursor},r.prototype.update=function(e){this.updateImp(this._container,this._bounding,e??3)},r.prototype.destroy=function(){this._rootContainer.removeChild(this._container)},r}(fi),mi=function(t){St(r,t);function r(e,a){var i=t.call(this,e,a)||this;i._mainCanvas=new qi({position:"absolute",top:"0",left:"0",zIndex:"2",boxSizing:"border-box"},function(){i.updateMain(i._mainCanvas.getContext())}),i._overlayCanvas=new qi({position:"absolute",top:"0",left:"0",zIndex:"2",boxSizing:"border-box"},function(){i.updateOverlay(i._overlayCanvas.getContext())});var n=i.getContainer();return n.appendChild(i._mainCanvas.getElement()),n.appendChild(i._overlayCanvas.getElement()),i}return r.prototype.createContainer=function(){return nr("div",{margin:"0",padding:"0",position:"absolute",top:"0",overflow:"hidden",boxSizing:"border-box",zIndex:"1"})},r.prototype.updateImp=function(e,a,i){var n=a.width,o=a.height,s=a.left;e.style.left="".concat(s,"px");var l=i,c=e.clientWidth,u=e.clientHeight;switch((n!==c||o!==u)&&(e.style.width="".concat(n,"px"),e.style.height="".concat(o,"px"),l=3),l){case 0:{this._mainCanvas.update(n,o);break}case 1:{this._overlayCanvas.update(n,o);break}case 3:case 4:{this._mainCanvas.update(n,o),this._overlayCanvas.update(n,o);break}}},r.prototype.destroy=function(){this._mainCanvas.destroy(),this._overlayCanvas.destroy()},r.prototype.getImage=function(e){var a=this.getBounding(),i=a.width,n=a.height,o=nr("canvas",{width:"".concat(i,"px"),height:"".concat(n,"px"),boxSizing:"border-box"}),s=o.getContext("2d"),l=hr(o);return o.width=i*l,o.height=n*l,s.scale(l,l),s.drawImage(this._mainCanvas.getElement(),0,0,i,n),e&&s.drawImage(this._overlayCanvas.getElement(),0,0,i,n),o},r}(mo);function yo(t,r){var e,a,i=[];i=i.concat(r);try{for(var n=Ze(i),o=n.next();!o.done;o=n.next()){var s=o.value,l=s.x,c=s.y,u=s.r,d=t.x-l,v=t.y-c;if(!(d*d+v*v>u*u))return!0}}catch(h){e={error:h}}finally{try{o&&!o.done&&(a=n.return)&&a.call(n)}finally{if(e)throw e.error}}return!1}function w1(t,r,e){var a=[];a=a.concat(r);var i=e.style,n=i===void 0?"fill":i,o=e.color,s=o===void 0?"currentColor":o,l=e.borderSize,c=l===void 0?1:l,u=e.borderColor,d=u===void 0?"currentColor":u,v=e.borderStyle,h=v===void 0?"solid":v,f=e.borderDashedValue,p=f===void 0?[2,2]:f,g=(n==="fill"||e.style==="stroke_fill")&&(!Gt(s)||!Ir(s));g&&(t.fillStyle=s,a.forEach(function(m){var b=m.x,x=m.y,L=m.r;t.beginPath(),t.arc(b,x,L,0,Math.PI*2),t.closePath(),t.fill()})),(n==="stroke"||e.style==="stroke_fill")&&c>0&&!Ir(d)&&(t.strokeStyle=d,t.lineWidth=c,h==="dashed"?t.setLineDash(p):t.setLineDash([]),a.forEach(function(m){var b=m.x,x=m.y,L=m.r;(!g||L>c)&&(t.beginPath(),t.arc(b,x,L,0,Math.PI*2),t.closePath(),t.stroke())}))}var L1={name:"circle",checkEventOn:yo,draw:function(t,r,e){w1(t,r,e)}};function _o(t,r){var e,a,i=[];i=i.concat(r);try{for(var n=Ze(i),o=n.next();!o.done;o=n.next()){for(var s=o.value,l=!1,c=s.coordinates,u=0,d=c.length-1;u<c.length;d=u++)c[u].y>t.y!=c[d].y>t.y&&t.x<(c[d].x-c[u].x)*(t.y-c[u].y)/(c[d].y-c[u].y)+c[u].x&&(l=!l);if(l)return!0}}catch(v){e={error:v}}finally{try{o&&!o.done&&(a=n.return)&&a.call(n)}finally{if(e)throw e.error}}return!1}function k1(t,r,e){var a=[];a=a.concat(r);var i=e.style,n=i===void 0?"fill":i,o=e.color,s=o===void 0?"currentColor":o,l=e.borderSize,c=l===void 0?1:l,u=e.borderColor,d=u===void 0?"currentColor":u,v=e.borderStyle,h=v===void 0?"solid":v,f=e.borderDashedValue,p=f===void 0?[2,2]:f;(n==="fill"||e.style==="stroke_fill")&&(!Gt(s)||!Ir(s))&&(t.fillStyle=s,a.forEach(function(g){var m=g.coordinates;t.beginPath(),t.moveTo(m[0].x,m[0].y);for(var b=1;b<m.length;b++)t.lineTo(m[b].x,m[b].y);t.closePath(),t.fill()})),(n==="stroke"||e.style==="stroke_fill")&&c>0&&!Ir(d)&&(t.strokeStyle=d,t.lineWidth=c,h==="dashed"?t.setLineDash(p):t.setLineDash([]),a.forEach(function(g){var m=g.coordinates;t.beginPath(),t.moveTo(m[0].x,m[0].y);for(var b=1;b<m.length;b++)t.lineTo(m[b].x,m[b].y);t.closePath(),t.stroke()}))}var S1={name:"polygon",checkEventOn:_o,draw:function(t,r,e){k1(t,r,e)}};function yi(t,r){var e,a,i=[];i=i.concat(r);try{for(var n=Ze(i),o=n.next();!o.done;o=n.next()){var s=o.value,l=s.x,c=s.width;c<ye*2&&(l-=ye,c=ye*2);var u=s.y,d=s.height;if(d<ye*2&&(u-=ye,d=ye*2),t.x>=l&&t.x<=l+c&&t.y>=u&&t.y<=u+d)return!0}}catch(v){e={error:v}}finally{try{o&&!o.done&&(a=n.return)&&a.call(n)}finally{if(e)throw e.error}}return!1}function Co(t,r,e){var a,i=[];i=i.concat(r);var n=e.style,o=n===void 0?"fill":n,s=e.color,l=s===void 0?"transparent":s,c=e.borderSize,u=c===void 0?1:c,d=e.borderColor,v=d===void 0?"transparent":d,h=e.borderStyle,f=h===void 0?"solid":h,p=e.borderRadius,g=p===void 0?0:p,m=e.borderDashedValue,b=m===void 0?[2,2]:m,x=(a=t.roundRect)!==null&&a!==void 0?a:t.rect,L=(o==="fill"||e.style==="stroke_fill")&&(!Gt(l)||!Ir(l));if(L&&(t.fillStyle=l,i.forEach(function(w){var k=w.x,C=w.y,y=w.width,T=w.height;t.beginPath(),x.call(t,k,C,y,T,g),t.closePath(),t.fill()})),(o==="stroke"||e.style==="stroke_fill")&&u>0&&!Ir(v)){t.strokeStyle=v,t.fillStyle=v,t.lineWidth=u,f==="dashed"?t.setLineDash(b):t.setLineDash([]);var _=u%2===1?.5:0,M=Math.round(_*2);i.forEach(function(w){var k=w.x,C=w.y,y=w.width,T=w.height;y>u*2&&T>u*2?(t.beginPath(),x.call(t,k+_,C+_,y-M,T-M,g),t.closePath(),t.stroke()):L||t.fillRect(k,C,y,T)})}}var M1={name:"rect",checkEventOn:yi,draw:function(t,r,e){Co(t,r,e)}};function bo(t,r){var e=r.size,a=e===void 0?12:e,i=r.paddingLeft,n=i===void 0?0:i,o=r.paddingTop,s=o===void 0?0:o,l=r.paddingRight,c=l===void 0?0:l,u=r.paddingBottom,d=u===void 0?0:u,v=r.weight,h=v===void 0?"normal":v,f=r.family,p=t.x,g=t.y,m=t.text,b=t.align,x=b===void 0?"left":b,L=t.baseline,_=L===void 0?"top":L,M=t.width,w=t.height,k=M??n+Ye(m,a,h,f)+c,C=w??s+a+d,y=0;switch(x){case"left":case"start":{y=p;break}case"right":case"end":{y=p-k;break}default:{y=p-k/2;break}}var T=0;switch(_){case"top":case"hanging":{T=g;break}case"bottom":case"ideographic":case"alphabetic":{T=g-C;break}default:{T=g-C/2;break}}return{x:y,y:T,width:k,height:C}}function xo(t,r,e){var a,i,n=[];n=n.concat(r);try{for(var o=Ze(n),s=o.next();!s.done;s=o.next()){var l=s.value,c=bo(l,e),u=c.x,d=c.y,v=c.width,h=c.height;if(t.x>=u&&t.x<=u+v&&t.y>=d&&t.y<=d+h)return!0}}catch(f){a={error:f}}finally{try{s&&!s.done&&(i=o.return)&&i.call(o)}finally{if(a)throw a.error}}return!1}function T1(t,r,e){var a=[];a=a.concat(r);var i=e.color,n=i===void 0?"currentColor":i,o=e.size,s=o===void 0?12:o,l=e.family,c=e.weight,u=e.paddingLeft,d=u===void 0?0:u,v=e.paddingTop,h=v===void 0?0:v,f=e.paddingRight,p=f===void 0?0:f,g=a.map(function(m){return bo(m,e)});Co(t,g,U(U({},e),{color:e.backgroundColor})),t.textAlign="left",t.textBaseline="top",t.font=_r(s,c,l),t.fillStyle=n,a.forEach(function(m,b){var x=g[b];t.fillText(m.text,x.x+d,x.y+h,x.width-d-p)})}var E1={name:"text",checkEventOn:xo,draw:function(t,r,e){T1(t,r,e)}};function I1(t,r){var e=t.x-r.x,a=t.y-r.y;return Math.sqrt(e*e+a*a)}function wo(t,r){var e,a,i=[];i=i.concat(r);try{for(var n=Ze(i),o=n.next();!o.done;o=n.next()){var s=o.value;if(Math.abs(I1(t,s)-s.r)<ye){var l=s.r,c=s.startAngle,u=s.endAngle,d=l*Math.cos(c)+s.x,v=l*Math.sin(c)+s.y,h=l*Math.cos(u)+s.x,f=l*Math.sin(u)+s.y;if(t.x<=Math.max(d,h)+ye&&t.x>=Math.min(d,h)-ye&&t.y<=Math.max(v,f)+ye&&t.y>=Math.min(v,f)-ye)return!0}}}catch(p){e={error:p}}finally{try{o&&!o.done&&(a=n.return)&&a.call(n)}finally{if(e)throw e.error}}return!1}function A1(t,r,e){var a=[];a=a.concat(r);var i=e.style,n=i===void 0?"solid":i,o=e.size,s=o===void 0?1:o,l=e.color,c=l===void 0?"currentColor":l,u=e.dashedValue,d=u===void 0?[2,2]:u;t.lineWidth=s,t.strokeStyle=c,n==="dashed"?t.setLineDash(d):t.setLineDash([]),a.forEach(function(v){var h=v.x,f=v.y,p=v.r,g=v.startAngle,m=v.endAngle;t.beginPath(),t.arc(h,f,p,g,m),t.stroke(),t.closePath()})}var D1={name:"arc",checkEventOn:wo,draw:function(t,r,e){A1(t,r,e)}};function Ji(t,r,e,a,i,n,o){var s=ar(a,7),l=s[0],c=s[1],u=s[2],d=s[3],v=s[4],h=s[5],f=s[6],p=o?r+h:h+i,g=o?e+f:f+n,m=P1(r,e,l,c,u,d,v,p,g);m.forEach(function(b){t.bezierCurveTo(b[0],b[1],b[2],b[3],b[4],b[5])})}function P1(t,r,e,a,i,n,o,s,l){for(var c=F1(t,r,e,a,i,n,o,s,l),u=c.cx,d=c.cy,v=c.startAngle,h=c.deltaAngle,f=[],p=Math.ceil(Math.abs(h)/(Math.PI/2)),g=0;g<p;g++){var m=v+g*h/p,b=v+(g+1)*h/p,x=R1(u,d,e,a,i,m,b);f.push(x)}return f}function F1(t,r,e,a,i,n,o,s,l){var c=i*Math.PI/180,u=(t-s)/2,d=(r-l)/2,v=Math.cos(c)*u+Math.sin(c)*d,h=-Math.sin(c)*u+Math.cos(c)*d,f=Math.pow(v,2)/Math.pow(e,2)+Math.pow(h,2)/Math.pow(a,2);f>1&&(e*=Math.sqrt(f),a*=Math.sqrt(f));var p=n===o?-1:1,g=Math.pow(e,2)*Math.pow(a,2)-Math.pow(e,2)*Math.pow(h,2)-Math.pow(a,2)*Math.pow(v,2),m=Math.pow(e,2)*Math.pow(h,2)+Math.pow(a,2)*Math.pow(v,2),b=p*Math.sqrt(Math.abs(g/m))*(e*h/a),x=p*Math.sqrt(Math.abs(g/m))*(-a*v/e),L=Math.cos(c)*b-Math.sin(c)*x+(t+s)/2,_=Math.sin(c)*b+Math.cos(c)*x+(r+l)/2,M=Math.atan2((h-x)/a,(v-b)/e),w=Math.atan2((-h-x)/a,(-v-b)/e)-M;return w<0&&o===1?w+=2*Math.PI:w>0&&o===0&&(w-=2*Math.PI),{cx:L,cy:_,startAngle:M,deltaAngle:w}}function R1(t,r,e,a,i,n,o){var s=Math.sin(o-n)*(Math.sqrt(4+3*Math.pow(Math.tan((o-n)/2),2))-1)/3,l=Math.cos(i),c=Math.sin(i),u=t+e*Math.cos(n)*l-a*Math.sin(n)*c,d=r+e*Math.cos(n)*c+a*Math.sin(n)*l,v=t+e*Math.cos(o)*l-a*Math.sin(o)*c,h=r+e*Math.cos(o)*c+a*Math.sin(o)*l,f=u+s*(-e*Math.sin(n)*l-a*Math.cos(n)*c),p=d+s*(-e*Math.sin(n)*c+a*Math.cos(n)*l),g=v-s*(-e*Math.sin(o)*l-a*Math.cos(o)*c),m=h-s*(-e*Math.sin(o)*c+a*Math.cos(o)*l);return[f,p,g,m,v,h]}function B1(t,r,e){var a=[];a=a.concat(r);var i=e.lineWidth,n=i===void 0?1:i,o=e.color,s=o===void 0?"currentColor":o;t.lineWidth=n,t.strokeStyle=s,t.setLineDash([]),a.forEach(function(l){var c=l.x,u=l.y,d=l.path,v=d.match(/[MLHVCSQTAZ][^MLHVCSQTAZ]*/gi);if(P(v)){var h=c,f=u;t.beginPath(),v.forEach(function(p){var g=0,m=0,b=0,x=0,L=p[0],_=p.slice(1).trim().split(/[\s,]+/).map(Number);switch(L){case"M":g=_[0]+h,m=_[1]+f,t.moveTo(g,m),b=g,x=m;break;case"m":g+=_[0],m+=_[1],t.moveTo(g,m),b=g,x=m;break;case"L":g=_[0]+h,m=_[1]+f,t.lineTo(g,m);break;case"l":g+=_[0],m+=_[1],t.lineTo(g,m);break;case"H":g=_[0]+h,t.lineTo(g,m);break;case"h":g+=_[0],t.lineTo(g,m);break;case"V":m=_[0]+f,t.lineTo(g,m);break;case"v":m+=_[0],t.lineTo(g,m);break;case"C":t.bezierCurveTo(_[0]+h,_[1]+f,_[2]+h,_[3]+f,_[4]+h,_[5]+f),g=_[4]+h,m=_[5]+f;break;case"c":t.bezierCurveTo(g+_[0],m+_[1],g+_[2],m+_[3],g+_[4],m+_[5]),g+=_[4],m+=_[5];break;case"S":t.bezierCurveTo(g,m,_[0]+h,_[1]+f,_[2]+h,_[3]+f),g=_[2]+h,m=_[3]+f;break;case"s":t.bezierCurveTo(g,m,g+_[0],m+_[1],g+_[2],m+_[3]),g+=_[2],m+=_[3];break;case"Q":t.quadraticCurveTo(_[0]+h,_[1]+f,_[2]+h,_[3]+f),g=_[2]+h,m=_[3]+f;break;case"q":t.quadraticCurveTo(g+_[0],m+_[1],g+_[2],m+_[3]),g+=_[2],m+=_[3];break;case"T":t.quadraticCurveTo(g,m,_[0]+h,_[1]+f),g=_[0]+h,m=_[1]+f;break;case"t":t.quadraticCurveTo(g,m,g+_[0],m+_[1]),g+=_[0],m+=_[1];break;case"A":Ji(t,g,m,_,h,f,!1),g=_[5]+h,m=_[6]+f;break;case"a":Ji(t,g,m,_,h,f,!0),g+=_[5],m+=_[6];break;case"Z":case"z":t.closePath(),g=b,m=x;break}}),e.style==="fill"?t.fill():t.stroke()}})}var N1={name:"path",checkEventOn:yi,draw:function(t,r,e){B1(t,r,e)}},Pa={},O1=[L1,t1,S1,M1,E1,D1,N1];O1.forEach(function(t){Pa[t.name]=ho.extend(t)});function V1(t){Pa[t.name]=ho.extend(t)}function z1(t){var r;return(r=Pa[t])!==null&&r!==void 0?r:null}function $1(t){var r;return(r=Pa[t])!==null&&r!==void 0?r:null}var Ve=function(t){St(r,t);function r(e){var a=t.call(this)||this;return a._widget=e,a}return r.prototype.getWidget=function(){return this._widget},r.prototype.createFigure=function(e,a){var i=z1(e.name);if(i!==null){var n=new i(e);if(P(a)){for(var o in a)a.hasOwnProperty(o)&&n.registerEvent(o,a[o]);this.addChild(n)}return n}return null},r.prototype.draw=function(e){for(var a=[],i=1;i<arguments.length;i++)a[i-1]=arguments[i];this.clear(),this.drawImp(e,a)},r.prototype.checkEventOn=function(e){return!0},r}(fi),W1=function(t){St(r,t);function r(){return t!==null&&t.apply(this,arguments)||this}return r.prototype.drawImp=function(e){var a,i,n=this.getWidget(),o=this.getWidget().getPane(),s=o.getChart(),l=n.getBounding(),c=s.getStyles().grid,u=c.show;if(u){e.save(),e.globalCompositeOperation="destination-over";var d=c.horizontal,v=d.show;if(v){var h=o.getAxisComponent(),f=h.getTicks().map(function(b){return{coordinates:[{x:0,y:b.coord},{x:l.width,y:b.coord}]}});(a=this.createFigure({name:"line",attrs:f,styles:d}))===null||a===void 0||a.draw(e)}var p=c.vertical,g=p.show;if(g){var m=s.getXAxisPane().getAxisComponent(),f=m.getTicks().map(function(x){return{coordinates:[{x:x.coord,y:0},{x:x.coord,y:l.height}]}});(i=this.createFigure({name:"line",attrs:f,styles:p}))===null||i===void 0||i.draw(e)}e.restore()}},r}(Ve),_i=function(t){St(r,t);function r(){return t!==null&&t.apply(this,arguments)||this}return r.prototype.eachChildren=function(e){for(var a=this.getWidget().getPane(),i=a.getChart().getChartStore(),n=i.getVisibleRangeDataList(),o=i.getBarSpace(),s=n.length,l=0;l<s;)e(n[l],o,l),++l},r}(Ve),Lo=function(t){St(r,t);function r(){var e=t.apply(this,dr([],ar(arguments),!1))||this;return e._boundCandleBarClickEvent=function(a){return function(){return e.getWidget().getPane().getChart().getChartStore().executeAction("onCandleBarClick",a),!1}},e}return r.prototype.drawImp=function(e){var a=this,i=this.getWidget().getPane(),n=i.getId()===wt.CANDLE,o=i.getChart().getChartStore();if(o.isCandleVisible()){var s=this.getCandleBarOptions();if(s!==null){var l=s.type,c=s.styles,u=0,d=0;if(s.type==="ohlc"){var v=o.getBarSpace().gapBar;u=Math.min(Math.max(Math.round(v*.2),1),8),u>2&&u%2===1&&u--,d=Math.floor(u/2)}var h=i.getAxisComponent();this.eachChildren(function(f,p){var g,m=f.x,b=f.data,x=b.current,L=b.prev;if(P(x)){var _=x.open,M=x.high,w=x.low,k=x.close,C=c.compareRule==="current_open"?_:(g=L==null?void 0:L.close)!==null&&g!==void 0?g:k,y=[];k>C?(y[0]=c.upColor,y[1]=c.upBorderColor,y[2]=c.upWickColor):k<C?(y[0]=c.downColor,y[1]=c.downBorderColor,y[2]=c.downWickColor):(y[0]=c.noChangeColor,y[1]=c.noChangeBorderColor,y[2]=c.noChangeWickColor);var T=h.convertToPixel(_),I=h.convertToPixel(k),E=[T,I,h.convertToPixel(M),h.convertToPixel(w)];E.sort(function(N,B){return N-B});var A=p.gapBar%2===0?1:0,F=[];switch(l){case"candle_solid":{F=a._createSolidBar(m,E,p,y,A);break}case"candle_stroke":{F=a._createStrokeBar(m,E,p,y,A);break}case"candle_up_stroke":{k>_?F=a._createStrokeBar(m,E,p,y,A):F=a._createSolidBar(m,E,p,y,A);break}case"candle_down_stroke":{_>k?F=a._createStrokeBar(m,E,p,y,A):F=a._createSolidBar(m,E,p,y,A);break}case"ohlc":{F=[{name:"rect",attrs:[{x:m-d,y:E[0],width:u,height:E[3]-E[0]},{x:m-p.halfGapBar,y:T+u>E[3]?E[3]-u:T,width:p.halfGapBar-d,height:u},{x:m+d,y:I+u>E[3]?E[3]-u:I,width:p.halfGapBar-d,height:u}],styles:{color:y[0]}}];break}}F.forEach(function(N){var B,Y=null;n&&(Y={mouseClickEvent:a._boundCandleBarClickEvent(f)}),(B=a.createFigure(N,Y??void 0))===null||B===void 0||B.draw(e)})}})}}},r.prototype.getCandleBarOptions=function(){var e=this.getWidget().getPane().getChart().getStyles().candle;return{type:e.type,styles:e.bar}},r.prototype._createSolidBar=function(e,a,i,n,o){return[{name:"rect",attrs:{x:e,y:a[0],width:1,height:a[3]-a[0]},styles:{color:n[2]}},{name:"rect",attrs:{x:e-i.halfGapBar,y:a[1],width:i.gapBar+o,height:Math.max(1,a[2]-a[1])},styles:{style:"stroke_fill",color:n[0],borderColor:n[1]}}]},r.prototype._createStrokeBar=function(e,a,i,n,o){return[{name:"rect",attrs:[{x:e,y:a[0],width:1,height:a[1]-a[0]},{x:e,y:a[2],width:1,height:a[3]-a[2]}],styles:{color:n[2]}},{name:"rect",attrs:{x:e-i.halfGapBar,y:a[1],width:i.gapBar+o,height:Math.max(1,a[2]-a[1])},styles:{style:"stroke",borderColor:n[1]}}]},r}(_i),Y1=function(t){St(r,t);function r(){return t!==null&&t.apply(this,arguments)||this}return r.prototype.getCandleBarOptions=function(){var e,a,i=this.getWidget().getPane(),n=i.getAxisComponent();if(!n.isInCandle()){var o=i.getChart().getChartStore(),s=o.getIndicatorsByPaneId(i.getId());try{for(var l=Ze(s),c=l.next();!c.done;c=l.next()){var u=c.value;if(u.shouldOhlc&&u.visible){var d=u.styles,v=o.getStyles().indicator,h=ge(d,"ohlc.compareRule",v.ohlc.compareRule),f=ge(d,"ohlc.upColor",v.ohlc.upColor),p=ge(d,"ohlc.downColor",v.ohlc.downColor),g=ge(d,"ohlc.noChangeColor",v.ohlc.noChangeColor);return{type:"ohlc",styles:{compareRule:h,upColor:f,downColor:p,noChangeColor:g,upBorderColor:f,downBorderColor:p,noChangeBorderColor:g,upWickColor:f,downWickColor:p,noChangeWickColor:g}}}}}catch(m){e={error:m}}finally{try{c&&!c.done&&(a=l.return)&&a.call(l)}finally{if(e)throw e.error}}}return null},r.prototype.drawImp=function(e){var a=this;t.prototype.drawImp.call(this,e);var i=this.getWidget(),n=i.getPane(),o=n.getChart(),s=i.getBounding(),l=o.getXAxisPane().getAxisComponent(),c=n.getAxisComponent(),u=o.getChartStore(),d=u.getIndicatorsByPaneId(n.getId()),v=u.getStyles().indicator;e.save(),d.forEach(function(h){if(h.visible){h.zLevel<0?e.globalCompositeOperation="destination-over":e.globalCompositeOperation="source-over";var f=!1;if(h.draw!==null&&(e.save(),f=h.draw({ctx:e,chart:o,indicator:h,bounding:s,xAxis:l,yAxis:c}),e.restore()),!f){var p=h.result,g=[];a.eachChildren(function(m,b){var x,L,_,M=b.halfGapBar,w=m.dataIndex,k=m.x,C=l.convertToPixel(w-1),y=l.convertToPixel(w+1),T=(x=p[w-1])!==null&&x!==void 0?x:null,I=(L=p[w])!==null&&L!==void 0?L:null,E=(_=p[w+1])!==null&&_!==void 0?_:null,A={x:C},F={x:k},N={x:y};h.figures.forEach(function(B){var Y=B.key,K=T==null?void 0:T[Y];vt(K)&&(A[Y]=c.convertToPixel(K));var $=I==null?void 0:I[Y];vt($)&&(F[Y]=c.convertToPixel($));var ut=E==null?void 0:E[Y];vt(ut)&&(N[Y]=c.convertToPixel(ut))}),hi(h,w,v,function(B,Y,K){var $,ut,rt;if(P(I==null?void 0:I[B.key])){var z=F[B.key],tt=($=B.attrs)===null||$===void 0?void 0:$.call(B,{data:{prev:T,current:I,next:E},coordinate:{prev:A,current:F,next:N},bounding:s,barSpace:b,xAxis:l,yAxis:c});if(!P(tt))switch(B.type){case"circle":{tt={x:k,y:z,r:Math.max(1,M)};break}case"rect":case"bar":{var mt=(ut=B.baseValue)!==null&&ut!==void 0?ut:c.getRange().from,ft=c.convertToPixel(mt),yt=Math.abs(ft-z);mt!==(I==null?void 0:I[B.key])&&(yt=Math.max(1,yt));var V=0;z>ft?V=ft:V=z,tt={x:k-M,y:V,width:Math.max(1,M*2),height:yt};break}case"line":{P(g[K])||(g[K]=[]),vt(F[B.key])&&vt(N[B.key])&&g[K].push({coordinates:[{x:F.x,y:F[B.key]},{x:N.x,y:N[B.key]}],styles:Y});break}}var q=B.type;P(tt)&&q!=="line"&&((rt=a.createFigure({name:q==="bar"?"rect":q,attrs:tt,styles:Y}))===null||rt===void 0||rt.draw(e))}})}),g.forEach(function(m){var b,x,L,_;if(m.length>1){for(var M=[{coordinates:[m[0].coordinates[0],m[0].coordinates[1]],styles:m[0].styles}],w=1;w<m.length;w++){var k=M[M.length-1],C=m[w],y=k.coordinates[k.coordinates.length-1];y.x===C.coordinates[0].x&&y.y===C.coordinates[0].y&&k.styles.style===C.styles.style&&k.styles.color===C.styles.color&&k.styles.size===C.styles.size&&k.styles.smooth===C.styles.smooth&&((b=k.styles.dashedValue)===null||b===void 0?void 0:b[0])===((x=C.styles.dashedValue)===null||x===void 0?void 0:x[0])&&((L=k.styles.dashedValue)===null||L===void 0?void 0:L[1])===((_=C.styles.dashedValue)===null||_===void 0?void 0:_[1])?k.coordinates.push(C.coordinates[1]):M.push({coordinates:[C.coordinates[0],C.coordinates[1]],styles:C.styles})}M.forEach(function(T){var I,E=T.coordinates,A=T.styles;(I=a.createFigure({name:"line",attrs:{coordinates:E},styles:A}))===null||I===void 0||I.draw(e)})}})}}}),e.restore()},r}(Lo),Z1=function(t){St(r,t);function r(){return t!==null&&t.apply(this,arguments)||this}return r.prototype.drawImp=function(e){var a=this.getWidget(),i=a.getPane(),n=a.getBounding(),o=a.getPane().getChart().getChartStore(),s=o.getCrosshair(),l=o.getStyles().crosshair;if(Gt(s.paneId)&&l.show){if(s.paneId===i.getId()){var c=s.y;this._drawLine(e,[{x:0,y:c},{x:n.width,y:c}],l.horizontal)}var u=s.realX;this._drawLine(e,[{x:u,y:0},{x:u,y:n.height}],l.vertical)}},r.prototype._drawLine=function(e,a,i){var n;if(i.show){var o=i.line;o.show&&((n=this.createFigure({name:"line",attrs:{coordinates:a},styles:o}))===null||n===void 0||n.draw(e))}},r}(Ve),ko=function(t){St(r,t);function r(e){var a=t.call(this,e)||this;return a._activeFeatureInfo=null,a._featureClickEvent=function(i,n){return function(){var o=a.getWidget().getPane();return o.getChart().getChartStore().executeAction(i,n),!0}},a._featureMouseMoveEvent=function(i){return function(){return a._activeFeatureInfo=i,!0}},a.registerEvent("mouseMoveEvent",function(i){return a._activeFeatureInfo=null,!1}),a}return r.prototype.drawImp=function(e){var a=this.getWidget(),i=a.getPane(),n=i.getChart().getChartStore(),o=n.getCrosshair();if(P(o.kLineData)){var s=a.getBounding(),l=n.getStyles().indicator.tooltip,c=l.offsetLeft,u=l.offsetTop,d=l.offsetRight;this.drawIndicatorTooltip(e,c,u,s.width-d)}},r.prototype.drawIndicatorTooltip=function(e,a,i,n){var o=this,s=this.getWidget().getPane(),l=s.getChart().getChartStore(),c=l.getStyles().indicator,u=c.tooltip;if(this.isDrawTooltip(l.getCrosshair(),u)){var d=l.getIndicatorsByPaneId(s.getId()),v=u.title,h=u.legend;d.forEach(function(f){var p=0,g={x:a,y:i},m=o.getIndicatorTooltipData(f),b=m.name,x=m.calcParamsText,L=m.legends,_=m.features,M=b.length>0,w=L.length>0;if(M||w){var k=o.classifyTooltipFeatures(_);if(p=o.drawStandardTooltipFeatures(e,k[0],g,f,a,p,n),M){var C=b;x.length>0&&(C="".concat(C).concat(x));var y=v.color;p=o.drawStandardTooltipLegends(e,[{title:{text:"",color:y},value:{text:C,color:y}}],g,a,p,n,v)}p=o.drawStandardTooltipFeatures(e,k[1],g,f,a,p,n),w&&(p=o.drawStandardTooltipLegends(e,L,g,a,p,n,h)),p=o.drawStandardTooltipFeatures(e,k[2],g,f,a,p,n),i=g.y+p}})}return i},r.prototype.drawStandardTooltipFeatures=function(e,a,i,n,o,s,l){var c=this;if(a.length>0){var u=0,d=0;a.forEach(function(f){var p=f.marginLeft,g=p===void 0?0:p,m=f.marginTop,b=m===void 0?0:m,x=f.marginRight,L=x===void 0?0:x,_=f.marginBottom,M=_===void 0?0:_,w=f.paddingLeft,k=w===void 0?0:w,C=f.paddingTop,y=C===void 0?0:C,T=f.paddingRight,I=T===void 0?0:T,E=f.paddingBottom,A=E===void 0?0:E,F=f.size,N=F===void 0?0:F,B=f.type,Y=f.content,K=0;if(B==="icon_font"){var $=Y;e.font=_r(N,"normal",$.family),K=e.measureText($.code).width}else K=N;u+=g+k+K+I+L,d=Math.max(d,b+y+N+A+M)}),i.x+u>l?(i.x=o,i.y+=s,s=d):s=Math.max(s,d);var v=this.getWidget().getPane(),h=v.getId();a.forEach(function(f){var p,g,m,b,x,L=f.marginLeft,_=L===void 0?0:L,M=f.marginTop,w=M===void 0?0:M,k=f.marginRight,C=k===void 0?0:k,y=f.paddingLeft,T=y===void 0?0:y,I=f.paddingTop,E=I===void 0?0:I,A=f.paddingRight,F=A===void 0?0:A,N=f.paddingBottom,B=N===void 0?0:N,Y=f.backgroundColor,K=f.activeBackgroundColor,$=f.borderRadius,ut=f.size,rt=ut===void 0?0:ut,z=f.color,tt=f.activeColor,mt=f.type,ft=f.content,yt=z,V=Y;((p=c._activeFeatureInfo)===null||p===void 0?void 0:p.paneId)===h&&((g=c._activeFeatureInfo.indicator)===null||g===void 0?void 0:g.id)===(n==null?void 0:n.id)&&c._activeFeatureInfo.feature.id===f.id&&(yt=tt??z,V=K??Y);var q="onCandleTooltipFeatureClick",dt={paneId:h,feature:f};P(n)&&(q="onIndicatorTooltipFeatureClick",dt.indicator=n);var J={mouseClickEvent:c._featureClickEvent(q,dt),mouseMoveEvent:c._featureMouseMoveEvent(dt)},_t=0;if(mt==="icon_font"){var Lt=ft;(m=c.createFigure({name:"text",attrs:{text:Lt.code,x:i.x+_,y:i.y+w},styles:{paddingLeft:T,paddingTop:E,paddingRight:F,paddingBottom:B,borderRadius:$,size:rt,family:Lt.family,color:yt,backgroundColor:V}},J))===null||m===void 0||m.draw(e),_t=e.measureText(Lt.code).width}else{(b=c.createFigure({name:"rect",attrs:{x:i.x+_,y:i.y+w,width:rt,height:rt},styles:{paddingLeft:T,paddingTop:E,paddingRight:F,paddingBottom:B,color:V}},J))===null||b===void 0||b.draw(e);var Ht=ft;(x=c.createFigure({name:"path",attrs:{path:Ht.path,x:i.x+_+T,y:i.y+w+E,width:rt,height:rt},styles:{style:Ht.style,lineWidth:Ht.lineWidth,color:yt}}))===null||x===void 0||x.draw(e),_t=rt}i.x+=_+T+_t+F+C})}return s},r.prototype.drawStandardTooltipLegends=function(e,a,i,n,o,s,l){var c=this;if(a.length>0){var u=l.marginLeft,d=l.marginTop,v=l.marginRight,h=l.marginBottom,f=l.size,p=l.family,g=l.weight;e.font=_r(f,g,p),a.forEach(function(m){var b,x,L=m.title,_=m.value,M=e.measureText(L.text).width,w=e.measureText(_.text).width,k=M+w,C=d+f+h;i.x+u+k+v>s?(i.x=n,i.y+=o,o=C):o=Math.max(o,C),L.text.length>0&&((b=c.createFigure({name:"text",attrs:{x:i.x+u,y:i.y+d,text:L.text},styles:{color:L.color,size:f,family:p,weight:g}}))===null||b===void 0||b.draw(e)),(x=c.createFigure({name:"text",attrs:{x:i.x+u+M,y:i.y+d,text:_.text},styles:{color:_.color,size:f,family:p,weight:g}}))===null||x===void 0||x.draw(e),i.x+=u+k+v})}return o},r.prototype.isDrawTooltip=function(e,a){var i=a.showRule;return i==="always"||i==="follow_cross"&&Gt(e.paneId)},r.prototype.getIndicatorTooltipData=function(e){var a,i,n=this.getWidget().getPane().getChart().getChartStore(),o=n.getStyles().indicator,s=o.tooltip,l=s.title,c="",u="";if(l.show&&(l.showName&&(c=e.shortName),l.showParams)){var d=e.calcParams;d.length>0&&(u="(".concat(d.join(","),")"))}var v={name:c,calcParamsText:u,legends:[],features:s.features},h=n.getCrosshair().dataIndex,f=e.result,p=n.getInnerFormatter(),g=n.getDecimalFold(),m=n.getThousandsSeparator(),b=[];if(e.visible){var x=(i=(a=f[h])!==null&&a!==void 0?a:f[h-1])!==null&&i!==void 0?i:{},L=s.legend.defaultValue;hi(e,h,o,function(F,N){if(Gt(F.title)){var B=N.color,Y=x[F.key];vt(Y)&&(Y=xe(Y,e.precision),e.shouldFormatBigNumber&&(Y=p.formatBigNumber(Y)),Y=g.format(m.format(Y))),b.push({title:{text:F.title,color:B},value:{text:Y??L,color:B}})}}),v.legends=b}if(Me(e.createTooltipDataSource)){var _=this.getWidget(),M=_.getPane(),w=M.getChart(),k=e.createTooltipDataSource({chart:w,indicator:e,crosshair:n.getCrosshair(),bounding:_.getBounding(),xAxis:M.getChart().getXAxisPane().getAxisComponent(),yAxis:M.getAxisComponent()}),C=k.name,y=k.calcParamsText,T=k.legends,I=k.features;if(l.show&&(Gt(C)&&l.showName&&(v.name=C),Gt(y)&&l.showParams&&(v.calcParamsText=y)),P(I)&&(v.features=I),P(T)&&e.visible){var E=[],A=o.tooltip.legend.color;T.forEach(function(F){var N={text:"",color:A};We(F.title)?N=F.title:N.text=F.title;var B={text:"",color:A};We(F.value)?B=F.value:B.text=F.value,vt(Number(B.text))&&(B.text=g.format(m.format(B.text))),E.push({title:N,value:B})}),v.legends=E}}return v},r.prototype.classifyTooltipFeatures=function(e){var a=[],i=[],n=[];return e.forEach(function(o){switch(o.position){case"left":{a.push(o);break}case"middle":{i.push(o);break}case"right":{n.push(o);break}}}),[a,i,n]},r}(Ve),So=function(t){St(r,t);function r(e){var a=t.call(this,e)||this;return a._initEvent(),a}return r.prototype._initEvent=function(){var e=this,a=this.getWidget(),i=a.getPane(),n=i.getId(),o=i.getChart(),s=o.getChartStore();this.registerEvent("mouseMoveEvent",function(l){var c,u=s.getProgressOverlayInfo();if(u!==null){var d=u.overlay,v=u.paneId;d.isStart()&&(s.updateProgressOverlayInfo(n),v=n);var h=d.points.length-1;return d.isDrawing()&&v===n&&(d.eventMoveForDrawing(e._coordinateToPoint(d,l)),(c=d.onDrawing)===null||c===void 0||c.call(d,U({chart:o,overlay:d},l))),e._figureMouseMoveEvent(d,"point",h,{key:"".concat(Lr,"point_").concat(h),type:"circle",attrs:{}})(l)}return s.setHoverOverlayInfo({paneId:n,overlay:null,figureType:"none",figureIndex:-1,figure:null},function(f,p){return e._processOverlayMouseEnterEvent(f,p,l)},function(f,p){return e._processOverlayMouseLeaveEvent(f,p,l)}),a.setForceCursor(null),!1}).registerEvent("mouseClickEvent",function(l){var c,u,d=s.getProgressOverlayInfo();if(d!==null){var v=d.overlay,h=d.paneId;v.isStart()&&(s.updateProgressOverlayInfo(n,!0),h=n);var f=v.points.length-1;return v.isDrawing()&&h===n&&(v.eventMoveForDrawing(e._coordinateToPoint(v,l)),(c=v.onDrawing)===null||c===void 0||c.call(v,U({chart:o,overlay:v},l)),v.nextStep(),v.isDrawing()||(s.progressOverlayComplete(),(u=v.onDrawEnd)===null||u===void 0||u.call(v,U({chart:o,overlay:v},l)))),e._figureMouseClickEvent(v,"point",f,{key:"".concat(Lr,"point_").concat(f),type:"circle",attrs:{}})(l)}return s.setClickOverlayInfo({paneId:n,overlay:null,figureType:"none",figureIndex:-1,figure:null},function(p,g){return e._processOverlaySelectedEvent(p,g,l)},function(p,g){return e._processOverlayDeselectedEvent(p,g,l)}),!1}).registerEvent("mouseDoubleClickEvent",function(l){var c,u=s.getProgressOverlayInfo();if(u!==null){var d=u.overlay,v=u.paneId;d.isDrawing()&&v===n&&(d.forceComplete(),d.isDrawing()||(s.progressOverlayComplete(),(c=d.onDrawEnd)===null||c===void 0||c.call(d,U({chart:o,overlay:d},l))));var h=d.points.length-1;return e._figureMouseClickEvent(d,"point",h,{key:"".concat(Lr,"point_").concat(h),type:"circle",attrs:{}})(l)}return!1}).registerEvent("mouseRightClickEvent",function(l){var c=s.getProgressOverlayInfo();if(c!==null){var u=c.overlay;if(u.isDrawing()){var d=u.points.length-1;return e._figureMouseRightClickEvent(u,"point",d,{key:"".concat(Lr,"point_").concat(d),type:"circle",attrs:{}})(l)}}return!1}).registerEvent("mouseUpEvent",function(l){var c,u=s.getPressedOverlayInfo(),d=u.overlay,v=u.figure;return d!==null&&$e("onPressedMoveEnd",v)&&((c=d.onPressedMoveEnd)===null||c===void 0||c.call(d,U({chart:o,overlay:d,figure:v??void 0},l))),s.setPressedOverlayInfo({paneId:n,overlay:null,figureType:"none",figureIndex:-1,figure:null}),!1}).registerEvent("pressedMouseMoveEvent",function(l){var c,u=s.getPressedOverlayInfo(),d=u.overlay,v=u.figureType,h=u.figureIndex,f=u.figure;if(d!==null&&$e("onPressedMoving",f)){if(!d.lock){var p=e._coordinateToPoint(d,l);v==="point"?d.eventPressedPointMove(p,h):d.eventPressedOtherMove(p,e.getWidget().getPane().getChart().getChartStore());var g=!1;(c=d.onPressedMoving)===null||c===void 0||c.call(d,U(U({chart:o,overlay:d,figure:f??void 0},l),{preventDefault:function(){g=!0}})),g?e.getWidget().setForceCursor(null):e.getWidget().setForceCursor("pointer")}return!0}return e.getWidget().setForceCursor(null),!1})},r.prototype._createFigureEvents=function(e,a,i,n){return e.isDrawing()?null:{mouseMoveEvent:this._figureMouseMoveEvent(e,a,i,n),mouseDownEvent:this._figureMouseDownEvent(e,a,i,n),mouseClickEvent:this._figureMouseClickEvent(e,a,i,n),mouseRightClickEvent:this._figureMouseRightClickEvent(e,a,i,n),mouseDoubleClickEvent:this._figureMouseDoubleClickEvent(e,a,i,n)}},r.prototype._processOverlayMouseEnterEvent=function(e,a,i){return Me(e.onMouseEnter)&&$e("onMouseEnter",a)?(e.onMouseEnter(U({chart:this.getWidget().getPane().getChart(),overlay:e,figure:a??void 0},i)),!0):!1},r.prototype._processOverlayMouseLeaveEvent=function(e,a,i){return Me(e.onMouseLeave)&&$e("onMouseLeave",a)?(e.onMouseLeave(U({chart:this.getWidget().getPane().getChart(),overlay:e,figure:a??void 0},i)),!0):!1},r.prototype._processOverlaySelectedEvent=function(e,a,i){var n;return $e("onSelected",a)?((n=e.onSelected)===null||n===void 0||n.call(e,U({chart:this.getWidget().getPane().getChart(),overlay:e,figure:a??void 0},i)),!0):!1},r.prototype._processOverlayDeselectedEvent=function(e,a,i){var n;return $e("onDeselected",a)?((n=e.onDeselected)===null||n===void 0||n.call(e,U({chart:this.getWidget().getPane().getChart(),overlay:e,figure:a??void 0},i)),!0):!1},r.prototype._figureMouseMoveEvent=function(e,a,i,n){var o=this;return function(s){var l,c=o.getWidget().getPane(),u=!e.isDrawing()&&$e("onMouseMove",n);if(u){var d=!1;(l=e.onMouseMove)===null||l===void 0||l.call(e,U(U({chart:c.getChart(),overlay:e,figure:n},s),{preventDefault:function(){d=!0}})),d?o.getWidget().setForceCursor(null):o.getWidget().setForceCursor("pointer")}return c.getChart().getChartStore().setHoverOverlayInfo({paneId:c.getId(),overlay:e,figureType:a,figure:n,figureIndex:i},function(v,h){return o._processOverlayMouseEnterEvent(v,h,s)},function(v,h){return o._processOverlayMouseLeaveEvent(v,h,s)}),u}},r.prototype._figureMouseDownEvent=function(e,a,i,n){var o=this;return function(s){var l,c=o.getWidget().getPane(),u=c.getId();return e.startPressedMove(o._coordinateToPoint(e,s)),$e("onPressedMoveStart",n)?((l=e.onPressedMoveStart)===null||l===void 0||l.call(e,U({chart:c.getChart(),overlay:e,figure:n},s)),c.getChart().getChartStore().setPressedOverlayInfo({paneId:u,overlay:e,figureType:a,figureIndex:i,figure:n}),!e.isDrawing()):!1}},r.prototype._figureMouseClickEvent=function(e,a,i,n){var o=this;return function(s){var l,c=o.getWidget().getPane(),u=c.getId(),d=!e.isDrawing()&&$e("onClick",n);return d&&((l=e.onClick)===null||l===void 0||l.call(e,U({chart:o.getWidget().getPane().getChart(),overlay:e,figure:n},s))),c.getChart().getChartStore().setClickOverlayInfo({paneId:u,overlay:e,figureType:a,figureIndex:i,figure:n},function(v,h){return o._processOverlaySelectedEvent(v,h,s)},function(v,h){return o._processOverlayDeselectedEvent(v,h,s)}),d}},r.prototype._figureMouseDoubleClickEvent=function(e,a,i,n){var o=this;return function(s){var l;return $e("onDoubleClick",n)?((l=e.onDoubleClick)===null||l===void 0||l.call(e,U(U({},s),{chart:o.getWidget().getPane().getChart(),figure:n,overlay:e})),!e.isDrawing()):!1}},r.prototype._figureMouseRightClickEvent=function(e,a,i,n){var o=this;return function(s){var l;if($e("onRightClick",n)){var c=!1;return(l=e.onRightClick)===null||l===void 0||l.call(e,U(U({chart:o.getWidget().getPane().getChart(),overlay:e,figure:n},s),{preventDefault:function(){c=!0}})),c||o.getWidget().getPane().getChart().getChartStore().removeOverlay(e),!e.isDrawing()}return!1}},r.prototype._coordinateToPoint=function(e,a){var i,n={},o=this.getWidget().getPane(),s=o.getChart(),l=o.getId(),c=s.getChartStore();if(this.coordinateToPointTimestampDataIndexFlag()){var u=s.getXAxisPane().getAxisComponent(),d=u.convertFromPixel(a.x),v=(i=c.dataIndexToTimestamp(d))!==null&&i!==void 0?i:void 0;n.timestamp=v,n.dataIndex=d}if(this.coordinateToPointValueFlag()){var h=o.getAxisComponent(),f=h.convertFromPixel(a.y);if(e.mode!=="normal"&&l===wt.CANDLE&&vt(n.dataIndex)){var p=c.getDataByDataIndex(n.dataIndex);if(p!==null){var g=e.modeSensitivity;if(f>p.high)if(e.mode==="weak_magnet"){var m=h.convertToPixel(p.high),b=h.convertFromPixel(m-g);f<b&&(f=p.high)}else f=p.high;else if(f<p.low)if(e.mode==="weak_magnet"){var x=h.convertToPixel(p.low),b=h.convertFromPixel(x-g);f>b&&(f=p.low)}else f=p.low;else{var L=Math.max(p.open,p.close),_=Math.min(p.open,p.close);f>L?f-L<p.high-f?f=L:f=p.high:f<_?f-p.low<_-f?f=p.low:f=_:L-f<f-_?f=L:f=_}}}n.value=f}return n},r.prototype.coordinateToPointValueFlag=function(){return!0},r.prototype.coordinateToPointTimestampDataIndexFlag=function(){return!0},r.prototype.dispatchEvent=function(e,a){return this.getWidget().getPane().getChart().getChartStore().isOverlayDrawing()?this.onEvent(e,a):t.prototype.dispatchEvent.call(this,e,a)},r.prototype.drawImp=function(e){var a=this,i=this.getCompleteOverlays();i.forEach(function(o){o.visible&&a._drawOverlay(e,o)});var n=this.getProgressOverlay();P(n)&&n.visible&&this._drawOverlay(e,n)},r.prototype._drawOverlay=function(e,a){var i=a.points,n=this.getWidget().getPane(),o=n.getChart(),s=o.getChartStore(),l=n.getAxisComponent(),c=o.getXAxisPane().getAxisComponent(),u=i.map(function(v){var h,f=null;vt(v.timestamp)&&(f=s.timestampToDataIndex(v.timestamp));var p={x:0,y:0};return vt(f)&&(p.x=c.convertToPixel(f)),vt(v.value)&&(p.y=(h=l==null?void 0:l.convertToPixel(v.value))!==null&&h!==void 0?h:0),p});if(u.length>0){var d=[].concat(this.getFigures(a,u));this.drawFigures(e,a,d)}this.drawDefaultFigures(e,a,u)},r.prototype.drawFigures=function(e,a,i){var n=this,o=this.getWidget().getPane().getChart().getStyles().overlay;i.forEach(function(s,l){var c=s.type,u=s.styles,d=s.attrs,v=[].concat(d);v.forEach(function(h){var f,p,g=n._createFigureEvents(a,"other",l,s),m=U(U(U({},o[c]),(f=a.styles)===null||f===void 0?void 0:f[c]),u);(p=n.createFigure({name:c,attrs:h,styles:m},g??void 0))===null||p===void 0||p.draw(e)})})},r.prototype.getCompleteOverlays=function(){var e=this.getWidget().getPane();return e.getChart().getChartStore().getOverlaysByPaneId(e.getId())},r.prototype.getProgressOverlay=function(){var e=this.getWidget().getPane(),a=e.getChart().getChartStore().getProgressOverlayInfo();return P(a)&&a.paneId===e.getId()?a.overlay:null},r.prototype.getFigures=function(e,a){var i,n,o=this.getWidget(),s=o.getPane(),l=s.getChart(),c=s.getAxisComponent(),u=l.getXAxisPane().getAxisComponent(),d=o.getBounding();return(n=(i=e.createPointFigures)===null||i===void 0?void 0:i.call(e,{chart:l,overlay:e,coordinates:a,bounding:d,xAxis:u,yAxis:c}))!==null&&n!==void 0?n:[]},r.prototype.drawDefaultFigures=function(e,a,i){var n=this,o,s;if(a.needDefaultPointFigure){var l=this.getWidget().getPane().getChart().getChartStore(),c=l.getHoverOverlayInfo(),u=l.getClickOverlayInfo();if(((o=c.overlay)===null||o===void 0?void 0:o.id)===a.id&&c.figureType!=="none"||((s=u.overlay)===null||s===void 0?void 0:s.id)===a.id&&u.figureType!=="none"){var d=l.getStyles().overlay,v=a.styles,h=U(U({},d.point),v==null?void 0:v.point);i.forEach(function(f,p){var g,m,b,x,L,_=f.x,M=f.y,w=h.radius,k=h.color,C=h.borderColor,y=h.borderSize;((g=c.overlay)===null||g===void 0?void 0:g.id)===a.id&&c.figureType==="point"&&((m=c.figure)===null||m===void 0?void 0:m.key)==="".concat(Lr,"point_").concat(p)&&(w=h.activeRadius,k=h.activeColor,C=h.activeBorderColor,y=h.activeBorderSize),(x=n.createFigure({name:"circle",attrs:{x:_,y:M,r:w+y},styles:{color:C}},(b=n._createFigureEvents(a,"point",p,{key:"".concat(Lr,"point_").concat(p),type:"circle",attrs:{x:_,y:M,r:w+y},styles:{color:C}}))!==null&&b!==void 0?b:void 0))===null||x===void 0||x.draw(e),(L=n.createFigure({name:"circle",attrs:{x:_,y:M,r:w},styles:{color:k}}))===null||L===void 0||L.draw(e)})}}},r}(Ve),Mo=function(t){St(r,t);function r(e,a){var i=t.call(this,e,a)||this;return i._gridView=new W1(i),i._indicatorView=new Y1(i),i._crosshairLineView=new Z1(i),i._tooltipView=i.createTooltipView(),i._overlayView=new So(i),i.addChild(i._tooltipView),i.addChild(i._overlayView),i}return r.prototype.getName=function(){return Vt.MAIN},r.prototype.updateMain=function(e){this.getPane().getOptions().state!=="minimize"&&(this.updateMainContent(e),this._indicatorView.draw(e),this._gridView.draw(e))},r.prototype.createTooltipView=function(){return new ko(this)},r.prototype.updateMainContent=function(e){},r.prototype.updateOverlayContent=function(e){},r.prototype.updateOverlay=function(e){this.getPane().getOptions().state!=="minimize"&&(this._overlayView.draw(e),this._crosshairLineView.draw(e),this.updateOverlayContent(e)),this._tooltipView.draw(e)},r}(mi),H1=function(t){St(r,t);function r(){var e=t.apply(this,dr([],ar(arguments),!1))||this;return e._ripplePoint=e.createFigure({name:"circle",attrs:{x:0,y:0,r:0},styles:{style:"fill"}}),e._animationFrameTime=0,e._animation=new qa({iterationCount:1/0}).doFrame(function(a){e._animationFrameTime=a;var i=e.getWidget().getPane();i.getChart().updatePane(0,i.getId())}),e}return r.prototype.drawImp=function(e){var a,i,n,o=this.getWidget(),s=o.getPane(),l=s.getChart(),c=l.getDataList(),u=c.length-1,d=o.getBounding(),v=s.getAxisComponent(),h=l.getStyles().candle.area,f=[],p=Number.MAX_SAFE_INTEGER,g=Number.MIN_SAFE_INTEGER,m=null;if(this.eachChildren(function(w){var k=w.x,C=w.data.current,y=C==null?void 0:C[h.value];if(vt(y)){var T=v.convertToPixel(y);g===Number.MIN_SAFE_INTEGER&&(g=k),f.push({x:k,y:T}),p=Math.min(p,T),w.dataIndex===u&&(m={x:k,y:T})}}),f.length>0){(a=this.createFigure({name:"line",attrs:{coordinates:f},styles:{color:h.lineColor,size:h.lineSize,smooth:h.smooth}}))===null||a===void 0||a.draw(e);var b=h.backgroundColor,x="";if(He(b)){var L=e.createLinearGradient(0,d.height,0,p);try{b.forEach(function(w){var k=w.offset,C=w.color;L.addColorStop(k,C)})}catch{}x=L}else x=b;e.fillStyle=x,e.beginPath(),e.moveTo(g,d.height),e.lineTo(f[0].x,f[0].y),fo(e,f,h.smooth),e.lineTo(f[f.length-1].x,d.height),e.closePath(),e.fill()}var _=h.point;if(_.show&&P(m)){(i=this.createFigure({name:"circle",attrs:{x:m.x,y:m.y,r:_.radius},styles:{style:"fill",color:_.color}}))===null||i===void 0||i.draw(e);var M=_.rippleRadius;_.animation&&(M=_.radius+this._animationFrameTime/_.animationDuration*(_.rippleRadius-_.radius),this._animation.setDuration(_.animationDuration).start()),(n=this._ripplePoint)===null||n===void 0||n.setAttrs({x:m.x,y:m.y,r:M}).setStyles({style:"fill",color:_.rippleColor}).draw(e)}else this.stopAnimation()},r.prototype.stopAnimation=function(){this._animation.stop()},r}(_i),j1=function(t){St(r,t);function r(){return t!==null&&t.apply(this,arguments)||this}return r.prototype.drawImp=function(e){var a,i,n=this.getWidget(),o=n.getPane(),s=o.getChart().getChartStore(),l=s.getStyles().candle.priceMark,c=l.high,u=l.low;if(l.show&&(c.show||u.show)){var d=s.getVisibleRangeHighLowPrice(),v=(i=(a=s.getSymbol())===null||a===void 0?void 0:a.pricePrecision)!==null&&i!==void 0?i:2,h=o.getAxisComponent(),f=d[0],p=f.price,g=f.x,m=d[1],b=m.price,x=m.x,L=h.convertToPixel(p),_=h.convertToPixel(b),M=s.getDecimalFold(),w=s.getThousandsSeparator();c.show&&p!==Number.MIN_SAFE_INTEGER&&this._drawMark(e,M.format(w.format(xe(p,v))),{x:g,y:L},L<_?[-2,-5]:[2,5],c),u.show&&b!==Number.MAX_SAFE_INTEGER&&this._drawMark(e,M.format(w.format(xe(b,v))),{x,y:_},L<_?[2,5]:[-2,-5],u)}},r.prototype._drawMark=function(e,a,i,n,o){var s,l,c,u=i.x,d=i.y+n[0];(s=this.createFigure({name:"line",attrs:{coordinates:[{x:u-2,y:d+n[0]},{x:u,y:d},{x:u+2,y:d+n[0]}]},styles:{color:o.color}}))===null||s===void 0||s.draw(e);var v=0,h=0,f="left",p=this.getWidget().getBounding().width;u>p/2?(v=u-5,h=v-o.textOffset,f="right"):(v=u+5,f="left",h=v+o.textOffset);var g=d+n[1];(l=this.createFigure({name:"line",attrs:{coordinates:[{x:u,y:d},{x:u,y:g},{x:v,y:g}]},styles:{color:o.color}}))===null||l===void 0||l.draw(e),(c=this.createFigure({name:"text",attrs:{x:h,y:g,text:a,align:f,baseline:"middle"},styles:{color:o.color,size:o.textSize,family:o.textFamily,weight:o.textWeight}}))===null||c===void 0||c.draw(e)},r}(Ve),X1=function(t){St(r,t);function r(){return t!==null&&t.apply(this,arguments)||this}return r.prototype.drawImp=function(e){var a,i,n,o=this.getWidget(),s=o.getPane(),l=o.getBounding(),c=s.getChart().getChartStore(),u=c.getStyles().candle.priceMark,d=u.last,v=d.line;if(u.show&&d.show&&v.show){var h=s.getAxisComponent(),f=c.getDataList(),p=f[f.length-1];if(P(p)){var g=p.close,m=p.open,b=d.compareRule==="current_open"?m:(i=(a=f[f.length-2])===null||a===void 0?void 0:a.close)!==null&&i!==void 0?i:g,x=h.convertToNicePixel(g),L="";g>b?L=d.upColor:g<b?L=d.downColor:L=d.noChangeColor,(n=this.createFigure({name:"line",attrs:{coordinates:[{x:0,y:x},{x:l.width,y:x}]},styles:{style:v.style,color:L,size:v.size,dashedValue:v.dashedValue}}))===null||n===void 0||n.draw(e)}}},r}(Ve),K1=function(t){St(r,t);function r(){var e=t.apply(this,dr([],ar(arguments),!1))||this;return e._orderFlowDataList=[],e._expandedBars=new Set,e._momentumLines=[],e._boundOrderFlowClickEvent=function(a,i){return function(){var n=e.getWidget().getPane(),o=n.getChart().getChartStore();return o.executeAction("onCandleBarClick",U(U({},a),{orderFlowPriceLevel:i})),!1}},e._boundExpandClickEvent=function(a){return function(){return e._expandedBars.has(a)?e._expandedBars.delete(a):e._expandedBars.add(a),!1}},e}return r.prototype.setOrderFlowData=function(e){this._orderFlowDataList=e,this._updateMomentumLines()},r.prototype.getOrderFlowData=function(){return this._orderFlowDataList},r.prototype._calculateKlineDelta=function(e){return e.priceLevels.reduce(function(a,i){return a+(i.buyVolume-i.sellVolume)},0)},r.prototype._checkPriceDirection=function(e,a,i){var n=e.close,o=a.close,s=i.close;return o>n&&s>o?"up":o<n&&s<o?"down":"mixed"},r.prototype._updateMomentumLines=function(){var e=this;if(this._orderFlowDataList.length<3){this._momentumLines=[];return}for(var a=function(o){var s=i._orderFlowDataList[o],l=i._orderFlowDataList[o-1],c=i._orderFlowDataList[o-2],u=i._calculateKlineDelta(c),d=i._calculateKlineDelta(l),v=i._calculateKlineDelta(s),h=i._checkPriceDirection(c,l,s),f=!1,p=!1;if(u>0&&d>0&&v>0&&h==="up"?(f=!0,p=!0):u<0&&d<0&&v<0&&h==="down"&&(f=!0,p=!1),f){var g=i._momentumLines.find(function(b){return b.startTimestamp===s.timestamp});if(g===void 0){var m=s.open;i._momentumLines.push({startIndex:o,startTimestamp:s.timestamp,startPrice:s.close,linePrice:m,isPositive:p,isActive:!0,totalDelta:u+d+v})}}},i=this,n=2;n<this._orderFlowDataList.length;n++)a(n);this._momentumLines.forEach(function(o){if(o.isActive){var s=e._orderFlowDataList.findIndex(function(u){return u.timestamp===o.startTimestamp});if(s!==-1)for(var l=s+1;l<e._orderFlowDataList.length;l++){var c=e._orderFlowDataList[l];if(c.close<o.startPrice){o.isActive=!1,o.endIndex=l,o.endTimestamp=c.timestamp;break}}}})},r.prototype.drawImp=function(e){var a=this,i=this.getWidget().getPane(),n=this.getOrderFlowOptions();if(!(n===null||!n.enabled)&&this._orderFlowDataList.length!==0){var o=n.styles,s=i.getAxisComponent(),l=i.getId()===wt.CANDLE;e.save(),this._drawMomentumLines(e,s),this.eachChildren(function(c,u){var d,v=c.x,h=c.data.current;if(P(h)){var f=a._getOrderFlowByTimestamp(h.timestamp);if(f!==null){var p=s.convertToPixel(h.high),g=s.convertToPixel(h.low),m=Math.max(1,u.bar),b=m<=30?m*.8:m*.9,x=b/2,L=a._expandedBars.has(h.timestamp),_=f.priceLevels.filter(function(V){return V.totalVolume>0}),M=50,w=L?_:_.slice(0,M);if(w.length!==0){var k=Math.max(w.length,3),C=(g-p)/k*.95,y=Math.max(15,Math.min(60,C)),T=m>5&&y>10&&m>88;try{var I=new CustomEvent("orderFlowDebug",{detail:{barWidth:m.toFixed(1),levelHeight:y.toFixed(1),barSpace:u.bar.toFixed(1),shouldShowText:T}});window.dispatchEvent(I)}catch{}var E=8,A=Math.max(.6,Math.min(1.4,m/20)),F=Math.round(E*A),N=Math.max(8,Math.round(F*.8)),B=y*w.length,Y=p,K=Y+B,$=_.length>M,ut=$?15:0,rt=B+ut;if(a._drawOrderFlowBorder(e,v,Y,b,rt),w.forEach(function(V,q){var dt=Y+(q+.5)*y,J=a._createTwoColumnOrderFlow(v,dt,V,x,y,o,b,w,_,L,T,F);J.forEach(function(_t){var Lt,Ht=null;l&&(Ht={mouseClickEvent:a._boundOrderFlowClickEvent(c,V)}),(Lt=a.createFigure(_t,Ht??void 0))===null||Lt===void 0||Lt.draw(e)})}),$&&T){var z=K+8,tt=L?"收起":"+".concat(_.length-M,"更多"),mt={x:v,y:z,text:tt,align:"center",baseline:"top"},ft={name:"text",attrs:mt,styles:{color:"#888888",size:N,weight:"normal",family:"Arial"}},yt=null;l&&(yt={mouseClickEvent:a._boundExpandClickEvent(h.timestamp)}),(d=a.createFigure(ft,yt??void 0))===null||d===void 0||d.draw(e)}b>0&&a._drawSimpleOHLC(e,v,b,h,s)}}}}),e.restore()}},r.prototype.getOrderFlowOptions=function(){return{enabled:!0,styles:{buyVolume:{color:"rgba(0, 150, 0, 0.8)",textColor:"#ffffff"},sellVolume:{color:"rgba(220, 20, 60, 0.8)",textColor:"#ffffff"},dominantVolume:{buyColor:"rgba(0, 200, 0, 0.9)",sellColor:"rgba(255, 20, 20, 0.9)",textColor:"#ffffff"},text:{size:12,weight:"normal",family:"Arial"},background:{color:"rgba(128, 128, 128, 0.1)",opacity:.2},showVolumeNumbers:!0,showImbalance:!0,imbalanceThreshold:70}}},r.prototype._getOrderFlowByTimestamp=function(e){var a;return(a=this._orderFlowDataList.find(function(i){return i.timestamp===e}))!==null&&a!==void 0?a:null},r.prototype._formatVolume=function(e){return e>=1e6?"".concat((e/1e6).toFixed(1),"M"):e>=1e3?"".concat((e/1e3).toFixed(1),"K"):e.toString()},r.prototype._drawOrderFlowBorder=function(e,a,i,n,o){e.save(),e.setLineDash([]),e.strokeStyle="rgba(200, 200, 200, 0.8)",e.lineWidth=1;var s=a-n/2;e.strokeRect(s,i,n,o),e.strokeStyle="rgba(160, 160, 160, 0.7)";var l=s+n/2;e.beginPath(),e.moveTo(l,i),e.lineTo(l,i+o),e.stroke(),e.restore()},r.prototype._createTwoColumnOrderFlow=function(e,a,i,n,o,s,l,c,u,d,v,h){var f=[],p=d?u:c,g=Math.max.apply(Math,dr([],ar(p.map(function(rt){return rt.totalVolume})),!1)),m=Math.max.apply(Math,dr([],ar(p.map(function(rt){return Math.abs(rt.buyVolume-rt.sellVolume)})),!1));if(g===0||i.totalVolume===0)return f;var b=i.buyVolume-i.sellVolume,x=m>0?Math.abs(b)/m:0,L=i.totalVolume/g,_=e-l/2,M=_,w=b>0;if(Math.abs(b)>0&&x>0){var k=this._getDeltaGradientColor(x,w),C=Math.max(1,n*.95*x),y={x:M+1,y:a-o/2+1,width:C,height:o-2};f.push({name:"rect",attrs:y,styles:{style:"fill",color:k}})}if(v){var T="rgba(220, 220, 220, 0.9)",I=b===0?"0":b>0?"+".concat(this._formatVolume(Math.abs(b))):"-".concat(this._formatVolume(Math.abs(b))),E={x:M+n/2,y:a,text:I,align:"center",baseline:"middle"};f.push({name:"text",attrs:E,styles:{color:T,size:h,weight:s.text.weight,family:s.text.family}})}var A=_+n,F=L>0?Math.max(2,n*.95*L):0;if(F>0){var N=this._getVolumeGradientColor(L),B={x:A+1,y:a-o/2+1,width:F,height:o-2};f.push({name:"rect",attrs:B,styles:{style:"fill",color:N}})}if(v){var Y=this._formatVolume(i.buyVolume),K=this._formatVolume(i.sellVolume),$="".concat(Y," ").concat(K),ut={x:A+n/2,y:a,text:$,align:"center",baseline:"middle"};f.push({name:"text",attrs:ut,styles:{color:"rgba(200, 200, 200, 0.85)",size:h,weight:s.text.weight,family:s.text.family}})}return f},r.prototype._getDeltaGradientColor=function(e,a){var i=Math.sqrt(e),n=Math.min(.8,Math.max(.4,i*.6+.2));return a?"rgba(220, 100, 100, ".concat(n,")"):"rgba(100, 180, 100, ".concat(n,")")},r.prototype._getVolumeGradientColor=function(e){var a=Math.sqrt(e),i=Math.min(.9,Math.max(.4,a*.6+.3));return"rgba(100, 150, 200, ".concat(i,")")},r.prototype._drawSimpleOHLC=function(e,a,i,n,o){var s=o.convertToPixel(n.open),l=o.convertToPixel(n.close),c=o.convertToPixel(n.high),u=o.convertToPixel(n.low),d=n.close>=n.open,v=d?"#15803d":"#b91c1c",h=a-i/2-5;e.save(),e.setLineDash([]),e.strokeStyle=v,e.lineWidth=2,e.beginPath(),e.moveTo(h,c),e.lineTo(h,u),e.stroke(),Math.abs(s-l)>=1&&(e.lineWidth=8,e.beginPath(),e.moveTo(h,s),e.lineTo(h,l),e.stroke()),e.restore()},r.prototype.getOrderFlowDrawingBounds=function(){return this._orderFlowDataList.length===0?null:{topY:0,bottomY:1e3}},r.prototype._drawMomentumLines=function(e,a){var i=this;if(this._momentumLines.length!==0){e.save();var n=new Map,o=Number.MAX_SAFE_INTEGER,s=Number.MIN_SAFE_INTEGER;if(this.eachChildren(function(c,u){var d=c.x,v=c.data.current;v!=null&&(n.set(v.timestamp,d),o=Math.min(o,d),s=Math.max(s,d))}),o===Number.MAX_SAFE_INTEGER){e.restore();return}var l=Math.max.apply(Math,dr([],ar(this._momentumLines.map(function(c){return Math.abs(c.totalDelta)})),!1));this._momentumLines.forEach(function(c){var u=n.get(c.startTimestamp);if(u!==void 0){var d=i._orderFlowDataList.find(function(k){return k.timestamp===c.startTimestamp});if(d!==void 0){var v=a.convertToPixel(d.open),h=a.convertToPixel(d.close),f=Math.abs(h-v),p=Math.min(v,h),g=u+200;if(c.isActive)g=s+50;else if(c.endTimestamp!==void 0){var m=n.get(c.endTimestamp);g=m??u+200}if(!(g<o||u>s+50)){var b=Math.max(u,o-10),x=Math.min(g,s+50),L=x-b,_=l>0?Math.abs(c.totalDelta)/l:.5,M=Math.max(.1,Math.min(.4,_*.3+.1)),w=Math.max(.3,Math.min(.7,_*.4+.3));c.isPositive?(e.fillStyle="rgba(255, 99, 99, ".concat(M,")"),e.strokeStyle="rgba(255, 99, 99, ".concat(w,")")):(e.fillStyle="rgba(76, 175, 80, ".concat(M,")"),e.strokeStyle="rgba(76, 175, 80, ".concat(w,")")),e.fillRect(b,p,L,f),e.lineWidth=1,e.setLineDash([]),e.strokeRect(b,p,L,f),e.fillStyle=c.isPositive?"rgba(255, 99, 99, ".concat(w,")"):"rgba(76, 175, 80, ".concat(w,")"),e.fillRect(u-1,p-2,2,f+4)}}}}),e.restore()}},r}(_i),G1={second:"HH:mm:ss",minute:"HH:mm",hour:"MM-DD HH:mm",day:"YYYY-MM-DD",week:"YYYY-MM-DD",month:"YYYY-MM",year:"YYYY"},To={second:"HH:mm:ss",minute:"YYYY-MM-DD HH:mm",hour:"YYYY-MM-DD HH:mm",day:"YYYY-MM-DD",week:"YYYY-MM-DD",month:"YYYY-MM",year:"YYYY"},U1={time:"时间：",open:"开：",high:"高：",low:"低：",close:"收：",volume:"成交量：",turnover:"成交额：",change:"涨幅：",second:"秒",minute:"",hour:"小时",day:"天",week:"周",month:"月",year:"年"},Q1={time:"Time: ",open:"Open: ",high:"High: ",low:"Low: ",close:"Close: ",volume:"Volume: ",turnover:"Turnover: ",change:"Change: ",second:"S",minute:"",hour:"H",day:"D",week:"W",month:"M",year:"Y"},q1={"zh-CN":U1,"en-US":Q1};function tn(t,r){var e;return(e=q1[r][t])!==null&&e!==void 0?e:t}var J1=function(t){St(r,t);function r(){return t!==null&&t.apply(this,arguments)||this}return r.prototype.drawImp=function(e){var a=this.getWidget(),i=a.getPane().getChart().getChartStore(),n=i.getCrosshair();if(P(n.kLineData)){var o=a.getBounding(),s=i.getStyles(),l=s.candle,c=s.indicator;if(l.tooltip.showType==="rect"&&c.tooltip.showType==="rect"){var u=this.isDrawTooltip(n,l.tooltip),d=this.isDrawTooltip(n,c.tooltip);this._drawRectTooltip(e,u,d,l.tooltip.offsetTop)}else if(l.tooltip.showType==="standard"&&c.tooltip.showType==="standard"){var v=l.tooltip,h=v.offsetLeft,f=v.offsetTop,p=v.offsetRight,g=o.width-p,m=this._drawCandleStandardTooltip(e,h,f,g);this.drawIndicatorTooltip(e,h,m,g)}else if(l.tooltip.showType==="rect"&&c.tooltip.showType==="standard"){var b=l.tooltip,h=b.offsetLeft,f=b.offsetTop,p=b.offsetRight,g=o.width-p,x=this.drawIndicatorTooltip(e,h,f,g),u=this.isDrawTooltip(n,l.tooltip);this._drawRectTooltip(e,u,!1,x)}else{var L=l.tooltip,h=L.offsetLeft,f=L.offsetTop,p=L.offsetRight,g=o.width-p,_=this._drawCandleStandardTooltip(e,h,f,g),d=this.isDrawTooltip(n,c.tooltip);this._drawRectTooltip(e,!1,d,_)}}},r.prototype._drawCandleStandardTooltip=function(e,a,i,n){var o,s=this.getWidget().getPane().getChart().getChartStore(),l=s.getStyles().candle,c=l.tooltip,u=c.legend,d=0,v={x:a,y:i},h=s.getCrosshair();if(this.isDrawTooltip(h,c)){var f=c.title;if(f.show){var p=(o=s.getPeriod())!==null&&o!==void 0?o:{},g=p.type,m=g===void 0?"":g,b=p.span,x=b===void 0?"":b,L=$i(f.template,U(U({},s.getSymbol()),{period:"".concat(x).concat(tn(m,s.getLocale()))})),_=f.color,M=this.drawStandardTooltipLegends(e,[{title:{text:"",color:_},value:{text:L,color:_}}],{x:a,y:i},a,0,n,f);v.y=v.y+M}var w=this._getCandleTooltipLegends(),k=this.classifyTooltipFeatures(c.features);d=this.drawStandardTooltipFeatures(e,k[0],v,null,a,d,n),d=this.drawStandardTooltipFeatures(e,k[1],v,null,a,d,n),w.length>0&&(d=this.drawStandardTooltipLegends(e,w,v,a,d,n,u)),d=this.drawStandardTooltipFeatures(e,k[2],v,null,a,d,n)}return v.y+d},r.prototype._drawRectTooltip=function(e,a,i,n){var o=this,s,l,c=this.getWidget(),u=c.getPane(),d=u.getChart().getChartStore(),v=d.getStyles(),h=v.candle,f=v.indicator,p=h.tooltip,g=f.tooltip;if(a||i){var m=this._getCandleTooltipLegends(),b=p.offsetLeft,x=p.offsetTop,L=p.offsetRight,_=p.offsetBottom,M=p.legend,w=M.marginLeft,k=M.marginRight,C=M.marginTop,y=M.marginBottom,T=M.size,I=M.weight,E=M.family,A=p.rect,F=A.position,N=A.paddingLeft,B=A.paddingRight,Y=A.paddingTop,K=A.paddingBottom,$=A.offsetLeft,ut=A.offsetRight,rt=A.offsetTop,z=A.offsetBottom,tt=A.borderSize,mt=A.borderRadius,ft=A.borderColor,yt=A.color,V=0,q=0,dt=0;a&&(e.font=_r(T,I,E),m.forEach(function(me){var Le=me.title,de=me.value,ke="".concat(Le.text).concat(de.text),ze=e.measureText(ke).width+w+k;V=Math.max(V,ze)}),dt+=(y+C+T)*m.length);var J=g.legend,_t=J.marginLeft,Lt=J.marginRight,Ht=J.marginTop,jt=J.marginBottom,Dt=J.size,Xt=J.weight,qt=J.family,et=[];if(i){var nt=d.getIndicatorsByPaneId(u.getId());e.font=_r(Dt,Xt,qt),nt.forEach(function(me){var Le=o.getIndicatorTooltipData(me).legends;et.push(Le),Le.forEach(function(de){var ke=de.title,ze=de.value,fr="".concat(ke.text).concat(ze.text),wr=e.measureText(fr).width+_t+Lt;V=Math.max(V,wr),dt+=Ht+jt+Dt})})}if(q+=V,q!==0&&dt!==0){var Pt=d.getCrosshair(),Et=c.getBounding(),Yt=u.getYAxisWidget().getBounding();q+=tt*2+N+B,dt+=tt*2+Y+K;var ie=Et.width/2,ne=F==="pointer"&&Pt.paneId===wt.CANDLE,ee=((s=Pt.realX)!==null&&s!==void 0?s:0)>ie,Ft=0;if(ne){var oe=Pt.realX;ee?Ft=oe-ut-q:Ft=oe+$}else{var le=this.getWidget().getPane().getAxisComponent();ee?(Ft=$+b,le.inside&&le.position==="left"&&(Ft+=Yt.width)):(Ft=Et.width-ut-q-L,le.inside&&le.position==="right"&&(Ft-=Yt.width))}var Ut=n+rt;if(ne){var Qe=Pt.y;Ut=Qe-dt/2,Ut+dt>Et.height-z-_&&(Ut=Et.height-z-dt-_),Ut<n+rt&&(Ut=n+rt+x)}(l=this.createFigure({name:"rect",attrs:{x:Ft,y:Ut,width:q,height:dt},styles:{style:"stroke_fill",color:yt,borderColor:ft,borderSize:tt,borderRadius:mt}}))===null||l===void 0||l.draw(e);var we=Ft+tt+N+w,fe=Ut+tt+Y;if(a&&m.forEach(function(me){var Le,de;fe+=C;var ke=me.title;(Le=o.createFigure({name:"text",attrs:{x:we,y:fe,text:ke.text},styles:{color:ke.color,size:T,family:E,weight:I}}))===null||Le===void 0||Le.draw(e);var ze=me.value;(de=o.createFigure({name:"text",attrs:{x:Ft+q-tt-k-B,y:fe,text:ze.text,align:"right"},styles:{color:ze.color,size:T,family:E,weight:I}}))===null||de===void 0||de.draw(e),fe+=T+y}),i){var xr=Ft+tt+N+_t;et.forEach(function(me){me.forEach(function(Le){var de,ke;fe+=Ht;var ze=Le.title,fr=Le.value;(de=o.createFigure({name:"text",attrs:{x:xr,y:fe,text:ze.text},styles:{color:ze.color,size:Dt,family:qt,weight:Xt}}))===null||de===void 0||de.draw(e),(ke=o.createFigure({name:"text",attrs:{x:Ft+q-tt-Lt-B,y:fe,text:fr.text,align:"right"},styles:{color:fr.color,size:Dt,family:qt,weight:Xt}}))===null||ke===void 0||ke.draw(e),fe+=Dt+jt})})}}}},r.prototype._getCandleTooltipLegends=function(){var e,a,i,n,o,s,l,c,u=this.getWidget().getPane().getChart().getChartStore(),d=u.getStyles().candle,v=u.getDataList(),h=u.getInnerFormatter(),f=u.getDecimalFold(),p=u.getThousandsSeparator(),g=u.getLocale(),m=(e=u.getSymbol())!==null&&e!==void 0?e:{},b=m.pricePrecision,x=b===void 0?2:b,L=m.volumePrecision,_=L===void 0?0:L,M=u.getPeriod(),w=(a=u.getCrosshair().dataIndex)!==null&&a!==void 0?a:0,k=d.tooltip,C=k.legend,y=C.color,T=C.defaultValue,I=C.custom,E=(i=v[w-1])!==null&&i!==void 0?i:null,A=v[w],F=(n=E==null?void 0:E.close)!==null&&n!==void 0?n:A.close,N=A.close-F,B=U(U({},A),{time:h.formatDate(A.timestamp,To[(o=M==null?void 0:M.type)!==null&&o!==void 0?o:"day"],"tooltip"),open:f.format(p.format(xe(A.open,x))),high:f.format(p.format(xe(A.high,x))),low:f.format(p.format(xe(A.low,x))),close:f.format(p.format(xe(A.close,x))),volume:f.format(p.format(h.formatBigNumber(xe((s=A.volume)!==null&&s!==void 0?s:T,_)))),turnover:f.format(p.format(xe((l=A.turnover)!==null&&l!==void 0?l:T,x))),change:F===0?T:"".concat(p.format(xe(N/F*100)),"%")}),Y=Me(I)?I({prev:E,current:A,next:(c=v[w+1])!==null&&c!==void 0?c:null},d):I;return Y.map(function(K){var $=K.title,ut=K.value,rt={text:"",color:y};We($)?rt=U({},$):rt.text=$,rt.text=tn(rt.text,g);var z={text:T,color:y};return We(ut)?z=U({},ut):z.text=ut,P(/{change}/.exec(z.text))&&(z.color=N===0?d.priceMark.last.noChangeColor:N>0?d.priceMark.last.upColor:d.priceMark.last.downColor),z.text=$i(z.text,B),{title:rt,value:z}})},r}(ko),t0=function(t){St(r,t);function r(e){var a=t.call(this,e)||this;return a._activeFeatureInfo=null,a._featureClickEvent=function(i){return function(){var n=a.getWidget().getPane();return n.getChart().getChartStore().executeAction("onCrosshairFeatureClick",i),!0}},a._featureMouseMoveEvent=function(i){return function(){return a._activeFeatureInfo=i,a.getWidget().setForceCursor("pointer"),!0}},a.registerEvent("mouseMoveEvent",function(i){return a._activeFeatureInfo=null,a.getWidget().setForceCursor(null),!1}),a}return r.prototype.drawImp=function(e){var a=this,i,n,o=this.getWidget(),s=o.getPane(),l=o.getPane().getChart().getChartStore(),c=l.getCrosshair(),u=this.getWidget(),d=u.getPane().getAxisComponent();if(Gt(c.paneId)&&c.paneId===s.getId()&&d.isInCandle()){var v=l.getStyles().crosshair,h=v.horizontal.features;if(v.show&&v.horizontal.show&&h.length>0){var f=d.position==="right",p=u.getBounding(),g=0,m=v.horizontal.text;if(d.inside&&m.show){var b=d.convertFromPixel(c.y),x=d.getRange(),L=d.displayValueToText(d.realValueToDisplayValue(d.valueToRealValue(b,{range:x}),{range:x}),(n=(i=l.getSymbol())===null||i===void 0?void 0:i.pricePrecision)!==null&&n!==void 0?n:2);L=l.getDecimalFold().format(l.getThousandsSeparator().format(L)),g=m.paddingLeft+Ye(L,m.size,m.weight,m.family)+m.paddingRight}var _=g;f&&(_=p.width-g);var M=c.y;h.forEach(function(w){var k,C,y,T,I=w.marginLeft,E=I===void 0?0:I,A=w.marginTop,F=A===void 0?0:A,N=w.marginRight,B=N===void 0?0:N,Y=w.paddingLeft,K=Y===void 0?0:Y,$=w.paddingTop,ut=$===void 0?0:$,rt=w.paddingRight,z=rt===void 0?0:rt,tt=w.paddingBottom,mt=tt===void 0?0:tt,ft=w.color,yt=w.activeColor,V=w.backgroundColor,q=w.activeBackgroundColor,dt=w.borderRadius,J=w.size,_t=J===void 0?0:J,Lt=w.type,Ht=w.content,jt=_t;if(Lt==="icon_font"){var Dt=Ht;jt=K+Ye(Dt.code,_t,"normal",Dt.family)+z}f?_-=jt+B:_+=E;var Xt=ft,qt=V;((k=a._activeFeatureInfo)===null||k===void 0?void 0:k.feature.id)===w.id&&(Xt=yt??ft,qt=q??V);var et={mouseClickEvent:a._featureClickEvent({crosshair:c,feature:w}),mouseMoveEvent:a._featureMouseMoveEvent({crosshair:c,feature:w})};if(Lt==="icon_font"){var Dt=Ht;(C=a.createFigure({name:"text",attrs:{text:Dt.code,x:_,y:M+F,baseline:"middle"},styles:{paddingLeft:K,paddingTop:ut,paddingRight:z,paddingBottom:mt,borderRadius:dt,size:_t,family:Dt.family,color:Xt,backgroundColor:qt}},et))===null||C===void 0||C.draw(e)}else{(y=a.createFigure({name:"rect",attrs:{x:_,y:M+F-_t/2,width:_t,height:_t},styles:{paddingLeft:K,paddingTop:ut,paddingRight:z,paddingBottom:mt,color:qt}},et))===null||y===void 0||y.draw(e);var nt=Ht;(T=a.createFigure({name:"path",attrs:{path:nt.path,x:_,y:M+F+ut-_t/2,width:_t,height:_t},styles:{style:nt.style,lineWidth:nt.lineWidth,color:Xt}}))===null||T===void 0||T.draw(e)}f?_-=E:_+=jt+B})}}},r}(Ve),e0=function(t){St(r,t);function r(e,a){var i=t.call(this,e,a)||this;return i._candleBarView=new Lo(i),i._candleAreaView=new H1(i),i._candleHighLowPriceView=new j1(i),i._candleLastPriceLineView=new X1(i),i._crosshairFeatureView=new t0(i),i._orderFlowView=new K1(i),i.addChild(i._candleBarView),i.addChild(i._crosshairFeatureView),i.addChild(i._orderFlowView),i}return r.prototype.updateMainContent=function(e){var a=this.getPane().getChart().getStyles().candle;a.type!=="area"?(this._candleBarView.draw(e),this._candleHighLowPriceView.draw(e),this._candleAreaView.stopAnimation()):this._candleAreaView.draw(e),this._candleLastPriceLineView.draw(e),this._orderFlowView.draw(e)},r.prototype.updateOverlayContent=function(e){this._crosshairFeatureView.draw(e)},r.prototype.createTooltipView=function(){return new J1(this)},r.prototype.setOrderFlowData=function(e){this._orderFlowView.setOrderFlowData(e)},r.prototype.getOrderFlowData=function(){return this._orderFlowView.getOrderFlowData()},r.prototype.getOrderFlowView=function(){return this._orderFlowView},r.prototype.getOrderFlowDrawingBounds=function(){return this._orderFlowView.getOrderFlowDrawingBounds()},r}(Mo),Eo=function(t){St(r,t);function r(){return t!==null&&t.apply(this,arguments)||this}return r.prototype.drawImp=function(e,a){var i=this,n,o,s=this.getWidget(),l=s.getPane(),c=s.getBounding(),u=l.getAxisComponent(),d=this.getAxisStyles(l.getChart().getStyles());if(d.show&&(d.axisLine.show&&((n=this.createFigure({name:"line",attrs:this.createAxisLine(c,d),styles:d.axisLine}))===null||n===void 0||n.draw(e)),!a[0])){var v=u.getTicks();if(d.tickLine.show){var h=this.createTickLines(v,c,d);h.forEach(function(p){var g;(g=i.createFigure({name:"line",attrs:p,styles:d.tickLine}))===null||g===void 0||g.draw(e)})}if(d.tickText.show){var f=this.createTickTexts(v,c,d);(o=this.createFigure({name:"text",attrs:f,styles:d.tickText}))===null||o===void 0||o.draw(e)}}},r}(Ve),r0=function(t){St(r,t);function r(){return t!==null&&t.apply(this,arguments)||this}return r.prototype.getAxisStyles=function(e){return e.yAxis},r.prototype.createAxisLine=function(e,a){var i=this.getWidget().getPane().getAxisComponent(),n=a.axisLine.size,o=0;return i.isFromZero()?o=0:o=e.width-n,{coordinates:[{x:o,y:0},{x:o,y:e.height}]}},r.prototype.createTickLines=function(e,a,i){var n=this.getWidget().getPane().getAxisComponent(),o=i.axisLine,s=i.tickLine,l=0,c=0;return n.isFromZero()?(l=0,o.show&&(l+=o.size),c=l+s.length):(l=a.width,o.show&&(l-=o.size),c=l-s.length),e.map(function(u){return{coordinates:[{x:l,y:u.coord},{x:c,y:u.coord}]}})},r.prototype.createTickTexts=function(e,a,i){var n=this.getWidget().getPane().getAxisComponent(),o=i.axisLine,s=i.tickLine,l=i.tickText,c=0;n.isFromZero()?(c=l.marginStart,o.show&&(c+=o.size),s.show&&(c+=s.length)):(c=a.width-l.marginEnd,o.show&&(c-=o.size),s.show&&(c-=s.length));var u=this.getWidget().getPane().getAxisComponent().isFromZero()?"left":"right";return e.map(function(d){return{x:c,y:d.coord,text:d.text,align:u,baseline:"middle"}})},r}(Eo),a0=function(t){St(r,t);function r(){return t!==null&&t.apply(this,arguments)||this}return r.prototype.drawImp=function(e){var a=this,i,n,o,s,l=this.getWidget(),c=l.getPane(),u=l.getBounding(),d=c.getChart().getChartStore(),v=d.getStyles().candle.priceMark,h=v.last,f=h.text;if(v.show&&h.show&&f.show){var p=(n=(i=d.getSymbol())===null||i===void 0?void 0:i.pricePrecision)!==null&&n!==void 0?n:2,g=c.getAxisComponent(),m=d.getDataList(),b=m[m.length-1];if(P(b)){var x=b.close,L=b.open,_=h.compareRule==="current_open"?L:(s=(o=m[m.length-2])===null||o===void 0?void 0:o.close)!==null&&s!==void 0?s:x,M=g.convertToNicePixel(x),w="";x>_?w=h.upColor:x<_?w=h.downColor:w=h.noChangeColor;var k=0,C="left";g.isFromZero()?(k=0,C="left"):(k=u.width,C="right");var y=[],T=g.getRange(),I=g.displayValueToText(g.realValueToDisplayValue(g.valueToRealValue(x,{range:T}),{range:T}),p);I=d.getDecimalFold().format(d.getThousandsSeparator().format(I));var E=f.paddingLeft,A=f.paddingRight,F=f.paddingTop,N=f.paddingBottom,B=f.size,Y=f.family,K=f.weight,$=E+Ye(I,B,K,Y)+A,ut=F+B+N;y.push({name:"text",attrs:{x:k,y:M,width:$,height:ut,text:I,align:C,baseline:"middle"},styles:U(U({},f),{backgroundColor:w})});var rt=d.getInnerFormatter().formatExtendText,z=B/2,tt=M-z-F,mt=M+z+N;h.extendTexts.forEach(function(ft,yt){var V=rt({type:"last_price",data:b,index:yt});if(V.length>0&&ft.show){var q=ft.size/2,dt=0;ft.position==="above_price"?(tt-=ft.paddingBottom+q,dt=tt,tt-=q+ft.paddingTop):(mt+=ft.paddingTop+q,dt=mt,mt+=q+ft.paddingBottom),$=Math.max($,ft.paddingLeft+Ye(V,ft.size,ft.weight,ft.family)+ft.paddingRight),y.push({name:"text",attrs:{x:k,y:dt,width:$,height:ft.paddingTop+ft.size+ft.paddingBottom,text:V,align:C,baseline:"middle"},styles:U(U({},ft),{backgroundColor:w})})}}),y.forEach(function(ft){var yt;ft.attrs.width=$,(yt=a.createFigure(ft))===null||yt===void 0||yt.draw(e)})}}},r}(Ve),i0=function(t){St(r,t);function r(){return t!==null&&t.apply(this,arguments)||this}return r.prototype.drawImp=function(e){var a=this,i=this.getWidget(),n=i.getPane(),o=i.getBounding(),s=n.getChart().getChartStore(),l=s.getStyles().indicator,c=l.lastValueMark,u=c.text;if(c.show){var d=n.getAxisComponent(),v=d.getRange(),h=s.getDataList(),f=h.length-1,p=s.getIndicatorsByPaneId(n.getId()),g=s.getInnerFormatter(),m=s.getDecimalFold(),b=s.getThousandsSeparator();p.forEach(function(x){var L,_,M=x.result,w=(_=(L=M[f])!==null&&L!==void 0?L:M[f-1])!==null&&_!==void 0?_:{};if(P(w)&&x.visible){var k=x.precision;hi(x,f,l,function(C,y){var T,I=w[C.key];if(vt(I)){var E=d.convertToNicePixel(I),A=d.displayValueToText(d.realValueToDisplayValue(d.valueToRealValue(I,{range:v}),{range:v}),k);x.shouldFormatBigNumber&&(A=g.formatBigNumber(A)),A=m.format(b.format(A));var F=0,N="left";d.isFromZero()?(F=0,N="left"):(F=o.width,N="right"),(T=a.createFigure({name:"text",attrs:{x:F,y:E,text:A,align:N,baseline:"middle"},styles:U(U({},u),{backgroundColor:y.color})}))===null||T===void 0||T.draw(e)}})}})}},r}(Ve),Io=function(t){St(r,t);function r(){return t!==null&&t.apply(this,arguments)||this}return r.prototype.coordinateToPointTimestampDataIndexFlag=function(){return!1},r.prototype.drawDefaultFigures=function(e,a,i){this.drawFigures(e,a,this.getDefaultFigures(a,i))},r.prototype.getDefaultFigures=function(e,a){var i,n=this.getWidget(),o=n.getPane(),s=o.getChart().getChartStore(),l=s.getClickOverlayInfo(),c=[];if(e.needDefaultYAxisFigure&&e.id===((i=l.overlay)===null||i===void 0?void 0:i.id)&&l.paneId===o.getId()){var u=o.getAxisComponent(),d=n.getBounding(),v=Number.MAX_SAFE_INTEGER,h=Number.MIN_SAFE_INTEGER,f=u.isFromZero(),p="left",g=0;f?(p="left",g=0):(p="right",g=d.width);var m=s.getDecimalFold(),b=s.getThousandsSeparator();a.forEach(function(x,L){var _,M,w=e.points[L];if(vt(w.value)){v=Math.min(v,x.y),h=Math.max(h,x.y);var k=m.format(b.format(xe(w.value,(M=(_=s.getSymbol())===null||_===void 0?void 0:_.pricePrecision)!==null&&M!==void 0?M:2)));c.push({type:"text",attrs:{x:g,y:x.y,text:k,align:p,baseline:"middle"},ignoreEvent:!0})}}),a.length>1&&c.unshift({type:"rect",attrs:{x:0,y:v,width:d.width,height:h-v},ignoreEvent:!0})}return c},r.prototype.getFigures=function(e,a){var i,n,o=this.getWidget(),s=o.getPane(),l=s.getChart(),c=s.getAxisComponent(),u=l.getXAxisPane().getAxisComponent(),d=o.getBounding();return(n=(i=e.createYAxisFigures)===null||i===void 0?void 0:i.call(e,{chart:l,overlay:e,coordinates:a,bounding:d,xAxis:u,yAxis:c}))!==null&&n!==void 0?n:[]},r}(So),Ao=function(t){St(r,t);function r(){return t!==null&&t.apply(this,arguments)||this}return r.prototype.drawImp=function(e){var a,i=this.getWidget(),n=i.getPane(),o=i.getPane().getChart().getChartStore(),s=o.getCrosshair();if(Gt(s.paneId)&&this.compare(s,n.getId())){var l=o.getStyles().crosshair;if(l.show){var c=this.getDirectionStyles(l),u=c.text;if(c.show&&u.show){var d=i.getBounding(),v=n.getAxisComponent(),h=this.getText(s,o,v);e.font=_r(u.size,u.weight,u.family),(a=this.createFigure({name:"text",attrs:this.getTextAttrs(h,e.measureText(h).width,s,d,v,u),styles:u}))===null||a===void 0||a.draw(e)}}}},r.prototype.compare=function(e,a){return e.paneId===a},r.prototype.getDirectionStyles=function(e){return e.horizontal},r.prototype.getText=function(e,a,i){var n,o,s=i,l=i.convertFromPixel(e.y),c=0,u=!1;if(s.isInCandle())c=(o=(n=a.getSymbol())===null||n===void 0?void 0:n.pricePrecision)!==null&&o!==void 0?o:2;else{var d=a.getIndicatorsByPaneId(e.paneId);d.forEach(function(f){c=Math.max(f.precision,c),u||(u=f.shouldFormatBigNumber)})}var v=s.getRange(),h=s.displayValueToText(s.realValueToDisplayValue(s.valueToRealValue(l,{range:v}),{range:v}),c);return u&&(h=a.getInnerFormatter().formatBigNumber(h)),a.getDecimalFold().format(a.getThousandsSeparator().format(h))},r.prototype.getTextAttrs=function(e,a,i,n,o,s){var l=o,c=0,u="left";return l.isFromZero()?(c=0,u="left"):(c=n.width,u="right"),{x:c,y:i.y,text:e,align:u,baseline:"middle"}},r}(Ve),n0=function(t){St(r,t);function r(e,a){var i=t.call(this,e,a)||this;return i._yAxisView=new r0(i),i._candleLastPriceLabelView=new a0(i),i._indicatorLastValueView=new i0(i),i._overlayYAxisView=new Io(i),i._crosshairHorizontalLabelView=new Ao(i),i.setCursor("ns-resize"),i.addChild(i._overlayYAxisView),i}return r.prototype.getName=function(){return Vt.Y_AXIS},r.prototype.updateMain=function(e){var a=this.getPane().getOptions().state==="minimize";this._yAxisView.draw(e,a),a||(this.getPane().getAxisComponent().isInCandle()&&this._candleLastPriceLabelView.draw(e),this._indicatorLastValueView.draw(e))},r.prototype.updateOverlay=function(e){this.getPane().getOptions().state!=="minimize"&&(this._overlayYAxisView.draw(e),this._crosshairHorizontalLabelView.draw(e))},r}(mi);function en(){return{from:0,to:0,range:0,realFrom:0,realTo:0,realRange:0,displayFrom:0,displayTo:0,displayRange:0}}var Do=function(){function t(r){this.scrollZoomEnabled=!0,this._range=en(),this._prevRange=en(),this._ticks=[],this._autoCalcTickFlag=!0,this._parent=r}return t.prototype.getParent=function(){return this._parent},t.prototype.buildTicks=function(r){return this._autoCalcTickFlag&&(this._range=this.createRangeImp()),this._prevRange.from!==this._range.from||this._prevRange.to!==this._range.to||r?(this._prevRange=this._range,this._ticks=this.createTicksImp(),!0):!1},t.prototype.getTicks=function(){return this._ticks},t.prototype.setRange=function(r){this._autoCalcTickFlag=!1,this._range=r},t.prototype.getRange=function(){return this._range},t.prototype.setAutoCalcTickFlag=function(r){this._autoCalcTickFlag=r},t.prototype.getAutoCalcTickFlag=function(){return this._autoCalcTickFlag},t}(),ra=8,ha=function(t){St(r,t);function r(e,a){var i=t.call(this,e)||this;return i.reverse=!1,i.inside=!1,i.position="right",i.gap={top:.2,bottom:.1},i.createRange=function(n){return n.defaultRange},i.minSpan=function(n){return yr(-n)},i.valueToRealValue=function(n){return n},i.realValueToDisplayValue=function(n){return n},i.displayValueToRealValue=function(n){return n},i.realValueToValue=function(n){return n},i.displayValueToText=function(n,o){return xe(n,o)},i.override(a),i}return r.prototype.override=function(e){var a=e.name,i=e.gap,n=ga(e,["name","gap"]);Gt(this.name)||(this.name=a),he(this.gap,i),he(this,n)},r.prototype.createRangeImp=function(){var e,a,i=this.getParent(),n=i.getChart(),o=n.getChartStore(),s=i.getId(),l=Number.MAX_SAFE_INTEGER,c=Number.MIN_SAFE_INTEGER,u=!1,d=Number.MAX_SAFE_INTEGER,v=Number.MIN_SAFE_INTEGER,h=Number.MAX_SAFE_INTEGER,f=o.getIndicatorsByPaneId(s);f.forEach(function(Dt){u||(u=Dt.shouldOhlc),h=Math.min(h,Dt.precision),vt(Dt.minValue)&&(d=Math.min(d,Dt.minValue)),vt(Dt.maxValue)&&(v=Math.max(v,Dt.maxValue))});var p=4,g=this.isInCandle();if(g){var m=(a=(e=o.getSymbol())===null||e===void 0?void 0:e.pricePrecision)!==null&&a!==void 0?a:2;h!==Number.MAX_SAFE_INTEGER?p=Math.min(h,m):p=m}else h!==Number.MAX_SAFE_INTEGER&&(p=h);var b=o.getVisibleRangeDataList(),x=n.getStyles().candle,L=x.type==="area",_=x.area.value,M=g&&!L||!g&&u,w=g&&!o.isCandleVisible();if(w){var k=this.getParent();if(k.getId()===wt.CANDLE){var C=k.getMainWidget();if(P(C)&&typeof C.getOrderFlowData=="function"){var y=C.getOrderFlowData(),T=Number.MAX_SAFE_INTEGER,I=Number.MIN_SAFE_INTEGER,E=!1;if(b.forEach(function(Dt){var Xt,qt,et=Dt.data.current;if(P(et)){var nt=y.find(function(fe){return fe.timestamp===et.timestamp});if(P(nt)){E=!0;var Pt=et.high,Et=et.low,Yt=Pt-Et,ie=(qt=(Xt=nt.priceLevels)===null||Xt===void 0?void 0:Xt.filter(function(fe){return fe.totalVolume>0}))!==null&&qt!==void 0?qt:[],ne=50,ee=ie.slice(0,ne);if(Array.isArray(ee)&&ee.length>0){var Ft=Math.max(ee.length,3),oe=Math.max(1.5,Ft/10),le=Yt*oe,Ut=(le-Yt)/2,Qe=Pt+Ut,we=Et-Ut;T=Math.min(T,we),I=Math.max(I,Qe)}}}}),E&&T<Number.MAX_SAFE_INTEGER&&I>Number.MIN_SAFE_INTEGER){var A=I-T,F=A*.2;l=Math.min(l,T-F),c=Math.max(c,I+F)}}}}b.forEach(function(Dt){var Xt=Dt.data.current;if(P(Xt)&&(M&&(l=Math.min(l,Xt.low),c=Math.max(c,Xt.high)),g&&L)){var qt=Xt[_];vt(qt)&&(l=Math.min(l,qt),c=Math.max(c,qt))}}),b.forEach(function(Dt){var Xt=Dt.dataIndex;f.forEach(function(qt){var et,nt=qt.result,Pt=qt.figures,Et=(et=nt[Xt])!==null&&et!==void 0?et:{};Pt.forEach(function(Yt){var ie=Et[Yt.key];vt(ie)&&(l=Math.min(l,ie),c=Math.max(c,ie))})})}),l!==Number.MAX_SAFE_INTEGER&&c!==Number.MIN_SAFE_INTEGER?(l=Math.min(d,l),c=Math.max(v,c)):(l=0,c=10);var N=c-l,B={from:l,to:c,range:N,realFrom:l,realTo:c,realRange:N,displayFrom:l,displayTo:c,displayRange:N},Y=this.createRange({chart:n,paneId:s,defaultRange:B}),K=Y.realFrom,$=Y.realTo,ut=Y.realRange,rt=this.minSpan(p);if(K===$||ut<rt){var z=d===K,tt=v===$,mt=ra/2;K=z?K:tt?K-ra*rt:K-mt*rt,$=tt?$:z?$+ra*rt:$+mt*rt}var ft=this.getBounding().height,yt=this.gap,V=yt.top,q=yt.bottom,dt=V;dt>=1&&(dt=dt/ft);var J=q;J>=1&&(J=J/ft),ut=$-K,K=K-ut*J,$=$+ut*dt;var _t=this.realValueToValue(K,{range:Y}),Lt=this.realValueToValue($,{range:Y}),Ht=this.realValueToDisplayValue(K,{range:Y}),jt=this.realValueToDisplayValue($,{range:Y});return{from:_t,to:Lt,range:Lt-_t,realFrom:K,realTo:$,realRange:$-K,displayFrom:Ht,displayTo:jt,displayRange:jt-Ht}},r.prototype.isInCandle=function(){return this.getParent().getId()===wt.CANDLE},r.prototype.isFromZero=function(){return this.position==="left"&&this.inside||this.position==="right"&&!this.inside},r.prototype.createTicksImp=function(){var e=this,a,i,n,o,s=this.getRange(),l=s.displayFrom,c=s.displayTo,u=s.displayRange,d=[];if(u>=0){var v=sl(u/ra),h=ll(v),f=Hi(Math.ceil(l/v)*v,h),p=Hi(Math.floor(c/v)*v,h),g=0,m=f;if(v!==0)for(;m<=p;){var b=m.toFixed(h);d[g]={text:b,coord:0,value:b},++g,m+=v}}var x=this.getParent(),L=(i=(a=x.getYAxisWidget())===null||a===void 0?void 0:a.getBounding().height)!==null&&i!==void 0?i:0,_=x.getChart().getChartStore(),M=[],w=_.getIndicatorsByPaneId(x.getId()),k=_.getStyles(),C=0,y=!1;this.isInCandle()?C=(o=(n=_.getSymbol())===null||n===void 0?void 0:n.pricePrecision)!==null&&o!==void 0?o:2:w.forEach(function(N){C=Math.max(C,N.precision),y||(y=N.shouldFormatBigNumber)});var T=_.getInnerFormatter(),I=_.getThousandsSeparator(),E=_.getDecimalFold(),A=k.xAxis.tickText.size,F=NaN;return d.forEach(function(N){var B=N.value,Y=e.displayValueToText(+B,C),K=e.convertToPixel(e.realValueToValue(e.displayValueToRealValue(+B,{range:s}),{range:s}));y&&(Y=T.formatBigNumber(B)),Y=E.format(I.format(Y));var $=vt(F);K>A&&K<L-A&&($&&Math.abs(F-K)>A*2||!$)&&(M.push({text:Y,coord:K,value:B}),F=K)}),Me(this.createTicks)?this.createTicks({range:this.getRange(),bounding:this.getBounding(),defaultTicks:M}):M},r.prototype.getAutoSize=function(){var e,a,i=this.getParent(),n=i.getChart(),o=n.getChartStore(),s=o.getStyles(),l=s.yAxis,c=l.size;if(c!=="auto")return c;var u=0;if(l.show&&(l.axisLine.show&&(u+=l.axisLine.size),l.tickLine.show&&(u+=l.tickLine.length),l.tickText.show)){var d=0;this.getTicks().forEach(function(K){d=Math.max(d,Ye(K.text,l.tickText.size,l.tickText.weight,l.tickText.family))}),u+=l.tickText.marginStart+l.tickText.marginEnd+d}var v=s.candle.priceMark,h=v.show&&v.last.show&&v.last.text.show,f=0,p=s.crosshair,g=p.show&&p.horizontal.show&&p.horizontal.text.show,m=0;if(h||g){var b=(a=(e=o.getSymbol())===null||e===void 0?void 0:e.pricePrecision)!==null&&a!==void 0?a:2,x=this.getRange().displayTo;if(h){var L=o.getDataList(),_=L[L.length-1];if(P(_)){var M=v.last.text,w=M.paddingLeft,k=M.paddingRight,C=M.size,y=M.family,T=M.weight;f=w+Ye(xe(_.close,b),C,T,y)+k;var I=o.getInnerFormatter().formatExtendText;v.last.extendTexts.forEach(function(K,$){var ut=I({type:"last_price",data:_,index:$});ut.length>0&&K.show&&(f=Math.max(f,K.paddingLeft+Ye(ut,K.size,K.weight,K.family)+K.paddingRight))})}}if(g){var E=o.getIndicatorsByPaneId(i.getId()),A=0,F=!1;E.forEach(function(K){A=Math.max(K.precision,A),F||(F=K.shouldFormatBigNumber)});var N=2;if(this.isInCandle()){var B=s.indicator.lastValueMark;B.show&&B.text.show?N=Math.max(A,b):N=b}else N=A;var Y=xe(x,N);F&&(Y=o.getInnerFormatter().formatBigNumber(Y)),Y=o.getDecimalFold().format(Y),m+=p.horizontal.text.paddingLeft+p.horizontal.text.paddingRight+p.horizontal.text.borderSize*2+Ye(Y,p.horizontal.text.size,p.horizontal.text.weight,p.horizontal.text.family)}}return Math.max(u,f,m)},r.prototype.getBounding=function(){return this.getParent().getYAxisWidget().getBounding()},r.prototype.convertFromPixel=function(e){var a=this.getBounding().height,i=this.getRange(),n=i.realFrom,o=i.realRange,s=this.reverse?e/a:1-e/a,l=s*o+n;return this.realValueToValue(l,{range:i})},r.prototype.convertToPixel=function(e){var a,i,n=this.getRange(),o=this.valueToRealValue(e,{range:n}),s=(i=(a=this.getParent().getYAxisWidget())===null||a===void 0?void 0:a.getBounding().height)!==null&&i!==void 0?i:0,l=n.realFrom,c=n.realRange,u=(o-l)/c;return this.reverse?Math.round(u*s):Math.round((1-u)*s)},r.prototype.convertToNicePixel=function(e){var a,i,n=(i=(a=this.getParent().getYAxisWidget())===null||a===void 0?void 0:a.getBounding().height)!==null&&i!==void 0?i:0,o=this.convertToPixel(e);return Math.round(Math.max(n*.05,Math.min(o,n*.98)))},r.extend=function(e){var a=function(i){St(n,i);function n(o){return i.call(this,o,e)||this}return n}(r);return a},r}(Do),o0={name:"normal"},s0={name:"percentage",minSpan:function(){return Math.pow(10,-2)},displayValueToText:function(t){return"".concat(xe(t,2),"%")},valueToRealValue:function(t,r){var e=r.range;return(t-e.from)/e.range*e.realRange+e.realFrom},realValueToValue:function(t,r){var e=r.range;return(t-e.realFrom)/e.realRange*e.range+e.from},createRange:function(t){var r=t.chart,e=t.defaultRange,a=r.getDataList(),i=r.getVisibleRange(),n=a[i.from];if(P(n)){var o=e.from,s=e.to,l=e.range,c=(e.from-n.close)/n.close*100,u=(e.to-n.close)/n.close*100,d=u-c;return{from:o,to:s,range:l,realFrom:c,realTo:u,realRange:d,displayFrom:c,displayTo:u,displayRange:d}}return e}},l0={name:"logarithm",minSpan:function(t){return .05*yr(-t)},valueToRealValue:function(t){return t<0?-qe(Math.abs(t)):qe(t)},realValueToDisplayValue:function(t){return t<0?-yr(Math.abs(t)):yr(t)},displayValueToRealValue:function(t){return t<0?-qe(Math.abs(t)):qe(t)},realValueToValue:function(t){return t<0?-yr(Math.abs(t)):yr(t)},createRange:function(t){var r=t.defaultRange,e=r.from,a=r.to,i=r.range,n=e<0?-qe(Math.abs(e)):qe(e),o=a<0?-qe(Math.abs(a)):qe(a);return{from:e,to:a,range:i,realFrom:n,realTo:o,realRange:o-n,displayFrom:e,displayTo:a,displayRange:i}}},rn={normal:ha.extend(o0),percentage:ha.extend(s0),logarithm:ha.extend(l0)};function c0(t){var r;return(r=rn[t])!==null&&r!==void 0?r:rn.normal}var Po=function(){function t(r,e){this._bounding=ma(),this._originalBounding=ma(),this._visible=!0,this._chart=r,this._id=e,this._container=nr("div",{width:"100%",margin:"0",padding:"0",position:"relative",overflow:"hidden",boxSizing:"border-box"})}return t.prototype.getContainer=function(){return this._container},t.prototype.setVisible=function(r){this._visible!==r&&(this._container.style.display=r?"block":"none",this._visible=r)},t.prototype.getVisible=function(){return this._visible},t.prototype.getId=function(){return this._id},t.prototype.getChart=function(){return this._chart},t.prototype.getBounding=function(){return this._bounding},t.prototype.setOriginalBounding=function(r){he(this._originalBounding,r)},t.prototype.getOriginalBounding=function(){return this._originalBounding},t.prototype.update=function(r){this._bounding.height!==this._container.clientHeight&&(this._container.style.height="".concat(this._bounding.height,"px")),this.updateImp(r??3,this._container,this._bounding)},t}(),Fo=function(t){St(r,t);function r(e,a,i){var n=t.call(this,e,a)||this;n._yAxisWidget=null,n._options={id:"",minHeight:da,dragEnabled:!0,order:0,height:go,state:"normal",axis:{name:"normal",scrollZoomEnabled:!0}};var o=n.getContainer();return n._mainWidget=n.createMainWidget(o),n._yAxisWidget=n.createYAxisWidget(o),n.setOptions(i),n}return r.prototype.setOptions=function(e){var a,i,n,o,s,l=this.getId();if(l===wt.CANDLE||l===wt.X_AXIS){var c=(a=e.axis)===null||a===void 0?void 0:a.name;(!P(this._axis)||P(c)&&this._options.axis.name!==c)&&(this._axis=this.createAxisComponent(c??"normal"))}else P(this._axis)||(this._axis=this.createAxisComponent("normal"));this._axis instanceof ha&&this._axis.setAutoCalcTickFlag(!0),he(this._options,e),this._axis.override(U(U({},this._options.axis),{name:(n=(i=e.axis)===null||i===void 0?void 0:i.name)!==null&&n!==void 0?n:"normal"}));var u=null,d="default";return this.getId()===wt.X_AXIS?(u=this.getMainWidget().getContainer(),d="ew-resize"):(u=this.getYAxisWidget().getContainer(),d="ns-resize"),!((s=(o=e.axis)===null||o===void 0?void 0:o.scrollZoomEnabled)!==null&&s!==void 0)||s?u.style.cursor=d:u.style.cursor="default",this},r.prototype.getOptions=function(){return this._options},r.prototype.getAxisComponent=function(){return this._axis},r.prototype.setBounding=function(e,a,i,n){var o,s,l,c;he(this.getBounding(),e);var u={};P(e.height)&&(u.height=e.height),P(e.top)&&(u.top=e.top),this._mainWidget.setBounding(u);var d=P(a);if(d&&this._mainWidget.setBounding(a),P(this._yAxisWidget)){this._yAxisWidget.setBounding(u);var v=this._axis;v.position==="left"?P(i)&&this._yAxisWidget.setBounding(U(U({},i),{left:0})):P(n)&&(this._yAxisWidget.setBounding(n),d&&this._yAxisWidget.setBounding({left:((o=a.left)!==null&&o!==void 0?o:0)+((s=a.width)!==null&&s!==void 0?s:0)+((l=a.right)!==null&&l!==void 0?l:0)-((c=n.width)!==null&&c!==void 0?c:0)}))}return this},r.prototype.getMainWidget=function(){return this._mainWidget},r.prototype.getYAxisWidget=function(){return this._yAxisWidget},r.prototype.updateImp=function(e){var a;this._mainWidget.update(e),(a=this._yAxisWidget)===null||a===void 0||a.update(e)},r.prototype.destroy=function(){var e;this._mainWidget.destroy(),(e=this._yAxisWidget)===null||e===void 0||e.destroy()},r.prototype.getImage=function(e){var a=this.getBounding(),i=a.width,n=a.height,o=nr("canvas",{width:"".concat(i,"px"),height:"".concat(n,"px"),boxSizing:"border-box"}),s=o.getContext("2d"),l=hr(o);o.width=i*l,o.height=n*l,s.scale(l,l);var c=this._mainWidget.getBounding();if(s.drawImage(this._mainWidget.getImage(e),c.left,0,c.width,c.height),this._yAxisWidget!==null){var u=this._yAxisWidget.getBounding();s.drawImage(this._yAxisWidget.getImage(e),u.left,0,u.width,u.height)}return o},r.prototype.createYAxisWidget=function(e){return null},r}(Po),Ro=function(t){St(r,t);function r(){return t!==null&&t.apply(this,arguments)||this}return r.prototype.createAxisComponent=function(e){var a=c0(e??"default");return new a(this)},r.prototype.createMainWidget=function(e){return new Mo(e,this)},r.prototype.createYAxisWidget=function(e){return new n0(e,this)},r}(Fo),u0=function(t){St(r,t);function r(){return t!==null&&t.apply(this,arguments)||this}return r.prototype.createMainWidget=function(e){return new e0(e,this)},r}(Ro),d0=function(t){St(r,t);function r(){return t!==null&&t.apply(this,arguments)||this}return r.prototype.getAxisStyles=function(e){return e.xAxis},r.prototype.createAxisLine=function(e){return{coordinates:[{x:0,y:0},{x:e.width,y:0}]}},r.prototype.createTickLines=function(e,a,i){var n=i.tickLine,o=i.axisLine.size;return e.map(function(s){return{coordinates:[{x:s.coord,y:0},{x:s.coord,y:o+n.length}]}})},r.prototype.createTickTexts=function(e,a,i){var n=i.tickText,o=i.axisLine.size,s=i.tickLine.length;return e.map(function(l){return{x:l.coord,y:o+s+n.marginStart,text:l.text,align:"center",baseline:"top"}})},r}(Eo),h0=function(t){St(r,t);function r(){return t!==null&&t.apply(this,arguments)||this}return r.prototype.coordinateToPointTimestampDataIndexFlag=function(){return!0},r.prototype.coordinateToPointValueFlag=function(){return!1},r.prototype.getCompleteOverlays=function(){return this.getWidget().getPane().getChart().getChartStore().getOverlaysByPaneId()},r.prototype.getProgressOverlay=function(){var e,a;return(a=(e=this.getWidget().getPane().getChart().getChartStore().getProgressOverlayInfo())===null||e===void 0?void 0:e.overlay)!==null&&a!==void 0?a:null},r.prototype.getDefaultFigures=function(e,a){var i,n=[],o=this.getWidget(),s=o.getPane(),l=s.getChart().getChartStore(),c=l.getClickOverlayInfo();if(e.needDefaultXAxisFigure&&e.id===((i=c.overlay)===null||i===void 0?void 0:i.id)){var u=Number.MAX_SAFE_INTEGER,d=Number.MIN_SAFE_INTEGER;a.forEach(function(v,h){u=Math.min(u,v.x),d=Math.max(d,v.x);var f=e.points[h];if(vt(f.timestamp)){var p=l.getInnerFormatter().formatDate(f.timestamp,"YYYY-MM-DD HH:mm","crosshair");n.push({type:"text",attrs:{x:v.x,y:0,text:p,align:"center"},ignoreEvent:!0})}}),a.length>1&&n.unshift({type:"rect",attrs:{x:u,y:0,width:d-u,height:o.getBounding().height},ignoreEvent:!0})}return n},r.prototype.getFigures=function(e,a){var i,n,o=this.getWidget(),s=o.getPane(),l=s.getChart(),c=s.getAxisComponent(),u=l.getXAxisPane().getAxisComponent(),d=o.getBounding();return(n=(i=e.createXAxisFigures)===null||i===void 0?void 0:i.call(e,{chart:l,overlay:e,coordinates:a,bounding:d,xAxis:u,yAxis:c}))!==null&&n!==void 0?n:[]},r}(Io),v0=function(t){St(r,t);function r(){return t!==null&&t.apply(this,arguments)||this}return r.prototype.compare=function(e){return P(e.timestamp)},r.prototype.getDirectionStyles=function(e){return e.vertical},r.prototype.getText=function(e,a){var i,n,o=e.timestamp;return a.getInnerFormatter().formatDate(o,To[(n=(i=a.getPeriod())===null||i===void 0?void 0:i.type)!==null&&n!==void 0?n:"day"],"crosshair")},r.prototype.getTextAttrs=function(e,a,i,n,o,s){var l=i.realX,c=0,u="center";return l-a/2-s.paddingLeft<0?(c=0,u="left"):l+a/2+s.paddingRight>n.width?(c=n.width,u="right"):c=l,{x:c,y:0,text:e,align:u,baseline:"top"}},r}(Ao),f0=function(t){St(r,t);function r(e,a){var i=t.call(this,e,a)||this;return i._xAxisView=new d0(i),i._overlayXAxisView=new h0(i),i._crosshairVerticalLabelView=new v0(i),i.setCursor("ew-resize"),i.addChild(i._overlayXAxisView),i}return r.prototype.getName=function(){return Vt.X_AXIS},r.prototype.updateMain=function(e){this._xAxisView.draw(e)},r.prototype.updateOverlay=function(e){this._overlayXAxisView.draw(e),this._crosshairVerticalLabelView.draw(e)},r}(mi),p0=function(t){St(r,t);function r(e,a){var i=t.call(this,e)||this;return i.override(a),i}return r.prototype.override=function(e){var a=e.name,i=e.scrollZoomEnabled,n=e.createTicks;Gt(this.name)||(this.name=a),this.scrollZoomEnabled=i??this.scrollZoomEnabled,this.createTicks=n??this.createTicks},r.prototype.createRangeImp=function(){var e=this.getParent().getChart().getChartStore(),a=e.getVisibleRange(),i=a.realFrom,n=a.realTo,o=i,s=n,l=n-i+1,c={from:o,to:s,range:l,realFrom:o,realTo:s,realRange:l,displayFrom:o,displayTo:s,displayRange:l};return c},r.prototype.createTicksImp=function(){var e,a=this.getRange(),i=a.realFrom,n=a.realTo,o=a.from,s=this.getParent().getChart().getChartStore(),l=s.getInnerFormatter().formatDate,c=s.getPeriod(),u=[],d=s.getBarSpace().bar,v=s.getStyles().xAxis.tickText,h=Math.max(Ye("YYYY-MM-DD HH:mm:ss",v.size,v.weight,v.family),this.getBounding().width/8),f=Math.ceil(h/d);f%2!==0&&(f+=1);for(var p=Math.floor(i/f)*f,g=p;g<n;g+=f)if(g>=o){var m=s.dataIndexToTimestamp(g);vt(m)&&u.push({coord:this.convertToPixel(g),value:m,text:l(m,G1[(e=c==null?void 0:c.type)!==null&&e!==void 0?e:"day"],"xAxis")})}return Me(this.createTicks)?this.createTicks({range:this.getRange(),bounding:this.getBounding(),defaultTicks:u}):u},r.prototype.getAutoSize=function(){var e=this.getParent().getChart().getStyles(),a=e.xAxis,i=a.size;if(i!=="auto")return i;var n=e.crosshair,o=0;a.show&&(a.axisLine.show&&(o+=a.axisLine.size),a.tickLine.show&&(o+=a.tickLine.length),a.tickText.show&&(o+=a.tickText.marginStart+a.tickText.marginEnd+a.tickText.size));var s=0;return n.show&&n.vertical.show&&n.vertical.text.show&&(s+=n.vertical.text.paddingTop+n.vertical.text.paddingBottom+n.vertical.text.borderSize*2+n.vertical.text.size),Math.max(o,s)},r.prototype.getBounding=function(){return this.getParent().getMainWidget().getBounding()},r.prototype.convertTimestampFromPixel=function(e){var a=this.getParent().getChart().getChartStore(),i=a.coordinateToDataIndex(e);return a.dataIndexToTimestamp(i)},r.prototype.convertTimestampToPixel=function(e){var a=this.getParent().getChart().getChartStore(),i=a.timestampToDataIndex(e);return a.dataIndexToCoordinate(i)},r.prototype.convertFromPixel=function(e){return this.getParent().getChart().getChartStore().coordinateToDataIndex(e)},r.prototype.convertToPixel=function(e){return this.getParent().getChart().getChartStore().dataIndexToCoordinate(e)},r.extend=function(e){var a=function(i){St(n,i);function n(o){return i.call(this,o,e)||this}return n}(r);return a},r}(Do),g0={name:"normal"},an={normal:p0.extend(g0)};function m0(t){var r;return(r=an[t])!==null&&r!==void 0?r:an.normal}var y0=function(t){St(r,t);function r(){return t!==null&&t.apply(this,arguments)||this}return r.prototype.createAxisComponent=function(e){var a=m0(e);return new a(this)},r.prototype.createMainWidget=function(e){return new f0(e,this)},r}(Fo);function _0(t,r){var e=0;return function(){var a=Date.now();a-e>r&&(t.apply(this,arguments),e=a)}}var C0=function(t){St(r,t);function r(e,a){var i=t.call(this,e,a)||this;return i._dragFlag=!1,i._dragStartY=0,i._topPaneHeight=0,i._bottomPaneHeight=0,i._topPane=null,i._bottomPane=null,i._pressedMouseMoveEvent=_0(i._pressedTouchMouseMoveEvent,20),i.registerEvent("touchStartEvent",i._mouseDownEvent.bind(i)).registerEvent("touchMoveEvent",i._pressedMouseMoveEvent.bind(i)).registerEvent("touchEndEvent",i._mouseUpEvent.bind(i)).registerEvent("mouseDownEvent",i._mouseDownEvent.bind(i)).registerEvent("mouseUpEvent",i._mouseUpEvent.bind(i)).registerEvent("pressedMouseMoveEvent",i._pressedMouseMoveEvent.bind(i)).registerEvent("mouseEnterEvent",i._mouseEnterEvent.bind(i)).registerEvent("mouseLeaveEvent",i._mouseLeaveEvent.bind(i)),i}return r.prototype.getName=function(){return Vt.SEPARATOR},r.prototype._mouseDownEvent=function(e){var a=this;this._dragFlag=!0,this._dragStartY=e.pageY;var i=this.getPane(),n=i.getChart();this._topPane=i.getTopPane(),this._bottomPane=i.getBottomPane();var o=n.getDrawPanes();if(this._topPane.getOptions().state==="minimize")for(var s=o.findIndex(function(d){var v;return d.getId()===((v=a._topPane)===null||v===void 0?void 0:v.getId())}),l=s-1;l>-1;l--){var c=o[l];if(c.getOptions().state!=="minimize"){this._topPane=c;break}}if(this._bottomPane.getOptions().state==="minimize")for(var s=o.findIndex(function(v){var h;return v.getId()===((h=a._bottomPane)===null||h===void 0?void 0:h.getId())}),l=s+1;l<o.length;l++){var u=o[l];if(u.getOptions().state!=="minimize"){this._bottomPane=u;break}}return this._topPaneHeight=this._topPane.getBounding().height,this._bottomPaneHeight=this._bottomPane.getBounding().height,!0},r.prototype._mouseUpEvent=function(){return this._dragFlag=!1,this._topPane=null,this._bottomPane=null,this._topPaneHeight=0,this._bottomPaneHeight=0,this._mouseLeaveEvent()},r.prototype._pressedTouchMouseMoveEvent=function(e){var a=e.pageY-this._dragStartY,i=a<0;if(P(this._topPane)&&P(this._bottomPane)){var n=this._bottomPane.getOptions();if(this._topPane.getOptions().state!=="minimize"&&n.state!=="minimize"&&n.dragEnabled){var o=null,s=null,l=0,c=0;i?(o=this._topPane,s=this._bottomPane,l=this._topPaneHeight,c=this._bottomPaneHeight):(o=this._bottomPane,s=this._topPane,l=this._bottomPaneHeight,c=this._topPaneHeight);var u=o.getOptions().minHeight;if(l>u){var d=Math.max(l-Math.abs(a),u),v=l-d;o.setBounding({height:d}),s.setBounding({height:c+v});var h=this.getPane(),f=h.getChart();f.getChartStore().executeAction("onPaneDrag",{paneId:h.getId()}),f.layout({measureHeight:!0,measureWidth:!0,update:!0,buildYAxisTick:!0,forceBuildYAxisTick:!0})}}}return!0},r.prototype._mouseEnterEvent=function(){var e=this.getPane(),a=e.getBottomPane();if(a.getOptions().dragEnabled){var i=e.getChart(),n=i.getStyles().separator;return this.getContainer().style.background=n.activeBackgroundColor,!0}return!1},r.prototype._mouseLeaveEvent=function(){return this._dragFlag?!1:(this.getContainer().style.background="transparent",!0)},r.prototype.createContainer=function(){return nr("div",{width:"100%",height:"".concat(Gr,"px"),margin:"0",padding:"0",position:"absolute",top:"-3px",zIndex:"20",boxSizing:"border-box",cursor:"ns-resize"})},r.prototype.updateImp=function(e,a,i){if(i===4||i===2){var n=this.getPane().getChart().getStyles().separator;e.style.top="".concat(-Math.floor((Gr-n.size)/2),"px"),e.style.height="".concat(Gr,"px")}},r}(mo),b0=function(t){St(r,t);function r(e,a,i,n){var o=t.call(this,e,a)||this;return o.getContainer().style.overflow="",o._topPane=i,o._bottomPane=n,o._separatorWidget=new C0(o.getContainer(),o),o}return r.prototype.setBounding=function(e){return he(this.getBounding(),e),this},r.prototype.getTopPane=function(){return this._topPane},r.prototype.setTopPane=function(e){return this._topPane=e,this},r.prototype.getBottomPane=function(){return this._bottomPane},r.prototype.setBottomPane=function(e){return this._bottomPane=e,this},r.prototype.getWidget=function(){return this._separatorWidget},r.prototype.getImage=function(e){var a=this.getBounding(),i=a.width,n=a.height,o=this.getChart().getStyles().separator,s=nr("canvas",{width:"".concat(i,"px"),height:"".concat(n,"px"),boxSizing:"border-box"}),l=s.getContext("2d"),c=hr(s);return s.width=i*c,s.height=n*c,l.scale(c,c),l.fillStyle=o.color,l.fillRect(0,0,i,n),s},r.prototype.updateImp=function(e,a,i){if(e===4||e===2){var n=this.getChart().getStyles().separator;a.style.backgroundColor=n.color,a.style.height="".concat(i.height,"px"),a.style.marginLeft="".concat(i.left,"px"),a.style.width="".concat(i.width,"px"),this._separatorWidget.update(e)}},r}(Po);function nn(){return typeof window>"u"?!1:window.navigator.userAgent.toLowerCase().includes("firefox")}function Za(){return typeof window>"u"?!1:/iPhone|iPad|iPod|iOS/.test(window.navigator.userAgent)}var aa={ResetClick:500,LongTap:500,PreventFiresTouchEvents:500},kr={CancelClick:5,CancelTap:5,DoubleClick:5,DoubleTap:30},Xr={Left:0,Middle:1,Right:2},x0=10,w0=function(){function t(r,e,a){var i=this;this._clickCount=0,this._clickTimeoutId=null,this._clickCoordinate={x:Number.NEGATIVE_INFINITY,y:Number.POSITIVE_INFINITY},this._tapCount=0,this._tapTimeoutId=null,this._tapCoordinate={x:Number.NEGATIVE_INFINITY,y:Number.POSITIVE_INFINITY},this._longTapTimeoutId=null,this._longTapActive=!1,this._mouseMoveStartCoordinate=null,this._touchMoveStartCoordinate=null,this._touchMoveExceededManhattanDistance=!1,this._cancelClick=!1,this._cancelTap=!1,this._unsubscribeOutsideMouseEvents=null,this._unsubscribeOutsideTouchEvents=null,this._unsubscribeMobileSafariEvents=null,this._unsubscribeMousemove=null,this._unsubscribeMouseWheel=null,this._unsubscribeContextMenu=null,this._unsubscribeRootMouseEvents=null,this._unsubscribeRootTouchEvents=null,this._startPinchMiddleCoordinate=null,this._startPinchDistance=0,this._pinchPrevented=!1,this._preventTouchDragProcess=!1,this._mousePressed=!1,this._lastTouchEventTimeStamp=0,this._activeTouchId=null,this._acceptMouseLeave=!Za(),this._onFirefoxOutsideMouseUp=function(n){i._mouseUpHandler(n)},this._onMobileSafariDoubleClick=function(n){if(i._firesTouchEvents(n)){if(++i._tapCount,i._tapTimeoutId!==null&&i._tapCount>1){var o=i._mouseTouchMoveWithDownInfo(i._getCoordinate(n),i._tapCoordinate).manhattanDistance;o<kr.DoubleTap&&!i._cancelTap&&i._processEvent(i._makeCompatEvent(n),i._handler.doubleTapEvent),i._resetTapTimeout()}}else if(++i._clickCount,i._clickTimeoutId!==null&&i._clickCount>1){var o=i._mouseTouchMoveWithDownInfo(i._getCoordinate(n),i._clickCoordinate).manhattanDistance;o<kr.DoubleClick&&!i._cancelClick&&i._processEvent(i._makeCompatEvent(n),i._handler.mouseDoubleClickEvent),i._resetClickTimeout()}},this._target=r,this._handler=e,this._options=a,this._init()}return t.prototype.destroy=function(){this._unsubscribeOutsideMouseEvents!==null&&(this._unsubscribeOutsideMouseEvents(),this._unsubscribeOutsideMouseEvents=null),this._unsubscribeOutsideTouchEvents!==null&&(this._unsubscribeOutsideTouchEvents(),this._unsubscribeOutsideTouchEvents=null),this._unsubscribeMousemove!==null&&(this._unsubscribeMousemove(),this._unsubscribeMousemove=null),this._unsubscribeMouseWheel!==null&&(this._unsubscribeMouseWheel(),this._unsubscribeMouseWheel=null),this._unsubscribeContextMenu!==null&&(this._unsubscribeContextMenu(),this._unsubscribeContextMenu=null),this._unsubscribeRootMouseEvents!==null&&(this._unsubscribeRootMouseEvents(),this._unsubscribeRootMouseEvents=null),this._unsubscribeRootTouchEvents!==null&&(this._unsubscribeRootTouchEvents(),this._unsubscribeRootTouchEvents=null),this._unsubscribeMobileSafariEvents!==null&&(this._unsubscribeMobileSafariEvents(),this._unsubscribeMobileSafariEvents=null),this._clearLongTapTimeout(),this._resetClickTimeout()},t.prototype._mouseEnterHandler=function(r){var e=this,a,i,n;(a=this._unsubscribeMousemove)===null||a===void 0||a.call(this),(i=this._unsubscribeMouseWheel)===null||i===void 0||i.call(this),(n=this._unsubscribeContextMenu)===null||n===void 0||n.call(this);var o=this._mouseMoveHandler.bind(this);this._unsubscribeMousemove=function(){e._target.removeEventListener("mousemove",o)},this._target.addEventListener("mousemove",o);var s=this._mouseWheelHandler.bind(this);this._unsubscribeMouseWheel=function(){e._target.removeEventListener("wheel",s)},this._target.addEventListener("wheel",s,{passive:!1});var l=this._contextMenuHandler.bind(this);this._unsubscribeContextMenu=function(){e._target.removeEventListener("contextmenu",l)},this._target.addEventListener("contextmenu",l,{passive:!1}),!this._firesTouchEvents(r)&&(this._processEvent(this._makeCompatEvent(r),this._handler.mouseEnterEvent),this._acceptMouseLeave=!0)},t.prototype._resetClickTimeout=function(){this._clickTimeoutId!==null&&clearTimeout(this._clickTimeoutId),this._clickCount=0,this._clickTimeoutId=null,this._clickCoordinate={x:Number.NEGATIVE_INFINITY,y:Number.POSITIVE_INFINITY}},t.prototype._resetTapTimeout=function(){this._tapTimeoutId!==null&&clearTimeout(this._tapTimeoutId),this._tapCount=0,this._tapTimeoutId=null,this._tapCoordinate={x:Number.NEGATIVE_INFINITY,y:Number.POSITIVE_INFINITY}},t.prototype._mouseMoveHandler=function(r){this._mousePressed||this._touchMoveStartCoordinate!==null||this._firesTouchEvents(r)||(this._processEvent(this._makeCompatEvent(r),this._handler.mouseMoveEvent),this._acceptMouseLeave=!0)},t.prototype._mouseWheelHandler=function(r){if(Math.abs(r.deltaX)>Math.abs(r.deltaY)){if(!P(this._handler.mouseWheelHortEvent)||(this._preventDefault(r),Math.abs(r.deltaX)===0))return;this._handler.mouseWheelHortEvent(this._makeCompatEvent(r),-r.deltaX)}else{if(!P(this._handler.mouseWheelVertEvent))return;var e=-(r.deltaY/100);if(e===0)return;switch(this._preventDefault(r),r.deltaMode){case r.DOM_DELTA_PAGE:{e*=120;break}case r.DOM_DELTA_LINE:{e*=32;break}}if(e!==0){var a=Math.sign(e)*Math.min(1,Math.abs(e));this._handler.mouseWheelVertEvent(this._makeCompatEvent(r),a)}}},t.prototype._contextMenuHandler=function(r){this._preventDefault(r)},t.prototype._touchMoveHandler=function(r){var e=this._touchWithId(r.changedTouches,this._activeTouchId);if(e!==null&&(this._lastTouchEventTimeStamp=this._eventTimeStamp(r),this._startPinchMiddleCoordinate===null&&!this._preventTouchDragProcess)){this._pinchPrevented=!0;var a=this._mouseTouchMoveWithDownInfo(this._getCoordinate(e),this._touchMoveStartCoordinate),i=a.xOffset,n=a.yOffset,o=a.manhattanDistance;if(!(!this._touchMoveExceededManhattanDistance&&o<kr.CancelTap)){if(!this._touchMoveExceededManhattanDistance){var s=i*.5,l=n>=s&&!this._options.treatVertDragAsPageScroll(),c=s>n&&!this._options.treatHorzDragAsPageScroll();!l&&!c&&(this._preventTouchDragProcess=!0),this._touchMoveExceededManhattanDistance=!0,this._cancelTap=!0,this._clearLongTapTimeout(),this._resetTapTimeout()}this._preventTouchDragProcess||this._processEvent(this._makeCompatEvent(r,e),this._handler.touchMoveEvent)}}},t.prototype._mouseMoveWithDownHandler=function(r){if(r.button===Xr.Left){var e=this._mouseTouchMoveWithDownInfo(this._getCoordinate(r),this._mouseMoveStartCoordinate),a=e.manhattanDistance;a>=kr.CancelClick&&(this._cancelClick=!0,this._resetClickTimeout()),this._cancelClick&&this._processEvent(this._makeCompatEvent(r),this._handler.pressedMouseMoveEvent)}},t.prototype._mouseTouchMoveWithDownInfo=function(r,e){var a=Math.abs(e.x-r.x),i=Math.abs(e.y-r.y),n=a+i;return{xOffset:a,yOffset:i,manhattanDistance:n}},t.prototype._touchEndHandler=function(r){var e=this._touchWithId(r.changedTouches,this._activeTouchId);if(e===null&&r.touches.length===0&&(e=r.changedTouches[0]),e!==null){this._activeTouchId=null,this._lastTouchEventTimeStamp=this._eventTimeStamp(r),this._clearLongTapTimeout(),this._touchMoveStartCoordinate=null,this._unsubscribeRootTouchEvents!==null&&(this._unsubscribeRootTouchEvents(),this._unsubscribeRootTouchEvents=null);var a=this._makeCompatEvent(r,e);if(this._processEvent(a,this._handler.touchEndEvent),++this._tapCount,this._tapTimeoutId!==null&&this._tapCount>1){var i=this._mouseTouchMoveWithDownInfo(this._getCoordinate(e),this._tapCoordinate).manhattanDistance;i<kr.DoubleTap&&!this._cancelTap&&this._processEvent(a,this._handler.doubleTapEvent),this._resetTapTimeout()}else this._cancelTap||(this._processEvent(a,this._handler.tapEvent),P(this._handler.tapEvent)&&this._preventDefault(r));this._tapCount===0&&this._preventDefault(r),r.touches.length===0&&this._longTapActive&&(this._longTapActive=!1,this._preventDefault(r))}},t.prototype._mouseUpHandler=function(r){if(r.button===Xr.Left){var e=this._makeCompatEvent(r);if(this._mouseMoveStartCoordinate=null,this._mousePressed=!1,this._unsubscribeRootMouseEvents!==null&&(this._unsubscribeRootMouseEvents(),this._unsubscribeRootMouseEvents=null),nn()){var a=this._target.ownerDocument.documentElement;a.removeEventListener("mouseleave",this._onFirefoxOutsideMouseUp)}if(!this._firesTouchEvents(r))if(this._processEvent(e,this._handler.mouseUpEvent),++this._clickCount,this._clickTimeoutId!==null&&this._clickCount>1){var i=this._mouseTouchMoveWithDownInfo(this._getCoordinate(r),this._clickCoordinate).manhattanDistance;i<kr.DoubleClick&&!this._cancelClick&&this._processEvent(e,this._handler.mouseDoubleClickEvent),this._resetClickTimeout()}else this._cancelClick||this._processEvent(e,this._handler.mouseClickEvent)}},t.prototype._clearLongTapTimeout=function(){this._longTapTimeoutId!==null&&(clearTimeout(this._longTapTimeoutId),this._longTapTimeoutId=null)},t.prototype._touchStartHandler=function(r){if(this._activeTouchId===null){var e=r.changedTouches[0];this._activeTouchId=e.identifier,this._lastTouchEventTimeStamp=this._eventTimeStamp(r);var a=this._target.ownerDocument.documentElement;this._cancelTap=!1,this._touchMoveExceededManhattanDistance=!1,this._preventTouchDragProcess=!1,this._touchMoveStartCoordinate=this._getCoordinate(e),this._unsubscribeRootTouchEvents!==null&&(this._unsubscribeRootTouchEvents(),this._unsubscribeRootTouchEvents=null);{var i=this._touchMoveHandler.bind(this),n=this._touchEndHandler.bind(this);this._unsubscribeRootTouchEvents=function(){a.removeEventListener("touchmove",i),a.removeEventListener("touchend",n)},a.addEventListener("touchmove",i,{passive:!1}),a.addEventListener("touchend",n,{passive:!1}),this._clearLongTapTimeout(),this._longTapTimeoutId=setTimeout(this._longTapHandler.bind(this,r),aa.LongTap)}this._processEvent(this._makeCompatEvent(r,e),this._handler.touchStartEvent),this._tapTimeoutId===null&&(this._tapCount=0,this._tapTimeoutId=setTimeout(this._resetTapTimeout.bind(this),aa.ResetClick),this._tapCoordinate=this._getCoordinate(e))}},t.prototype._mouseDownHandler=function(r){if(r.button===Xr.Right){this._preventDefault(r),this._processEvent(this._makeCompatEvent(r),this._handler.mouseRightClickEvent);return}if(r.button===Xr.Left){var e=this._target.ownerDocument.documentElement;nn()&&e.addEventListener("mouseleave",this._onFirefoxOutsideMouseUp),this._cancelClick=!1,this._mouseMoveStartCoordinate=this._getCoordinate(r),this._unsubscribeRootMouseEvents!==null&&(this._unsubscribeRootMouseEvents(),this._unsubscribeRootMouseEvents=null);{var a=this._mouseMoveWithDownHandler.bind(this),i=this._mouseUpHandler.bind(this);this._unsubscribeRootMouseEvents=function(){e.removeEventListener("mousemove",a),e.removeEventListener("mouseup",i)},e.addEventListener("mousemove",a),e.addEventListener("mouseup",i)}this._mousePressed=!0,!this._firesTouchEvents(r)&&(this._processEvent(this._makeCompatEvent(r),this._handler.mouseDownEvent),this._clickTimeoutId===null&&(this._clickCount=0,this._clickTimeoutId=setTimeout(this._resetClickTimeout.bind(this),aa.ResetClick),this._clickCoordinate=this._getCoordinate(r)))}},t.prototype._init=function(){var r=this;this._target.addEventListener("mouseenter",this._mouseEnterHandler.bind(this)),this._target.addEventListener("touchcancel",this._clearLongTapTimeout.bind(this));{var e=this._target.ownerDocument,a=function(i){r._handler.mouseDownOutsideEvent!=null&&(i.composed&&r._target.contains(i.composedPath()[0])||i.target!==null&&r._target.contains(i.target)||r._handler.mouseDownOutsideEvent({x:0,y:0,pageX:0,pageY:0}))};this._unsubscribeOutsideTouchEvents=function(){e.removeEventListener("touchstart",a)},this._unsubscribeOutsideMouseEvents=function(){e.removeEventListener("mousedown",a)},e.addEventListener("mousedown",a),e.addEventListener("touchstart",a,{passive:!0})}Za()&&(this._unsubscribeMobileSafariEvents=function(){r._target.removeEventListener("dblclick",r._onMobileSafariDoubleClick)},this._target.addEventListener("dblclick",this._onMobileSafariDoubleClick)),this._target.addEventListener("mouseleave",this._mouseLeaveHandler.bind(this)),this._target.addEventListener("touchstart",this._touchStartHandler.bind(this),{passive:!0}),this._target.addEventListener("mousedown",function(i){if(i.button===Xr.Middle)return i.preventDefault(),!1}),this._target.addEventListener("mousedown",this._mouseDownHandler.bind(this)),this._initPinch(),this._target.addEventListener("touchmove",function(){},{passive:!1})},t.prototype._initPinch=function(){var r=this;!P(this._handler.pinchStartEvent)&&!P(this._handler.pinchEvent)&&!P(this._handler.pinchEndEvent)||(this._target.addEventListener("touchstart",function(e){r._checkPinchState(e.touches)},{passive:!0}),this._target.addEventListener("touchmove",function(e){if(!(e.touches.length!==2||r._startPinchMiddleCoordinate===null)&&P(r._handler.pinchEvent)){var a=r._getTouchDistance(e.touches[0],e.touches[1]),i=a/r._startPinchDistance;r._handler.pinchEvent(U(U({},r._startPinchMiddleCoordinate),{pageX:0,pageY:0}),i),r._preventDefault(e)}},{passive:!1}),this._target.addEventListener("touchend",function(e){r._checkPinchState(e.touches)}))},t.prototype._checkPinchState=function(r){r.length===1&&(this._pinchPrevented=!1),r.length!==2||this._pinchPrevented||this._longTapActive?this._stopPinch():this._startPinch(r)},t.prototype._startPinch=function(r){var e=this._target.getBoundingClientRect();this._startPinchMiddleCoordinate={x:(r[0].clientX-e.left+(r[1].clientX-e.left))/2,y:(r[0].clientY-e.top+(r[1].clientY-e.top))/2},this._startPinchDistance=this._getTouchDistance(r[0],r[1]),P(this._handler.pinchStartEvent)&&this._handler.pinchStartEvent({x:0,y:0,pageX:0,pageY:0}),this._clearLongTapTimeout()},t.prototype._stopPinch=function(){this._startPinchMiddleCoordinate!==null&&(this._startPinchMiddleCoordinate=null,P(this._handler.pinchEndEvent)&&this._handler.pinchEndEvent({x:0,y:0,pageX:0,pageY:0}))},t.prototype._mouseLeaveHandler=function(r){var e,a,i;(e=this._unsubscribeMousemove)===null||e===void 0||e.call(this),(a=this._unsubscribeMouseWheel)===null||a===void 0||a.call(this),(i=this._unsubscribeContextMenu)===null||i===void 0||i.call(this),!this._firesTouchEvents(r)&&this._acceptMouseLeave&&(this._processEvent(this._makeCompatEvent(r),this._handler.mouseLeaveEvent),this._acceptMouseLeave=!Za())},t.prototype._longTapHandler=function(r){var e=this._touchWithId(r.touches,this._activeTouchId);e!==null&&(this._processEvent(this._makeCompatEvent(r,e),this._handler.longTapEvent),this._cancelTap=!0,this._longTapActive=!0)},t.prototype._firesTouchEvents=function(r){var e;return P((e=r.sourceCapabilities)===null||e===void 0?void 0:e.firesTouchEvents)?r.sourceCapabilities.firesTouchEvents:this._eventTimeStamp(r)<this._lastTouchEventTimeStamp+aa.PreventFiresTouchEvents},t.prototype._processEvent=function(r,e){e==null||e.call(this._handler,r)},t.prototype._makeCompatEvent=function(r,e){var a=this,i=e??r,n=this._target.getBoundingClientRect();return{x:i.clientX-n.left,y:i.clientY-n.top,pageX:i.pageX,pageY:i.pageY,isTouch:!r.type.startsWith("mouse")&&r.type!=="contextmenu"&&r.type!=="click"&&r.type!=="wheel",preventDefault:function(){r.type!=="touchstart"&&a._preventDefault(r)}}},t.prototype._getTouchDistance=function(r,e){var a=r.clientX-e.clientX,i=r.clientY-e.clientY;return Math.sqrt(a*a+i*i)},t.prototype._preventDefault=function(r){r.cancelable&&r.preventDefault()},t.prototype._getCoordinate=function(r){return{x:r.pageX,y:r.pageY}},t.prototype._eventTimeStamp=function(r){var e;return(e=r.timeStamp)!==null&&e!==void 0?e:performance.now()},t.prototype._touchWithId=function(r,e){for(var a=0;a<r.length;++a)if(r[a].identifier===e)return r[a];return null},t}(),L0=function(){function t(r,e){var a=this;this._flingStartTime=new Date().getTime(),this._flingScrollRequestId=null,this._startScrollCoordinate=null,this._touchCoordinate=null,this._touchCancelCrosshair=!1,this._touchZoomed=!1,this._pinchScale=1,this._mouseDownWidget=null,this._prevYAxisRange=null,this._xAxisStartScaleCoordinate=null,this._xAxisStartScaleDistance=0,this._xAxisScale=1,this._yAxisStartScaleDistance=0,this._mouseMoveTriggerWidgetInfo={pane:null,widget:null},this._boundKeyBoardDownEvent=function(i){if(i.shiftKey)switch(i.code){case"Equal":{a._chart.getChartStore().zoom(.5);break}case"Minus":{a._chart.getChartStore().zoom(-.5);break}case"ArrowLeft":{var n=a._chart.getChartStore();n.startScroll(),n.scroll(-3*n.getBarSpace().bar);break}case"ArrowRight":{var n=a._chart.getChartStore();n.startScroll(),n.scroll(3*n.getBarSpace().bar);break}}},this._container=r,this._chart=e,this._event=new w0(r,this,{treatVertDragAsPageScroll:function(){return!1},treatHorzDragAsPageScroll:function(){return!1}}),r.addEventListener("keydown",this._boundKeyBoardDownEvent)}return t.prototype.pinchStartEvent=function(){return this._touchZoomed=!0,this._pinchScale=1,!0},t.prototype.pinchEvent=function(r,e){var a=this._findWidgetByEvent(r),i=a.pane,n=a.widget;if((i==null?void 0:i.getId())!==wt.X_AXIS&&(n==null?void 0:n.getName())===Vt.MAIN){var o=this._makeWidgetEvent(r,n),s=(e-this._pinchScale)*5;return this._pinchScale=e,this._chart.getChartStore().zoom(s,{x:o.x,y:o.y}),!0}return!1},t.prototype.mouseWheelHortEvent=function(r,e){var a=this._chart.getChartStore();return a.startScroll(),a.scroll(e),!0},t.prototype.mouseWheelVertEvent=function(r,e){var a=this._findWidgetByEvent(r).widget,i=this._makeWidgetEvent(r,a),n=a==null?void 0:a.getName();return n===Vt.MAIN?(this._chart.getChartStore().zoom(e,{x:i.x,y:i.y}),!0):!1},t.prototype.mouseDownEvent=function(r){var e=this._findWidgetByEvent(r),a=e.pane,i=e.widget;if(this._mouseDownWidget=i,i!==null){var n=this._makeWidgetEvent(r,i),o=i.getName();switch(o){case Vt.SEPARATOR:return i.dispatchEvent("mouseDownEvent",n);case Vt.MAIN:{var s=a.getAxisComponent();if(!s.getAutoCalcTickFlag()){var l=s.getRange();this._prevYAxisRange=U({},l)}return this._startScrollCoordinate={x:n.x,y:n.y},this._chart.getChartStore().startScroll(),i.dispatchEvent("mouseDownEvent",n)}case Vt.X_AXIS:return this._processXAxisScrollStartEvent(i,n);case Vt.Y_AXIS:return this._processYAxisScaleStartEvent(i,n)}}return!1},t.prototype.mouseMoveEvent=function(r){var e,a,i,n=this._findWidgetByEvent(r),o=n.pane,s=n.widget,l=this._makeWidgetEvent(r,s);if((((e=this._mouseMoveTriggerWidgetInfo.pane)===null||e===void 0?void 0:e.getId())!==(o==null?void 0:o.getId())||((a=this._mouseMoveTriggerWidgetInfo.widget)===null||a===void 0?void 0:a.getName())!==(s==null?void 0:s.getName()))&&(s==null||s.dispatchEvent("mouseEnterEvent",l),(i=this._mouseMoveTriggerWidgetInfo.widget)===null||i===void 0||i.dispatchEvent("mouseLeaveEvent",l),this._mouseMoveTriggerWidgetInfo={pane:o,widget:s}),s!==null){var c=s.getName();switch(c){case Vt.MAIN:{var u=s.dispatchEvent("mouseMoveEvent",l),d={x:l.x,y:l.y,paneId:o==null?void 0:o.getId()};return u?(s.getForceCursor()!=="pointer"&&(d=void 0),s.setCursor("pointer")):s.setCursor("crosshair"),this._chart.getChartStore().setCrosshair(d),u}case Vt.SEPARATOR:case Vt.X_AXIS:case Vt.Y_AXIS:{var u=s.dispatchEvent("mouseMoveEvent",l);return this._chart.getChartStore().setCrosshair(),u}}}return!1},t.prototype.pressedMouseMoveEvent=function(r){var e,a;if(this._mouseDownWidget!==null&&this._mouseDownWidget.getName()===Vt.SEPARATOR)return this._mouseDownWidget.dispatchEvent("pressedMouseMoveEvent",r);var i=this._findWidgetByEvent(r),n=i.pane,o=i.widget;if(o!==null&&((e=this._mouseDownWidget)===null||e===void 0?void 0:e.getPane().getId())===(n==null?void 0:n.getId())&&((a=this._mouseDownWidget)===null||a===void 0?void 0:a.getName())===o.getName()){var s=this._makeWidgetEvent(r,o),l=o.getName();switch(l){case Vt.MAIN:{var c=void 0,u=o.dispatchEvent("pressedMouseMoveEvent",s);return u||this._processMainScrollingEvent(o,s),(!u||o.getForceCursor()==="pointer")&&(c={x:s.x,y:s.y,paneId:n==null?void 0:n.getId()}),this._chart.getChartStore().setCrosshair(c,{forceInvalidate:!0}),u}case Vt.X_AXIS:return this._processXAxisScrollingEvent(o,s);case Vt.Y_AXIS:return this._processYAxisScalingEvent(o,s)}}return!1},t.prototype.mouseUpEvent=function(r){var e=this._findWidgetByEvent(r).widget,a=!1;if(e!==null){var i=this._makeWidgetEvent(r,e),n=e.getName();switch(n){case Vt.MAIN:case Vt.SEPARATOR:case Vt.X_AXIS:case Vt.Y_AXIS:{a=e.dispatchEvent("mouseUpEvent",i);break}}a&&this._chart.updatePane(1)}return this._mouseDownWidget=null,this._startScrollCoordinate=null,this._prevYAxisRange=null,this._xAxisStartScaleCoordinate=null,this._xAxisStartScaleDistance=0,this._xAxisScale=1,this._yAxisStartScaleDistance=0,a},t.prototype.mouseClickEvent=function(r){var e=this._findWidgetByEvent(r).widget;if(e!==null){var a=this._makeWidgetEvent(r,e);return e.dispatchEvent("mouseClickEvent",a)}return!1},t.prototype.mouseRightClickEvent=function(r){var e=this._findWidgetByEvent(r).widget,a=!1;if(e!==null){var i=this._makeWidgetEvent(r,e),n=e.getName();switch(n){case Vt.MAIN:case Vt.X_AXIS:case Vt.Y_AXIS:{a=e.dispatchEvent("mouseRightClickEvent",i);break}}a&&this._chart.updatePane(1)}return!1},t.prototype.mouseDoubleClickEvent=function(r){var e=this._findWidgetByEvent(r),a=e.pane,i=e.widget;if(i!==null){var n=i.getName();switch(n){case Vt.MAIN:{var o=this._makeWidgetEvent(r,i);return i.dispatchEvent("mouseDoubleClickEvent",o)}case Vt.Y_AXIS:{var s=a.getAxisComponent();if(!s.getAutoCalcTickFlag())return s.setAutoCalcTickFlag(!0),this._chart.layout({measureWidth:!0,update:!0,buildYAxisTick:!0}),!0;break}}}return!1},t.prototype.mouseLeaveEvent=function(){return this._chart.getChartStore().setCrosshair(),!0},t.prototype.touchStartEvent=function(r){var e,a=this._findWidgetByEvent(r),i=a.pane,n=a.widget;if(n!==null){var o=this._makeWidgetEvent(r,n);(e=o.preventDefault)===null||e===void 0||e.call(o);var s=n.getName();switch(s){case Vt.MAIN:{var l=this._chart.getChartStore();if(n.dispatchEvent("mouseDownEvent",o))return this._touchCancelCrosshair=!0,this._touchCoordinate=null,l.setCrosshair(void 0,{notInvalidate:!0}),this._chart.updatePane(1),!0;this._flingScrollRequestId!==null&&(Wi(this._flingScrollRequestId),this._flingScrollRequestId=null),this._flingStartTime=new Date().getTime();var c=i.getAxisComponent();if(!c.getAutoCalcTickFlag()){var u=c.getRange();this._prevYAxisRange=U({},u)}if(this._startScrollCoordinate={x:o.x,y:o.y},l.startScroll(),this._touchZoomed=!1,this._touchCoordinate!==null){var d=o.x-this._touchCoordinate.x,v=o.y-this._touchCoordinate.y,h=Math.sqrt(d*d+v*v);h<x0?(this._touchCoordinate={x:o.x,y:o.y},l.setCrosshair({x:o.x,y:o.y,paneId:i==null?void 0:i.getId()})):(this._touchCoordinate=null,this._touchCancelCrosshair=!0,l.setCrosshair())}return!0}case Vt.X_AXIS:return this._processXAxisScrollStartEvent(n,o);case Vt.Y_AXIS:return this._processYAxisScaleStartEvent(n,o)}}return!1},t.prototype.touchMoveEvent=function(r){var e,a=this._findWidgetByEvent(r),i=a.pane,n=a.widget;if(n!==null){var o=this._makeWidgetEvent(r,n);(e=o.preventDefault)===null||e===void 0||e.call(o);var s=n.getName(),l=this._chart.getChartStore();switch(s){case Vt.MAIN:return n.dispatchEvent("pressedMouseMoveEvent",o)?(l.setCrosshair(void 0,{notInvalidate:!0}),this._chart.updatePane(1),!0):(this._touchCoordinate!==null?l.setCrosshair({x:o.x,y:o.y,paneId:i==null?void 0:i.getId()}):this._processMainScrollingEvent(n,o),!0);case Vt.X_AXIS:return this._processXAxisScrollingEvent(n,o);case Vt.Y_AXIS:return this._processYAxisScalingEvent(n,o)}}return!1},t.prototype.touchEndEvent=function(r){var e=this,a=this._findWidgetByEvent(r).widget;if(a!==null){var i=this._makeWidgetEvent(r,a),n=a.getName();switch(n){case Vt.MAIN:{if(a.dispatchEvent("mouseUpEvent",i),this._startScrollCoordinate!==null){var o=new Date().getTime()-this._flingStartTime,s=i.x-this._startScrollCoordinate.x,l=s/(o>0?o:1)*20;if(o<200&&Math.abs(l)>0){var c=this._chart.getChartStore(),u=function(){e._flingScrollRequestId=ya(function(){c.startScroll(),c.scroll(l),l=l*(1-.025),Math.abs(l)<1?e._flingScrollRequestId!==null&&(Wi(e._flingScrollRequestId),e._flingScrollRequestId=null):u()})};u()}}return!0}case Vt.X_AXIS:case Vt.Y_AXIS:{var d=a.dispatchEvent("mouseUpEvent",i);d&&this._chart.updatePane(1)}}this._startScrollCoordinate=null,this._prevYAxisRange=null,this._xAxisStartScaleCoordinate=null,this._xAxisStartScaleDistance=0,this._xAxisScale=1,this._yAxisStartScaleDistance=0}return!1},t.prototype.tapEvent=function(r){var e=this._findWidgetByEvent(r),a=e.pane,i=e.widget,n=!1;if(i!==null){var o=this._makeWidgetEvent(r,i),s=i.dispatchEvent("mouseClickEvent",o);if(i.getName()===Vt.MAIN){var l=this._makeWidgetEvent(r,i),c=this._chart.getChartStore();s?(this._touchCancelCrosshair=!0,this._touchCoordinate=null,c.setCrosshair(void 0,{notInvalidate:!0}),n=!0):(!this._touchCancelCrosshair&&!this._touchZoomed&&(this._touchCoordinate={x:l.x,y:l.y},c.setCrosshair({x:l.x,y:l.y,paneId:a==null?void 0:a.getId()},{notInvalidate:!0}),n=!0),this._touchCancelCrosshair=!1)}(n||s)&&this._chart.updatePane(1)}return n},t.prototype.doubleTapEvent=function(r){return this.mouseDoubleClickEvent(r)},t.prototype.longTapEvent=function(r){var e=this._findWidgetByEvent(r),a=e.pane,i=e.widget;if(i!==null&&i.getName()===Vt.MAIN){var n=this._makeWidgetEvent(r,i);return this._touchCoordinate={x:n.x,y:n.y},this._chart.getChartStore().setCrosshair({x:n.x,y:n.y,paneId:a==null?void 0:a.getId()}),!0}return!1},t.prototype._processMainScrollingEvent=function(r,e){if(this._startScrollCoordinate!==null){var a=r.getPane().getAxisComponent();if(this._prevYAxisRange!==null&&!a.getAutoCalcTickFlag()&&a.scrollZoomEnabled){var i=this._prevYAxisRange,n=i.from,o=i.to,s=i.range,l=0;a.reverse?l=this._startScrollCoordinate.y-e.y:l=e.y-this._startScrollCoordinate.y;var c=r.getBounding(),u=l/c.height,d=s*u,v=n+d,h=o+d,f=a.valueToRealValue(v,{range:this._prevYAxisRange}),p=a.valueToRealValue(h,{range:this._prevYAxisRange}),g=a.realValueToDisplayValue(f,{range:this._prevYAxisRange}),m=a.realValueToDisplayValue(p,{range:this._prevYAxisRange});a.setRange({from:v,to:h,range:h-v,realFrom:f,realTo:p,realRange:p-f,displayFrom:g,displayTo:m,displayRange:m-g})}var b=e.x-this._startScrollCoordinate.x;this._chart.getChartStore().scroll(b)}},t.prototype._processXAxisScrollStartEvent=function(r,e){var a=r.dispatchEvent("mouseDownEvent",e);return a&&this._chart.updatePane(1),this._xAxisStartScaleCoordinate={x:e.x,y:e.y},this._xAxisStartScaleDistance=e.pageX,a},t.prototype._processXAxisScrollingEvent=function(r,e){var a,i=r.dispatchEvent("pressedMouseMoveEvent",e);if(i)this._chart.updatePane(1);else{var n=r.getPane().getAxisComponent();if(n.scrollZoomEnabled&&this._xAxisStartScaleDistance!==0){var o=this._xAxisStartScaleDistance/e.pageX;if(Number.isFinite(o)){var s=(o-this._xAxisScale)*10;this._xAxisScale=o,this._chart.getChartStore().zoom(s,(a=this._xAxisStartScaleCoordinate)!==null&&a!==void 0?a:void 0)}}}return i},t.prototype._processYAxisScaleStartEvent=function(r,e){var a=r.dispatchEvent("mouseDownEvent",e);a&&this._chart.updatePane(1);var i=r.getPane().getAxisComponent().getRange();return this._prevYAxisRange=U({},i),this._yAxisStartScaleDistance=e.pageY,a},t.prototype._processYAxisScalingEvent=function(r,e){var a=r.dispatchEvent("pressedMouseMoveEvent",e);if(a)this._chart.updatePane(1);else{var i=r.getPane().getAxisComponent();if(this._prevYAxisRange!==null&&i.scrollZoomEnabled&&this._yAxisStartScaleDistance!==0){var n=this._prevYAxisRange,o=n.from,s=n.to,l=n.range,c=e.pageY/this._yAxisStartScaleDistance,u=l*c,d=(u-l)/2,v=o-d,h=s+d,f=i.valueToRealValue(v,{range:this._prevYAxisRange}),p=i.valueToRealValue(h,{range:this._prevYAxisRange}),g=i.realValueToDisplayValue(f,{range:this._prevYAxisRange}),m=i.realValueToDisplayValue(p,{range:this._prevYAxisRange});i.setRange({from:v,to:h,range:u,realFrom:f,realTo:p,realRange:p-f,displayFrom:g,displayTo:m,displayRange:m-g}),this._chart.layout({measureWidth:!0,update:!0,buildYAxisTick:!0})}}return a},t.prototype._findWidgetByEvent=function(r){var e,a,i,n,o=r.x,s=r.y,l=this._chart.getSeparatorPanes(),c=this._chart.getStyles().separator.size;try{for(var u=Ze(l),d=u.next();!d.done;d=u.next()){var v=d.value,h=v[1],f=h.getBounding(),p=f.top-Math.round((Gr-c)/2);if(o>=f.left&&o<=f.left+f.width&&s>=p&&s<=p+Gr)return{pane:h,widget:h.getWidget()}}}catch(y){e={error:y}}finally{try{d&&!d.done&&(a=u.return)&&a.call(u)}finally{if(e)throw e.error}}var g=this._chart.getDrawPanes(),m=null;try{for(var b=Ze(g),x=b.next();!x.done;x=b.next()){var L=x.value,f=L.getBounding();if(o>=f.left&&o<=f.left+f.width&&s>=f.top&&s<=f.top+f.height){m=L;break}}}catch(y){i={error:y}}finally{try{x&&!x.done&&(n=b.return)&&n.call(b)}finally{if(i)throw i.error}}var _=null;if(m!==null){if(!P(_)){var M=m.getMainWidget(),w=M.getBounding();o>=w.left&&o<=w.left+w.width&&s>=w.top&&s<=w.top+w.height&&(_=M)}if(!P(_)){var k=m.getYAxisWidget();if(k!==null){var C=k.getBounding();o>=C.left&&o<=C.left+C.width&&s>=C.top&&s<=C.top+C.height&&(_=k)}}}return{pane:m,widget:_}},t.prototype._makeWidgetEvent=function(r,e){var a,i,n,o=(a=e==null?void 0:e.getBounding())!==null&&a!==void 0?a:null;return U(U({},r),{x:r.x-((i=o==null?void 0:o.left)!==null&&i!==void 0?i:0),y:r.y-((n=o==null?void 0:o.top)!==null&&n!==void 0?n:0)})},t.prototype.destroy=function(){this._container.removeEventListener("keydown",this._boundKeyBoardDownEvent),this._event.destroy()},t}(),Bo=function(){function t(r,e){this._chartBounding=ma(),this._drawPanes=[],this._separatorPanes=new Map,this._layoutOptions={sort:!0,measureHeight:!0,measureWidth:!0,update:!0,buildYAxisTick:!1,cacheYAxisWidth:!1,forceBuildYAxisTick:!1},this._layoutPending=!1,this._cacheYAxisWidth={left:0,right:0},this._initContainer(r),this._chartEvent=new L0(this._chartContainer,this),this._chartStore=new b1(this,e),this._initPanes(e),this._layout()}return t.prototype._initContainer=function(r){this._container=r,this._chartContainer=nr("div",{position:"relative",width:"100%",height:"100%",outline:"none",borderStyle:"none",cursor:"crosshair",boxSizing:"border-box",userSelect:"none",webkitUserSelect:"none",overflow:"hidden",msUserSelect:"none",MozUserSelect:"none",webkitTapHighlightColor:"transparent"}),this._chartContainer.tabIndex=1,r.appendChild(this._chartContainer),this._cacheChartBounding()},t.prototype._cacheChartBounding=function(){this._chartBounding.width=Math.floor(this._chartContainer.clientWidth),this._chartBounding.height=Math.floor(this._chartContainer.clientHeight)},t.prototype._initPanes=function(r){var e=this,a,i=(a=r==null?void 0:r.layout)!==null&&a!==void 0?a:[{type:"candle"}],n=function(s){var l,c;if(!P(e._candlePane)){var u=(l=s.options)!==null&&l!==void 0?l:{};he(u,{id:wt.CANDLE}),e._candlePane=e._createPane(u0,wt.CANDLE,u);var d=(c=s.content)!==null&&c!==void 0?c:[];d.forEach(function(v){e.createIndicator(v,!0,u)})}},o=function(s){if(!P(e._xAxisPane)){var l=e._createPane(y0,wt.X_AXIS,s??{});e._xAxisPane=l}};i.forEach(function(s){var l,c,u;switch(s.type){case"candle":{n(s);break}case"indicator":{var d=(l=s.content)!==null&&l!==void 0?l:[];if(d.length>0){var v=(u=(c=s.options)===null||c===void 0?void 0:c.id)!==null&&u!==void 0?u:null;P(v)&&(v=ua(wt.INDICATOR));var h=U(U({},s.options),{id:v});d.forEach(function(f){e.createIndicator(f,!0,h)})}break}case"xAxis":{o(s.options);break}}}),n({}),o({order:Number.MAX_SAFE_INTEGER})},t.prototype._createPane=function(r,e,a){var i=new r(this,e,a??{});return this._drawPanes.push(i),i},t.prototype._recalculatePaneHeight=function(r,e,a){if(a===0)return!1;var i=this._drawPanes.filter(function(d){var v=d.getId();return d.getOptions().state==="normal"&&v!==r.getId()&&v!==wt.X_AXIS}),n=i.length;if(n===0)return!1;if(r.getId()!==wt.CANDLE&&P(this._candlePane)&&this._candlePane.getOptions().state==="normal"){var o=this._candlePane.getBounding().height;if(o>0){var s=this._candlePane.getOptions().minHeight,l=o+a;l<s&&(l=s,e-=o+a-l),this._candlePane.setBounding({height:l})}}else{var c=a,u=Math.floor(a/n);i.forEach(function(d,v){var h=d.getBounding().height,f=0;v===n-1?f=h+c:f=h+u,f<d.getOptions().minHeight&&(f=d.getOptions().minHeight),d.setBounding({height:f}),c-=f-h}),Math.abs(c)>0&&(e-=c)}return r.setBounding({height:e}),!0},t.prototype.getDrawPaneById=function(r){if(r===wt.CANDLE)return this._candlePane;if(r===wt.X_AXIS)return this._xAxisPane;var e=this._drawPanes.find(function(a){return a.getId()===r});return e??null},t.prototype.getContainer=function(){return this._container},t.prototype.getChartStore=function(){return this._chartStore},t.prototype.getXAxisPane=function(){return this._xAxisPane},t.prototype.getDrawPanes=function(){return this._drawPanes},t.prototype.getSeparatorPanes=function(){return this._separatorPanes},t.prototype.layout=function(r){var e=this,a,i,n,o,s,l,c;(a=r.sort)!==null&&a!==void 0&&a&&(this._layoutOptions.sort=r.sort),(i=r.measureHeight)!==null&&i!==void 0&&i&&(this._layoutOptions.measureHeight=r.measureHeight),(n=r.measureWidth)!==null&&n!==void 0&&n&&(this._layoutOptions.measureWidth=r.measureWidth),(o=r.update)!==null&&o!==void 0&&o&&(this._layoutOptions.update=r.update),(s=r.buildYAxisTick)!==null&&s!==void 0&&s&&(this._layoutOptions.buildYAxisTick=r.buildYAxisTick),(l=r.cacheYAxisWidth)!==null&&l!==void 0&&l&&(this._layoutOptions.cacheYAxisWidth=r.cacheYAxisWidth),(c=r.buildYAxisTick)!==null&&c!==void 0&&c&&(this._layoutOptions.forceBuildYAxisTick=r.forceBuildYAxisTick),this._layoutPending||(this._layoutPending=!0,Promise.resolve().then(function(u){e._layout(),e._layoutPending=!1}).catch(function(u){}))},t.prototype._layout=function(){var r=this,e=this._layoutOptions,a=e.sort,i=e.measureHeight,n=e.measureWidth,o=e.update,s=e.buildYAxisTick,l=e.cacheYAxisWidth,c=e.forceBuildYAxisTick;if(a){for(;P(this._chartContainer.firstChild);)this._chartContainer.removeChild(this._chartContainer.firstChild);this._separatorPanes.clear(),this._drawPanes.sort(function(N,B){return N.getOptions().order-B.getOptions().order});var u=null;this._drawPanes.forEach(function(N){if(N.getId()!==wt.X_AXIS){if(P(u)){var B=new b0(r,"",u,N);r._chartContainer.appendChild(B.getContainer()),r._separatorPanes.set(N,B)}u=N}r._chartContainer.appendChild(N.getContainer())})}if(i){var d=this._chartBounding.height,v=this.getStyles().separator.size,h=this._xAxisPane.getAxisComponent().getAutoSize(),f=d-h;f<0&&(f=0),this._drawPanes.forEach(function(N){var B=N.getId();if(P(r._separatorPanes.get(N))&&(f-=v),B!==wt.X_AXIS&&B!==wt.CANDLE&&N.getVisible()){var Y=N.getBounding().height;Y>f?(Y=f,f=0):f-=Y,N.setBounding({height:Y})}}),this._candlePane.setBounding({height:Math.max(f,0)}),this._xAxisPane.setBounding({height:h});var p=0;this._drawPanes.forEach(function(N){var B=r._separatorPanes.get(N);P(B)&&(B.setBounding({height:v,top:p}),p+=v),N.setBounding({top:p}),p+=N.getBounding().height})}var g=n;if((s||c)&&this._drawPanes.forEach(function(N){var B=N.getAxisComponent().buildTicks(c);g||(g=B)}),g){var m=this._chartBounding.width,b=this.getStyles(),x=0,L=!0,_=0,M=!0;this._drawPanes.forEach(function(N){if(N.getId()!==wt.X_AXIS){var B=N.getAxisComponent(),Y=B.inside,K=B.getAutoSize();B.position==="left"?(x=Math.max(x,K),Y&&(L=!1)):(_=Math.max(_,K),Y&&(M=!1))}}),l&&(x=Math.max(this._cacheYAxisWidth.left,x),_=Math.max(this._cacheYAxisWidth.right,_)),this._cacheYAxisWidth.left=x,this._cacheYAxisWidth.right=_;var w=m,k=0,C=0;L&&(w-=x,k=x),M&&(w-=_,C=_),this._chartStore.setTotalBarSpace(w);var y={width:m},T={width:w,left:k,right:C},I={width:x},E={width:_},A=b.separator.fill,F={};A?F=y:F=T,this._drawPanes.forEach(function(N){var B;(B=r._separatorPanes.get(N))===null||B===void 0||B.setBounding(F),N.setBounding(y,T,I,E)})}o&&(this._xAxisPane.getAxisComponent().buildTicks(!0),this.updatePane(4)),this._layoutOptions={sort:!1,measureHeight:!1,measureWidth:!1,update:!1,buildYAxisTick:!1,cacheYAxisWidth:!1,forceBuildYAxisTick:!1}},t.prototype.updatePane=function(r,e){var a=this;if(P(e)){var i=this.getDrawPaneById(e);i==null||i.update(r)}else this._drawPanes.forEach(function(n){var o;n.update(r),(o=a._separatorPanes.get(n))===null||o===void 0||o.update(r)})},t.prototype.crosshairChange=function(r){var e=this;if(this._chartStore.hasAction("onCrosshairChange")){var a={};this._drawPanes.forEach(function(i){var n=i.getId(),o={},s=e._chartStore.getIndicatorsByPaneId(n);s.forEach(function(l){var c,u=l.result;o[l.name]=u[(c=r.dataIndex)!==null&&c!==void 0?c:u.length-1]}),a[n]=o}),Gt(r.paneId)&&this._chartStore.executeAction("onCrosshairChange",{crosshair:r,indicatorData:a})}},t.prototype.getDom=function(r,e){var a,i;if(P(r)){var n=this.getDrawPaneById(r);if(P(n)){var o=e??"root";switch(o){case"root":return n.getContainer();case"main":return n.getMainWidget().getContainer();case"yAxis":return(i=(a=n.getYAxisWidget())===null||a===void 0?void 0:a.getContainer())!==null&&i!==void 0?i:null}}}else return this._chartContainer;return null},t.prototype.getSize=function(r,e){var a,i;if(P(r)){var n=this.getDrawPaneById(r);if(P(n)){var o=e??"root";switch(o){case"root":return n.getBounding();case"main":return n.getMainWidget().getBounding();case"yAxis":return(i=(a=n.getYAxisWidget())===null||a===void 0?void 0:a.getBounding())!==null&&i!==void 0?i:null}}}else return this._chartBounding;return null},t.prototype.setSymbol=function(r){this._chartStore.setSymbol(r)},t.prototype.getSymbol=function(){return this._chartStore.getSymbol()},t.prototype.setPeriod=function(r){this._chartStore.setPeriod(r)},t.prototype.getPeriod=function(){return this._chartStore.getPeriod()},t.prototype.setStyles=function(r){var e=this;this._setOptions(function(){e._chartStore.setStyles(r)})},t.prototype.getStyles=function(){return this._chartStore.getStyles()},t.prototype.setFormatter=function(r){var e=this;this._setOptions(function(){e._chartStore.setFormatter(r)})},t.prototype.getFormatter=function(){return this._chartStore.getFormatter()},t.prototype.setLocale=function(r){var e=this;this._setOptions(function(){e._chartStore.setLocale(r)})},t.prototype.getLocale=function(){return this._chartStore.getLocale()},t.prototype.setTimezone=function(r){var e=this;this._setOptions(function(){e._chartStore.setTimezone(r)})},t.prototype.getTimezone=function(){return this._chartStore.getTimezone()},t.prototype.setThousandsSeparator=function(r){var e=this;this._setOptions(function(){e._chartStore.setThousandsSeparator(r)})},t.prototype.getThousandsSeparator=function(){return this._chartStore.getThousandsSeparator()},t.prototype.setDecimalFold=function(r){var e=this;this._setOptions(function(){e._chartStore.setDecimalFold(r)})},t.prototype.getDecimalFold=function(){return this._chartStore.getDecimalFold()},t.prototype._setOptions=function(r){r(),this.layout({measureHeight:!0,measureWidth:!0,update:!0,buildYAxisTick:!0,forceBuildYAxisTick:!0})},t.prototype.setOffsetRightDistance=function(r){this._chartStore.setOffsetRightDistance(r,!0)},t.prototype.getOffsetRightDistance=function(){return this._chartStore.getOffsetRightDistance()},t.prototype.setMaxOffsetLeftDistance=function(r){r<0||this._chartStore.setMaxOffsetLeftDistance(r)},t.prototype.setMaxOffsetRightDistance=function(r){r<0||this._chartStore.setMaxOffsetRightDistance(r)},t.prototype.setLeftMinVisibleBarCount=function(r){r<0||this._chartStore.setLeftMinVisibleBarCount(Math.ceil(r))},t.prototype.setRightMinVisibleBarCount=function(r){r<0||this._chartStore.setRightMinVisibleBarCount(Math.ceil(r))},t.prototype.setBarSpace=function(r){this._chartStore.setBarSpace(r)},t.prototype.getBarSpace=function(){return this._chartStore.getBarSpace()},t.prototype.getVisibleRange=function(){return this._chartStore.getVisibleRange()},t.prototype.resetData=function(){this._chartStore.resetData()},t.prototype.getDataList=function(){return this._chartStore.getDataList()},t.prototype.applyNewData=function(r,e){this._chartStore.applyNewData(r,e)},t.prototype.updateData=function(r){this._chartStore.updateData(r)},t.prototype.setDataLoader=function(r){this._chartStore.setDataLoader(r)},t.prototype.createIndicator=function(r,e,a){var i,n=Gt(r)?{name:r}:r;if(co(n.name)===null)return null;var o=a??{};Gt(o.id)||(o.id=ua(wt.INDICATOR)),Gt(n.id)||(n.id=ua(n.name));var s=this._chartStore.addIndicator(n,o.id,e??!1);if(s){var l=!1;return P(this.getDrawPaneById(o.id))||(this._createPane(Ro,o.id,o),(i=o.height)!==null&&i!==void 0||(o.height=go),l=!0),this.setPaneOptions(o),this.layout({sort:l,measureHeight:!0,measureWidth:!0,update:!0,buildYAxisTick:!0,forceBuildYAxisTick:!0}),n.id}return null},t.prototype.overrideIndicator=function(r){return this._chartStore.overrideIndicator(r)},t.prototype.getIndicators=function(r){return this._chartStore.getIndicatorsByFilter(r??{})},t.prototype.removeIndicator=function(r){var e=this,a=this._chartStore.removeIndicator(r??{});if(a){var i=!1,n=[];this._drawPanes.forEach(function(o){var s=o.getId();s!==wt.CANDLE&&s!==wt.X_AXIS&&n.push(s)}),n.forEach(function(o){if(!e._chartStore.hasIndicators(o)){var s=e._drawPanes.findIndex(function(c){return c.getId()===o}),l=e._drawPanes[s];P(l)&&(i=!0,e._recalculatePaneHeight(l,0,l.getBounding().height),e._drawPanes.splice(s,1),l.destroy())}}),this._drawPanes.length===2&&(this._candlePane.setVisible(!0),this._candlePane.setBounding({height:this._chartBounding.height-this._xAxisPane.getBounding().height})),this.layout({sort:i,measureHeight:i,measureWidth:!0,update:!0,buildYAxisTick:!0,forceBuildYAxisTick:!0})}return a},t.prototype.createOverlay=function(r){var e=this,a=[],i=[],n=function(s){!P(s.paneId)||e.getDrawPaneById(s.paneId)===null?(s.paneId=wt.CANDLE,i.push(!1)):i.push(!0),a.push(s)};Gt(r)?n({name:r}):He(r)?r.forEach(function(s){var l=null;Gt(s)?l={name:s}:l=s,n(l)}):n(r);var o=this._chartStore.addOverlays(a,i);return He(r)?o:o[0]},t.prototype.getOverlays=function(r){return this._chartStore.getOverlaysByFilter(r??{})},t.prototype.overrideOverlay=function(r){return this._chartStore.overrideOverlay(r)},t.prototype.removeOverlay=function(r){return this._chartStore.removeOverlay(r??{})},t.prototype.setPaneOptions=function(r){var e,a,i=this,n,o=!1,s=!1,l=P(r.id),c=function(p){var g=p.getId();if(l&&r.id===g||!l){if(g!==wt.X_AXIS){if(vt(r.height)&&r.height>0){var m=Math.max((n=r.minHeight)!==null&&n!==void 0?n:p.getOptions().minHeight,0),b=Math.max(m,r.height);s=!0,o=!0,p.setOriginalBounding({height:b}),u._recalculatePaneHeight(p,b,-b)}if(P(r.state)&&p.getOptions().state!==r.state){o=!0,s=!0;var x=r.state;switch(x){case"maximize":{var L=u._drawPanes.find(function(C){var y=C.getId();return C.getOptions().state==="maximize"&&y!==wt.X_AXIS});if(!P(L)){p.getOptions().state==="normal"&&p.setOriginalBounding({height:p.getBounding().height}),p.setOptions({state:x});var _=u._chartBounding.height;p.setBounding({height:_-u._xAxisPane.getBounding().height}),u._drawPanes.forEach(function(C){var y;C.getId()!==wt.X_AXIS&&C.getId()!==g&&(C.setBounding({height:C.getOriginalBounding().height}),C.setVisible(!1),(y=i._separatorPanes.get(C))===null||y===void 0||y.setVisible(!1))})}break}case"minimize":{var b=p.getBounding().height,M=p.getOptions().state,w=b-da;M==="maximize"&&(w=p.getOriginalBounding().height-da),u._recalculatePaneHeight(p,da,w)&&(M==="normal"&&p.setOriginalBounding({height:b}),p.setOptions({state:x})),u._drawPanes.forEach(function(y){var T;y.getId()!==wt.X_AXIS&&(y.setVisible(!0),(T=i._separatorPanes.get(y))===null||T===void 0||T.setVisible(!0))});break}default:{var b=p.getOriginalBounding().height;u._recalculatePaneHeight(p,b,p.getBounding().height-b)&&p.setOptions({state:x}),u._drawPanes.forEach(function(y){var T;y.getId()!==wt.X_AXIS&&(y.setVisible(!0),(T=i._separatorPanes.get(y))===null||T===void 0||T.setVisible(!0))});break}}}}P(r.axis)&&(s=!0);var k=U({},r);if(delete k.state,p.setOptions(k),g===r.id)return"break"}},u=this;try{for(var d=Ze(this._drawPanes),v=d.next();!v.done;v=d.next()){var h=v.value,f=c(h);if(f==="break")break}}catch(p){e={error:p}}finally{try{v&&!v.done&&(a=d.return)&&a.call(d)}finally{if(e)throw e.error}}s&&this.layout({measureHeight:o,measureWidth:!0,update:!0,buildYAxisTick:!0,forceBuildYAxisTick:!0})},t.prototype.getPaneOptions=function(r){var e;if(P(r)){var a=this.getDrawPaneById(r);return(e=a==null?void 0:a.getOptions())!==null&&e!==void 0?e:null}return this._drawPanes.map(function(i){return i.getOptions()})},t.prototype.setZoomEnabled=function(r){this._chartStore.setZoomEnabled(r)},t.prototype.isZoomEnabled=function(){return this._chartStore.isZoomEnabled()},t.prototype.setScrollEnabled=function(r){this._chartStore.setScrollEnabled(r)},t.prototype.isScrollEnabled=function(){return this._chartStore.isScrollEnabled()},t.prototype.scrollByDistance=function(r,e){var a=this,i=vt(e)&&e>0?e:0;if(this._chartStore.startScroll(),i>0){var n=new qa({duration:i});n.doFrame(function(o){var s=r*(o/i);a._chartStore.scroll(s)}),n.start()}else this._chartStore.scroll(r)},t.prototype.scrollToRealTime=function(r){var e=this._chartStore.getBarSpace().bar,a=this._chartStore.getLastBarRightSideDiffBarCount()-this._chartStore.getInitialOffsetRightDistance()/e,i=a*e;this.scrollByDistance(i,r)},t.prototype.scrollToDataIndex=function(r,e){var a=(this._chartStore.getLastBarRightSideDiffBarCount()+(this.getDataList().length-1-r))*this._chartStore.getBarSpace().bar;this.scrollByDistance(a,e)},t.prototype.scrollToTimestamp=function(r,e){var a=Ja(this.getDataList(),"timestamp",r);this.scrollToDataIndex(a,e)},t.prototype.zoomAtCoordinate=function(r,e,a){var i=this,n=vt(a)&&a>0?a:0,o=this._chartStore.getBarSpace().bar,s=o*r,l=s-o;if(n>0){var c=0,u=new qa({duration:n});u.doFrame(function(d){var v=l*(d/n),h=(v-c)/i._chartStore.getBarSpace().bar*ti;i._chartStore.zoom(h,e),c=v}),u.start()}else this._chartStore.zoom(l/o*ti,e)},t.prototype.zoomAtDataIndex=function(r,e,a){var i=this._chartStore.dataIndexToCoordinate(e);this.zoomAtCoordinate(r,{x:i,y:0},a)},t.prototype.zoomAtTimestamp=function(r,e,a){var i=Ja(this.getDataList(),"timestamp",e);this.zoomAtDataIndex(r,i,a)},t.prototype.convertToPixel=function(r,e){var a=this,i,n=e??{},o=n.paneId,s=o===void 0?wt.CANDLE:o,l=n.absolute,c=l===void 0?!1:l,u=[];if(s!==wt.X_AXIS){var d=this.getDrawPaneById(s);if(d!==null){var v=d.getBounding(),h=[].concat(r),f=this._xAxisPane.getAxisComponent(),p=d.getAxisComponent();u=h.map(function(g){var m={},b=g.dataIndex;if(vt(g.timestamp)&&(b=a._chartStore.timestampToDataIndex(g.timestamp)),vt(b)&&(m.x=f.convertToPixel(b)),vt(g.value)){var x=p.convertToPixel(g.value);m.y=c?v.top+x:x}return m})}}return He(r)?u:(i=u[0])!==null&&i!==void 0?i:{}},t.prototype.convertFromPixel=function(r,e){var a=this,i,n=e??{},o=n.paneId,s=o===void 0?wt.CANDLE:o,l=n.absolute,c=l===void 0?!1:l,u=[];if(s!==wt.X_AXIS){var d=this.getDrawPaneById(s);if(d!==null){var v=d.getBounding(),h=[].concat(r),f=this._xAxisPane.getAxisComponent(),p=d.getAxisComponent();u=h.map(function(g){var m,b={};if(vt(g.x)){var x=f.convertFromPixel(g.x);b.dataIndex=x,b.timestamp=(m=a._chartStore.dataIndexToTimestamp(x))!==null&&m!==void 0?m:void 0}if(vt(g.y)){var L=c?g.y-v.top:g.y;b.value=p.convertFromPixel(L)}return b})}}return He(r)?u:(i=u[0])!==null&&i!==void 0?i:{}},t.prototype.executeAction=function(r,e){var a;switch(r){case"onCrosshairChange":{var i=U({},e);(a=i.paneId)!==null&&a!==void 0||(i.paneId=wt.CANDLE),this._chartStore.setCrosshair(i,{notExecuteAction:!0});break}}},t.prototype.subscribeAction=function(r,e){this._chartStore.subscribeAction(r,e)},t.prototype.unsubscribeAction=function(r,e){this._chartStore.unsubscribeAction(r,e)},t.prototype.getConvertPictureUrl=function(r,e,a){var i=this,n=this._chartBounding,o=n.width,s=n.height,l=nr("canvas",{width:"".concat(o,"px"),height:"".concat(s,"px"),boxSizing:"border-box"}),c=l.getContext("2d"),u=hr(l);l.width=o*u,l.height=s*u,c.scale(u,u),c.fillStyle=a??"#FFFFFF",c.fillRect(0,0,o,s);var d=r??!1;return this._drawPanes.forEach(function(v){var h=i._separatorPanes.get(v);if(P(h)){var f=h.getBounding();c.drawImage(h.getImage(d),f.left,f.top,f.width,f.height)}var p=v.getBounding();c.drawImage(v.getImage(d),0,p.top,o,p.height)}),l.toDataURL("image/".concat(e??"jpeg"))},t.prototype.setOrderFlowData=function(r){this._chartStore.setOrderFlowData(r)},t.prototype.getOrderFlowData=function(){return this._chartStore.getOrderFlowData()},t.prototype.resize=function(){this._cacheChartBounding(),this.layout({measureHeight:!0,measureWidth:!0,update:!0,buildYAxisTick:!0,forceBuildYAxisTick:!0})},t.prototype.destroy=function(){this._chartEvent.destroy(),this._drawPanes.forEach(function(r){r.destroy()}),this._drawPanes=[],this._separatorPanes.clear(),this._chartStore.destroy(),this._container.removeChild(this._chartContainer)},t.prototype.setCandleVisible=function(r){this._chartStore.setCandleVisible(r),this.updatePane(4)},t.prototype.isCandleVisible=function(){return this._chartStore.isCandleVisible()},t}(),Ca=new Map,k0=1;function S0(t,r){var e=null;if(Gt(t)?e=document.getElementById(t):e=t,e===null)return null;var a=Ca.get(e.id);if(P(a))return a;var i="k_line_chart_".concat(k0++);return a=new Bo(e,r),a.id=i,e.setAttribute("k-line-chart-id",i),Ca.set(i,a),a}function M0(t){var r,e,a=null;if(t instanceof Bo)a=t.id;else{var i=null;Gt(t)?i=document.getElementById(t):i=t,a=(r=i==null?void 0:i.getAttribute("k-line-chart-id"))!==null&&r!==void 0?r:null}a!==null&&((e=Ca.get(a))===null||e===void 0||e.destroy(),Ca.delete(a))}var Ae={clone:Er,merge:he,isString:Gt,isNumber:vt,isValid:P,isObject:We,isArray:He,isFunction:Me,isBoolean:Qr,formatValue:ge,formatPrecision:xe,formatBigNumber:ro,formatDate:eo,formatThousands:ao,formatFoldDecimal:io,calcTextWidth:Ye,getLinearSlopeIntercept:Da,getLinearYFromSlopeIntercept:pi,getLinearYFromCoordinates:qr,checkCoordinateOnArc:wo,checkCoordinateOnCircle:yo,checkCoordinateOnLine:vo,checkCoordinateOnPolygon:_o,checkCoordinateOnRect:yi,checkCoordinateOnText:xo},T0=Object.defineProperty,E0=(t,r,e)=>r in t?T0(t,r,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[r]=e,on=(t,r,e)=>(E0(t,typeof r!="symbol"?r+"":r,e),e);function Ur(t,r,e){const a=(t.x-r.x)*Math.cos(e)-(t.y-r.y)*Math.sin(e)+r.x,i=(t.x-r.x)*Math.sin(e)+(t.y-r.y)*Math.cos(e)+r.y;return{x:a,y:i}}function ei(t,r){if(t.length>1){let e;return t[0].x===t[1].x&&t[0].y!==t[1].y?t[0].y<t[1].y?e={x:t[0].x,y:r.height}:e={x:t[0].x,y:0}:t[0].x>t[1].x?e={x:0,y:Ae.getLinearYFromCoordinates(t[0],t[1],{x:0,y:t[0].y})}:e={x:r.width,y:Ae.getLinearYFromCoordinates(t[0],t[1],{x:r.width,y:t[0].y})},{coordinates:[t[0],e]}}return[]}function No(t,r){const e=Math.abs(t.x-r.x),a=Math.abs(t.y-r.y);return Math.sqrt(e*e+a*a)}const I0={name:"arrow",totalStep:3,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:({coordinates:t})=>{if(t.length>1){const r=t[1].x>t[0].x?0:1,e=Ae.getLinearSlopeIntercept(t[0],t[1]);let a;e?a=Math.atan(e[0])+Math.PI*r:t[1].y>t[0].y?a=Math.PI/2:a=Math.PI/2*3;const i=Ur({x:t[1].x-8,y:t[1].y+4},t[1],a),n=Ur({x:t[1].x-8,y:t[1].y-4},t[1],a);return[{type:"line",attrs:{coordinates:t}},{type:"line",ignoreEvent:!0,attrs:{coordinates:[i,t[1],n]}}]}return[]}},A0={name:"circle",totalStep:3,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,styles:{circle:{color:"rgba(22, 119, 255, 0.15)"}},createPointFigures:({coordinates:t})=>{if(t.length>1){const r=No(t[0],t[1]);return{type:"circle",attrs:{...t[0],r},styles:{style:"stroke_fill"}}}return[]}},D0={name:"rect",totalStep:3,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,styles:{polygon:{color:"rgba(22, 119, 255, 0.15)"}},createPointFigures:({coordinates:t})=>t.length>1?[{type:"polygon",attrs:{coordinates:[t[0],{x:t[1].x,y:t[0].y},t[1],{x:t[0].x,y:t[1].y}]},styles:{style:"stroke_fill"}}]:[]},P0={name:"parallelogram",totalStep:4,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,styles:{polygon:{color:"rgba(22, 119, 255, 0.15)"}},createPointFigures:({coordinates:t})=>{if(t.length===2)return[{type:"line",ignoreEvent:!0,attrs:{coordinates:t}}];if(t.length===3){const r={x:t[0].x+(t[2].x-t[1].x),y:t[2].y};return[{type:"polygon",attrs:{coordinates:[t[0],t[1],t[2],r]},styles:{style:"stroke_fill"}}]}return[]},performEventPressedMove:({points:t,performPointIndex:r,performPoint:e})=>{r<2&&(t[0].price=e.price,t[1].price=e.price)},performEventMoveForDrawing:({currentStep:t,points:r,performPoint:e})=>{t===2&&(r[0].price=e.price)}},F0={name:"triangle",totalStep:4,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,styles:{polygon:{color:"rgba(22, 119, 255, 0.15)"}},createPointFigures:({coordinates:t})=>[{type:"polygon",attrs:{coordinates:t},styles:{style:"stroke_fill"}}]},R0={name:"fibonacciCircle",totalStep:3,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:({coordinates:t})=>{if(t.length>1){const r=Math.abs(t[0].x-t[1].x),e=Math.abs(t[0].y-t[1].y),a=Math.sqrt(r*r+e*e),i=[.236,.382,.5,.618,.786,1],n=[],o=[];return i.forEach(s=>{const l=a*s;n.push({...t[0],r:l}),o.push({x:t[0].x,y:t[0].y+l+6,text:`${(s*100).toFixed(1)}%`})}),[{type:"circle",attrs:n,styles:{style:"stroke"}},{type:"text",ignoreEvent:!0,attrs:o}]}return[]}},B0={name:"fibonacciSegment",totalStep:3,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:({coordinates:t,overlay:r})=>{const e=[],a=[];if(t.length>1){const i=t[1].x>t[0].x?t[0].x:t[1].x,n=[1,.786,.618,.5,.382,.236,0],o=t[0].y-t[1].y,s=r.points,l=s[0].value-s[1].value;n.forEach(c=>{const u=t[1].y+o*c,d=(s[1].value+l*c).toFixed(precision.price);e.push({coordinates:[{x:t[0].x,y:u},{x:t[1].x,y:u}]}),a.push({x:i,y:u,text:`${d} (${(c*100).toFixed(1)}%)`,baseline:"bottom"})})}return[{type:"line",attrs:e},{type:"text",ignoreEvent:!0,attrs:a}]}},N0={name:"fibonacciSpiral",totalStep:3,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:({coordinates:t,bounding:r})=>{if(t.length>1){const e=No(t[0],t[1])/Math.sqrt(24),a=t[1].x>t[0].x?0:1,i=Ae.getLinearSlopeIntercept(t[0],t[1]);let n;i?n=Math.atan(i[0])+Math.PI*a:t[1].y>t[0].y?n=Math.PI/2:n=Math.PI/2*3;const o=Ur({x:t[0].x-e,y:t[0].y},t[0],n),s=Ur({x:t[0].x-e,y:t[0].y-e},t[0],n),l=[{...o,r:e,startAngle:n,endAngle:n+Math.PI/2},{...s,r:e*2,startAngle:n+Math.PI/2,endAngle:n+Math.PI}];let c=t[0].x-e,u=t[0].y-e;for(let d=2;d<9;d++){const v=l[d-2].r+l[d-1].r;let h=0;switch(d%4){case 0:{h=n,c-=l[d-2].r;break}case 1:{h=n+Math.PI/2,u-=l[d-2].r;break}case 2:{h=n+Math.PI,c+=l[d-2].r;break}case 3:{h=n+Math.PI/2*3,u+=l[d-2].r;break}}const f=h+Math.PI/2,p=Ur({x:c,y:u},t[0],n);l.push({...p,r:v,startAngle:h,endAngle:f})}return[{type:"arc",attrs:l},{type:"line",attrs:ei(t,r)}]}return[]}},O0={name:"fibonacciSpeedResistanceFan",totalStep:3,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:({coordinates:t,bounding:r})=>{const e=[];let a=[];const i=[];if(t.length>1){const n=t[1].x>t[0].x?-38:4,o=t[1].y>t[0].y?-2:20,s=t[1].x-t[0].x,l=t[1].y-t[0].y;[1,.75,.618,.5,.382,.25,0].forEach(c=>{const u=t[1].x-s*c,d=t[1].y-l*c;e.push({coordinates:[{x:u,y:t[0].y},{x:u,y:t[1].y}]}),e.push({coordinates:[{x:t[0].x,y:d},{x:t[1].x,y:d}]}),a=a.concat(ei([t[0],{x:u,y:t[1].y}],r)),a=a.concat(ei([t[0],{x:t[1].x,y:d}],r)),i.unshift({x:t[0].x+n,y:d+10,text:`${c.toFixed(3)}`}),i.unshift({x:u-18,y:t[0].y+o,text:`${c.toFixed(3)}`})})}return[{type:"line",attrs:e},{type:"line",attrs:a},{type:"text",ignoreEvent:!0,attrs:i}]}},V0={name:"fibonacciExtension",totalStep:4,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:({coordinates:t,overlay:r})=>{const e=[],a=[];if(t.length>2){const i=r.points,n=i[1].value-i[0].value,o=t[1].y-t[0].y,s=[0,.236,.382,.5,.618,.786,1],l=t[2].x>t[1].x?t[1].x:t[2].x;s.forEach(c=>{const u=t[2].y+o*c,d=(i[2].value+n*c).toFixed(precision.price);e.push({coordinates:[{x:t[1].x,y:u},{x:t[2].x,y:u}]}),a.push({x:l,y:u,text:`${d} (${(c*100).toFixed(1)}%)`,baseline:"bottom"})})}return[{type:"line",attrs:{coordinates:t},styles:{style:"dashed"}},{type:"line",attrs:e},{type:"text",ignoreEvent:!0,attrs:a}]}},z0={name:"gannBox",totalStep:3,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,styles:{polygon:{color:"rgba(22, 119, 255, 0.15)"}},createPointFigures:({coordinates:t})=>{if(t.length>1){const r=(t[1].y-t[0].y)/4,e=t[1].x-t[0].x,a=[{coordinates:[t[0],{x:t[1].x,y:t[1].y-r}]},{coordinates:[t[0],{x:t[1].x,y:t[1].y-r*2}]},{coordinates:[{x:t[0].x,y:t[1].y},{x:t[1].x,y:t[0].y+r}]},{coordinates:[{x:t[0].x,y:t[1].y},{x:t[1].x,y:t[0].y+r*2}]},{coordinates:[{...t[0]},{x:t[0].x+e*.236,y:t[1].y}]},{coordinates:[{...t[0]},{x:t[0].x+e*.5,y:t[1].y}]},{coordinates:[{x:t[0].x,y:t[1].y},{x:t[0].x+e*.236,y:t[0].y}]},{coordinates:[{x:t[0].x,y:t[1].y},{x:t[0].x+e*.5,y:t[0].y}]}],i=[{coordinates:[t[0],t[1]]},{coordinates:[{x:t[0].x,y:t[1].y},{x:t[1].x,y:t[0].y}]}];return[{type:"line",attrs:[{coordinates:[t[0],{x:t[1].x,y:t[0].y}]},{coordinates:[{x:t[1].x,y:t[0].y},t[1]]},{coordinates:[t[1],{x:t[0].x,y:t[1].y}]},{coordinates:[{x:t[0].x,y:t[1].y},t[0]]}]},{type:"polygon",ignoreEvent:!0,attrs:{coordinates:[t[0],{x:t[1].x,y:t[0].y},t[1],{x:t[0].x,y:t[1].y}]},styles:{style:"fill"}},{type:"line",attrs:a,styles:{style:"dashed"}},{type:"line",attrs:i}]}return[]}},$0={name:"threeWaves",totalStep:5,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:({coordinates:t})=>{const r=t.map((e,a)=>({...e,text:`(${a})`,baseline:"bottom"}));return[{type:"line",attrs:{coordinates:t}},{type:"text",ignoreEvent:!0,attrs:r}]}},W0={name:"fiveWaves",totalStep:7,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:({coordinates:t})=>{const r=t.map((e,a)=>({...e,text:`(${a})`,baseline:"bottom"}));return[{type:"line",attrs:{coordinates:t}},{type:"text",ignoreEvent:!0,attrs:r}]}},Y0={name:"eightWaves",totalStep:10,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:({coordinates:t})=>{const r=t.map((e,a)=>({...e,text:`(${a})`,baseline:"bottom"}));return[{type:"line",attrs:{coordinates:t}},{type:"text",ignoreEvent:!0,attrs:r}]}},Z0={name:"anyWaves",totalStep:Number.MAX_SAFE_INTEGER,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:({coordinates:t})=>{const r=t.map((e,a)=>({...e,text:`(${a})`,baseline:"bottom"}));return[{type:"line",attrs:{coordinates:t}},{type:"text",ignoreEvent:!0,attrs:r}]}},H0={name:"abcd",totalStep:5,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:({coordinates:t})=>{let r=[],e=[];const a=["A","B","C","D"],i=t.map((n,o)=>({...n,baseline:"bottom",text:`(${a[o]})`}));return t.length>2&&(r=[t[0],t[2]],t.length>3&&(e=[t[1],t[3]])),[{type:"line",attrs:{coordinates:t}},{type:"line",attrs:[{coordinates:r},{coordinates:e}],styles:{style:"dashed"}},{type:"text",ignoreEvent:!0,attrs:i}]}},j0={name:"xabcd",totalStep:6,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,styles:{polygon:{color:"rgba(22, 119, 255, 0.15)"}},createPointFigures:({coordinates:t,overlay:r})=>{const e=[],a=[],i=["X","A","B","C","D"],n=t.map((o,s)=>({...o,baseline:"bottom",text:`(${i[s]})`}));return t.length>2&&(e.push({coordinates:[t[0],t[2]]}),a.push({coordinates:[t[0],t[1],t[2]]}),t.length>3&&(e.push({coordinates:[t[1],t[3]]}),t.length>4&&(e.push({coordinates:[t[2],t[4]]}),a.push({coordinates:[t[2],t[3],t[4]]})))),[{type:"line",attrs:{coordinates:t}},{type:"line",attrs:e,styles:{style:"dashed"}},{type:"polygon",ignoreEvent:!0,attrs:a},{type:"text",ignoreEvent:!0,attrs:n}]}},X0=[I0,A0,D0,F0,P0,R0,B0,N0,O0,V0,z0,$0,W0,Y0,Z0,H0,j0],K0=(t,r)=>t===r,ri=Symbol("solid-proxy"),G0=typeof Proxy=="function",U0=Symbol("solid-track"),ba={equals:K0};let Oo=Yo;const lr=1,xa=2,Vo={owned:null,cleanups:null,context:null,owner:null},Ha={};var te=null;let ja=null,Jt=null,Se=null,ir=null,Fa=0;function va(t,r){const e=Jt,a=te,i=t.length===0,n=r===void 0?a:r,o=i?Vo:{owned:null,cleanups:null,context:n?n.context:null,owner:n},s=i?t:()=>t(()=>Xe(()=>Jr(o)));te=o,Jt=null;try{return or(s,!0)}finally{Jt=e,te=a}}function Tt(t,r){r=r?Object.assign({},ba,r):ba;const e={value:t,observers:null,observerSlots:null,comparator:r.equals||void 0},a=i=>(typeof i=="function"&&(i=i(e.value)),Wo(e,i));return[$o.bind(e),a]}function Q0(t,r,e){const a=Ra(t,r,!0,lr);Dr(a)}function ve(t,r,e){const a=Ra(t,r,!1,lr);Dr(a)}function er(t,r,e){Oo=nc;const a=Ra(t,r,!1,lr);a.user=!0,ir?ir.push(a):Dr(a)}function je(t,r,e){e=e?Object.assign({},ba,e):ba;const a=Ra(t,r,!0,0);return a.observers=null,a.observerSlots=null,a.comparator=e.equals||void 0,Dr(a),$o.bind(a)}function q0(t){return t&&typeof t=="object"&&"then"in t}function J0(t,r,e){let a,i,n;typeof r=="function"?(a=t,i=r,n={}):(a=!0,i=t,n=r||{});let o=null,s=Ha,l=!1,c="initialValue"in n,u=typeof a=="function"&&je(a);const d=new Set,[v,h]=(n.storage||Tt)(n.initialValue),[f,p]=Tt(void 0),[g,m]=Tt(void 0,{equals:!1}),[b,x]=Tt(c?"ready":"unresolved");function L(C,y,T,I){return o===C&&(o=null,I!==void 0&&(c=!0),(C===s||y===s)&&n.onHydrated&&queueMicrotask(()=>n.onHydrated(I,{value:y})),s=Ha,_(y,T)),y}function _(C,y){or(()=>{y===void 0&&h(()=>C),x(y!==void 0?"errored":c?"ready":"unresolved"),p(y);for(const T of d.keys())T.decrement();d.clear()},!1)}function M(){const C=rc,y=v(),T=f();if(T!==void 0&&!o)throw T;return Jt&&Jt.user,y}function w(C=!0){if(C!==!1&&l)return;l=!1;const y=u?u():a;if(y==null||y===!1){L(o,Xe(v));return}let T;const I=s!==Ha?s:Xe(()=>{try{return i(y,{value:v(),refetching:C})}catch(E){T=E}});if(T!==void 0){L(o,void 0,fa(T),y);return}else if(!q0(I))return L(o,I,void 0,y),I;return o=I,"v"in I?(I.s===1?L(o,I.v,void 0,y):L(o,void 0,fa(I.v),y),I):(l=!0,queueMicrotask(()=>l=!1),or(()=>{x(c?"refreshing":"pending"),m()},!1),I.then(E=>L(I,E,void 0,y),E=>L(I,void 0,fa(E),y)))}Object.defineProperties(M,{state:{get:()=>b()},error:{get:()=>f()},loading:{get(){const C=b();return C==="pending"||C==="refreshing"}},latest:{get(){if(!c)return M();const C=f();if(C&&!o)throw C;return v()}}});let k=te;return u?Q0(()=>(k=te,w(!1))):w(!1),[M,{refetch:C=>tc(k,()=>w(C)),mutate:h}]}function Xe(t){if(Jt===null)return t();const r=Jt;Jt=null;try{return t()}finally{Jt=r}}function zo(t){er(()=>Xe(t))}function wa(t){return te===null||(te.cleanups===null?te.cleanups=[t]:te.cleanups.push(t)),t}function tc(t,r){const e=te,a=Jt;te=t,Jt=null;try{return or(r,!0)}catch(i){Ci(i)}finally{te=e,Jt=a}}function ec(t){const r=Jt,e=te;return Promise.resolve().then(()=>{Jt=r,te=e;let a;return or(t,!1),Jt=te=null,a?a.done:void 0})}let rc;function $o(){if(this.sources&&this.state)if(this.state===lr)Dr(this);else{const t=Se;Se=null,or(()=>ka(this),!1),Se=t}if(Jt){const t=this.observers?this.observers.length:0;Jt.sources?(Jt.sources.push(this),Jt.sourceSlots.push(t)):(Jt.sources=[this],Jt.sourceSlots=[t]),this.observers?(this.observers.push(Jt),this.observerSlots.push(Jt.sources.length-1)):(this.observers=[Jt],this.observerSlots=[Jt.sources.length-1])}return this.value}function Wo(t,r,e){let a=t.value;return(!t.comparator||!t.comparator(a,r))&&(t.value=r,t.observers&&t.observers.length&&or(()=>{for(let i=0;i<t.observers.length;i+=1){const n=t.observers[i],o=ja&&ja.running;o&&ja.disposed.has(n),(o?!n.tState:!n.state)&&(n.pure?Se.push(n):ir.push(n),n.observers&&Zo(n)),o||(n.state=lr)}if(Se.length>1e6)throw Se=[],new Error},!1)),r}function Dr(t){if(!t.fn)return;Jr(t);const r=Fa;ac(t,t.value,r)}function ac(t,r,e){let a;const i=te,n=Jt;Jt=te=t;try{a=t.fn(r)}catch(o){return t.pure&&(t.state=lr,t.owned&&t.owned.forEach(Jr),t.owned=null),t.updatedAt=e+1,Ci(o)}finally{Jt=n,te=i}(!t.updatedAt||t.updatedAt<=e)&&(t.updatedAt!=null&&"observers"in t?Wo(t,a):t.value=a,t.updatedAt=e)}function Ra(t,r,e,a=lr,i){const n={fn:t,state:a,updatedAt:null,owned:null,sources:null,sourceSlots:null,cleanups:null,value:r,owner:te,context:te?te.context:null,pure:e};return te===null||te!==Vo&&(te.owned?te.owned.push(n):te.owned=[n]),n}function La(t){if(t.state===0)return;if(t.state===xa)return ka(t);if(t.suspense&&Xe(t.suspense.inFallback))return t.suspense.effects.push(t);const r=[t];for(;(t=t.owner)&&(!t.updatedAt||t.updatedAt<Fa);)t.state&&r.push(t);for(let e=r.length-1;e>=0;e--)if(t=r[e],t.state===lr)Dr(t);else if(t.state===xa){const a=Se;Se=null,or(()=>ka(t,r[0]),!1),Se=a}}function or(t,r){if(Se)return t();let e=!1;r||(Se=[]),ir?e=!0:ir=[],Fa++;try{const a=t();return ic(e),a}catch(a){e||(ir=null),Se=null,Ci(a)}}function ic(t){if(Se&&(Yo(Se),Se=null),t)return;const r=ir;ir=null,r.length&&or(()=>Oo(r),!1)}function Yo(t){for(let r=0;r<t.length;r++)La(t[r])}function nc(t){let r,e=0;for(r=0;r<t.length;r++){const a=t[r];a.user?t[e++]=a:La(a)}for(r=0;r<e;r++)La(t[r])}function ka(t,r){t.state=0;for(let e=0;e<t.sources.length;e+=1){const a=t.sources[e];if(a.sources){const i=a.state;i===lr?a!==r&&(!a.updatedAt||a.updatedAt<Fa)&&La(a):i===xa&&ka(a,r)}}}function Zo(t){for(let r=0;r<t.observers.length;r+=1){const e=t.observers[r];e.state||(e.state=xa,e.pure?Se.push(e):ir.push(e),e.observers&&Zo(e))}}function Jr(t){let r;if(t.sources)for(;t.sources.length;){const e=t.sources.pop(),a=t.sourceSlots.pop(),i=e.observers;if(i&&i.length){const n=i.pop(),o=e.observerSlots.pop();a<i.length&&(n.sourceSlots[o]=a,i[a]=n,e.observerSlots[a]=o)}}if(t.tOwned){for(r=t.tOwned.length-1;r>=0;r--)Jr(t.tOwned[r]);delete t.tOwned}if(t.owned){for(r=t.owned.length-1;r>=0;r--)Jr(t.owned[r]);t.owned=null}if(t.cleanups){for(r=t.cleanups.length-1;r>=0;r--)t.cleanups[r]();t.cleanups=null}t.state=0}function fa(t){return t instanceof Error?t:new Error(typeof t=="string"?t:"Unknown error",{cause:t})}function Ci(t,r=te){throw fa(t)}const oc=Symbol("fallback");function sn(t){for(let r=0;r<t.length;r++)t[r]()}function sc(t,r,e={}){let a=[],i=[],n=[],o=0,s=r.length>1?[]:null;return wa(()=>sn(n)),()=>{let l=t()||[],c=l.length,u,d;return l[U0],Xe(()=>{let h,f,p,g,m,b,x,L,_;if(c===0)o!==0&&(sn(n),n=[],a=[],i=[],o=0,s&&(s=[])),e.fallback&&(a=[oc],i[0]=va(M=>(n[0]=M,e.fallback())),o=1);else if(o===0){for(i=new Array(c),d=0;d<c;d++)a[d]=l[d],i[d]=va(v);o=c}else{for(p=new Array(c),g=new Array(c),s&&(m=new Array(c)),b=0,x=Math.min(o,c);b<x&&a[b]===l[b];b++);for(x=o-1,L=c-1;x>=b&&L>=b&&a[x]===l[L];x--,L--)p[L]=i[x],g[L]=n[x],s&&(m[L]=s[x]);for(h=new Map,f=new Array(L+1),d=L;d>=b;d--)_=l[d],u=h.get(_),f[d]=u===void 0?-1:u,h.set(_,d);for(u=b;u<=x;u++)_=a[u],d=h.get(_),d!==void 0&&d!==-1?(p[d]=i[u],g[d]=n[u],s&&(m[d]=s[u]),d=f[d],h.set(_,d)):n[u]();for(d=b;d<c;d++)d in p?(i[d]=p[d],n[d]=g[d],s&&(s[d]=m[d],s[d](d))):i[d]=va(v);i=i.slice(0,o=c),a=l.slice(0)}return i});function v(h){if(n[d]=h,s){const[f,p]=Tt(d);return s[d]=p,r(l[d],f)}return r(l[d])}}}function st(t,r){return Xe(()=>t(r||{}))}function ia(){return!0}const lc={get(t,r,e){return r===ri?e:t.get(r)},has(t,r){return r===ri?!0:t.has(r)},set:ia,deleteProperty:ia,getOwnPropertyDescriptor(t,r){return{configurable:!0,enumerable:!0,get(){return t.get(r)},set:ia,deleteProperty:ia}},ownKeys(t){return t.keys()}};function Xa(t){return(t=typeof t=="function"?t():t)?t:{}}function cc(){for(let t=0,r=this.length;t<r;++t){const e=this[t]();if(e!==void 0)return e}}function Ho(...t){let r=!1;for(let o=0;o<t.length;o++){const s=t[o];r=r||!!s&&ri in s,t[o]=typeof s=="function"?(r=!0,je(s)):s}if(G0&&r)return new Proxy({get(o){for(let s=t.length-1;s>=0;s--){const l=Xa(t[s])[o];if(l!==void 0)return l}},has(o){for(let s=t.length-1;s>=0;s--)if(o in Xa(t[s]))return!0;return!1},keys(){const o=[];for(let s=0;s<t.length;s++)o.push(...Object.keys(Xa(t[s])));return[...new Set(o)]}},lc);const e={},a=Object.create(null);for(let o=t.length-1;o>=0;o--){const s=t[o];if(!s)continue;const l=Object.getOwnPropertyNames(s);for(let c=l.length-1;c>=0;c--){const u=l[c];if(u==="__proto__"||u==="constructor")continue;const d=Object.getOwnPropertyDescriptor(s,u);if(!a[u])a[u]=d.get?{enumerable:!0,configurable:!0,get:cc.bind(e[u]=[d.get.bind(s)])}:d.value!==void 0?d:void 0;else{const v=e[u];v&&(d.get?v.push(d.get.bind(s)):d.value!==void 0&&v.push(()=>d.value))}}}const i={},n=Object.keys(a);for(let o=n.length-1;o>=0;o--){const s=n[o],l=a[s];l&&l.get?Object.defineProperty(i,s,l):i[s]=l?l.value:void 0}return i}const uc=t=>`Stale read from <${t}>.`;function dc(t){const r="fallback"in t&&{fallback:()=>t.fallback};return je(sc(()=>t.each,t.children,r||void 0))}function be(t){const r=t.keyed,e=je(()=>t.when,void 0,void 0),a=r?e:je(e,void 0,{equals:(i,n)=>!i==!n});return je(()=>{const i=a();if(i){const n=t.children;return typeof n=="function"&&n.length>0?Xe(()=>n(r?i:()=>{if(!Xe(a))throw uc("Show");return e()})):n}return t.fallback},void 0,void 0)}const Ie=t=>je(()=>t());function hc(t,r,e){let a=e.length,i=r.length,n=a,o=0,s=0,l=r[i-1].nextSibling,c=null;for(;o<i||s<n;){if(r[o]===e[s]){o++,s++;continue}for(;r[i-1]===e[n-1];)i--,n--;if(i===o){const u=n<a?s?e[s-1].nextSibling:e[n-s]:l;for(;s<n;)t.insertBefore(e[s++],u)}else if(n===s)for(;o<i;)(!c||!c.has(r[o]))&&r[o].remove(),o++;else if(r[o]===e[n-1]&&e[s]===r[i-1]){const u=r[--i].nextSibling;t.insertBefore(e[s++],r[o++].nextSibling),t.insertBefore(e[--n],u),r[i]=e[n]}else{if(!c){c=new Map;let d=s;for(;d<n;)c.set(e[d],d++)}const u=c.get(r[o]);if(u!=null)if(s<u&&u<n){let d=o,v=1,h;for(;++d<i&&d<n&&!((h=c.get(r[d]))==null||h!==u+v);)v++;if(v>u-s){const f=r[o];for(;s<u;)t.insertBefore(e[s++],f)}else t.replaceChild(e[s++],r[o++])}else o++;else r[o++].remove()}}}const ln="_$DX_DELEGATE";function vc(t,r,e,a={}){let i;return va(n=>{i=n,r===document?t():ct(r,t(),r.firstChild?null:void 0,e)},a.owner),()=>{i(),r.textContent=""}}function X(t,r,e,a){let i;const n=()=>{const s=document.createElement("template");return s.innerHTML=t,s.content.firstChild},o=()=>(i||(i=n())).cloneNode(!0);return o.cloneNode=o,o}function Ge(t,r=window.document){const e=r[ln]||(r[ln]=new Set);for(let a=0,i=t.length;a<i;a++){const n=t[a];e.has(n)||(e.add(n),r.addEventListener(n,fc))}}function Fe(t,r,e){e==null?t.removeAttribute(r):t.setAttribute(r,e)}function Cr(t,r){r==null?t.removeAttribute("class"):t.className=r}function tr(t,r,e,a){Array.isArray(e)?(t[`$$${r}`]=e[0],t[`$$${r}Data`]=e[1]):t[`$$${r}`]=e}function Pr(t,r,e){if(!r)return e?Fe(t,"style"):r;const a=t.style;if(typeof r=="string")return a.cssText=r;typeof e=="string"&&(a.cssText=e=void 0),e||(e={}),r||(r={});let i,n;for(n in e)r[n]==null&&a.removeProperty(n),delete e[n];for(n in r)i=r[n],i!==e[n]&&(a.setProperty(n,i),e[n]=i);return e}function bi(t,r,e){return Xe(()=>t(r,e))}function ct(t,r,e,a){if(e!==void 0&&!a&&(a=[]),typeof r!="function")return Sa(t,r,a,e);ve(i=>Sa(t,r(),i,e),a)}function fc(t){let r=t.target;const e=`$$${t.type}`,a=t.target,i=t.currentTarget,n=l=>Object.defineProperty(t,"target",{configurable:!0,value:l}),o=()=>{const l=r[e];if(l&&!r.disabled){const c=r[`${e}Data`];if(c!==void 0?l.call(r,c,t):l.call(r,t),t.cancelBubble)return}return r.host&&typeof r.host!="string"&&!r.host._$host&&r.contains(t.target)&&n(r.host),!0},s=()=>{for(;o()&&(r=r._$host||r.parentNode||r.host););};if(Object.defineProperty(t,"currentTarget",{configurable:!0,get(){return r||document}}),t.composedPath){const l=t.composedPath();n(l[0]);for(let c=0;c<l.length-2&&(r=l[c],!!o());c++){if(r._$host){r=r._$host,s();break}if(r.parentNode===i)break}}else s();n(a)}function Sa(t,r,e,a,i){for(;typeof e=="function";)e=e();if(r===e)return e;const n=typeof r,o=a!==void 0;if(t=o&&e[0]&&e[0].parentNode||t,n==="string"||n==="number"){if(n==="number"&&(r=r.toString(),r===e))return e;if(o){let s=e[0];s&&s.nodeType===3?s.data!==r&&(s.data=r):s=document.createTextNode(r),e=Sr(t,e,a,s)}else e!==""&&typeof e=="string"?e=t.firstChild.data=r:e=t.textContent=r}else if(r==null||n==="boolean")e=Sr(t,e,a);else{if(n==="function")return ve(()=>{let s=r();for(;typeof s=="function";)s=s();e=Sa(t,s,e,a)}),()=>e;if(Array.isArray(r)){const s=[],l=e&&Array.isArray(e);if(ai(s,r,e,i))return ve(()=>e=Sa(t,s,e,a,!0)),()=>e;if(s.length===0){if(e=Sr(t,e,a),o)return e}else l?e.length===0?cn(t,s,a):hc(t,e,s):(e&&Sr(t),cn(t,s));e=s}else if(r.nodeType){if(Array.isArray(e)){if(o)return e=Sr(t,e,a,r);Sr(t,e,null,r)}else e==null||e===""||!t.firstChild?t.appendChild(r):t.replaceChild(r,t.firstChild);e=r}}return e}function ai(t,r,e,a){let i=!1;for(let n=0,o=r.length;n<o;n++){let s=r[n],l=e&&e[t.length],c;if(!(s==null||s===!0||s===!1))if((c=typeof s)=="object"&&s.nodeType)t.push(s);else if(Array.isArray(s))i=ai(t,s,l)||i;else if(c==="function")if(a){for(;typeof s=="function";)s=s();i=ai(t,Array.isArray(s)?s:[s],Array.isArray(l)?l:[l])||i}else t.push(s),i=!0;else{const u=String(s);l&&l.nodeType===3&&l.data===u?t.push(l):t.push(document.createTextNode(u))}}return i}function cn(t,r,e=null){for(let a=0,i=r.length;a<i;a++)t.insertBefore(r[a],e)}function Sr(t,r,e,a){if(e===void 0)return t.textContent="";const i=a||document.createTextNode("");if(r.length){let n=!1;for(let o=r.length-1;o>=0;o--){const s=r[o];if(i!==s){const l=s.parentNode===t;!n&&!o?l?t.replaceChild(i,s):t.insertBefore(i,e):l&&s.remove()}else n=!0}}else t.insertBefore(i,e);return[i]}var na=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function jo(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var pc=typeof na=="object"&&na&&na.Object===Object&&na,Xo=pc,gc=Xo,mc=typeof self=="object"&&self&&self.Object===Object&&self,yc=gc||mc||Function("return this")(),Ue=yc,_c=Ue,Cc=_c.Symbol,Ba=Cc,un=Ba,Ko=Object.prototype,bc=Ko.hasOwnProperty,xc=Ko.toString,Kr=un?un.toStringTag:void 0;function wc(t){var r=bc.call(t,Kr),e=t[Kr];try{t[Kr]=void 0;var a=!0}catch{}var i=xc.call(t);return a&&(r?t[Kr]=e:delete t[Kr]),i}var Lc=wc,kc=Object.prototype,Sc=kc.toString;function Mc(t){return Sc.call(t)}var Tc=Mc,dn=Ba,Ec=Lc,Ic=Tc,Ac="[object Null]",Dc="[object Undefined]",hn=dn?dn.toStringTag:void 0;function Pc(t){return t==null?t===void 0?Dc:Ac:hn&&hn in Object(t)?Ec(t):Ic(t)}var ea=Pc;function Fc(t){var r=typeof t;return t!=null&&(r=="object"||r=="function")}var Fr=Fc,Rc=ea,Bc=Fr,Nc="[object AsyncFunction]",Oc="[object Function]",Vc="[object GeneratorFunction]",zc="[object Proxy]";function $c(t){if(!Bc(t))return!1;var r=Rc(t);return r==Oc||r==Vc||r==Nc||r==zc}var Go=$c,Wc=Ue,Yc=Wc["__core-js_shared__"],Zc=Yc,Ka=Zc,vn=function(){var t=/[^.]+$/.exec(Ka&&Ka.keys&&Ka.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}();function Hc(t){return!!vn&&vn in t}var jc=Hc,Xc=Function.prototype,Kc=Xc.toString;function Gc(t){if(t!=null){try{return Kc.call(t)}catch{}try{return t+""}catch{}}return""}var Uo=Gc,Uc=Go,Qc=jc,qc=Fr,Jc=Uo,tu=/[\\^$.*+?()[\]{}|]/g,eu=/^\[object .+?Constructor\]$/,ru=Function.prototype,au=Object.prototype,iu=ru.toString,nu=au.hasOwnProperty,ou=RegExp("^"+iu.call(nu).replace(tu,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function su(t){if(!qc(t)||Qc(t))return!1;var r=Uc(t)?ou:eu;return r.test(Jc(t))}var lu=su;function cu(t,r){return t==null?void 0:t[r]}var uu=cu,du=lu,hu=uu;function vu(t,r){var e=hu(t,r);return du(e)?e:void 0}var br=vu,fu=br,pu=function(){try{var t=fu(Object,"defineProperty");return t({},"",{}),t}catch{}}(),gu=pu,fn=gu;function mu(t,r,e){r=="__proto__"&&fn?fn(t,r,{configurable:!0,enumerable:!0,value:e,writable:!0}):t[r]=e}var Qo=mu;function yu(t,r){return t===r||t!==t&&r!==r}var qo=yu,_u=Qo,Cu=qo,bu=Object.prototype,xu=bu.hasOwnProperty;function wu(t,r,e){var a=t[r];(!(xu.call(t,r)&&Cu(a,e))||e===void 0&&!(r in t))&&_u(t,r,e)}var xi=wu,Lu=Array.isArray,Rr=Lu;function ku(t){return t!=null&&typeof t=="object"}var Br=ku,Su=ea,Mu=Br,Tu="[object Symbol]";function Eu(t){return typeof t=="symbol"||Mu(t)&&Su(t)==Tu}var wi=Eu,Iu=Rr,Au=wi,Du=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Pu=/^\w*$/;function Fu(t,r){if(Iu(t))return!1;var e=typeof t;return e=="number"||e=="symbol"||e=="boolean"||t==null||Au(t)?!0:Pu.test(t)||!Du.test(t)||r!=null&&t in Object(r)}var Ru=Fu,Bu=br,Nu=Bu(Object,"create"),Na=Nu,pn=Na;function Ou(){this.__data__=pn?pn(null):{},this.size=0}var Vu=Ou;function zu(t){var r=this.has(t)&&delete this.__data__[t];return this.size-=r?1:0,r}var $u=zu,Wu=Na,Yu="__lodash_hash_undefined__",Zu=Object.prototype,Hu=Zu.hasOwnProperty;function ju(t){var r=this.__data__;if(Wu){var e=r[t];return e===Yu?void 0:e}return Hu.call(r,t)?r[t]:void 0}var Xu=ju,Ku=Na,Gu=Object.prototype,Uu=Gu.hasOwnProperty;function Qu(t){var r=this.__data__;return Ku?r[t]!==void 0:Uu.call(r,t)}var qu=Qu,Ju=Na,t9="__lodash_hash_undefined__";function e9(t,r){var e=this.__data__;return this.size+=this.has(t)?0:1,e[t]=Ju&&r===void 0?t9:r,this}var r9=e9,a9=Vu,i9=$u,n9=Xu,o9=qu,s9=r9;function Nr(t){var r=-1,e=t==null?0:t.length;for(this.clear();++r<e;){var a=t[r];this.set(a[0],a[1])}}Nr.prototype.clear=a9;Nr.prototype.delete=i9;Nr.prototype.get=n9;Nr.prototype.has=o9;Nr.prototype.set=s9;var l9=Nr;function c9(){this.__data__=[],this.size=0}var u9=c9,d9=qo;function h9(t,r){for(var e=t.length;e--;)if(d9(t[e][0],r))return e;return-1}var Oa=h9,v9=Oa,f9=Array.prototype,p9=f9.splice;function g9(t){var r=this.__data__,e=v9(r,t);if(e<0)return!1;var a=r.length-1;return e==a?r.pop():p9.call(r,e,1),--this.size,!0}var m9=g9,y9=Oa;function _9(t){var r=this.__data__,e=y9(r,t);return e<0?void 0:r[e][1]}var C9=_9,b9=Oa;function x9(t){return b9(this.__data__,t)>-1}var w9=x9,L9=Oa;function k9(t,r){var e=this.__data__,a=L9(e,t);return a<0?(++this.size,e.push([t,r])):e[a][1]=r,this}var S9=k9,M9=u9,T9=m9,E9=C9,I9=w9,A9=S9;function Or(t){var r=-1,e=t==null?0:t.length;for(this.clear();++r<e;){var a=t[r];this.set(a[0],a[1])}}Or.prototype.clear=M9;Or.prototype.delete=T9;Or.prototype.get=E9;Or.prototype.has=I9;Or.prototype.set=A9;var Va=Or,D9=br,P9=Ue,F9=D9(P9,"Map"),Li=F9,gn=l9,R9=Va,B9=Li;function N9(){this.size=0,this.__data__={hash:new gn,map:new(B9||R9),string:new gn}}var O9=N9;function V9(t){var r=typeof t;return r=="string"||r=="number"||r=="symbol"||r=="boolean"?t!=="__proto__":t===null}var z9=V9,$9=z9;function W9(t,r){var e=t.__data__;return $9(r)?e[typeof r=="string"?"string":"hash"]:e.map}var za=W9,Y9=za;function Z9(t){var r=Y9(this,t).delete(t);return this.size-=r?1:0,r}var H9=Z9,j9=za;function X9(t){return j9(this,t).get(t)}var K9=X9,G9=za;function U9(t){return G9(this,t).has(t)}var Q9=U9,q9=za;function J9(t,r){var e=q9(this,t),a=e.size;return e.set(t,r),this.size+=e.size==a?0:1,this}var t2=J9,e2=O9,r2=H9,a2=K9,i2=Q9,n2=t2;function Vr(t){var r=-1,e=t==null?0:t.length;for(this.clear();++r<e;){var a=t[r];this.set(a[0],a[1])}}Vr.prototype.clear=e2;Vr.prototype.delete=r2;Vr.prototype.get=a2;Vr.prototype.has=i2;Vr.prototype.set=n2;var Jo=Vr,ts=Jo,o2="Expected a function";function ki(t,r){if(typeof t!="function"||r!=null&&typeof r!="function")throw new TypeError(o2);var e=function(){var a=arguments,i=r?r.apply(this,a):a[0],n=e.cache;if(n.has(i))return n.get(i);var o=t.apply(this,a);return e.cache=n.set(i,o)||n,o};return e.cache=new(ki.Cache||ts),e}ki.Cache=ts;var s2=ki,l2=s2,c2=500;function u2(t){var r=l2(t,function(a){return e.size===c2&&e.clear(),a}),e=r.cache;return r}var d2=u2,h2=d2,v2=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,f2=/\\(\\)?/g,p2=h2(function(t){var r=[];return t.charCodeAt(0)===46&&r.push(""),t.replace(v2,function(e,a,i,n){r.push(i?n.replace(f2,"$1"):a||e)}),r}),g2=p2;function m2(t,r){for(var e=-1,a=t==null?0:t.length,i=Array(a);++e<a;)i[e]=r(t[e],e,t);return i}var y2=m2,mn=Ba,_2=y2,C2=Rr,b2=wi,yn=mn?mn.prototype:void 0,_n=yn?yn.toString:void 0;function es(t){if(typeof t=="string")return t;if(C2(t))return _2(t,es)+"";if(b2(t))return _n?_n.call(t):"";var r=t+"";return r=="0"&&1/t==-1/0?"-0":r}var x2=es,w2=x2;function L2(t){return t==null?"":w2(t)}var k2=L2,S2=Rr,M2=Ru,T2=g2,E2=k2;function I2(t,r){return S2(t)?t:M2(t,r)?[t]:T2(E2(t))}var A2=I2,D2=9007199254740991,P2=/^(?:0|[1-9]\d*)$/;function F2(t,r){var e=typeof t;return r=r??D2,!!r&&(e=="number"||e!="symbol"&&P2.test(t))&&t>-1&&t%1==0&&t<r}var rs=F2,R2=wi;function B2(t){if(typeof t=="string"||R2(t))return t;var r=t+"";return r=="0"&&1/t==-1/0?"-0":r}var N2=B2,O2=xi,V2=A2,z2=rs,Cn=Fr,$2=N2;function W2(t,r,e,a){if(!Cn(t))return t;r=V2(r,t);for(var i=-1,n=r.length,o=n-1,s=t;s!=null&&++i<n;){var l=$2(r[i]),c=e;if(l==="__proto__"||l==="constructor"||l==="prototype")return t;if(i!=o){var u=s[l];c=a?a(u,l,s):void 0,c===void 0&&(c=Cn(u)?u:z2(r[i+1])?[]:{})}O2(s,l,c),s=s[l]}return t}var Y2=W2,Z2=Y2;function H2(t,r,e){return t==null?t:Z2(t,r,e)}var j2=H2;const ii=jo(j2);var X2=Va;function K2(){this.__data__=new X2,this.size=0}var G2=K2;function U2(t){var r=this.__data__,e=r.delete(t);return this.size=r.size,e}var Q2=U2;function q2(t){return this.__data__.get(t)}var J2=q2;function t5(t){return this.__data__.has(t)}var e5=t5,r5=Va,a5=Li,i5=Jo,n5=200;function o5(t,r){var e=this.__data__;if(e instanceof r5){var a=e.__data__;if(!a5||a.length<n5-1)return a.push([t,r]),this.size=++e.size,this;e=this.__data__=new i5(a)}return e.set(t,r),this.size=e.size,this}var s5=o5,l5=Va,c5=G2,u5=Q2,d5=J2,h5=e5,v5=s5;function zr(t){var r=this.__data__=new l5(t);this.size=r.size}zr.prototype.clear=c5;zr.prototype.delete=u5;zr.prototype.get=d5;zr.prototype.has=h5;zr.prototype.set=v5;var f5=zr;function p5(t,r){for(var e=-1,a=t==null?0:t.length;++e<a&&r(t[e],e,t)!==!1;);return t}var g5=p5,m5=xi,y5=Qo;function _5(t,r,e,a){var i=!e;e||(e={});for(var n=-1,o=r.length;++n<o;){var s=r[n],l=a?a(e[s],t[s],s,e,t):void 0;l===void 0&&(l=t[s]),i?y5(e,s,l):m5(e,s,l)}return e}var $a=_5;function C5(t,r){for(var e=-1,a=Array(t);++e<t;)a[e]=r(e);return a}var b5=C5,x5=ea,w5=Br,L5="[object Arguments]";function k5(t){return w5(t)&&x5(t)==L5}var S5=k5,bn=S5,M5=Br,as=Object.prototype,T5=as.hasOwnProperty,E5=as.propertyIsEnumerable,I5=bn(function(){return arguments}())?bn:function(t){return M5(t)&&T5.call(t,"callee")&&!E5.call(t,"callee")},A5=I5,Ma={exports:{}};function D5(){return!1}var P5=D5;Ma.exports;(function(t,r){var e=Ue,a=P5,i=r&&!r.nodeType&&r,n=i&&!0&&t&&!t.nodeType&&t,o=n&&n.exports===i,s=o?e.Buffer:void 0,l=s?s.isBuffer:void 0,c=l||a;t.exports=c})(Ma,Ma.exports);var is=Ma.exports,F5=9007199254740991;function R5(t){return typeof t=="number"&&t>-1&&t%1==0&&t<=F5}var ns=R5,B5=ea,N5=ns,O5=Br,V5="[object Arguments]",z5="[object Array]",$5="[object Boolean]",W5="[object Date]",Y5="[object Error]",Z5="[object Function]",H5="[object Map]",j5="[object Number]",X5="[object Object]",K5="[object RegExp]",G5="[object Set]",U5="[object String]",Q5="[object WeakMap]",q5="[object ArrayBuffer]",J5="[object DataView]",td="[object Float32Array]",ed="[object Float64Array]",rd="[object Int8Array]",ad="[object Int16Array]",id="[object Int32Array]",nd="[object Uint8Array]",od="[object Uint8ClampedArray]",sd="[object Uint16Array]",ld="[object Uint32Array]",ae={};ae[td]=ae[ed]=ae[rd]=ae[ad]=ae[id]=ae[nd]=ae[od]=ae[sd]=ae[ld]=!0;ae[V5]=ae[z5]=ae[q5]=ae[$5]=ae[J5]=ae[W5]=ae[Y5]=ae[Z5]=ae[H5]=ae[j5]=ae[X5]=ae[K5]=ae[G5]=ae[U5]=ae[Q5]=!1;function cd(t){return O5(t)&&N5(t.length)&&!!ae[B5(t)]}var ud=cd;function dd(t){return function(r){return t(r)}}var Si=dd,Ta={exports:{}};Ta.exports;(function(t,r){var e=Xo,a=r&&!r.nodeType&&r,i=a&&!0&&t&&!t.nodeType&&t,n=i&&i.exports===a,o=n&&e.process,s=function(){try{var l=i&&i.require&&i.require("util").types;return l||o&&o.binding&&o.binding("util")}catch{}}();t.exports=s})(Ta,Ta.exports);var Mi=Ta.exports,hd=ud,vd=Si,xn=Mi,wn=xn&&xn.isTypedArray,fd=wn?vd(wn):hd,pd=fd,gd=b5,md=A5,yd=Rr,_d=is,Cd=rs,bd=pd,xd=Object.prototype,wd=xd.hasOwnProperty;function Ld(t,r){var e=yd(t),a=!e&&md(t),i=!e&&!a&&_d(t),n=!e&&!a&&!i&&bd(t),o=e||a||i||n,s=o?gd(t.length,String):[],l=s.length;for(var c in t)(r||wd.call(t,c))&&!(o&&(c=="length"||i&&(c=="offset"||c=="parent")||n&&(c=="buffer"||c=="byteLength"||c=="byteOffset")||Cd(c,l)))&&s.push(c);return s}var os=Ld,kd=Object.prototype;function Sd(t){var r=t&&t.constructor,e=typeof r=="function"&&r.prototype||kd;return t===e}var Ti=Sd;function Md(t,r){return function(e){return t(r(e))}}var ss=Md,Td=ss,Ed=Td(Object.keys,Object),Id=Ed,Ad=Ti,Dd=Id,Pd=Object.prototype,Fd=Pd.hasOwnProperty;function Rd(t){if(!Ad(t))return Dd(t);var r=[];for(var e in Object(t))Fd.call(t,e)&&e!="constructor"&&r.push(e);return r}var Bd=Rd,Nd=Go,Od=ns;function Vd(t){return t!=null&&Od(t.length)&&!Nd(t)}var ls=Vd,zd=os,$d=Bd,Wd=ls;function Yd(t){return Wd(t)?zd(t):$d(t)}var Ei=Yd,Zd=$a,Hd=Ei;function jd(t,r){return t&&Zd(r,Hd(r),t)}var Xd=jd;function Kd(t){var r=[];if(t!=null)for(var e in Object(t))r.push(e);return r}var Gd=Kd,Ud=Fr,Qd=Ti,qd=Gd,Jd=Object.prototype,t6=Jd.hasOwnProperty;function e6(t){if(!Ud(t))return qd(t);var r=Qd(t),e=[];for(var a in t)a=="constructor"&&(r||!t6.call(t,a))||e.push(a);return e}var r6=e6,a6=os,i6=r6,n6=ls;function o6(t){return n6(t)?a6(t,!0):i6(t)}var Ii=o6,s6=$a,l6=Ii;function c6(t,r){return t&&s6(r,l6(r),t)}var u6=c6,Ea={exports:{}};Ea.exports;(function(t,r){var e=Ue,a=r&&!r.nodeType&&r,i=a&&!0&&t&&!t.nodeType&&t,n=i&&i.exports===a,o=n?e.Buffer:void 0,s=o?o.allocUnsafe:void 0;function l(c,u){if(u)return c.slice();var d=c.length,v=s?s(d):new c.constructor(d);return c.copy(v),v}t.exports=l})(Ea,Ea.exports);var d6=Ea.exports;function h6(t,r){var e=-1,a=t.length;for(r||(r=Array(a));++e<a;)r[e]=t[e];return r}var v6=h6;function f6(t,r){for(var e=-1,a=t==null?0:t.length,i=0,n=[];++e<a;){var o=t[e];r(o,e,t)&&(n[i++]=o)}return n}var p6=f6;function g6(){return[]}var cs=g6,m6=p6,y6=cs,_6=Object.prototype,C6=_6.propertyIsEnumerable,Ln=Object.getOwnPropertySymbols,b6=Ln?function(t){return t==null?[]:(t=Object(t),m6(Ln(t),function(r){return C6.call(t,r)}))}:y6,Ai=b6,x6=$a,w6=Ai;function L6(t,r){return x6(t,w6(t),r)}var k6=L6;function S6(t,r){for(var e=-1,a=r.length,i=t.length;++e<a;)t[i+e]=r[e];return t}var us=S6,M6=ss,T6=M6(Object.getPrototypeOf,Object),ds=T6,E6=us,I6=ds,A6=Ai,D6=cs,P6=Object.getOwnPropertySymbols,F6=P6?function(t){for(var r=[];t;)E6(r,A6(t)),t=I6(t);return r}:D6,hs=F6,R6=$a,B6=hs;function N6(t,r){return R6(t,B6(t),r)}var O6=N6,V6=us,z6=Rr;function $6(t,r,e){var a=r(t);return z6(t)?a:V6(a,e(t))}var vs=$6,W6=vs,Y6=Ai,Z6=Ei;function H6(t){return W6(t,Z6,Y6)}var j6=H6,X6=vs,K6=hs,G6=Ii;function U6(t){return X6(t,G6,K6)}var Q6=U6,q6=br,J6=Ue,t3=q6(J6,"DataView"),e3=t3,r3=br,a3=Ue,i3=r3(a3,"Promise"),n3=i3,o3=br,s3=Ue,l3=o3(s3,"Set"),c3=l3,u3=br,d3=Ue,h3=u3(d3,"WeakMap"),v3=h3,ni=e3,oi=Li,si=n3,li=c3,ci=v3,fs=ea,$r=Uo,kn="[object Map]",f3="[object Object]",Sn="[object Promise]",Mn="[object Set]",Tn="[object WeakMap]",En="[object DataView]",p3=$r(ni),g3=$r(oi),m3=$r(si),y3=$r(li),_3=$r(ci),mr=fs;(ni&&mr(new ni(new ArrayBuffer(1)))!=En||oi&&mr(new oi)!=kn||si&&mr(si.resolve())!=Sn||li&&mr(new li)!=Mn||ci&&mr(new ci)!=Tn)&&(mr=function(t){var r=fs(t),e=r==f3?t.constructor:void 0,a=e?$r(e):"";if(a)switch(a){case p3:return En;case g3:return kn;case m3:return Sn;case y3:return Mn;case _3:return Tn}return r});var Di=mr,C3=Object.prototype,b3=C3.hasOwnProperty;function x3(t){var r=t.length,e=new t.constructor(r);return r&&typeof t[0]=="string"&&b3.call(t,"index")&&(e.index=t.index,e.input=t.input),e}var w3=x3,L3=Ue,k3=L3.Uint8Array,S3=k3,In=S3;function M3(t){var r=new t.constructor(t.byteLength);return new In(r).set(new In(t)),r}var Pi=M3,T3=Pi;function E3(t,r){var e=r?T3(t.buffer):t.buffer;return new t.constructor(e,t.byteOffset,t.byteLength)}var I3=E3,A3=/\w*$/;function D3(t){var r=new t.constructor(t.source,A3.exec(t));return r.lastIndex=t.lastIndex,r}var P3=D3,An=Ba,Dn=An?An.prototype:void 0,Pn=Dn?Dn.valueOf:void 0;function F3(t){return Pn?Object(Pn.call(t)):{}}var R3=F3,B3=Pi;function N3(t,r){var e=r?B3(t.buffer):t.buffer;return new t.constructor(e,t.byteOffset,t.length)}var O3=N3,V3=Pi,z3=I3,$3=P3,W3=R3,Y3=O3,Z3="[object Boolean]",H3="[object Date]",j3="[object Map]",X3="[object Number]",K3="[object RegExp]",G3="[object Set]",U3="[object String]",Q3="[object Symbol]",q3="[object ArrayBuffer]",J3="[object DataView]",th="[object Float32Array]",eh="[object Float64Array]",rh="[object Int8Array]",ah="[object Int16Array]",ih="[object Int32Array]",nh="[object Uint8Array]",oh="[object Uint8ClampedArray]",sh="[object Uint16Array]",lh="[object Uint32Array]";function ch(t,r,e){var a=t.constructor;switch(r){case q3:return V3(t);case Z3:case H3:return new a(+t);case J3:return z3(t,e);case th:case eh:case rh:case ah:case ih:case nh:case oh:case sh:case lh:return Y3(t,e);case j3:return new a;case X3:case U3:return new a(t);case K3:return $3(t);case G3:return new a;case Q3:return W3(t)}}var uh=ch,dh=Fr,Fn=Object.create,hh=function(){function t(){}return function(r){if(!dh(r))return{};if(Fn)return Fn(r);t.prototype=r;var e=new t;return t.prototype=void 0,e}}(),vh=hh,fh=vh,ph=ds,gh=Ti;function mh(t){return typeof t.constructor=="function"&&!gh(t)?fh(ph(t)):{}}var yh=mh,_h=Di,Ch=Br,bh="[object Map]";function xh(t){return Ch(t)&&_h(t)==bh}var wh=xh,Lh=wh,kh=Si,Rn=Mi,Bn=Rn&&Rn.isMap,Sh=Bn?kh(Bn):Lh,Mh=Sh,Th=Di,Eh=Br,Ih="[object Set]";function Ah(t){return Eh(t)&&Th(t)==Ih}var Dh=Ah,Ph=Dh,Fh=Si,Nn=Mi,On=Nn&&Nn.isSet,Rh=On?Fh(On):Ph,Bh=Rh,Nh=f5,Oh=g5,Vh=xi,zh=Xd,$h=u6,Wh=d6,Yh=v6,Zh=k6,Hh=O6,jh=j6,Xh=Q6,Kh=Di,Gh=w3,Uh=uh,Qh=yh,qh=Rr,Jh=is,t8=Mh,e8=Fr,r8=Bh,a8=Ei,i8=Ii,n8=1,o8=2,s8=4,ps="[object Arguments]",l8="[object Array]",c8="[object Boolean]",u8="[object Date]",d8="[object Error]",gs="[object Function]",h8="[object GeneratorFunction]",v8="[object Map]",f8="[object Number]",ms="[object Object]",p8="[object RegExp]",g8="[object Set]",m8="[object String]",y8="[object Symbol]",_8="[object WeakMap]",C8="[object ArrayBuffer]",b8="[object DataView]",x8="[object Float32Array]",w8="[object Float64Array]",L8="[object Int8Array]",k8="[object Int16Array]",S8="[object Int32Array]",M8="[object Uint8Array]",T8="[object Uint8ClampedArray]",E8="[object Uint16Array]",I8="[object Uint32Array]",re={};re[ps]=re[l8]=re[C8]=re[b8]=re[c8]=re[u8]=re[x8]=re[w8]=re[L8]=re[k8]=re[S8]=re[v8]=re[f8]=re[ms]=re[p8]=re[g8]=re[m8]=re[y8]=re[M8]=re[T8]=re[E8]=re[I8]=!0;re[d8]=re[gs]=re[_8]=!1;function pa(t,r,e,a,i,n){var o,s=r&n8,l=r&o8,c=r&s8;if(e&&(o=i?e(t,a,i,n):e(t)),o!==void 0)return o;if(!e8(t))return t;var u=qh(t);if(u){if(o=Gh(t),!s)return Yh(t,o)}else{var d=Kh(t),v=d==gs||d==h8;if(Jh(t))return Wh(t,s);if(d==ms||d==ps||v&&!i){if(o=l||v?{}:Qh(t),!s)return l?Hh(t,$h(o,t)):Zh(t,zh(o,t))}else{if(!re[d])return i?t:{};o=Uh(t,d,s)}}n||(n=new Nh);var h=n.get(t);if(h)return h;n.set(t,o),r8(t)?t.forEach(function(g){o.add(pa(g,r,e,g,t,n))}):t8(t)&&t.forEach(function(g,m){o.set(m,pa(g,r,e,m,t,n))});var f=c?l?Xh:jh:l?i8:a8,p=u?void 0:f(t);return Oh(p||t,function(g,m){p&&(m=g,g=t[m]),Vh(o,m,pa(g,r,e,m,t,n))}),o}var A8=pa,D8=A8,P8=1,F8=4;function R8(t){return D8(t,P8|F8)}var B8=R8;const N8=jo(B8);var O8=X("<button>");const V8=t=>(()=>{var r=O8();return tr(r,"click",t.onClick),ct(r,()=>t.children),ve(e=>{var a=t.style,i=`klinecharts-pro-button ${t.type??"confirm"} ${t.class??""}`;return e.e=Pr(r,a,e.e),i!==e.t&&Cr(r,e.t=i),e},{e:void 0,t:void 0}),r})();Ge(["click"]);var z8=X('<svg viewBox="0 0 1024 1024"class=icon><path d="M810.666667 128H213.333333c-46.933333 0-85.333333 38.4-85.333333 85.333333v597.333334c0 46.933333 38.4 85.333333 85.333333 85.333333h597.333334c46.933333 0 85.333333-38.4 85.333333-85.333333V213.333333c0-46.933333-38.4-85.333333-85.333333-85.333333z m-353.706667 567.04a42.496 42.496 0 0 1-60.16 0L243.626667 541.866667c-8.106667-8.106667-12.373333-18.773333-12.373334-29.866667s4.693333-22.186667 12.373334-29.866667a42.496 42.496 0 0 1 60.16 0L426.666667 604.586667l293.546666-293.546667a42.496 42.496 0 1 1 60.16 60.16l-323.413333 323.84z">'),$8=X('<svg viewBox="0 0 1024 1024"class=icon><path d="M245.333333 128h533.333334A117.333333 117.333333 0 0 1 896 245.333333v533.333334A117.333333 117.333333 0 0 1 778.666667 896H245.333333A117.333333 117.333333 0 0 1 128 778.666667V245.333333A117.333333 117.333333 0 0 1 245.333333 128z m0 64c-29.44 0-53.333333 23.893333-53.333333 53.333333v533.333334c0 29.44 23.893333 53.333333 53.333333 53.333333h533.333334c29.44 0 53.333333-23.893333 53.333333-53.333333V245.333333c0-29.44-23.893333-53.333333-53.333333-53.333333H245.333333z">'),W8=X("<div>"),Y8=X("<span class=label>");const Z8=()=>z8(),H8=()=>$8(),Vn=t=>{const[r,e]=Tt(t.checked??!1);return er(()=>{"checked"in t&&e(t.checked)}),(()=>{var a=W8();return a.$$click=i=>{const n=!r();t.onChange&&t.onChange(n),e(n)},ct(a,(()=>{var i=Ie(()=>!!r());return()=>i()?st(Z8,{}):st(H8,{})})(),null),ct(a,(()=>{var i=Ie(()=>!!t.label);return()=>i()&&(()=>{var n=Y8();return ct(n,()=>t.label),n})()})(),null),ve(i=>{var n=t.style,o=`klinecharts-pro-checkbox ${r()&&"checked"||""} ${t.class||""}`;return i.e=Pr(a,n,i.e),o!==i.t&&Cr(a,i.t=o),i},{e:void 0,t:void 0}),a})()};Ge(["click"]);var j8=X("<div class=klinecharts-pro-loading><i class=circle1></i><i class=circle2></i><i class=circle3>");const ys=()=>j8();var X8=X('<div class=klinecharts-pro-empty><svg class=icon viewBox="0 0 1024 1024"><path d="M855.6 427.2H168.5c-12.7 0-24.4 6.9-30.6 18L4.4 684.7C1.5 689.9 0 695.8 0 701.8v287.1c0 19.4 15.7 35.1 35.1 35.1H989c19.4 0 35.1-15.7 35.1-35.1V701.8c0-6-1.5-11.8-4.4-17.1L886.2 445.2c-6.2-11.1-17.9-18-30.6-18zM673.4 695.6c-16.5 0-30.8 11.5-34.3 27.7-12.7 58.5-64.8 102.3-127.2 102.3s-114.5-43.8-127.2-102.3c-3.5-16.1-17.8-27.7-34.3-27.7H119c-26.4 0-43.3-28-31.1-51.4l81.7-155.8c6.1-11.6 18-18.8 31.1-18.8h622.4c13 0 25 7.2 31.1 18.8l81.7 155.8c12.2 23.4-4.7 51.4-31.1 51.4H673.4zM819.9 209.5c-1-1.8-2.1-3.7-3.2-5.5-9.8-16.6-31.1-22.2-47.8-12.6L648.5 261c-17 9.8-22.7 31.6-12.6 48.4 0.9 1.4 1.7 2.9 2.5 4.4 9.5 17 31.2 22.8 48 13L807 257.3c16.7-9.7 22.4-31 12.9-47.8zM375.4 261.1L255 191.6c-16.7-9.6-38-4-47.8 12.6-1.1 1.8-2.1 3.6-3.2 5.5-9.5 16.8-3.8 38.1 12.9 47.8L337.3 327c16.9 9.7 38.6 4 48-13.1 0.8-1.5 1.7-2.9 2.5-4.4 10.2-16.8 4.5-38.6-12.4-48.4zM512 239.3h2.5c19.5 0.3 35.5-15.5 35.5-35.1v-139c0-19.3-15.6-34.9-34.8-35.1h-6.4C489.6 30.3 474 46 474 65.2v139c0 19.5 15.9 35.4 35.5 35.1h2.5z">');const K8=()=>X8();var G8=X("<ul>"),U8=X("<li>");const Ia=t=>(()=>{var r=G8();return ct(r,st(be,{get when(){return t.loading},get children(){return st(ys,{})}}),null),ct(r,st(be,{get when(){var e;return!t.loading&&!t.children&&!((e=t.dataSource)!=null&&e.length)},get children(){return st(K8,{})}}),null),ct(r,st(be,{get when(){return t.children},get children(){return t.children}}),null),ct(r,st(be,{get when(){return!t.children},get children(){var e;return(e=t.dataSource)==null?void 0:e.map(a=>{var i;return((i=t.renderItem)==null?void 0:i.call(t,a))??U8()})}}),null),ve(e=>{var a=t.style,i=`klinecharts-pro-list ${t.class??""}`;return e.e=Pr(r,a,e.e),i!==e.t&&Cr(r,e.t=i),e},{e:void 0,t:void 0}),r})();var Q8=X('<div class=klinecharts-pro-modal><div class=inner><div class=title-container><svg class=close-icon viewBox="0 0 1024 1024"><path d="M934.184927 199.723787 622.457206 511.452531l311.727721 311.703161c14.334473 14.229073 23.069415 33.951253 23.069415 55.743582 0 43.430138-35.178197 78.660524-78.735226 78.660524-21.664416 0-41.361013-8.865925-55.642275-23.069415L511.149121 622.838388 199.420377 934.490384c-14.204513 14.20349-33.901111 23.069415-55.642275 23.069415-43.482327 0-78.737272-35.230386-78.737272-78.660524 0-21.792329 8.864902-41.513486 23.094998-55.743582l311.677579-311.703161L88.135828 199.723787c-14.230096-14.255679-23.094998-33.92567-23.094998-55.642275 0-43.430138 35.254945-78.762855 78.737272-78.762855 21.741163 0 41.437761 8.813736 55.642275 23.069415l311.727721 311.727721L822.876842 88.389096c14.281261-14.255679 33.977859-23.069415 55.642275-23.069415 43.557028 0 78.735226 35.332716 78.735226 78.762855C957.254342 165.798117 948.5194 185.468109 934.184927 199.723787"></path></svg></div><div class=content-container>'),q8=X("<div class=button-container>");const Wr=t=>(()=>{var r=Q8(),e=r.firstChild,a=e.firstChild,i=a.firstChild,n=a.nextSibling;return ct(a,()=>t.title,i),tr(i,"click",t.onClose),ct(n,()=>t.children),ct(e,(()=>{var o=Ie(()=>!!(t.buttons&&t.buttons.length>0));return()=>o()&&(()=>{var s=q8();return ct(s,()=>t.buttons.map(l=>st(V8,Ho(l,{get children(){return l.children}})))),s})()})(),null),ve(o=>(o=`${t.width??400}px`)!=null?e.style.setProperty("width",o):e.style.removeProperty("width")),r})();Ge(["click"]);var J8=X("<div tabindex=0><div class=selector-container><span class=value></span><i class=arrow>"),tv=X("<div class=drop-down-container><ul>"),ev=X("<li>");const _s=t=>{const[r,e]=Tt(!1);return(()=>{var a=J8(),i=a.firstChild,n=i.firstChild;return a.addEventListener("blur",o=>{e(!1)}),a.$$click=o=>{e(s=>!s)},ct(n,()=>t.value),ct(a,(()=>{var o=Ie(()=>!!(t.dataSource&&t.dataSource.length>0));return()=>o()&&(()=>{var s=tv(),l=s.firstChild;return ct(l,()=>t.dataSource.map(c=>{const u=c[t.valueKey??"text"]??c;return(()=>{var d=ev();return d.$$click=v=>{var h;v.stopPropagation(),t.value!==u&&((h=t.onSelected)==null||h.call(t,c)),e(!1)},ct(d,u),d})()})),s})()})(),null),ve(o=>{var s=t.style,l=`klinecharts-pro-select ${t.class??""} ${r()?"klinecharts-pro-select-show":""}`;return o.e=Pr(a,s,o.e),l!==o.t&&Cr(a,o.t=l),o},{e:void 0,t:void 0}),a})()};Ge(["click"]);var rv=X("<span class=prefix>"),av=X("<span class=suffix>"),iv=X("<div><input class=value>");const Cs=t=>{const r=Ho({min:Number.MIN_SAFE_INTEGER,max:Number.MAX_SAFE_INTEGER},t);let e;const[a,i]=Tt("normal");return(()=>{var n=iv(),o=n.firstChild;return n.$$click=()=>{e==null||e.focus()},ct(n,st(be,{get when(){return r.prefix},get children(){var s=rv();return ct(s,()=>r.prefix),s}}),o),o.$$input=s=>{var l;const c=s.target.value;typeof c=="string"&&((l=r.onChange)==null||l.call(r,c))},o.addEventListener("blur",()=>{i("normal")}),o.addEventListener("focus",()=>{i("focus")}),bi(s=>{e=s},o),ct(n,st(be,{get when(){return r.suffix},get children(){var s=av();return ct(s,()=>r.suffix),s}}),null),ve(s=>{var l=r.style,c=`klinecharts-pro-input ${r.class??""}`,u=a(),d=r.placeholder??"";return s.e=Pr(n,l,s.e),c!==s.t&&Cr(n,s.t=c),u!==s.a&&Fe(n,"data-status",s.a=u),d!==s.o&&Fe(o,"placeholder",s.o=d),s},{e:void 0,t:void 0,a:void 0,o:void 0}),ve(()=>o.value=r.value),n})()};Ge(["click","input"]);var nv=X("<div><i class=thumb>");const ov=t=>(()=>{var r=nv();return r.$$click=e=>{t.onChange&&t.onChange()},ve(e=>{var a=t.style,i=`klinecharts-pro-switch ${t.open?"turn-on":"turn-off"} ${t.class??""}`;return e.e=Pr(r,a,e.e),i!==e.t&&Cr(r,e.t=i),e},{e:void 0,t:void 0}),r})();Ge(["click"]);const sv="指标",lv="主图指标",cv="副图指标",uv="设置",dv="时区",hv="截屏",vv="订单流",fv="全屏",pv="退出全屏",gv="保存",mv="确定",yv="取消",_v="MA(移动平均线)",Cv="EMA(指数平滑移动平均线)",bv="SMA",xv="BOLL(布林线)",wv="BBI(多空指数)",Lv="SAR(停损点指向指标)",kv="VOL(成交量)",Sv="MACD(指数平滑异同移动平均线)",Mv="KDJ(随机指标)",Tv="RSI(相对强弱指标)",Ev="BIAS(乖离率)",Iv="BRAR(情绪指标)",Av="CCI(顺势指标)",Dv="DMI(动向指标)",Pv="CR(能量指标)",Fv="PSY(心理线)",Rv="DMA(平行线差指标)",Bv="TRIX(三重指数平滑平均线)",Nv="OBV(能量潮指标)",Ov="VR(成交量变异率)",Vv="WR(威廉指标)",zv="MTM(动量指标)",$v="EMV(简易波动指标)",Wv="ROC(变动率指标)",Yv="PVT(价量趋势指标)",Zv="AO(动量震荡指标)",Hv="世界统一时间",jv="(UTC-10) 檀香山",Xv="(UTC-8) 朱诺",Kv="(UTC-7) 洛杉矶",Gv="(UTC-5) 芝加哥",Uv="(UTC-4) 多伦多",Qv="(UTC-3) 圣保罗",qv="(UTC+1) 伦敦",Jv="(UTC+2) 柏林",t7="(UTC+3) 巴林",e7="(UTC+4) 迪拜",r7="(UTC+5) 阿什哈巴德",a7="(UTC+6) 阿拉木图",i7="(UTC+7) 曼谷",n7="(UTC+8) 上海",o7="(UTC+9) 东京",s7="(UTC+10) 悉尼",l7="(UTC+12) 诺福克岛",c7="水平直线",u7="水平射线",d7="水平线段",h7="垂直直线",v7="垂直射线",f7="垂直线段",p7="直线",g7="射线",m7="线段",y7="箭头",_7="价格线",C7="价格通道线",b7="平行直线",x7="斐波那契回调直线",w7="斐波那契回调线段",L7="斐波那契圆环",k7="斐波那契螺旋",S7="斐波那契速度阻力扇",M7="斐波那契趋势扩展",T7="江恩箱",E7="矩形",I7="平行四边形",A7="圆",D7="三角形",P7="三浪",F7="五浪",R7="八浪",B7="任意浪",N7="ABCD形态",O7="XABCD形态",V7="弱磁模式",z7="强磁模式",$7="商品搜索",W7="商品代码",Y7="参数1",Z7="参数2",H7="参数3",j7="参数4",X7="参数5",K7="周期",G7="标准差",U7="蜡烛图类型",Q7="全实心",q7="全空心",J7="涨空心",t4="跌空心",e4="OHLC",r4="面积图",a4="最新价显示",i4="最高价显示",n4="最低价显示",o4="指标最新值显示",s4="价格轴类型",l4="线性轴",c4="百分比轴",u4="对数轴",d4="倒置坐标",h4="网格线显示",v4="恢复默认",f4={indicator:sv,main_indicator:lv,sub_indicator:cv,setting:uv,timezone:dv,screenshot:hv,order_flow:vv,full_screen:fv,exit_full_screen:pv,save:gv,confirm:mv,cancel:yv,ma:_v,ema:Cv,sma:bv,boll:xv,bbi:wv,sar:Lv,vol:kv,macd:Sv,kdj:Mv,rsi:Tv,bias:Ev,brar:Iv,cci:Av,dmi:Dv,cr:Pv,psy:Fv,dma:Rv,trix:Bv,obv:Nv,vr:Ov,wr:Vv,mtm:zv,emv:$v,roc:Wv,pvt:Yv,ao:Zv,utc:Hv,honolulu:jv,juneau:Xv,los_angeles:Kv,chicago:Gv,toronto:Uv,sao_paulo:Qv,london:qv,berlin:Jv,bahrain:t7,dubai:e7,ashkhabad:r7,almaty:a7,bangkok:i7,shanghai:n7,tokyo:o7,sydney:s7,norfolk:l7,horizontal_straight_line:c7,horizontal_ray_line:u7,horizontal_segment:d7,vertical_straight_line:h7,vertical_ray_line:v7,vertical_segment:f7,straight_line:p7,ray_line:g7,segment:m7,arrow:y7,price_line:_7,price_channel_line:C7,parallel_straight_line:b7,fibonacci_line:x7,fibonacci_segment:w7,fibonacci_circle:L7,fibonacci_spiral:k7,fibonacci_speed_resistance_fan:S7,fibonacci_extension:M7,gann_box:T7,rect:E7,parallelogram:I7,circle:A7,triangle:D7,three_waves:P7,five_waves:F7,eight_waves:R7,any_waves:B7,abcd:N7,xabcd:O7,weak_magnet:V7,strong_magnet:z7,symbol_search:$7,symbol_code:W7,params_1:Y7,params_2:Z7,params_3:H7,params_4:j7,params_5:X7,period:K7,standard_deviation:G7,candle_type:U7,candle_solid:Q7,candle_stroke:q7,candle_up_stroke:J7,candle_down_stroke:t4,ohlc:e4,area:r4,last_price_show:a4,high_price_show:i4,low_price_show:n4,indicator_last_value_show:o4,price_axis_type:s4,normal:l4,percentage:c4,log:u4,reverse_coordinate:d4,grid_show:h4,restore_default:v4},p4="Indicator",g4="Main Indicator",m4="Sub Indicator",y4="Setting",_4="Timezone",C4="Screenshot",b4="Order Flow",x4="Full Screen",w4="Exit",L4="Save",k4="Confirm",S4="Cancel",M4="MA(Moving Average)",T4="EMA(Exponential Moving Average)",E4="SMA",I4="BOLL(Bolinger Bands)",A4="BBI(Bull And Bearlndex)",D4="SAR(Stop and Reverse)",P4="VOL(Volume)",F4="MACD(Moving Average Convergence / Divergence)",R4="KDJ(KDJ Index)",B4="RSI(Relative Strength Index)",N4="BIAS(Bias Ratio)",O4="BRAR(情绪指标)",V4="CCI(Commodity Channel Index)",z4="DMI(Directional Movement Index)",$4="CR(能量指标)",W4="PSY(Psychological Line)",Y4="DMA(Different of Moving Average)",Z4="TRIX(Triple Exponentially Smoothed Moving Average)",H4="OBV(On Balance Volume)",j4="VR(Volatility Volume Ratio)",X4="WR(Williams %R)",K4="MTM(Momentum Index)",G4="EMV(Ease of Movement Value)",U4="ROC(Price Rate of Change)",Q4="PVT(Price and Volume Trend)",q4="AO(Awesome Oscillator)",J4="UTC",tf="(UTC-10) Honolulu",ef="(UTC-8) Juneau",rf="(UTC-7) Los Angeles",af="(UTC-5) Chicago",nf="(UTC-4) Toronto",of="(UTC-3) Sao Paulo",sf="(UTC+1) London",lf="(UTC+2) Berlin",cf="(UTC+3) Bahrain",uf="(UTC+4) Dubai",df="(UTC+5) Ashkhabad",hf="(UTC+6) Almaty",vf="(UTC+7) Bangkok",ff="(UTC+8) Shanghai",pf="(UTC+9) Tokyo",gf="(UTC+10) Sydney",mf="(UTC+12) Norfolk",yf="Horizontal Line",_f="Horizontal Ray",Cf="Horizontal Segment",bf="Vertical Line",xf="Vertical Ray",wf="Vertical Segment",Lf="Trend Line",kf="Ray",Sf="Segment",Mf="Arrow",Tf="Price Line",Ef="Price Channel Line",If="Parallel Line",Af="Fibonacci Line",Df="Fibonacci Segment",Pf="Fibonacci Circle",Ff="Fibonacci Spiral",Rf="Fibonacci Sector",Bf="Fibonacci Extension",Nf="Gann Box",Of="Rect",Vf="Parallelogram",zf="Circle",$f="Triangle",Wf="Three Waves",Yf="Five Waves",Zf="Eight Waves",Hf="Any Waves",jf="ABCD Pattern",Xf="XABCD Pattern",Kf="Weak Magnet",Gf="Strong Magnet",Uf="Symbol Search",Qf="Symbol Code",qf="Parameter 1",Jf="Parameter 2",tp="Parameter 3",ep="Parameter 4",rp="Parameter 5",ap="Period",ip="Standard Deviation",np="Candle Type",op="Candle Solid",sp="Candle Stroke",lp="Candle Up Stroke",cp="Candle Down Stroke",up="OHLC",dp="Area",hp="Show Last Price",vp="Show Highest Price",fp="Show Lowest Price",pp="Show indicator's last value",gp="Price Axis Type",mp="Normal",yp="Percentage",_p="Log",Cp="Reverse Coordinate",bp="Show Grids",xp="Restore Defaults",wp={indicator:p4,main_indicator:g4,sub_indicator:m4,setting:y4,timezone:_4,screenshot:C4,order_flow:b4,full_screen:x4,exit_full_screen:w4,save:L4,confirm:k4,cancel:S4,ma:M4,ema:T4,sma:E4,boll:I4,bbi:A4,sar:D4,vol:P4,macd:F4,kdj:R4,rsi:B4,bias:N4,brar:O4,cci:V4,dmi:z4,cr:$4,psy:W4,dma:Y4,trix:Z4,obv:H4,vr:j4,wr:X4,mtm:K4,emv:G4,roc:U4,pvt:Q4,ao:q4,utc:J4,honolulu:tf,juneau:ef,los_angeles:rf,chicago:af,toronto:nf,sao_paulo:of,london:sf,berlin:lf,bahrain:cf,dubai:uf,ashkhabad:df,almaty:hf,bangkok:vf,shanghai:ff,tokyo:pf,sydney:gf,norfolk:mf,horizontal_straight_line:yf,horizontal_ray_line:_f,horizontal_segment:Cf,vertical_straight_line:bf,vertical_ray_line:xf,vertical_segment:wf,straight_line:Lf,ray_line:kf,segment:Sf,arrow:Mf,price_line:Tf,price_channel_line:Ef,parallel_straight_line:If,fibonacci_line:Af,fibonacci_segment:Df,fibonacci_circle:Pf,fibonacci_spiral:Ff,fibonacci_speed_resistance_fan:Rf,fibonacci_extension:Bf,gann_box:Nf,rect:Of,parallelogram:Vf,circle:zf,triangle:$f,three_waves:Wf,five_waves:Yf,eight_waves:Zf,any_waves:Hf,abcd:jf,xabcd:Xf,weak_magnet:Kf,strong_magnet:Gf,symbol_search:Uf,symbol_code:Qf,params_1:qf,params_2:Jf,params_3:tp,params_4:ep,params_5:rp,period:ap,standard_deviation:ip,candle_type:np,candle_solid:op,candle_stroke:sp,candle_up_stroke:lp,candle_down_stroke:cp,ohlc:up,area:dp,last_price_show:hp,high_price_show:vp,low_price_show:fp,indicator_last_value_show:pp,price_axis_type:gp,normal:mp,percentage:yp,log:_p,reverse_coordinate:Cp,grid_show:bp,restore_default:xp},Lp={"zh-CN":f4,"en-US":wp},O=(t,r)=>{var e;return((e=Lp[r])==null?void 0:e[t])??t};var kp=X("<img alt=symbol>"),Sp=X("<div class=symbol><span>"),Mp=X('<div class=klinecharts-pro-period-bar><div class=menu-container><svg viewBox="0 0 1024 1024"><path d="M192.037 287.953h640.124c17.673 0 32-14.327 32-32s-14.327-32-32-32H192.037c-17.673 0-32 14.327-32 32s14.327 32 32 32zM832.161 479.169H438.553c-17.673 0-32 14.327-32 32s14.327 32 32 32h393.608c17.673 0 32-14.327 32-32s-14.327-32-32-32zM832.161 735.802H192.037c-17.673 0-32 14.327-32 32s14.327 32 32 32h640.124c17.673 0 32-14.327 32-32s-14.327-32-32-32zM319.028 351.594l-160 160 160 160z"></path></svg></div><div class="item tools"><svg viewBox="0 0 20 20"><path d=M15.873,20L3.65079,20C1.5873,20,0,18.3871,0,16.2903L0,3.70968C-3.78442e-7,1.6129,1.5873,0,3.65079,0L15.873,0C17.9365,0,19.5238,1.6129,19.5238,3.70968C19.5238,4.35484,19.2063,4.51613,18.5714,4.51613C17.9365,4.51613,17.619,4.19355,17.619,3.70968C17.619,2.74194,16.8254,1.93548,15.873,1.93548L3.65079,1.93548C2.69841,1.93548,1.90476,2.74194,1.90476,3.70968L1.90476,16.2903C1.90476,17.2581,2.69841,18.0645,3.65079,18.0645L15.873,18.0645C16.8254,18.0645,17.619,17.2581,17.619,16.2903C17.619,15.8065,18.0952,15.3226,18.5714,15.3226C19.0476,15.3226,19.5238,15.8065,19.5238,16.2903C19.5238,18.2258,17.9365,20,15.873,20ZM14.9206,12.9032C14.7619,12.9032,14.4444,12.9032,14.2857,12.7419L11.2698,9.35484C10.9524,9.03226,10.9524,8.54839,11.2698,8.22581C11.5873,7.90323,12.0635,7.90323,12.381,8.22581L15.3968,11.6129C15.7143,11.9355,15.7143,12.4194,15.3968,12.7419C15.3968,12.9032,15.2381,12.9032,14.9206,12.9032ZM11.4286,13.2258C11.2698,13.2258,11.1111,13.2258,10.9524,13.0645C10.6349,12.7419,10.6349,12.4194,10.9524,12.0968L15.0794,7.74193C15.3968,7.41935,15.7143,7.41935,16.0317,7.74193C16.3492,8.06452,16.3492,8.3871,16.0317,8.70968L11.9048,13.0645C11.746,13.2258,11.5873,13.2258,11.4286,13.2258ZM10.3175,3.70968C10.6349,3.70968,11.4286,3.87097,11.4286,4.67742C11.4286,5.32258,10.4762,5.16129,10.1587,5.16129C8.73016,5.16129,8.25397,5.96774,8.09524,6.6129L7.77778,8.54839L9.36508,8.54839C9.68254,8.54839,10,8.87097,10,9.19355C10,9.51613,9.68254,9.83871,9.36508,9.83871L7.61905,9.83871L6.50794,14.8387Q6.34921,16.2903,5.39683,16.2903Q4.44444,16.2903,4.92064,14.8387L6.03175,10L4.60317,10C4.28571,10,3.96825,9.67742,3.96825,9.35484C3.96825,8.70968,4.28571,8.54839,4.60317,8.54839L6.34921,8.54839L6.8254,6.45161C7.14286,3.70968,9.52381,3.54839,10.3175,3.70968ZM18.4127,6.6129C18.5714,6.12903,18.8889,5.96774,19.3651,5.96774C19.8413,6.12903,20,6.45161,20,6.93548L18.4127,13.3871C18.254,13.871,17.9365,14.0323,17.4603,14.0323C16.9841,13.871,16.8254,13.5484,16.8254,13.0645L18.4127,6.6129Z></path></svg><span></span></div><div class="item tools"><svg width=20 height=20 viewBox="0 0 20 20"><path d=M18.5446,9.09091C18.3333,6.61616,17.2887,4.31818,15.5751,2.63889C13.8498,0.94697,11.6197,0,9.28404,0C8.02817,0,6.81925,0.265151,5.66901,0.782828C5.65728,0.782828,5.65728,0.795454,5.64554,0.795454C5.6338,0.795454,5.6338,0.808081,5.62207,0.808081C4.53052,1.31313,3.55634,2.0202,2.71127,2.92929C1.85446,3.85101,1.18545,4.91162,0.715963,6.11111C0.246479,7.33586,0,8.64899,0,10C0,10.8712,0.105634,11.7172,0.305164,12.5379C0.305164,12.5631,0.316901,12.5884,0.328638,12.6136C0.739437,14.2298,1.51408,15.7197,2.62911,16.9571C4.07277,18.548,5.92723,19.5581,7.93427,19.8737C7.95775,19.8737,7.96948,19.8864,7.99296,19.8864C8.3216,19.9369,8.66197,19.9747,9.00235,19.9747L9.21362,19.9747C9.61268,19.9747,10.3756,19.9369,11.0094,19.697C11.1737,19.6338,11.3028,19.5076,11.3732,19.3434C11.4437,19.1793,11.4554,18.9899,11.3967,18.8131C11.3028,18.5354,11.0563,18.346,10.7864,18.346C10.716,18.346,10.6338,18.3586,10.5634,18.3838C10.0939,18.5606,9.46009,18.5859,9.20188,18.5859L9.09624,18.5859C9.20188,18.2702,9.23709,17.9167,9.15493,17.5505C9.00235,16.8939,8.50939,16.3384,7.58216,15.7955L7.19484,15.5682C6.57277,15.2146,6.23239,15.0253,6.03286,14.7348C5.83333,14.4444,5.69249,13.9899,5.51643,12.9798C5.38732,12.298,5.04695,11.7677,4.50704,11.4646C4.14319,11.2626,3.70892,11.149,3.19249,11.149C2.82864,11.149,2.42958,11.1995,2.00704,11.3005C1.79578,11.351,1.59624,11.4141,1.42019,11.4646C1.33803,10.9848,1.30282,10.4798,1.30282,9.97475C1.30282,6.93182,2.76995,4.26768,4.98826,2.72727C5,3.00505,5.05869,3.29545,5.17606,3.57323C5.48122,4.26768,6.10329,4.7096,7.01878,4.89899C7.06573,4.91162,7.10094,4.91162,7.13615,4.91162L7.1831,4.91162C7.26526,4.91162,7.57042,4.92424,7.88732,5.0505C8.3216,5.2399,8.56808,5.55555,8.65023,6.04798C8.84977,7.61364,9.07277,10.4293,8.79108,11.3384C8.76761,11.4141,8.75587,11.4899,8.75587,11.5657C8.75587,11.9444,9.0493,12.2601,9.40141,12.2601C9.57747,12.2601,9.74179,12.1843,9.85915,12.0581C9.97653,11.9318,12.6174,9.05303,13.3216,8.09343C13.4038,7.97979,13.4859,7.87878,13.5798,7.76515C13.9202,7.33586,14.2723,6.90656,14.4014,6.26262C14.554,5.56818,14.4014,4.79798,13.9437,3.85101C13.615,3.16919,13.5563,2.86616,13.5446,2.75252C13.5563,2.7399,13.5798,2.72727,13.6033,2.71464C15.6221,4.10353,17.0188,6.43939,17.2535,9.19192C17.2887,9.55808,17.5587,9.82323,17.8991,9.82323L17.9577,9.82323C18.3099,9.8106,18.5681,9.48232,18.5446,9.09091ZM3.19249,12.5631C3.48592,12.5631,3.72066,12.6136,3.89671,12.7146C4.08451,12.8283,4.19014,12.9924,4.23709,13.2702C4.43662,14.3434,4.61268,15.0631,5,15.6061C5.37559,16.1364,5.85681,16.4015,6.58451,16.8182L6.60798,16.8308C6.71362,16.8939,6.84272,16.9571,6.96009,17.0328C7.69953,17.4621,7.86385,17.7525,7.89906,17.8914C7.93427,18.0303,7.85211,18.2323,7.74648,18.4343C4.91784,17.8535,2.65258,15.6944,1.73709,12.8283C2.15962,12.702,2.71127,12.5631,3.19249,12.5631ZM12.7934,4.5202C13.4272,5.83333,13.1455,6.18687,12.5822,6.89394C12.4883,7.00758,12.3944,7.12121,12.3005,7.24747C11.9484,7.72727,11.0211,8.77525,10.2113,9.68434C10.2113,9.24242,10.1878,8.73737,10.1526,8.19444C10.0704,6.95707,9.92958,5.90909,9.92958,5.87121L9.92958,5.83333C9.75352,4.83586,9.20188,4.11616,8.3216,3.76263C7.82864,3.56061,7.37089,3.53535,7.19484,3.53535C6.73709,3.43434,6.4554,3.24495,6.33803,2.99242C6.19718,2.68939,6.29108,2.24747,6.38498,1.9697C7.28873,1.59091,8.26291,1.37626,9.28404,1.37626C10.3873,1.37626,11.4437,1.61616,12.4061,2.04545C12.3357,2.18434,12.277,2.34848,12.2535,2.5505C12.2066,3.04293,12.3709,3.64899,12.7934,4.5202Z></path><path d=M15.22299772857666,9.722223632261718C12.59389772857666,9.722223632261718,10.44600772857666,12.020201374511718,10.44600772857666,14.861111374511719C10.44600772857666,17.70202137451172,12.58215772857666,20.000021374511718,15.223007728576661,20.000021374511718C17.86384772857666,20.000021374511718,19.99999772857666,17.70202137451172,19.99999772857666,14.861111374511719C19.99999772857666,12.020201374511718,17.85211772857666,9.72222212709572,15.22299772857666,9.722223632261718ZM15.22299772857666,18.598491374511717C13.30985772857666,18.598491374511717,11.737087728576661,16.91919137451172,11.737087728576661,14.848481374511719C11.737087728576661,12.777781374511719,13.29811772857666,11.098491374511719,15.22299772857666,11.098491374511719C17.14787772857666,11.098491374511719,18.708917728576658,12.777781374511719,18.708917728576658,14.848481374511719C18.708917728576658,16.91919137451172,17.13614772857666,18.59848137451172,15.22299772857666,18.598491374511717Z></path><path d=M15.692486288146974,15.050496970825195L15.692486288146974,12.676760970825196C15.692486288146974,12.297972970825196,15.399058288146973,11.982316970825195,15.046945288146972,11.982316970825195C14.694833288146972,11.982316970825195,14.401406288146973,12.297972970825196,14.401406288146973,12.676760970825196L14.401406288146973,15.340896970825195C14.401406288146973,15.530296970825194,14.471829288146973,15.694436970825196,14.589200288146973,15.833326970825196L15.751176288146972,17.095956970825195C15.868546288146973,17.222216970825194,16.032866288146973,17.297976970825196,16.208916288146973,17.297976970825196C16.384976288146973,17.297976970825196,16.537556288146973,17.222216970825194,16.666666288146974,17.095956970825195C16.78403628814697,16.969686970825194,16.854456288146974,16.792916970825196,16.854456288146974,16.603526970825193C16.854456288146974,16.414136970825197,16.78403628814697,16.237366970825196,16.666666288146974,16.111106970825197L15.692486288146974,15.050496970825195Z></path></svg><span></span></div><div class="item tools"><svg viewBox="0 0 20 20"><path d=M19.7361,12.542L18.1916,11.2919C18.2647,10.8678,18.3025,10.4347,18.3025,10.0017C18.3025,9.56861,18.2647,9.13555,18.1916,8.71142L19.7361,7.46135C19.9743,7.26938,20.0615,6.95686,19.9554,6.6756L19.9342,6.61756C19.5074,5.49026,18.8755,4.45449,18.0549,3.53926L18.0124,3.49238C17.8096,3.26692,17.4819,3.1821,17.1848,3.28032L15.2677,3.92544C14.5603,3.3763,13.7704,2.94324,12.9168,2.63966L12.5466,0.742229C12.49,0.449802,12.2472,0.222111,11.9383,0.168536L11.8746,0.157375C10.6461,-0.0524583,9.35391,-0.0524583,8.1254,0.157375L8.06174,0.168536C7.75284,0.222111,7.50997,0.449802,7.45338,0.742229L7.08082,2.64859C6.2343,2.95217,5.44909,3.383,4.74641,3.92991L2.81522,3.28032C2.52047,3.1821,2.19036,3.26469,1.98757,3.49238L1.94513,3.53926C1.12455,4.45672,0.492609,5.49249,0.0658141,6.61756L0.0445921,6.6756C-0.0615171,6.95463,0.0257283,7.26715,0.263885,7.46135L1.82723,8.72482C1.75413,9.14448,1.71876,9.57308,1.71876,9.99944C1.71876,10.428,1.75413,10.8566,1.82723,11.2741L0.263885,12.5375C0.025729,12.7295,-0.0615164,13.042,0.0445929,13.3233L0.0658148,13.3813C0.49261,14.5064,1.12455,15.5444,1.94513,16.4596L1.98757,16.5065C2.19036,16.732,2.51812,16.8168,2.81522,16.7186L4.74641,16.069C5.44909,16.6159,6.2343,17.0489,7.08082,17.3503L7.45338,19.2567C7.50997,19.5491,7.75284,19.7768,8.06174,19.8303L8.1254,19.8415C8.74084,19.9464,9.37042,20,10,20C10.6296,20,11.2615,19.9464,11.8746,19.8415L11.9383,19.8303C12.2472,19.7768,12.49,19.5491,12.5466,19.2567L12.9168,17.3592C13.7704,17.0556,14.5603,16.6248,15.2677,16.0734L17.1848,16.7186C17.4795,16.8168,17.8096,16.7342,18.0124,16.5065L18.0549,16.4596C18.8755,15.5422,19.5074,14.5064,19.9342,13.3813L19.9554,13.3233C20.0615,13.0487,19.9743,12.7362,19.7361,12.542ZM16.5175,8.97483C16.5764,9.3119,16.6071,9.65791,16.6071,10.0039C16.6071,10.3499,16.5764,10.6959,16.5175,11.033L16.3618,11.9281L18.1233,13.3545C17.8568,13.9372,17.5196,14.4863,17.1188,14.9975L14.9305,14.2631L14.1901,14.839C13.6266,15.2765,12.9994,15.6203,12.3203,15.8614L11.4219,16.1806L10.9998,18.3459C10.3372,18.4173,9.66045,18.4173,8.9955,18.3459L8.57342,16.1761L7.6821,15.8524C7.01008,15.6114,6.38521,15.2676,5.82637,14.8323L5.08596,14.2541L2.88361,14.9953C2.48275,14.4841,2.14791,13.9327,1.8791,13.3523L3.65938,11.9125L3.50611,11.0196C3.44952,10.687,3.41887,10.3432,3.41887,10.0039C3.41887,9.66237,3.44716,9.32083,3.50611,8.98822L3.65938,8.09531L1.8791,6.6555C2.14556,6.07288,2.48275,5.52374,2.88361,5.01255L5.08596,5.75367L5.82637,5.17551C6.38521,4.74022,7.01008,4.39645,7.6821,4.15536L8.57578,3.83615L8.99786,1.66638C9.66045,1.59495,10.3372,1.59495,11.0021,1.66638L11.4242,3.83168L12.3226,4.1509C12.9994,4.39198,13.6289,4.73575,14.1925,5.17328L14.9329,5.7492L17.1211,5.01479C17.522,5.52598,17.8568,6.07734,18.1256,6.65773L16.3642,8.08416L16.5175,8.97483ZM10.0024,5.85189C7.7104,5.85189,5.85231,7.61092,5.85231,9.78068C5.85231,11.9504,7.7104,13.7095,10.0024,13.7095C12.2943,13.7095,14.1524,11.9504,14.1524,9.78068C14.1524,7.61092,12.2943,5.85189,10.0024,5.85189ZM11.8699,11.5486C11.37,12.0196,10.7074,12.2808,10.0024,12.2808C9.29732,12.2808,8.63473,12.0196,8.13483,11.5486C7.6373,11.0754,7.36142,10.4481,7.36142,9.78068C7.36142,9.11323,7.6373,8.48596,8.13483,8.01272C8.63473,7.53948,9.29732,7.28054,10.0024,7.28054C10.7074,7.28054,11.37,7.53948,11.8699,8.01272C12.3674,8.48596,12.6433,9.11323,12.6433,9.78068C12.6433,10.4481,12.3674,11.0754,11.8699,11.5486Z></path></svg><span></span></div><div class="item tools"><svg viewBox="0 0 20 20"><path d=M6.50977,1L13.4902,1C13.6406,1,13.7695,1.1104910000000001,13.7969,1.2631700000000001L14.0273,2.52277C14.1387,3.13147,14.6543,3.57143,15.2559,3.57143L17.5,3.57143C18.8809,3.57143,20,4.72254,20,6.14286L20,16.4286C20,17.8489,18.8809,19,17.5,19L2.5,19C1.11914,19,0,17.8489,0,16.4286L0,6.14286C0,4.72254,1.11914,3.57143,2.5,3.57143L4.74414,3.57143C5.3457,3.57143,5.86133,3.13147,5.97266,2.52277L6.20312,1.2631700000000001C6.23047,1.1104910000000001,6.35937,1,6.50977,1ZM15.2559,4.857139999999999C14.0547,4.857139999999999,13.0215,3.97522,12.7988,2.75982L12.7129,2.28571L7.28711,2.28571L7.20117,2.75982C6.98047,3.97522,5.94727,4.857139999999999,4.74414,4.857139999999999L2.5,4.857139999999999C1.81055,4.857139999999999,1.25,5.43371,1.25,6.14286L1.25,16.4286C1.25,17.1377,1.81055,17.7143,2.5,17.7143L17.5,17.7143C18.1895,17.7143,18.75,17.1377,18.75,16.4286L18.75,6.14286C18.75,5.43371,18.1895,4.857139999999999,17.5,4.857139999999999L15.2559,4.857139999999999ZM4.375,6.78571L3.125,6.78571C2.7793,6.78571,2.5,6.49844,2.5,6.14286C2.5,5.78728,2.7793,5.5,3.125,5.5L4.375,5.5C4.7207,5.5,5,5.78728,5,6.14286C5,6.49844,4.7207,6.78571,4.375,6.78571ZM10,6.14286C7.06641,6.14286,4.6875,8.58973,4.6875,11.6071C4.6875,14.6246,7.06641,17.0714,10,17.0714C12.9336,17.0714,15.3125,14.6246,15.3125,11.6071C15.3125,8.58973,12.9336,6.14286,10,6.14286ZM10,7.42857C11.0859,7.42857,12.1055,7.8625,12.873,8.65201C13.6406,9.44152,14.0625,10.49018,14.0625,11.6071C14.0625,12.7241,13.6406,13.7728,12.873,14.5623C12.1055,15.3518,11.0859,15.7857,10,15.7857C8.91406,15.7857,7.89453,15.3518,7.12695,14.5623C6.35937,13.7728,5.9375,12.7241,5.9375,11.6071C5.9375,10.49018,6.35938,9.44152,7.12695,8.65201C7.89453,7.8625,8.91406,7.42857,10,7.42857ZM10,9.67857C8.96484,9.67857,8.125,10.54241,8.125,11.6071C8.125,12.6719,8.96484,13.5357,10,13.5357C11.0352,13.5357,11.875,12.6719,11.875,11.6071C11.875,10.54241,11.0352,9.67857,10,9.67857ZM10,10.96429C10.3438,10.96429,10.625,11.2536,10.625,11.6071C10.625,11.9607,10.3438,12.25,10,12.25C9.65625,12.25,9.375,11.9607,9.375,11.6071C9.375,11.2536,9.65625,10.96429,10,10.96429Z></path></svg><span></span></div><div class="item tools"><svg viewBox="0 0 20 20"><path d="M2.22 11.78h1.6V18h-1.6zM6.76 9.78h1.6V18h-1.6zM11.3 11h1.6v7h-1.6zM15.84 7.43h1.6V18h-1.6z"></path><path d="M3.43 10.5l-0.78-0.92 4.75-3.93 0.74-0.02 3.83 2.93 4.35-5.9 0.96 0.7-4.73 6.5-0.84 0.12-3.95-3.03z"></path><path d="M16.62 3.23a1.2 1.2 0 1 1 2.4 0 1.2 1.2 0 1 1-2.4 0z"></path></svg><span></span></div><div class="item tools">'),Ga=X("<span>"),Tp=X('<svg viewBox="0 0 20 20"><path d=M1.08108,0L0,1.079L4.18919,5.27938L2.54826,6.91715L6.9112,6.91715L6.9112,2.56262L5.28957,4.18112L1.08108,0ZM15.8108,5.27938L20,1.079L18.9189,0L14.7104,4.18112L13.0888,2.56262L13.0888,6.91715L17.4517,6.91715L15.8108,5.27938ZM4.16988,14.7014L0.07722,18.8054L1.1583,20L5.27027,15.7996L6.9112,17.4374L6.9112,13.0829L2.54826,13.0829L4.16988,14.7014ZM17.4517,13.0829L13.0888,13.0829L13.0888,17.4374L14.7297,15.7996L18.8417,20L19.9228,18.8054L15.8301,14.7013L17.4517,13.0829Z>'),Ep=X('<svg viewBox="0 0 20 20"><path d=M2.93444,1.76899L7.57544,6.40999L6.38918,7.59626L1.76899,2.93444L0,4.70343L0,0L4.70343,0L2.93444,1.76899ZM6.40999,12.4037L1.76899,17.0447L0,15.2758L0,19.9792L4.70343,19.9792L2.93444,18.2102L7.57544,13.5692L6.40999,12.4037ZM15.2758,0L17.0447,1.76899L12.4037,6.40999L13.59,7.59626L18.231,2.95526L20,4.72425L20,0L15.2758,0ZM13.5692,12.4037L12.3829,13.59L17.0239,18.231L15.2549,20L19.9792,20L19.9792,15.2758L18.2102,17.0447L13.5692,12.4037Z>');const Ip=t=>{let r;const[e,a]=Tt(!1),i=()=>{a(n=>!n)};return zo(()=>{document.addEventListener("fullscreenchange",i),document.addEventListener("mozfullscreenchange",i),document.addEventListener("webkitfullscreenchange",i),document.addEventListener("msfullscreenchange",i)}),wa(()=>{document.removeEventListener("fullscreenchange",i),document.removeEventListener("mozfullscreenchange",i),document.removeEventListener("webkitfullscreenchange",i),document.removeEventListener("msfullscreenchange",i)}),(()=>{var n=Mp(),o=n.firstChild,s=o.firstChild,l=o.nextSibling,c=l.firstChild,u=c.nextSibling,d=l.nextSibling,v=d.firstChild,h=v.nextSibling,f=d.nextSibling,p=f.firstChild,g=p.nextSibling,m=f.nextSibling,b=m.firstChild,x=b.nextSibling,L=m.nextSibling,_=L.firstChild,M=_.nextSibling,w=L.nextSibling;return bi(k=>{r=k},n),tr(s,"click",t.onMenuClick),ct(n,st(be,{get when(){return t.symbol},get children(){var k=Sp(),C=k.firstChild;return tr(k,"click",t.onSymbolClick),ct(k,st(be,{get when(){return t.symbol.logo},get children(){var y=kp();return ve(()=>Fe(y,"src",t.symbol.logo)),y}}),C),ct(C,()=>t.symbol.shortName??t.symbol.name??t.symbol.ticker),k}}),l),ct(n,()=>t.periods.map(k=>(()=>{var C=Ga();return C.$$click=()=>{t.onPeriodChange(k)},ct(C,()=>k.text),ve(()=>Cr(C,`item period ${k.text===t.period.text?"selected":""}`)),C})()),l),tr(l,"click",t.onIndicatorClick),ct(u,()=>O("indicator",t.locale)),tr(d,"click",t.onTimezoneClick),ct(h,()=>O("timezone",t.locale)),tr(f,"click",t.onSettingClick),ct(g,()=>O("setting",t.locale)),tr(m,"click",t.onScreenshotClick),ct(x,()=>O("screenshot",t.locale)),tr(L,"click",t.onOrderFlowClick),ct(M,()=>O("order_flow",t.locale)),w.$$click=()=>{if(e())(document.exitFullscreen??document.msExitFullscreen??document.mozCancelFullScreen??document.webkitExitFullscreen).call(document);else{const k=r==null?void 0:r.parentElement;k&&(k.requestFullscreen??k.webkitRequestFullscreen??k.mozRequestFullScreen??k.msRequestFullscreen).call(k)}},ct(w,(()=>{var k=Ie(()=>!!e());return()=>k()?[Tp(),(()=>{var C=Ga();return ct(C,()=>O("exit_full_screen",t.locale)),C})()]:[Ep(),(()=>{var C=Ga();return ct(C,()=>O("full_screen",t.locale)),C})()]})()),ve(()=>Fe(s,"class",t.spread?"":"rotate")),n})()};Ge(["click"]);var Ap=X('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M12.41465,11L18.5,11C18.7761,11,19,11.22386,19,11.5C19,11.77614,18.7761,12,18.5,12L12.41465,12C12.20873,12.5826,11.65311,13,11,13C10.34689,13,9.79127,12.5826,9.58535,12L3.5,12C3.223857,12,3,11.77614,3,11.5C3,11.22386,3.223857,11,3.5,11L9.58535,11C9.79127,10.417404,10.34689,10,11,10C11.65311,10,12.20873,10.417404,12.41465,11Z stroke-opacity=0 stroke=none>');const Dp=()=>Ap();var Pp=X('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M6.91465,11L11.08535,11C11.29127,10.417404,11.84689,10,12.5,10C13.15311,10,13.70873,10.417404,13.91465,11L18.5,11C18.7761,11,19,11.22386,19,11.5C19,11.77614,18.7761,12,18.5,12L13.91465,12C13.70873,12.5826,13.15311,13,12.5,13C11.84689,13,11.29127,12.5826,11.08535,12L6.91465,12C6.70873,12.5826,6.15311,13,5.5,13C4.671573,13,4,12.32843,4,11.5C4,10.671573,4.671573,10,5.5,10C6.15311,10,6.70873,10.417404,6.91465,11Z stroke-opacity=0 stroke=none>');const Fp=()=>Pp();var Rp=X('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M6.91465,12.5C6.70873,13.0826,6.15311,13.5,5.5,13.5C4.671573,13.5,4,12.82843,4,12C4,11.171573,4.671573,10.5,5.5,10.5C6.15311,10.5,6.70873,10.917404,6.91465,11.5L16.0853,11.5C16.2913,10.917404,16.846899999999998,10.5,17.5,10.5C18.328400000000002,10.5,19,11.171573,19,12C19,12.82843,18.328400000000002,13.5,17.5,13.5C16.846899999999998,13.5,16.2913,13.0826,16.0853,12.5L6.91465,12.5Z stroke-opacity=0 stroke=none>');const Bp=()=>Rp();var Np=X('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M11,12.41465L11,18.5C11,18.7761,11.22386,19,11.5,19C11.77614,19,12,18.7761,12,18.5L12,12.41465C12.5826,12.20873,13,11.65311,13,11C13,10.34689,12.5826,9.79127,12,9.58535L12,3.5C12,3.223857,11.77614,3,11.5,3C11.22386,3,11,3.223857,11,3.5L11,9.58535C10.417404,9.79127,10,10.34689,10,11C10,11.65311,10.417404,12.20873,11,12.41465Z stroke-opacity=0 stroke=none>');const Op=()=>Np();var Vp=X('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M11.66558837890625,19C10.83716137890625,19,10.16558837890625,18.328400000000002,10.16558837890625,17.5C10.16558837890625,16.846899999999998,10.58298437890625,16.2913,11.16557337890625,16.0854L11.16557337890625,11.91464C10.58298437890625,11.70872,10.16558837890625,11.1531,10.16558837890625,10.5C10.16558837890625,9.8469,10.58298437890625,9.29128,11.16557337890625,9.08536L11.16557337890625,4.5C11.16557337890625,4.223857,11.38942837890625,4,11.66556837890625,4C11.94171837890625,4,12.16556837890625,4.223857,12.16556837890625,4.5L12.16556837890625,9.08535C12.74817837890625,9.291260000000001,13.16558837890625,9.846879999999999,13.16558837890625,10.5C13.16558837890625,11.153120000000001,12.74817837890625,11.708739999999999,12.16556837890625,11.91465L12.16556837890625,16.0854C12.74817837890625,16.2913,13.16558837890625,16.846899999999998,13.16558837890625,17.5C13.16558837890625,18.328400000000002,12.49401837890625,19,11.66558837890625,19Z stroke-opacity=0 stroke=none>');const zp=()=>Vp();var $p=X('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M11.165603637695312,6.91465C11.748203637695312,6.70873,12.165603637695312,6.15311,12.165603637695312,5.5C12.165603637695312,4.671573,11.494033637695313,4,10.665603637695312,4C9.837176637695313,4,9.165603637695312,4.671573,9.165603637695312,5.5C9.165603637695312,6.15311,9.583007637695312,6.70873,10.165603637695312,6.91465L10.165603637695312,16.0854C9.583007637695312,16.2913,9.165603637695312,16.846899999999998,9.165603637695312,17.5C9.165603637695312,18.328400000000002,9.837176637695313,19,10.665603637695312,19C11.494033637695313,19,12.165603637695312,18.328400000000002,12.165603637695312,17.5C12.165603637695312,16.846899999999998,11.748203637695312,16.2913,11.165603637695312,16.0854L11.165603637695312,6.91465Z stroke-opacity=0 fill-rule=evenodd fill-opacity=1>');const Wp=()=>$p();var Yp=X('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M5.146447,15.753C4.9511845,15.9483,4.9511845,16.2649,5.146447,16.4602C5.341709,16.6554,5.658291,16.6554,5.853554,16.4602L8.156600000000001,14.15711C8.352409999999999,14.25082,8.57173,14.3033,8.8033,14.3033C9.631730000000001,14.3033,10.3033,13.63172,10.3033,12.80329C10.3033,12.57172,10.250820000000001,12.352409999999999,10.157119999999999,12.15659L12.156600000000001,10.15711C12.352409999999999,10.250820000000001,12.571729999999999,10.30329,12.8033,10.30329C13.63173,10.30329,14.3033,9.63172,14.3033,8.80329C14.3033,8.57172,14.25082,8.352409999999999,14.15712,8.15659L16.4602,5.853553C16.6554,5.658291,16.6554,5.341709,16.4602,5.146447C16.2649,4.9511843,15.9483,4.9511843,15.753,5.146447L13.45001,7.449479999999999C13.25419,7.35577,13.03487,7.3032900000000005,12.8033,7.3032900000000005C11.97487,7.3032900000000005,11.3033,7.97487,11.3033,8.80329C11.3033,9.03487,11.35578,9.254190000000001,11.44949,9.450009999999999L9.450009999999999,11.449480000000001C9.254190000000001,11.35577,9.03487,11.30329,8.8033,11.30329C7.97487,11.30329,7.3033,11.97487,7.3033,12.80329C7.3033,13.03487,7.35578,13.25419,7.44949,13.45001L5.146447,15.753Z stroke-opacity=0 stroke=none>');const Zp=()=>Yp();var Hp=X('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M7.573332939453125,14.54567903564453C7.667042939453125,14.741499035644532,7.719512939453125,14.960809035644532,7.719512939453125,15.19239903564453C7.719512939453125,16.02079903564453,7.047942939453125,16.69239903564453,6.219512939453125,16.69239903564453C5.391085939453125,16.69239903564453,4.719512939453125,16.02079903564453,4.719512939453125,15.19239903564453C4.719512939453125,14.36394903564453,5.391085939453125,13.692379035644532,6.219512939453125,13.692379035644532C6.451092939453125,13.692379035644532,6.670412939453125,13.74485903564453,6.866232939453125,13.83856903564453L9.865702939453126,10.83909903564453C9.771992939453124,10.643279035644532,9.719512939453125,10.42395903564453,9.719512939453125,10.192379035644532C9.719512939453125,9.36394903564453,10.391082939453124,8.692379035644532,11.219512939453125,8.692379035644532C11.451092939453126,8.692379035644532,11.670412939453126,8.74485903564453,11.866232939453125,8.838569035644532L15.462112939453124,5.242645035644531C15.657412939453126,5.047383335644532,15.974012939453125,5.047383335644532,16.169212939453125,5.242645035644531C16.364512939453125,5.437907035644531,16.364512939453125,5.754489035644531,16.169212939453125,5.949752035644531L12.573332939453124,9.545679035644532C12.667042939453125,9.74149903564453,12.719512939453125,9.96080903564453,12.719512939453125,10.192379035644532C12.719512939453125,11.020809035644533,12.047942939453126,11.692379035644532,11.219512939453125,11.692379035644532C10.987942939453125,11.692379035644532,10.768632939453125,11.639909035644532,10.572812939453126,11.54619903564453L7.573332939453125,14.54567903564453Z stroke-opacity=0 stroke=none>');const jp=()=>Hp();var Xp=X('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M15.719512939453125,8.461776733398438C16.547912939453127,8.461776733398438,17.219512939453125,7.7902067333984375,17.219512939453125,6.9617767333984375C17.219512939453125,6.133349733398438,16.547912939453127,5.4617767333984375,15.719512939453125,5.4617767333984375C14.891082939453124,5.4617767333984375,14.219512939453125,6.133349733398438,14.219512939453125,6.9617767333984375C14.219512939453125,7.193346733398437,14.271992939453124,7.412666733398438,14.365692939453124,7.608486733398438L7.366222939453126,14.607956733398437C7.170402939453125,14.514256733398437,6.951082939453125,14.461776733398438,6.719512939453125,14.461776733398438C5.891085939453125,14.461776733398438,5.219512939453125,15.133346733398437,5.219512939453125,15.961776733398438C5.219512939453125,16.79017673339844,5.891085939453125,17.461776733398438,6.719512939453125,17.461776733398438C7.547942939453125,17.461776733398438,8.219512939453125,16.79017673339844,8.219512939453125,15.961776733398438C8.219512939453125,15.730176733398437,8.167032939453126,15.510876733398437,8.073322939453124,15.315066733398437L15.072802939453124,8.315586733398437C15.268612939453124,8.409296733398438,15.487912939453125,8.461776733398438,15.719512939453125,8.461776733398438Z stroke-opacity=0 stroke=none>');const Kp=()=>Xp();var Gp=X('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M17.0643,7.033864912109375L18,3.585784912109375L14.5078,4.509695912109375L15.3537,5.344934912109375L6.02026,14.560584912109375C5.87635,14.517484912109374,5.72366,14.494284912109375,5.5655,14.494284912109375C4.7009,14.494284912109375,4,15.186384912109375,4,16.040084912109375C4,16.893784912109375,4.7009,17.585784912109375,5.5655,17.585784912109375C6.43011,17.585784912109375,7.13101,16.893784912109375,7.13101,16.040084912109375C7.13101,15.722284912109375,7.03392,15.426984912109376,6.86744,15.181384912109374L16.0917,6.073604912109375L17.0643,7.033864912109375Z stroke-opacity=0 stroke=none>');const Up=()=>Gp();var Qp=X('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M6.91465,13.00505L18.5,13.00505C18.7761,13.00505,19,13.228909999999999,19,13.50505C19,13.781189999999999,18.7761,14.00505,18.5,14.00505L6.91465,14.00505C6.70873,14.58765,6.15311,15.00505,5.5,15.00505C4.671573,15.00505,4,14.33348,4,13.50505C4,12.67662,4.671573,12.00505,5.5,12.00505C6.15311,12.00505,6.70873,12.422450000000001,6.91465,13.00505ZM7.81404,11.625L10.48591,11.625L10.48591,10.90625L9.65193,10.90625L9.65193,7.125L8.997630000000001,7.125C8.71443,7.306641,8.415600000000001,7.419922,7.96443,7.498047L7.96443,8.05078L8.77497,8.05078L8.77497,10.90625L7.81404,10.90625L7.81404,11.625ZM11.081620000000001,11.625L14.0562,11.625L14.0562,10.88281L13.09724,10.88281C12.8863,10.88281,12.59333,10.90625,12.36482,10.93555C13.17537,10.11328,13.84724,9.2207,13.84724,8.39062C13.84724,7.541016,13.28865,7,12.4488,7C11.84333,7,11.446850000000001,7.234375,11.03279,7.679688L11.52497,8.16797C11.747630000000001,7.914062,12.0113,7.697266,12.33552,7.697266C12.7613,7.697266,13.00154,7.982422,13.00154,8.43359C13.00154,9.14648,12.29255,10.00781,11.081620000000001,11.11523L11.081620000000001,11.625ZM15.9605,11.75C16.8121,11.75,17.526899999999998,11.2832,17.526899999999998,10.4375C17.526899999999998,9.82031,17.142200000000003,9.43945,16.6441,9.30078L16.6441,9.27148C17.1129,9.08594,17.3824,8.7207,17.3824,8.21289C17.3824,7.421875,16.8004,7,15.9429,7C15.4215,7,14.9957,7.210938,14.6109,7.541016L15.066,8.11133C15.3258,7.849609,15.5836,7.697266,15.9019,7.697266C16.2789,7.697266,16.4957,7.914062,16.4957,8.28125C16.4957,8.70898,16.2301,9,15.4215,9L15.4215,9.63672C16.3804,9.63672,16.6383,9.91992,16.6383,10.38086C16.6383,10.79688,16.3336,11.03125,15.8824,11.03125C15.4742,11.03125,15.1578,10.82227,14.8922,10.55078L14.4781,11.13281C14.7906,11.486329999999999,15.2652,11.75,15.9605,11.75Z stroke-opacity=0 stroke=none>');const qp=()=>Qp();var Jp=X('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M3.146447,14.178126025390625C2.9511847,13.982826025390626,2.9511847,13.666226025390625,3.146447,13.470926025390625L7.39146,9.225966025390626C7.35417,9.095106025390624,7.33421,8.956946025390625,7.33421,8.814116025390625C7.33421,7.985696025390625,8.00578,7.314116025390625,8.834209999999999,7.314116025390625C8.97703,7.314116025390625,9.11519,7.334086025390625,9.24605,7.371366025390625L13.753,2.864373025390625C13.9483,2.669110325390625,14.2649,2.669110325390625,14.4602,2.864373025390625C14.6554,3.059635025390625,14.6554,3.376217025390625,14.4602,3.571479025390625L10.06916,7.962476025390625C10.23631,8.204386025390626,10.334209999999999,8.497826025390625,10.334209999999999,8.814116025390625C10.334209999999999,9.642546025390626,9.66264,10.314116025390625,8.834209999999999,10.314116025390625C8.51791,10.314116025390625,8.22448,10.216226025390625,7.98256,10.049076025390626L3.853554,14.178126025390625C3.658291,14.373326025390625,3.341709,14.373326025390625,3.146447,14.178126025390625ZM7.67736,19.188526025390626C7.4821,18.993226025390626,7.4821,18.676626025390625,7.67736,18.481426025390626L9.9804,16.178326025390625C9.88669,15.982526025390625,9.834209999999999,15.763226025390624,9.834209999999999,15.531626025390626C9.834209999999999,14.703226025390626,10.50578,14.031626025390626,11.33421,14.031626025390626C11.56579,14.031626025390626,11.78511,14.084126025390624,11.98093,14.177826025390624L13.9804,12.178356025390626C13.8867,11.982536025390624,13.8342,11.763216025390625,13.8342,11.531636025390625C13.8342,10.703206025390624,14.5058,10.031636025390625,15.3342,10.031636025390625C15.5658,10.031636025390625,15.7851,10.084116025390625,15.9809,10.177826025390626L18.284,7.874796025390625C18.4792,7.679536025390625,18.7958,7.679536025390625,18.9911,7.874796025390625C19.1863,8.070056025390624,19.1863,8.386636025390626,18.9911,8.581906025390625L16.688000000000002,10.884936025390624C16.7817,11.080756025390626,16.8342,11.300066025390626,16.8342,11.531636025390625C16.8342,12.360066025390624,16.162599999999998,13.031626025390626,15.3342,13.031626025390626C15.1026,13.031626025390626,14.8833,12.979126025390626,14.6875,12.885426025390625L12.68803,14.884926025390625C12.78174,15.080726025390625,12.83421,15.300026025390626,12.83421,15.531626025390626C12.83421,16.360026025390624,12.16264,17.031626025390626,11.33421,17.031626025390626C11.10264,17.031626025390626,10.88333,16.979126025390627,10.68751,16.885426025390625L8.38446,19.188526025390626C8.1892,19.383726025390626,7.87262,19.383726025390626,7.67736,19.188526025390626Z stroke-opacity=0 stroke=none>');const tg=()=>Jp();var eg=X('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M3.3367688759765626,12.63173C3.5320318759765623,12.82699,3.8486138759765627,12.82699,4.043876875976562,12.63173L11.822052875976562,4.853553C12.017312875976563,4.658291,12.017312875976563,4.341708,11.822052875976562,4.146446C11.626792875976562,3.9511843,11.310202875976563,3.9511843,11.114942875976563,4.146446L3.3367688759765626,11.92462C3.1415071759765625,12.11988,3.1415071759765625,12.43647,3.3367688759765626,12.63173ZM5.001492875976562,17.0351C4.806232875976562,16.8399,4.806232875976562,16.5233,5.001492875976562,16.328L7.304532875976562,14.025C7.210822875976563,13.82916,7.158352875976563,13.60984,7.158352875976563,13.37827C7.158352875976563,12.54984,7.829922875976562,11.87827,8.658352875976561,11.87827C8.889922875976563,11.87827,9.109232875976563,11.93075,9.305052875976562,12.02446L11.304532875976562,10.02498C11.210822875976563,9.82916,11.158352875976561,9.60984,11.158352875976561,9.37827C11.158352875976561,8.54984,11.829922875976562,7.8782700000000006,12.658352875976563,7.8782700000000006C12.889922875976563,7.8782700000000006,13.109232875976563,7.93075,13.305022875976562,8.024460000000001L15.608122875976562,5.72142C15.803322875976562,5.5261499999999995,16.119922875976563,5.5261499999999995,16.315222875976563,5.72142C16.510422875976563,5.9166799999999995,16.510422875976563,6.23326,16.315222875976563,6.42852L14.012122875976562,8.73156C14.105822875976562,8.92738,14.158322875976562,9.1467,14.158322875976562,9.37827C14.158322875976562,10.2067,13.486822875976562,10.87827,12.658352875976563,10.87827C12.426772875976562,10.87827,12.207452875976562,10.82579,12.011642875976563,10.73209L10.012162875976562,12.73156C10.105872875976562,12.92738,10.158352875976561,13.1467,10.158352875976561,13.37827C10.158352875976561,14.2067,9.486772875976563,14.8783,8.658352875976561,14.8783C8.426772875976562,14.8783,8.207452875976562,14.8258,8.011642875976563,14.7321L5.708602875976562,17.0351C5.513342875976562,17.2304,5.196752875976562,17.2304,5.001492875976562,17.0351ZM10.415712875976563,18.328C10.220452875976562,18.5233,9.903862875976563,18.5233,9.708602875976563,18.328C9.513342875976562,18.1328,9.513342875976562,17.816200000000002,9.708602875976563,17.6209L12.304532875976562,15.025C12.210822875976563,14.8292,12.158352875976563,14.6098,12.158352875976563,14.3783C12.158352875976563,13.54984,12.829922875976562,12.87827,13.658322875976562,12.87827C13.889922875976563,12.87827,14.109222875976563,12.93075,14.305022875976562,13.02446L17.486822875976564,9.84274C17.682022875976564,9.64747,17.99862287597656,9.64747,18.19392287597656,9.84274C18.38912287597656,10.038,18.38912287597656,10.35458,18.19392287597656,10.54984L15.012122875976562,13.73156C15.105822875976562,13.92738,15.158322875976562,14.1467,15.158322875976562,14.3783C15.158322875976562,15.2067,14.486822875976562,15.8783,13.658322875976562,15.8783C13.426822875976562,15.8783,13.207422875976562,15.8258,13.011642875976563,15.7321L10.415712875976563,18.328Z stroke-opacity=0 stroke=none>');const rg=()=>eg();var ag=X('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M13.1889,6C12.98303,6.582599999999999,12.42741,7,11.7743,7C11.12119,7,10.565570000000001,6.582599999999999,10.35965,6L3.5,6C3.223857,6,3,5.77614,3,5.5C3,5.22386,3.223857,5,3.5,5L10.35965,5C10.565570000000001,4.417404,11.12119,4,11.7743,4C12.42741,4,12.98303,4.417404,13.1889,5L18.5,5C18.7761,5,19,5.22386,19,5.5C19,5.77614,18.7761,6,18.5,6L13.1889,6ZM3,8.5C3,8.22386,3.223857,8,3.5,8L18.5,8C18.7761,8,19,8.22386,19,8.5C19,8.77614,18.7761,9,18.5,9L3.5,9C3.223857,9,3,8.77614,3,8.5ZM3.278549,11.5C3.278549,11.22386,3.502407,11,3.778549,11L18.7785,11C19.0547,11,19.2785,11.22386,19.2785,11.5C19.2785,11.77614,19.0547,12,18.7785,12L3.778549,12C3.502407,12,3.278549,11.77614,3.278549,11.5ZM3.139267,14.5C3.139267,14.2239,3.363124,14,3.6392670000000003,14L18.6393,14C18.915399999999998,14,19.1393,14.2239,19.1393,14.5C19.1393,14.7761,18.915399999999998,15,18.6393,15L3.6392670000000003,15C3.363124,15,3.139267,14.7761,3.139267,14.5ZM13.1889,18C12.98303,18.5826,12.42741,19,11.7743,19C11.12119,19,10.565570000000001,18.5826,10.35965,18L3.778549,18C3.502407,18,3.278549,17.7761,3.278549,17.5C3.278549,17.2239,3.502407,17,3.778549,17L10.35965,17C10.565570000000001,16.4174,11.12119,16,11.7743,16C12.42741,16,12.98303,16.4174,13.1889,17L18.7785,17C19.0547,17,19.2785,17.2239,19.2785,17.5C19.2785,17.7761,19.0547,18,18.7785,18L13.1889,18Z stroke-opacity=0 stroke=none>');const ig=()=>ag();var ng=X('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M4.91465,6C4.70873,6.582599999999999,4.15311,7,3.5,7C2.671573,7,2,6.32843,2,5.5C2,4.671573,2.671573,4,3.5,4C4.15311,4,4.70873,4.417404,4.91465,5L18.2257,5C18.5018,5,18.7257,5.22386,18.7257,5.5C18.7257,5.77614,18.5018,6,18.2257,6L4.91465,6ZM2.7257,8.5C2.7257,8.22386,2.949558,8,3.2257,8L18.2257,8C18.5018,8,18.7257,8.22386,18.7257,8.5C18.7257,8.77614,18.5018,9,18.2257,9L3.2257,9C2.949558,9,2.7257,8.77614,2.7257,8.5ZM3.00425,11.5C3.00425,11.22386,3.22811,11,3.50425,11L18.5042,11C18.7804,11,19.0042,11.22386,19.0042,11.5C19.0042,11.77614,18.7804,12,18.5042,12L3.50425,12C3.22811,12,3.00425,11.77614,3.00425,11.5ZM2.864967,14.5C2.864967,14.2239,3.08882,14,3.36497,14L18.365,14C18.6411,14,18.865,14.2239,18.865,14.5C18.865,14.7761,18.6411,15,18.365,15L3.36497,15C3.08882,15,2.864967,14.7761,2.864967,14.5ZM20,17.5C20,18.328400000000002,19.3284,19,18.5,19C17.846899999999998,19,17.2913,18.5826,17.0854,18L3.50425,18C3.22811,18,3.00425,17.7761,3.00425,17.5C3.00425,17.2239,3.22811,17,3.50425,17L17.0854,17C17.2913,16.4174,17.846899999999998,16,18.5,16C19.3284,16,20,16.671599999999998,20,17.5Z stroke-opacity=0 stroke=none>');const og=()=>ng();var sg=X('<svg class=icon-overlay viewBox="0 0 22 22"><ellipse cx=10.5 cy=11.5 rx=1.5 ry=1.5 stroke-opacity=0 stroke=none></ellipse><ellipse cx=17.5 cy=11.5 rx=1.5 ry=1.5 stroke-opacity=0 stroke=none></ellipse><ellipse cx=10.5 cy=11.5 rx=7 ry=7 fill-opacity=0 stroke-opacity=1 fill=none stroke-width=1></ellipse><ellipse cx=10.5 cy=11.5 rx=5 ry=5 fill-opacity=0 stroke-opacity=1 fill=none stroke-width=1></ellipse><ellipse cx=10.5 cy=11.5 rx=3 ry=3 fill-opacity=0 stroke-opacity=1 fill=none stroke-width=1>');const lg=()=>sg();var cg=X('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M3,7.32468C5.90649,3.3893050000000002,11.49833,2.81306,14.6674,6.31944C14.9056,6.1554199999999994,15.192,6.05979,15.5,6.05979C15.845,6.05979,16.1628,6.17974,16.4162,6.381349999999999L18.4509,4.23827L19,4.816615L16.8945,7.03429C16.962600000000002,7.21075,17,7.40319,17,7.60463C17,8.45782,16.328400000000002,9.14947,15.5,9.14947C14.6716,9.14947,14,8.45782,14,7.60463C14,7.36402,14.0534,7.13625,14.1487,6.93322C11.32695,3.748365,6.25159,4.253956,3.612785,7.82695L3,7.32468ZM14.09,15.4717C15.7427,13.78985,16.244500000000002,11.524740000000001,15.5633,9.30134L15.5618,9.30134L16.3012,9.0502C17.072400000000002,11.56646,16.497700000000002,14.158,14.6282,16.0599C12.28737,18.442,8.62386,18.6988,6.41348,16.4501C4.5526,14.5572,4.52076,11.19671,6.36766,9.3177C7.89069,7.76754,10.07544,7.706189999999999,11.56741,9.22363C11.95453,9.61742,12.24817,10.08363,12.43369,10.57677L14.1451,8.77421L14.6942,9.35256L12.64982,11.50582C12.65827,11.59712,12.66295,11.68839,12.66378,11.77936C12.87398,12.04523,13,12.38451,13,12.7541C13,13.60729,12.32843,14.2989,11.5,14.2989C10.67157,14.2989,10,13.60729,10,12.7541C10,11.90091,10.67157,11.20926,11.5,11.20926C11.60387,11.20926,11.70528,11.220130000000001,11.8032,11.240829999999999L11.81763,11.22564C11.69858,10.71874,11.42858,10.21929,11.0284,9.81179C9.844000000000001,8.60765,8.136890000000001,8.65592,6.90822,9.90586C5.37975,11.460930000000001,5.40693,14.288,6.95404,15.8619C8.84598,17.7867,12.03496,17.5626,14.09,15.4717Z stroke-opacity=0 stroke=none>');const ug=()=>cg();var dg=X('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M4,17.0854L4,3.5C4,3.223858,4.22386,3,4.5,3C4.77614,3,5,3.223858,5,3.5L5,10L7.57584,10L9.8127,4.46359C9.91614,4.20756,10.20756,4.08386,10.46359,4.1873000000000005C10.71963,4.29075,10.84333,4.58216,10.73988,4.8382000000000005L8.65438,10L11.08535,10C11.29127,9.4174,11.84689,9,12.5,9C12.65154,9,12.79784,9.02247,12.93573,9.06427L16.6464,5.35355C16.8417,5.15829,17.1583,5.15829,17.3536,5.35355C17.5488,5.54882,17.5488,5.8654,17.3536,6.06066L13.7475,9.66675C13.907,9.90508,14,10.19168,14,10.5C14,11.15311,13.5826,11.70873,13,11.91465L13,14.3638L18.3714,12.1936C18.6274,12.09015,18.918799999999997,12.21385,19.0222,12.46989C19.1257,12.72592,19.002,13.0173,18.746000000000002,13.1208L13,15.4423L13,18L19.5,18C19.7761,18,20,18.2239,20,18.5C20,18.7761,19.7761,19,19.5,19L5.91465,19C5.70873,19.5826,5.15311,20,4.5,20C3.671573,20,3,19.3284,3,18.5C3,17.846899999999998,3.417404,17.2913,4,17.0854ZM6.3729499999999994,17.0413L12,14.7678L12,11.91465C11.88136,11.87271,11.76956,11.81627,11.66675,11.74746L6.3729499999999994,17.0413ZM12,15.8463L6.6694700000000005,18L12,18L12,15.8463ZM6.38629,15.6137L8.250350000000001,11L11,11L6.38629,15.6137ZM5,11L7.17182,11L5,16.3754L5,11Z stroke-opacity=0 fill-rule=evenodd fill-opacity=1>');const hg=()=>dg();var vg=X('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M17,4.5C17,5.32843,16.328400000000002,6,15.5,6C15.0931,6,14.7241,5.83802,14.4539,5.57503L5.98992,8.32515C5.99658,8.38251,6,8.440850000000001,6,8.5C6,9.15311,5.582599999999999,9.70873,5,9.91465L5,11.08535C5.42621,11.236,5.763999999999999,11.57379,5.91465,12L19.5,12C19.7761,12,20,12.22386,20,12.5C20,12.77614,19.7761,13,19.5,13L5.91465,13C5.70873,13.5826,5.15311,14,4.5,14C3.671573,14,3,13.3284,3,12.5C3,11.84689,3.417404,11.29127,4,11.08535L4,9.91465C3.417404,9.70873,3,9.15311,3,8.5C3,7.67157,3.671573,7,4.5,7C4.90411,7,5.2709,7.15981,5.5406200000000005,7.41967L14.0093,4.66802C14.0032,4.6128599999999995,14,4.5568,14,4.5C14,3.671573,14.6716,3,15.5,3C16.328400000000002,3,17,3.671573,17,4.5ZM4,15.5C4,15.2239,4.22386,15,4.5,15L19.5,15C19.7761,15,20,15.2239,20,15.5C20,15.7761,19.7761,16,19.5,16L4.5,16C4.22386,16,4,15.7761,4,15.5ZM4,18.5C4,18.2239,4.22386,18,4.5,18L19.5,18C19.7761,18,20,18.2239,20,18.5C20,18.7761,19.7761,19,19.5,19L4.5,19C4.22386,19,4,18.7761,4,18.5Z stroke-opacity=0 stroke=none>');const fg=()=>vg();var pg=X('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M20,3.5C20,4.15311,19.5826,4.70873,19,4.91465L19,18.5C19,18.7761,18.7761,19,18.5,19L4.91465,19C4.70873,19.5826,4.15311,20,3.5,20C2.671573,20,2,19.3284,2,18.5C2,17.846899999999998,2.417404,17.2913,3,17.0854L3,3.5C3,3.22386,3.22386,3,3.5,3L17.0854,3C17.2913,2.417404,17.846899999999998,2,18.5,2C19.3284,2,20,2.671573,20,3.5ZM17.0854,4C17.236,4.42621,17.5738,4.763999999999999,18,4.91465L18,8L14,8L14,4L17.0854,4ZM13,4L13,8L9,8L9,4L13,4ZM13,9L9,9L9,13L13,13L13,9ZM13,14L9,14L9,18L13,18L13,14ZM14,18L14,14L18,14L18,18L14,18ZM18,13L14,13L14,9L18,9L18,13ZM4.91465,18C4.763999999999999,17.5738,4.42621,17.236,4,17.0854L4,14L8,14L8,18L4.91465,18ZM4,8L4,4L8,4L8,8L4,8ZM8,9L8,13L4,13L4,9L8,9Z stroke-opacity=0 fill-rule=evenodd fill-opacity=1>');const gg=()=>pg();var mg=X('<svg class=icon-overlay viewBox="0 0 22 22"><ellipse cx=10.5 cy=11.5 rx=1.5 ry=1.5 stroke-opacity=0 stroke=none></ellipse><ellipse cx=17.5 cy=11.5 rx=1.5 ry=1.5 stroke-opacity=0 stroke=none></ellipse><ellipse cx=10.5 cy=11.5 rx=7 ry=7 fill-opacity=0 fill=none stroke-opacity=1 stroke-width=1>');const yg=()=>mg();var _g=X('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M11.57625,6.9981C11.55099,6.999359999999999,11.52557,7,11.5,7C11.34,7,11.18584,6.97495,11.04125,6.9285499999999995L5.55401,16.4327C5.713760000000001,16.5905,5.83826,16.7839,5.91465,17L16.0854,17C16.2187,16.622700000000002,16.4987,16.314700000000002,16.8569,16.1445L11.57625,6.9981ZM12.50759,6.611219999999999C12.81005,6.336790000000001,13,5.94058,13,5.5C13,4.671573,12.32843,4,11.5,4C10.67157,4,10,4.671573,10,5.5C10,5.80059,10.08841,6.08052,10.24066,6.31522L4.64514,16.0069C4.59738,16.002299999999998,4.54896,16,4.5,16C3.671573,16,3,16.671599999999998,3,17.5C3,18.328400000000002,3.671573,19,4.5,19C5.15311,19,5.70873,18.5826,5.91465,18L16.0854,18C16.2913,18.5826,16.846899999999998,19,17.5,19C18.328400000000002,19,19,18.328400000000002,19,17.5C19,16.8365,18.5691,16.2735,17.971899999999998,16.075699999999998L12.50759,6.611219999999999Z stroke-opacity=0 fill-rule=evenodd fill-opacity=1>');const Cg=()=>_g();var bg=X('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M19,4.5C19,5.15311,18.5826,5.70873,18,5.91465L18,18.5C18,18.7761,17.7761,19,17.5,19L5.91465,19C5.70873,19.5826,5.15311,20,4.5,20C3.671573,20,3,19.3284,3,18.5C3,17.846899999999998,3.417404,17.2913,4,17.0854L4,4.5C4,4.22386,4.22386,4,4.5,4L16.0854,4C16.2913,3.417404,16.846899999999998,3,17.5,3C18.328400000000002,3,19,3.671573,19,4.5ZM5,5L16.0854,5C16.236,5.42621,16.5738,5.763999999999999,17,5.91465L17,18L5.91465,18C5.763999999999999,17.5738,5.42621,17.236,5,17.0854L5,5Z stroke-opacity=0 fill-rule=evenodd fill-opacity=1>');const xg=()=>bg();var wg=X('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M19.6401,7.99355C20.4028,7.92291,21,7.2811900000000005,21,6.5C21,5.671573,20.3284,5,19.5,5C18.8469,5,18.2913,5.417404,18.0854,6L7.62067,6C7.34453,6,7.12067,6.22386,7.12067,6.5C7.12067,6.5479,7.12741,6.59423,7.13999,6.63809L3.2294099999999997,15.0243C2.530138,15.1517,2,15.764,2,16.5C2,17.328400000000002,2.671573,18,3.5,18C4.15311,18,4.70873,17.5826,4.91465,17L14.5963,17C14.6456,17.076,14.7162,17.1396,14.8044,17.1807C15.0546,17.2974,15.3521,17.1891,15.4688,16.9388L19.6401,7.99355ZM14.7896,16.0293L18.6551,7.739599999999999C18.3942,7.56144,18.1925,7.30307,18.0854,7L8.0746,7L4.25044,15.2009C4.55701,15.3784,4.79493,15.6613,4.91465,16L14.6207,16C14.68,16,14.7368,16.0103,14.7896,16.0293Z stroke-opacity=0 fill-rule=evenodd fill-opacity=1>');const Lg=()=>wg();var kg=X('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M8.134443814697265,7.494615087890625L8.764323814697265,7.494615087890625L8.764323814697265,3.414215087890625L8.310223814697267,3.414215087890625L7.294603814697266,4.005035087890625L7.289713814697266,4.634915087890625L8.134443814697265,4.149892087890625L8.134443814697265,7.494615087890625ZM18.832003814697266,6.933095087890624Q19.004603814697266,6.635245087890625,19.004603814697266,6.2543850878906255Q19.004603814697266,5.884915087890625,18.845103814697264,5.593575087890625Q18.685503814697267,5.3006050878906255,18.399103814697266,5.136225087890625Q18.114303814697266,4.9702050878906245,17.754603814697266,4.9653250878906245L18.820603814697265,3.840647087890625L18.820603814697265,3.414215087890625L16.519203814697264,3.414215087890625L16.519203814697264,3.939931087890625L18.050803814697264,3.939931087890625L16.719403814697266,5.334785087890625L17.074203814697263,5.7205350878906245Q17.254903814697265,5.484525087890625,17.619503814697268,5.484525087890625Q17.980803814697268,5.484525087890625,18.187503814697266,5.689605087890625Q18.394203814697267,5.894685087890625,18.394203814697267,6.2543850878906255Q18.394203814697267,6.604315087890625,18.187503814697266,6.822415087890625Q17.980803814697268,7.0405150878906255,17.640603814697265,7.0405150878906255Q17.334603814697267,7.0405150878906255,17.124703814697266,6.890775087890625Q16.914703814697265,6.739415087890626,16.820303814697265,6.469225087890624L16.354803814697263,6.744295087890626Q16.480103814697266,7.125155087890625,16.821903814697265,7.341625087890625Q17.165403814697264,7.559725087890625,17.640603814697265,7.559725087890625Q18.039403814697266,7.559725087890625,18.348603814697267,7.393705087890625Q18.659503814697267,7.229315087890625,18.832003814697266,6.933095087890624ZM10.000003814697266,10.634915087890626C10.000003814697266,11.024655087890626,9.851363814697265,11.379685087890625,9.607683814697266,11.646395087890625L12.168903814697266,15.171615087890626C12.275403814697265,15.147615087890625,12.386203814697266,15.134915087890626,12.500003814697266,15.134915087890626C12.596503814697266,15.134915087890626,12.690803814697265,15.144015087890624,12.782303814697265,15.161415087890624L16.108803814697268,11.196955087890625C16.038703814697264,11.023375087890624,16.000003814697266,10.833655087890625,16.000003814697266,10.634915087890626C16.000003814697266,9.806495087890625,16.671603814697264,9.134915087890626,17.500003814697266,9.134915087890626C18.328403814697264,9.134915087890626,19.000003814697266,9.806495087890625,19.000003814697266,10.634915087890626C19.000003814697266,11.463345087890625,18.328403814697264,12.134915087890626,17.500003814697266,12.134915087890626C17.239503814697265,12.134915087890626,16.994503814697268,12.068495087890625,16.781003814697264,11.951675087890624L13.654703814697266,15.677415087890624C13.870303814697266,15.937215087890625,14.000003814697266,16.270915087890625,14.000003814697266,16.634915087890626C14.000003814697266,17.463315087890624,13.328403814697266,18.134915087890626,12.500003814697266,18.134915087890626C11.671573814697265,18.134915087890626,11.000003814697266,17.463315087890624,11.000003814697266,16.634915087890626C11.000003814697266,16.284415087890626,11.120193814697265,15.962015087890626,11.321603814697266,15.706715087890625L8.715393814697265,12.119565087890624C8.645053814697267,12.129685087890625,8.573143814697266,12.134915087890626,8.500003814697266,12.134915087890626C8.162103814697264,12.134915087890626,7.8503038146972655,12.023195087890626,7.599523814697266,11.834665087890626L4.505583814697266,15.521915087890624C4.809213814697266,15.796415087890624,5.000003814697266,16.193415087890624,5.000003814697266,16.634915087890626C5.000003814697266,17.463315087890624,4.328433814697266,18.134915087890626,3.5000038146972656,18.134915087890626C2.6715768146972656,18.134915087890626,2.0000038146972656,17.463315087890624,2.0000038146972656,16.634915087890626C2.0000038146972656,15.806515087890626,2.6715768146972656,15.134915087890626,3.5000038146972656,15.134915087890626C3.508253814697266,15.134915087890626,3.5164838146972657,15.135015087890626,3.524703814697266,15.135115087890625L7.033823814697266,10.953115087890625C7.011673814697265,10.850565087890626,7.000003814697266,10.744105087890624,7.000003814697266,10.634915087890626C7.000003814697266,9.806495087890625,7.671573814697266,9.134915087890626,8.500003814697266,9.134915087890626C9.328433814697267,9.134915087890626,10.000003814697266,9.806495087890625,10.000003814697266,10.634915087890626Z stroke-opacity=0 stroke=none>');const Sg=()=>kg();var Mg=X('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M8.13444,7.494615087890625L8.76432,7.494615087890625L8.76432,3.414215087890625L8.310220000000001,3.414215087890625L7.2946,4.005035087890625L7.28971,4.634915087890625L8.13444,4.149892087890625L8.13444,7.494615087890625ZM18.832,6.929835087890625Q19.0046,6.635245087890625,19.0046,6.2543850878906255Q19.0046,5.889805087890625,18.8451,5.5952050878906245Q18.6855,5.3006050878906255,18.3975,5.132965087890625Q18.1094,4.9653250878906245,17.7399,4.9653250878906245Q17.435499999999998,4.9653250878906245,17.1556,5.149245087890625L17.2793,3.939931087890625L18.8304,3.939931087890625L18.8304,3.414215087890625L16.7406,3.414215087890625L16.5094,5.665195087890625L17.0156,5.795405087890625Q17.095399999999998,5.655425087890626,17.2516,5.570795087890625Q17.4095,5.484525087890625,17.6357,5.484525087890625Q17.9694,5.484525087890625,18.1842,5.697745087890625Q18.4007,5.909335087890625,18.4007,6.2543850878906255Q18.4007,6.604315087890625,18.1842,6.822415087890625Q17.9694,7.0405150878906255,17.6292,7.0405150878906255Q17.3298,7.0405150878906255,17.119799999999998,6.890775087890625Q16.9098,6.739415087890626,16.825200000000002,6.474115087890625L16.3597,6.749175087890626Q16.470399999999998,7.110505087890624,16.807299999999998,7.335115087890625Q17.144199999999998,7.559725087890625,17.6292,7.559725087890625Q18.0296,7.559725087890625,18.3438,7.392075087890625Q18.6595,7.224435087890625,18.832,6.929835087890625ZM10,10.634915087890626C10,11.024655087890626,9.85136,11.379685087890625,9.60768,11.646395087890625L12.1689,15.171615087890626C12.2754,15.147615087890625,12.3862,15.134915087890626,12.5,15.134915087890626C12.5965,15.134915087890626,12.6908,15.144015087890624,12.7823,15.161415087890624L16.108800000000002,11.196955087890625C16.0387,11.023375087890624,16,10.833655087890625,16,10.634915087890626C16,9.806495087890625,16.671599999999998,9.134915087890626,17.5,9.134915087890626C18.3284,9.134915087890626,19,9.806495087890625,19,10.634915087890626C19,11.463345087890625,18.3284,12.134915087890626,17.5,12.134915087890626C17.2395,12.134915087890626,16.994500000000002,12.068505087890625,16.781,11.951675087890624L13.6547,15.677415087890624C13.8703,15.937215087890625,14,16.270915087890625,14,16.634915087890626C14,17.463315087890624,13.3284,18.134915087890626,12.5,18.134915087890626C11.67157,18.134915087890626,11,17.463315087890624,11,16.634915087890626C11,16.284415087890626,11.12019,15.962015087890626,11.3216,15.706715087890625L8.71539,12.119565087890624C8.645050000000001,12.129685087890625,8.57314,12.134915087890626,8.5,12.134915087890626C8.162099999999999,12.134915087890626,7.8503,12.023195087890626,7.59952,11.834665087890626L4.50558,15.521915087890624C4.80921,15.796415087890624,5,16.193415087890624,5,16.634915087890626C5,17.463315087890624,4.32843,18.134915087890626,3.5,18.134915087890626C2.671573,18.134915087890626,2,17.463315087890624,2,16.634915087890626C2,15.806515087890626,2.671573,15.134915087890626,3.5,15.134915087890626C3.5082500000000003,15.134915087890626,3.51648,15.135015087890626,3.5247,15.135115087890625L7.03382,10.953115087890625C7.01167,10.850565087890626,7,10.744105087890624,7,10.634915087890626C7,9.806495087890625,7.67157,9.134915087890626,8.5,9.134915087890626C9.32843,9.134915087890626,10,9.806495087890625,10,10.634915087890626Z stroke-opacity=0 stroke=none>');const Tg=()=>Mg();var Eg=X('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M18.8532,7.020985087890625Q19.0257,6.734525087890625,19.0257,6.369945087890625Q19.0257,6.020005087890625,18.8499,5.754705087890625Q18.6758,5.489415087890626,18.3649,5.339675087890625Q18.5944,5.209465087890625,18.7214,4.994615087890625Q18.8499,4.779775087890625,18.8499,4.5193550878906255Q18.8499,4.2003480878906245,18.7002,3.951324087890625Q18.5505,3.700673087890625,18.277,3.557444087890625Q18.0052,3.414215087890625,17.6455,3.414215087890625Q17.285800000000002,3.414215087890625,17.0107,3.557444087890625Q16.7357,3.700673087890625,16.5843,3.951324087890625Q16.4346,4.2003480878906245,16.4346,4.5193550878906255Q16.4346,4.779775087890625,16.561500000000002,4.994615087890625Q16.6901,5.209465087890625,16.919600000000003,5.339675087890625Q16.6055,5.489415087890626,16.4297,5.757965087890625Q16.255499999999998,6.024895087890625,16.255499999999998,6.369945087890625Q16.255499999999998,6.734525087890625,16.4297,7.020985087890625Q16.6055,7.305815087890625,16.919600000000003,7.465325087890625Q17.2354,7.624825087890625,17.6455,7.624825087890625Q18.0557,7.624825087890625,18.3682,7.465325087890625Q18.6807,7.305815087890625,18.8532,7.020985087890625ZM8.76432,7.559725087890625L8.13444,7.559725087890625L8.13444,4.214996087890625L7.28971,4.700025087890625L7.2946,4.070139087890625L8.310220000000001,3.479319087890625L8.76432,3.479319087890625L8.76432,7.559725087890625ZM17.1816,4.955555087890625Q17.0042,4.784655087890625,17.0042,4.5095950878906255Q17.0042,4.229645087890625,17.18,4.057119087890625Q17.355800000000002,3.884592087890625,17.6455,3.884592087890625Q17.935200000000002,3.884592087890625,18.1077,4.057119087890625Q18.2803,4.229645087890625,18.2803,4.5095950878906255Q18.2803,4.784655087890625,18.1045,4.955555087890625Q17.930300000000003,5.124825087890625,17.6455,5.124825087890625Q17.3607,5.124825087890625,17.1816,4.955555087890625ZM18.2217,5.7953950878906255Q18.4398,6.005365087890625,18.4398,6.3552950878906245Q18.4398,6.705235087890625,18.2217,6.915195087890625Q18.0052,7.125155087890625,17.6455,7.125155087890625Q17.285800000000002,7.125155087890625,17.067700000000002,6.915195087890625Q16.849600000000002,6.705235087890625,16.849600000000002,6.3552950878906245Q16.849600000000002,6.005365087890625,17.064500000000002,5.7953950878906255Q17.2793,5.585435087890625,17.6455,5.585435087890625Q18.0052,5.585435087890625,18.2217,5.7953950878906255ZM9.60768,11.711495087890626C9.85136,11.444785087890626,10,11.089765087890626,10,10.700025087890625C10,9.871595087890626,9.32843,9.200025087890625,8.5,9.200025087890625C7.67157,9.200025087890625,7,9.871595087890626,7,10.700025087890625C7,10.809205087890625,7.01167,10.915665087890625,7.03382,11.018215087890624L3.5247,15.200215087890625C3.51648,15.200115087890625,3.5082500000000003,15.200015087890625,3.5,15.200015087890625C2.671573,15.200015087890625,2,15.871615087890625,2,16.700015087890627C2,17.528415087890625,2.671573,18.200015087890627,3.5,18.200015087890627C4.32843,18.200015087890627,5,17.528415087890625,5,16.700015087890627C5,16.258515087890625,4.80921,15.861515087890625,4.50558,15.587015087890626L7.59952,11.899765087890625C7.8503,12.088295087890625,8.162099999999999,12.200025087890625,8.5,12.200025087890625C8.57314,12.200025087890625,8.645050000000001,12.194785087890626,8.71539,12.184675087890625L11.3216,15.771815087890625C11.12019,16.027215087890625,11,16.349515087890623,11,16.700015087890627C11,17.528415087890625,11.67157,18.200015087890627,12.5,18.200015087890627C13.3284,18.200015087890627,14,17.528415087890625,14,16.700015087890627C14,16.336015087890623,13.8703,16.002315087890626,13.6547,15.742515087890625L16.781,12.016775087890625C16.994500000000002,12.133605087890626,17.2395,12.200025087890625,17.5,12.200025087890625C18.3284,12.200025087890625,19,11.528445087890624,19,10.700025087890625C19,9.871595087890626,18.3284,9.200025087890625,17.5,9.200025087890625C16.671599999999998,9.200025087890625,16,9.871595087890626,16,10.700025087890625C16,10.898765087890624,16.0387,11.088475087890625,16.108800000000002,11.262055087890625L12.7823,15.226515087890625C12.6908,15.209115087890625,12.5965,15.200015087890625,12.5,15.200015087890625C12.3862,15.200015087890625,12.2754,15.212715087890626,12.1689,15.236715087890625L9.60768,11.711495087890626Z stroke-opacity=0 stroke=none>');const Ig=()=>Eg();var Ag=X('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M9.474616630859375,7.494615087890625L8.844736630859375,7.494615087890625L8.844736630859375,4.149892087890625L8.000006630859374,4.634915087890625L8.004896630859374,4.005035087890625L9.020516630859376,3.414215087890625L9.474616630859375,3.414215087890625L9.474616630859375,7.494615087890625ZM18.529296630859378,4.8318550878906255Q18.307996630859375,5.028795087890625,18.122396630859377,5.385245087890625Q17.868496630859376,5.019035087890625,17.629196630859376,4.8269750878906255Q17.389996630859375,4.634915087890625,17.168596630859376,4.634915087890625Q16.794296630859375,4.634915087890625,16.522496630859376,4.976715087890625Q16.252296630859377,5.3168850878906255,16.252296630859377,5.7856350878906255Q16.252296630859377,6.218575087890625,16.502896630859375,6.521315087890625Q16.755196630859373,6.822415087890625,17.114896630859377,6.822415087890625Q17.368796630859375,6.822415087890625,17.588596630859374,6.625475087890624Q17.809896630859377,6.428535087890625,17.998696630859374,6.0688350878906245Q18.249396630859373,6.439935087890625,18.488596630859377,6.631985087890625Q18.727896630859377,6.822415087890625,18.952496630859375,6.822415087890625Q19.326796630859373,6.822415087890625,19.596996630859376,6.482245087890625Q19.868796630859375,6.140455087890626,19.868796630859375,5.671705087890626Q19.868796630859375,5.238755087890625,19.618196630859376,4.937655087890625Q19.367496630859375,4.634915087890625,19.006196630859375,4.634915087890625Q18.750696630859377,4.634915087890625,18.529296630859378,4.8318550878906255ZM18.337296630859377,5.674955087890625L18.278696630859375,5.596835087890625Q18.449596630859375,5.272935087890625,18.622096630859374,5.1101750878906245Q18.794596630859374,4.947415087890625,18.967096630859373,4.947415087890625Q19.194996630859375,4.947415087890625,19.346396630859374,5.1345950878906255Q19.497696630859377,5.320135087890625,19.497696630859377,5.598455087890625Q19.497696630859377,5.8914250878906245,19.360996630859376,6.096505087890625Q19.224296630859374,6.301585087890626,19.027396630859375,6.301585087890626Q18.915096630859374,6.301585087890626,18.742496630859375,6.146965087890624Q18.569996630859375,5.992335087890625,18.337296630859377,5.674955087890625ZM17.785496630859377,5.779125087890625L17.842496630859372,5.857245087890625Q17.668296630859373,6.186025087890625,17.495796630859374,6.348785087890625Q17.324896630859374,6.509915087890625,17.153996630859375,6.509915087890625Q16.926096630859377,6.509915087890625,16.774796630859377,6.324375087890624Q16.623396630859375,6.137195087890625,16.623396630859375,5.858875087890625Q16.623396630859375,5.565905087890625,16.761696630859376,5.360825087890625Q16.900096630859373,5.1557550878906255,17.095396630859376,5.1557550878906255Q17.228896630859374,5.1557550878906255,17.365596630859375,5.2778250878906245Q17.502296630859377,5.399895087890625,17.785496630859377,5.779125087890625ZM10.710296630859375,10.634915087890626C10.710296630859375,11.024655087890626,10.561656630859375,11.379685087890625,10.317976630859375,11.646395087890625L12.879196630859376,15.171615087890626C12.985696630859374,15.147615087890625,13.096496630859376,15.134915087890626,13.210296630859375,15.134915087890626C13.306796630859376,15.134915087890626,13.401096630859374,15.144015087890624,13.492596630859374,15.161415087890624L16.819096630859377,11.196955087890625C16.748996630859374,11.023375087890624,16.710296630859375,10.833655087890625,16.710296630859375,10.634915087890626C16.710296630859375,9.806495087890625,17.381896630859373,9.134915087890626,18.210296630859375,9.134915087890626C19.038696630859373,9.134915087890626,19.710296630859375,9.806495087890625,19.710296630859375,10.634915087890626C19.710296630859375,11.463345087890625,19.038696630859373,12.134915087890626,18.210296630859375,12.134915087890626C17.949796630859375,12.134915087890626,17.704796630859377,12.068505087890625,17.491296630859374,11.951675087890624L14.364996630859375,15.677415087890624C14.580596630859375,15.937215087890625,14.710296630859375,16.270915087890625,14.710296630859375,16.634915087890626C14.710296630859375,17.463315087890624,14.038696630859375,18.134915087890626,13.210296630859375,18.134915087890626C12.381866630859374,18.134915087890626,11.710296630859375,17.463315087890624,11.710296630859375,16.634915087890626C11.710296630859375,16.284415087890626,11.830486630859374,15.962015087890626,12.031896630859375,15.706715087890625L9.425686630859374,12.119565087890624C9.355346630859376,12.129685087890625,9.283436630859375,12.134915087890626,9.210296630859375,12.134915087890626C8.872396630859374,12.134915087890626,8.560596630859376,12.023195087890626,8.309816630859375,11.834665087890626L5.215876630859375,15.521915087890624C5.519506630859375,15.796415087890624,5.710296630859375,16.193415087890624,5.710296630859375,16.634915087890626C5.710296630859375,17.463315087890624,5.038726630859375,18.134915087890626,4.210296630859375,18.134915087890626C3.381869630859375,18.134915087890626,2.710296630859375,17.463315087890624,2.710296630859375,16.634915087890626C2.710296630859375,15.806515087890626,3.381869630859375,15.134915087890626,4.210296630859375,15.134915087890626C4.218546630859375,15.134915087890626,4.226776630859375,15.135015087890626,4.234996630859375,15.135115087890625L7.744116630859375,10.953115087890625C7.721966630859375,10.850565087890626,7.710296630859375,10.744105087890624,7.710296630859375,10.634915087890626C7.710296630859375,9.806495087890625,8.381866630859374,9.134915087890626,9.210296630859375,9.134915087890626C10.038726630859376,9.134915087890626,10.710296630859375,9.806495087890625,10.710296630859375,10.634915087890626Z stroke-opacity=0 stroke=none>');const Dg=()=>Ag();var Pg=X('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M21,5.5C21,6.32843,20.3284,7,19.5,7C19.4136,7,19.3289,6.99269,19.2465,6.97866L15.6257,15.5086C15.8587,15.7729,16,16.119999999999997,16,16.5C16,17.328400000000002,15.3284,18,14.5,18C13.8469,18,13.2913,17.5826,13.0854,17L3.91465,17C3.70873,17.5826,3.15311,18,2.5,18C1.671573,18,1,17.328400000000002,1,16.5C1,15.6716,1.671573,15,2.5,15C2.5840199999999998,15,2.66643,15.0069,2.74668,15.0202L6.36934,6.48574C6.13933,6.22213,6,5.87733,6,5.5C6,4.671573,6.67157,4,7.5,4C8.15311,4,8.70873,4.417404,8.91465,5L18.0854,5C18.2913,4.417404,18.8469,4,19.5,4C20.3284,4,21,4.671573,21,5.5ZM18.0854,6L8.91465,6C8.892579999999999,6.06243,8.8665,6.12296,8.83672,6.18128L13.9814,15.0921C14.143,15.0325,14.3177,15,14.5,15C14.584,15,14.6664,15.0069,14.7467,15.0202L18.3693,6.48574C18.2462,6.3446,18.149,6.1802,18.0854,6ZM13.2036,15.745L8.0861,6.8811800000000005C7.90605,6.95768,7.70797,7,7.5,7C7.41359,7,7.32888,6.99269,7.24647,6.97866L3.62571,15.5086C3.7512,15.651,3.8501,15.8174,3.91465,16L13.0854,16C13.1169,15.9108,13.1566,15.8255,13.2036,15.745Z stroke-opacity=0 stroke=none>');const Fg=()=>Pg();var Rg=X('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M5.92159,5.93994C6.04014,5.90529,6.152620000000001,5.85639,6.25704,5.79523L9.12729,9.89437C9.045449999999999,10.07959,9,10.28449,9,10.5C9,10.79522,9.08529,11.07053,9.232569999999999,11.30262L4.97573,16.7511L5.92159,5.93994ZM4.92259,5.8848400000000005C4.38078,5.658659999999999,4,5.1238,4,4.5C4,3.671573,4.67157,3,5.5,3C6.2157,3,6.81433,3.50124,6.96399,4.17183L15.1309,4.88634C15.3654,4.36387,15.8902,4,16.5,4C17.328400000000002,4,18,4.67157,18,5.5C18,6.08983,17.659599999999998,6.60015,17.1645,6.84518L18.4264,14.0018C18.4508,14.0006,18.4753,14,18.5,14C19.3284,14,20,14.6716,20,15.5C20,16.328400000000002,19.3284,17,18.5,17C17.932499999999997,17,17.4386,16.6849,17.183799999999998,16.22L5.99686,18.5979C5.946429999999999,19.3807,5.29554,20,4.5,20C3.671573,20,3,19.3284,3,18.5C3,17.869300000000003,3.389292,17.3295,3.94071,17.1077L4.92259,5.8848400000000005ZM5.72452,17.6334C5.69799,17.596,5.6698,17.5599,5.64004,17.525100000000002L10.01843,11.92103C10.16958,11.97223,10.33155,12,10.5,12C10.80059,12,11.08052,11.91158,11.31522,11.75934L17.0606,15.0765C17.0457,15.1271,17.0335,15.1789,17.023899999999998,15.2317L5.72452,17.6334ZM11.92855,10.95875L17.4349,14.1379L16.1699,6.96356C15.9874,6.92257,15.8174,6.8483,15.6667,6.74746L11.99771,10.4165C11.99923,10.44414,12,10.47198,12,10.5C12,10.66,11.97495,10.814160000000001,11.92855,10.95875ZM10.5,9C10.259830000000001,9,10.03285,9.05644,9.83159,9.15679L7.04919,5.1831L15.0493,5.88302C15.054,5.90072,15.059,5.91829,15.0643,5.9357299999999995L11.56066,9.43934C11.28921,9.16789,10.91421,9,10.5,9Z stroke-opacity=0 fill-rule=evenodd fill-opacity=1>');const Bg=()=>Rg();var Ng=X('<svg viewBox="0 0 22 22"><path d=M4.727219638671875,8.007996215820313L9.973849638671876,2.7629472158203123C10.167279638671875,2.5696791158203123,10.480729638671875,2.5696791158203123,10.674169638671875,2.7629472158203123L13.223329638671874,5.311756215820313C13.416929638671874,5.505236215820313,13.416929638671874,5.8189862158203125,13.223329638671874,6.012466215820313L7.977129638671875,11.257906215820313C7.379859638671875,11.855176215820313,7.407609638671875,12.909396215820312,8.033809638671876,13.535596215820313C8.660409638671876,14.162596215820313,9.713849638671874,14.189996215820312,10.311129638671876,13.591896215820313L15.556929638671875,8.346066215820311C15.750429638671875,8.152526215820313,16.064229638671875,8.152526215820313,16.257629638671872,8.346066215820311L18.806529638671876,10.895266215820312C19.000029638671876,11.088746215820313,19.000029638671876,11.402496215820312,18.806529638671876,11.595976215820313L13.560629638671875,16.841796215820313C11.165619638671876,19.237196215820312,7.197149638671875,19.19919621582031,4.783499638671875,16.785496215820313C2.3698426386718747,14.371896215820312,2.331397638671875,10.403416215820313,4.727219638671875,8.007996215820313ZM12.172299638671875,5.662106215820312L10.323809638671875,3.8136162158203124L5.4287196386718755,8.709096215820313C3.422893638671875,10.714536215820312,3.4549956386718748,14.055196215820313,5.484999638671875,16.08479621582031C7.514609638671875,18.114796215820313,10.855289638671875,18.146496215820314,12.860719638671876,16.141096215820312L15.465629638671874,13.535796215820312L14.090929638671875,12.160756215820312L14.791629638671875,11.460436215820312L16.166229638671876,12.834996215820313L17.755829638671877,11.245226215820313L15.907729638671874,9.396736215820312L11.011839638671875,14.292596215820312C10.042809638671875,15.262396215820312,8.418249638671874,15.243796215820312,7.406019638671875,14.306496215820312L7.333099638671875,14.236296215820312C6.327599638671876,13.230796215820313,6.284009638671876,11.550396215820312,7.276419638671875,10.557586215820312L9.882199638671874,7.952026215820313L8.501079638671875,6.570906215820313L9.201789638671876,5.870186215820313L10.582939638671874,7.251336215820312L12.172299638671875,5.662106215820312Z stroke-opacity=0 fill-rule=evenodd fill-opacity=1>');const Og=t=>(()=>{var r=Ng();return Fe(r,"class",`icon-overlay ${t??""}`),r})();var Vg=X('<svg viewBox="0 0 22 22"><defs><clipPath id=master_svg0_151_615><rect x=0 y=0 width=22 height=22 rx=0></rect></clipPath></defs><g clip-path=url(#master_svg0_151_615)><path d=M19.672,3.0673368C19.4417,2.9354008,19.1463,3.00292252,18.9994,3.2210900000000002L17.4588,5.50622L16.743299999999998,3.781253L13.9915,7.4662L13.9618,7.51108C13.8339,7.72862,13.8936,8.005659999999999,14.1004,8.15391L14.1462,8.183430000000001C14.3683,8.308720000000001,14.6511,8.25001,14.8022,8.047229999999999L16.4907,5.78571L17.246299999999998,7.60713L19.8374,3.7635389999999997L19.8651,3.717088C19.9871,3.484615,19.9023,3.199273,19.672,3.0673368ZM4.79974,8.462530000000001L10.117740000000001,3.252975C10.31381,3.0610145,10.63152,3.0610145,10.82759,3.252975L13.4115,5.78453C13.6076,5.976710000000001,13.6076,6.28833,13.4115,6.4805L8.093869999999999,11.69045C7.48847,12.28368,7.51659,13.3308,8.151309999999999,13.9528C8.786439999999999,14.5755,9.85421,14.6027,10.45961,14.0087L15.7768,8.79831C15.9729,8.60609,16.2909,8.60609,16.487099999999998,8.79831L19.0705,11.33026C19.2667,11.52244,19.2667,11.83406,19.0705,12.02623L13.7533,17.2366C11.32572,19.6158,7.30328,19.578,4.85679,17.1807C2.410298,14.7834,2.371331,10.84174,4.79974,8.462530000000001ZM12.3461,6.1325199999999995L10.47246,4.29654L5.51079,9.15889C3.477674,11.15076,3.510214,14.4688,5.56784,16.4847C7.62506,18.500999999999998,11.01117,18.5325,13.0439,16.540599999999998L15.6842,13.9529L14.2908,12.58718L15.0011,11.89161L16.394399999999997,13.2569L18.0056,11.67786L16.1323,9.84188L11.16985,14.7046C10.18764,15.6679,8.540980000000001,15.6494,7.51498,14.7184L7.44107,14.6487C6.4219,13.65,6.37771,11.98096,7.38362,10.994869999999999L10.02485,8.40693L8.624939999999999,7.03516L9.335180000000001,6.33919L10.73512,7.71099L12.3461,6.1325199999999995Z stroke-opacity=0 fill-rule=evenodd fill-opacity=1>');const zg=t=>(()=>{var r=Vg();return Fe(r,"class",`icon-overlay ${t??""}`),r})();var $g=X('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M11,17C5.80945,17,3.667717,12.85,3.113386,11.575C2.9622047,11.2,2.9622047,10.8,3.113386,10.425C3.667717,9.15,5.80945,5,11,5C16.165399999999998,5,18.3323,9.15,18.8866,10.425C19.0378,10.8,19.0378,11.2,18.8866,11.575C18.3323,12.85,16.165399999999998,17,11,17ZM4.04567,10.8C3.995276,10.925,3.995276,11.05,4.04567,11.175C4.52441,12.325,6.43937,16,11,16C15.5606,16,17.4756,12.325,17.9543,11.2C18.0047,11.075,18.0047,10.95,17.9543,10.825C17.4756,9.675,15.5606,6,11,6C6.43937,6,4.52441,9.675,4.04567,10.8ZM11,13.5C9.61417,13.5,8.480319999999999,12.375,8.480319999999999,11C8.480319999999999,9.625,9.61417,8.5,11,8.5C12.38583,8.5,13.5197,9.625,13.5197,11C13.5197,12.375,12.38583,13.5,11,13.5ZM11,9.5C10.1685,9.5,9.48819,10.175,9.48819,11C9.48819,11.825,10.1685,12.5,11,12.5C11.8315,12.5,12.51181,11.825,12.51181,11C12.51181,10.175,11.8315,9.5,11,9.5Z stroke-opacity=0 fill-opacity=1>');const Wg=()=>$g();var Yg=X('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M5.80417,14.9887L4.62563,16.167299999999997C4.43037,16.3625,4.43037,16.6791,4.62563,16.8744C4.82089,17.0696,5.13748,17.0696,5.332739999999999,16.8744L6.62638,15.5807C7.75595,16.290100000000002,9.19328,16.7929,11,16.7929C16.165399999999998,16.7929,18.3323,12.64289,18.8866,11.36789C19.0378,10.99289,19.0378,10.59289,18.8866,10.21789C18.5549,9.45486,17.6456,7.66212,15.8617,6.34545L17.3536,4.853553C17.5488,4.658291,17.5488,4.341709,17.3536,4.146447C17.1583,3.9511845,16.8417,3.9511845,16.6464,4.146447L15.0014,5.7915399999999995C13.9314,5.1969,12.61166,4.792893,11,4.792893C5.80945,4.792893,3.667717,8.94289,3.113386,10.21789C2.9622049,10.59289,2.9622049,10.99289,3.113386,11.36789C3.424435,12.08333,4.2353000000000005,13.70399,5.80417,14.9887ZM7.36012,14.847C8.32327,15.4074,9.52286,15.7929,11,15.7929C15.5606,15.7929,17.4756,12.11789,17.9543,10.99289C18.0047,10.86789,18.0047,10.74289,17.9543,10.61789C17.659,9.90846,16.8171,8.23812,15.1447,7.06241L12.96929,9.23782C13.3134,9.66543,13.5197,10.20642,13.5197,10.79289C13.5197,12.16789,12.38583,13.29289,11,13.29289C10.41596,13.29289,9.87667,13.09308,9.44815,12.75896L7.36012,14.847ZM8.794609999999999,11.99829L6.520099999999999,14.2728C5.06905,13.12119,4.32057,11.628250000000001,4.04567,10.96789C3.995275,10.84289,3.995275,10.71789,4.04567,10.59289C4.52441,9.46789,6.43937,5.79289,11,5.79289C12.28868,5.79289,13.3661,6.086320000000001,14.2596,6.53329L12.19759,8.5953C11.84086,8.40257,11.43271,8.29289,11,8.29289C9.61417,8.29289,8.480319999999999,9.41789,8.480319999999999,10.79289C8.480319999999999,11.22918,8.594470000000001,11.64029,8.794609999999999,11.99829ZM10.16528,12.04183C10.404869999999999,12.20032,10.692070000000001,12.29289,11,12.29289C11.8315,12.29289,12.51181,11.61789,12.51181,10.79289C12.51181,10.48318,12.41593,10.194600000000001,12.25216,9.95494L10.16528,12.04183ZM11.43602,9.35687L9.55616,11.236740000000001C9.512,11.09633,9.48819,10.94724,9.48819,10.79289C9.48819,9.96789,10.1685,9.29289,11,9.29289C11.15142,9.29289,11.29782,9.31528,11.43602,9.35687Z stroke-opacity=0 fill-rule=evenodd fill-opacity=1>');const Zg=()=>Yg();var Hg=X('<svg class=icon-overlay viewBox="0 0 22 22"><defs><clipPath id=master_svg0_151_625><rect x=0 y=0 width=22 height=22 rx=0></rect></clipPath></defs><g clip-path=url(#master_svg0_151_625)><path d=M14.5385,9.76923L15.6538,9.76923C16.6538,9.76923,17.4615,10.576920000000001,17.4615,11.576920000000001L17.4615,17.1923C17.4615,18.1923,16.6538,19,15.6538,19L5.80769,19C4.807692,19,4,18.1923,4,17.1923L4,11.576920000000001C4,10.576920000000001,4.807692,9.76923,5.80769,9.76923L7.23077,9.76923L7.23077,7.576919999999999C7.23077,5.61538,8.88462,4,10.88462,4C12.88462,4,14.5385,5.61538,14.5385,7.576919999999999L14.5385,9.76923ZM10.88461,5.15385C9.5,5.15385,8.38461,6.23077,8.38461,7.576919999999999L8.38461,9.76923L13.38462,9.76923L13.38462,7.576919999999999C13.38462,6.23077,12.26923,5.15385,10.88461,5.15385ZM15.6538,17.8462C16,17.8462,16.3077,17.5385,16.3077,17.1923L16.3077,11.576920000000001C16.3077,11.23077,16,10.923079999999999,15.6538,10.923079999999999L5.80769,10.923079999999999C5.46154,10.923079999999999,5.15385,11.23077,5.15385,11.576920000000001L5.15385,17.1923C5.15385,17.5385,5.46154,17.8462,5.80769,17.8462L15.6538,17.8462ZM10.153839999999999,12.65385C10.153839999999999,12.34615,10.42307,12.07692,10.73076,12.07692C11.038450000000001,12.07692,11.307680000000001,12.34615,11.307680000000001,12.65385L11.307680000000001,14.5769C11.307680000000001,14.8846,11.038450000000001,15.1538,10.73076,15.1538C10.42307,15.1538,10.153839999999999,14.8846,10.153839999999999,14.5769L10.153839999999999,12.65385Z stroke-opacity=0 fill-rule=evenodd fill-opacity=1>');const jg=()=>Hg();var Xg=X('<svg class=icon-overlay viewBox="0 0 22 22"><defs><clipPath id=master_svg0_151_620><rect x=0 y=0 width=22 height=22 rx=0></rect></clipPath></defs><g clip-path=url(#master_svg0_151_620)><path d=M8.38461,9.76923L15.6538,9.76923C16.6538,9.76923,17.4615,10.576920000000001,17.4615,11.576920000000001L17.4615,17.1923C17.4615,18.1923,16.6538,19,15.6538,19L5.80769,19C4.807692,19,4,18.1923,4,17.1923L4,11.576920000000001C4,10.576920000000001,4.807693,9.76923,5.80769,9.76923L7.23077,9.76923L7.23077,7.576919999999999C7.23077,5.61538,8.88462,4,10.88462,4C12.46154,4,13.84615,4.961539,14.3462,6.423080000000001C14.4615,6.73077,14.3077,7.038460000000001,14,7.15385C13.69231,7.26923,13.38461,7.11538,13.26923,6.80769C12.92308,5.80769,11.96154,5.15385,10.88462,5.15385C9.5,5.15385,8.38461,6.23077,8.38461,7.576919999999999L8.38461,9.76923ZM15.6538,17.8462C16,17.8462,16.3077,17.5385,16.3077,17.1923L16.3077,11.576920000000001C16.3077,11.23077,16,10.923079999999999,15.6538,10.923079999999999L5.80769,10.923079999999999C5.46154,10.923079999999999,5.15385,11.23077,5.15385,11.576920000000001L5.15385,17.1923C5.15385,17.5385,5.46154,17.8462,5.80769,17.8462L15.6538,17.8462ZM10.153839999999999,12.65385C10.153839999999999,12.34615,10.42307,12.07692,10.73076,12.07692C11.03846,12.07692,11.307690000000001,12.34615,11.307690000000001,12.65385L11.307690000000001,14.5769C11.307690000000001,14.8846,11.03846,15.1538,10.73076,15.1538C10.42307,15.1538,10.153839999999999,14.8846,10.153839999999999,14.5769L10.153839999999999,12.65385Z stroke-opacity=0 fill-rule=evenodd fill-opacity=1>');const Kg=()=>Xg();var Gg=X('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M16.966900000000003,8.67144C16.6669,8.67144,16.4247,8.91558,16.4247,9.21802L16.4247,16.631500000000003C16.4247,17.322,16.007199999999997,17.9068,15.5139,17.9068L13.93072,17.9068L13.93072,9.2162C13.93072,8.91741,13.68675,8.67144,13.38855,8.67144C13.09036,8.67144,12.84639,8.91741,12.84639,9.21802L12.84639,17.9068L10.151810000000001,17.9068L10.151810000000001,9.21802C10.151810000000001,8.91741,9.90783,8.67144,9.609639999999999,8.67144C9.31145,8.67144,9.06747,8.91741,9.06747,9.219850000000001L9.06747,17.9068L7.48614,17.9068C6.99277,17.9068,6.5753,17.322,6.5753,16.631500000000003L6.5753,9.21802C6.5753,8.91558,6.333130000000001,8.67144,6.03313,8.67144C5.73313,8.67144,5.49096,8.91558,5.49096,9.21802L5.49096,16.631500000000003C5.49096,17.9378,6.385540000000001,19,7.48614,19L15.512,19C16.6127,19,17.509,17.9378,17.509,16.631500000000003L17.509,9.21802C17.509,8.91558,17.2669,8.67144,16.966900000000003,8.67144ZM18.4578,6.21183L4.542169,6.21183C4.243976,6.21183,4,6.45779,4,6.75841C4,7.05903,4.243976,7.30499,4.542169,7.30499L18.4578,7.30499C18.756,7.30499,19,7.05903,19,6.75841C19,6.45779,18.756,6.21183,18.4578,6.21183ZM8.68072,5.10045L14.3193,5.10045C14.6175,5.10045,14.8614,4.852666,14.8614,4.550225C14.8614,4.247783,14.6175,4,14.3193,4L8.68072,4C8.38253,4,8.13855,4.247783,8.13855,4.550225C8.13855,4.852666,8.38253,5.10045,8.68072,5.10045Z stroke-opacity=0 fill-opacity=1>');const Ug=()=>Gg(),Qg={horizontalStraightLine:Dp,horizontalRayLine:Fp,horizontalSegment:Bp,verticalStraightLine:Op,verticalRayLine:zp,verticalSegment:Wp,straightLine:Zp,rayLine:jp,segment:Kp,arrow:Up,priceLine:qp,priceChannelLine:tg,parallelStraightLine:rg,fibonacciLine:ig,fibonacciSegment:og,fibonacciCircle:lg,fibonacciSpiral:ug,fibonacciSpeedResistanceFan:hg,fibonacciExtension:fg,gannBox:gg,circle:yg,triangle:Cg,rect:xg,parallelogram:Lg,threeWaves:Sg,fiveWaves:Tg,eightWaves:Ig,anyWaves:Dg,abcd:Fg,xabcd:Bg,weak_magnet:Og,strong_magnet:zg,lock:jg,unlock:Kg,visible:Wg,invisible:Zg,remove:Ug};function qg(t){return[{key:"horizontalStraightLine",text:O("horizontal_straight_line",t)},{key:"horizontalRayLine",text:O("horizontal_ray_line",t)},{key:"horizontalSegment",text:O("horizontal_segment",t)},{key:"verticalStraightLine",text:O("vertical_straight_line",t)},{key:"verticalRayLine",text:O("vertical_ray_line",t)},{key:"verticalSegment",text:O("vertical_segment",t)},{key:"straightLine",text:O("straight_line",t)},{key:"rayLine",text:O("ray_line",t)},{key:"segment",text:O("segment",t)},{key:"arrow",text:O("arrow",t)},{key:"priceLine",text:O("price_line",t)}]}function Jg(t){return[{key:"priceChannelLine",text:O("price_channel_line",t)},{key:"parallelStraightLine",text:O("parallel_straight_line",t)}]}function tm(t){return[{key:"circle",text:O("circle",t)},{key:"rect",text:O("rect",t)},{key:"parallelogram",text:O("parallelogram",t)},{key:"triangle",text:O("triangle",t)}]}function em(t){return[{key:"fibonacciLine",text:O("fibonacci_line",t)},{key:"fibonacciSegment",text:O("fibonacci_segment",t)},{key:"fibonacciCircle",text:O("fibonacci_circle",t)},{key:"fibonacciSpiral",text:O("fibonacci_spiral",t)},{key:"fibonacciSpeedResistanceFan",text:O("fibonacci_speed_resistance_fan",t)},{key:"fibonacciExtension",text:O("fibonacci_extension",t)},{key:"gannBox",text:O("gann_box",t)}]}function rm(t){return[{key:"xabcd",text:O("xabcd",t)},{key:"abcd",text:O("abcd",t)},{key:"threeWaves",text:O("three_waves",t)},{key:"fiveWaves",text:O("five_waves",t)},{key:"eightWaves",text:O("eight_waves",t)},{key:"anyWaves",text:O("any_waves",t)}]}function am(t){return[{key:"weak_magnet",text:O("weak_magnet",t)},{key:"strong_magnet",text:O("strong_magnet",t)}]}const Be=t=>Qg[t.name](t.class);var im=X('<div class=klinecharts-pro-drawing-bar><span class=split-line></span><div class=item tabindex=0><span style=width:32px;height:32px></span><div class=icon-arrow><svg viewBox="0 0 4 6"><path d=M1.07298,0.159458C0.827521,-0.0531526,0.429553,-0.0531526,0.184094,0.159458C-0.0613648,0.372068,-0.0613648,0.716778,0.184094,0.929388L2.61275,3.03303L0.260362,5.07061C0.0149035,5.28322,0.0149035,5.62793,0.260362,5.84054C0.505822,6.05315,0.903789,6.05315,1.14925,5.84054L3.81591,3.53075C4.01812,3.3556,4.05374,3.0908,3.92279,2.88406C3.93219,2.73496,3.87113,2.58315,3.73964,2.46925L1.07298,0.159458Z stroke=none stroke-opacity=0></path></svg></div></div><div class=item><span style=width:32px;height:32px></span></div><div class=item><span style=width:32px;height:32px></span></div><span class=split-line></span><div class=item><span style=width:32px;height:32px>'),nm=X('<div class=item tabindex=0><span style=width:32px;height:32px></span><div class=icon-arrow><svg viewBox="0 0 4 6"><path d=M1.07298,0.159458C0.827521,-0.0531526,0.429553,-0.0531526,0.184094,0.159458C-0.0613648,0.372068,-0.0613648,0.716778,0.184094,0.929388L2.61275,3.03303L0.260362,5.07061C0.0149035,5.28322,0.0149035,5.62793,0.260362,5.84054C0.505822,6.05315,0.903789,6.05315,1.14925,5.84054L3.81591,3.53075C4.01812,3.3556,4.05374,3.0908,3.92279,2.88406C3.93219,2.73496,3.87113,2.58315,3.73964,2.46925L1.07298,0.159458Z stroke=none stroke-opacity=0>'),zn=X("<li><span style=padding-left:8px>");const $n="drawing_tools",om=t=>{const[r,e]=Tt("horizontalStraightLine"),[a,i]=Tt("priceChannelLine"),[n,o]=Tt("circle"),[s,l]=Tt("fibonacciLine"),[c,u]=Tt("xabcd"),[d,v]=Tt("weak_magnet"),[h,f]=Tt("normal"),[p,g]=Tt(!1),[m,b]=Tt(!0),[x,L]=Tt(""),_=je(()=>[{key:"singleLine",icon:r(),list:qg(t.locale),setter:e},{key:"moreLine",icon:a(),list:Jg(t.locale),setter:i},{key:"polygon",icon:n(),list:tm(t.locale),setter:o},{key:"fibonacci",icon:s(),list:em(t.locale),setter:l},{key:"wave",icon:c(),list:rm(t.locale),setter:u}]),M=je(()=>am(t.locale));return(()=>{var w=im(),k=w.firstChild,C=k.nextSibling,y=C.firstChild,T=y.nextSibling,I=T.firstChild,E=C.nextSibling,A=E.firstChild,F=E.nextSibling,N=F.firstChild,B=F.nextSibling,Y=B.nextSibling,K=Y.firstChild;return ct(w,()=>_().map($=>(()=>{var ut=nm(),rt=ut.firstChild,z=rt.nextSibling,tt=z.firstChild;return ut.addEventListener("blur",()=>{L("")}),rt.$$click=()=>{t.onDrawingItemClick({groupId:$n,name:$.icon,visible:m(),lock:p(),mode:h()})},ct(rt,st(Be,{get name(){return $.icon}})),z.$$click=()=>{$.key===x()?L(""):L($.key)},ct(ut,(()=>{var mt=Ie(()=>$.key===x());return()=>mt()&&st(Ia,{class:"list",get children(){return $.list.map(ft=>(()=>{var yt=zn(),V=yt.firstChild;return yt.$$click=()=>{$.setter(ft.key),t.onDrawingItemClick({name:ft.key,lock:p(),mode:h()}),L("")},ct(yt,st(Be,{get name(){return ft.key}}),V),ct(V,()=>ft.text),yt})())}})})(),null),ve(()=>Fe(tt,"class",$.key===x()?"rotate":"")),ut})()),k),C.addEventListener("blur",()=>{L("")}),y.$$click=()=>{let $=d();h()!=="normal"&&($="normal"),f($),t.onModeChange($)},ct(y,(()=>{var $=Ie(()=>d()==="weak_magnet");return()=>$()?Ie(()=>h()==="weak_magnet")()?st(Be,{name:"weak_magnet",class:"selected"}):st(Be,{name:"weak_magnet"}):Ie(()=>h()==="strong_magnet")()?st(Be,{name:"strong_magnet",class:"selected"}):st(Be,{name:"strong_magnet"})})()),T.$$click=()=>{x()==="mode"?L(""):L("mode")},ct(C,(()=>{var $=Ie(()=>x()==="mode");return()=>$()&&st(Ia,{class:"list",get children(){return M().map(ut=>(()=>{var rt=zn(),z=rt.firstChild;return rt.$$click=()=>{v(ut.key),f(ut.key),t.onModeChange(ut.key),L("")},ct(rt,st(Be,{get name(){return ut.key}}),z),ct(z,()=>ut.text),rt})())}})})(),null),A.$$click=()=>{const $=!p();g($),t.onLockChange($)},ct(A,(()=>{var $=Ie(()=>!!p());return()=>$()?st(Be,{name:"lock"}):st(Be,{name:"unlock"})})()),N.$$click=()=>{const $=!m();b($),t.onVisibleChange($)},ct(N,(()=>{var $=Ie(()=>!!m());return()=>$()?st(Be,{name:"visible"}):st(Be,{name:"invisible"})})()),K.$$click=()=>{t.onRemoveClick($n)},ct(K,st(Be,{name:"remove"})),ve(()=>Fe(I,"class",x()==="mode"?"rotate":"")),w})()};Ge(["click"]);var Wn=X("<li class=title>"),Yn=X("<li class=row>");const sm=t=>st(Wr,{get title(){return O("indicator",t.locale)},width:400,get onClose(){return t.onClose},get children(){return st(Ia,{class:"klinecharts-pro-indicator-modal-list",get children(){return[(()=>{var r=Wn();return ct(r,()=>O("main_indicator",t.locale)),r})(),Ie(()=>["MA","EMA","SMA","BOLL","SAR","BBI"].map(r=>{const e=t.mainIndicators.includes(r);return(()=>{var a=Yn();return a.$$click=i=>{t.onMainIndicatorChange({name:r,paneId:"candle_pane",added:!e})},ct(a,st(Vn,{checked:e,get label(){return O(r.toLowerCase(),t.locale)}})),a})()})),(()=>{var r=Wn();return ct(r,()=>O("sub_indicator",t.locale)),r})(),Ie(()=>["MA","EMA","VOL","MACD","BOLL","KDJ","RSI","BIAS","BRAR","CCI","DMI","CR","PSY","DMA","TRIX","OBV","VR","WR","MTM","EMV","SAR","SMA","ROC","PVT","BBI","AO"].map(r=>{const e=r in t.subIndicators;return(()=>{var a=Yn();return a.$$click=i=>{t.onSubIndicatorChange({name:r,paneId:t.subIndicators[r]??"",added:!e})},ct(a,st(Vn,{checked:e,get label(){return O(r.toLowerCase(),t.locale)}})),a})()}))]}})}});Ge(["click"]);function Zn(t,r){switch(t){case"Etc/UTC":return O("utc",r);case"Pacific/Honolulu":return O("honolulu",r);case"America/Juneau":return O("juneau",r);case"America/Los_Angeles":return O("los_angeles",r);case"America/Chicago":return O("chicago",r);case"America/Toronto":return O("toronto",r);case"America/Sao_Paulo":return O("sao_paulo",r);case"Europe/London":return O("london",r);case"Europe/Berlin":return O("berlin",r);case"Asia/Bahrain":return O("bahrain",r);case"Asia/Dubai":return O("dubai",r);case"Asia/Ashkhabad":return O("ashkhabad",r);case"Asia/Almaty":return O("almaty",r);case"Asia/Bangkok":return O("bangkok",r);case"Asia/Shanghai":return O("shanghai",r);case"Asia/Tokyo":return O("tokyo",r);case"Australia/Sydney":return O("sydney",r);case"Pacific/Norfolk":return O("norfolk",r)}return t}function lm(t){return[{key:"Etc/UTC",text:O("utc",t)},{key:"Pacific/Honolulu",text:O("honolulu",t)},{key:"America/Juneau",text:O("juneau",t)},{key:"America/Los_Angeles",text:O("los_angeles",t)},{key:"America/Chicago",text:O("chicago",t)},{key:"America/Toronto",text:O("toronto",t)},{key:"America/Sao_Paulo",text:O("sao_paulo",t)},{key:"Europe/London",text:O("london",t)},{key:"Europe/Berlin",text:O("berlin",t)},{key:"Asia/Bahrain",text:O("bahrain",t)},{key:"Asia/Dubai",text:O("dubai",t)},{key:"Asia/Ashkhabad",text:O("ashkhabad",t)},{key:"Asia/Almaty",text:O("almaty",t)},{key:"Asia/Bangkok",text:O("bangkok",t)},{key:"Asia/Shanghai",text:O("shanghai",t)},{key:"Asia/Tokyo",text:O("tokyo",t)},{key:"Australia/Sydney",text:O("sydney",t)},{key:"Pacific/Norfolk",text:O("norfolk",t)}]}const cm=t=>{const[r,e]=Tt(t.timezone),a=je(()=>lm(t.locale));return st(Wr,{get title(){return O("timezone",t.locale)},width:320,get buttons(){return[{children:O("confirm",t.locale),onClick:()=>{t.onConfirm(r()),t.onClose()}}]},get onClose(){return t.onClose},get children(){return st(_s,{style:{width:"100%","margin-top":"20px"},get value(){return r().text},onSelected:i=>{e(i)},get dataSource(){return a()}})}})};function Hn(t){return[{key:"candle.type",text:O("candle_type",t),component:"select",dataSource:[{key:"candle_solid",text:O("candle_solid",t)},{key:"candle_stroke",text:O("candle_stroke",t)},{key:"candle_up_stroke",text:O("candle_up_stroke",t)},{key:"candle_down_stroke",text:O("candle_down_stroke",t)},{key:"ohlc",text:O("ohlc",t)},{key:"area",text:O("area",t)}]},{key:"candle.priceMark.last.show",text:O("last_price_show",t),component:"switch"},{key:"candle.priceMark.high.show",text:O("high_price_show",t),component:"switch"},{key:"candle.priceMark.low.show",text:O("low_price_show",t),component:"switch"},{key:"indicator.lastValueMark.show",text:O("indicator_last_value_show",t),component:"switch"},{key:"yAxis.type",text:O("price_axis_type",t),component:"select",dataSource:[{key:"normal",text:O("normal",t)},{key:"percentage",text:O("percentage",t)},{key:"log",text:O("log",t)}]},{key:"yAxis.reverse",text:O("reverse_coordinate",t),component:"switch"},{key:"grid.show",text:O("grid_show",t),component:"switch"}]}var um=X("<div class=klinecharts-pro-setting-modal-content>"),dm=X("<span>");const hm=t=>{const[r,e]=Tt(t.currentStyles),[a,i]=Tt(Hn(t.locale));er(()=>{i(Hn(t.locale))});const n=(o,s)=>{const l={};ii(l,o.key,s);const c=Ae.clone(r());ii(c,o.key,s),e(c),i(a().map(u=>({...u}))),t.onChange(l)};return st(Wr,{get title(){return O("setting",t.locale)},width:560,get buttons(){return[{children:O("restore_default",t.locale),onClick:()=>{t.onRestoreDefault(a()),t.onClose()}}]},get onClose(){return t.onClose},get children(){var o=um();return ct(o,st(dc,{get each(){return a()},children:s=>{let l;const c=Ae.formatValue(r(),s.key);switch(s.component){case"select":{l=st(_s,{style:{width:"120px"},get value(){return O(c,t.locale)},get dataSource(){return s.dataSource},onSelected:u=>{const d=u.key;n(s,d)}});break}case"switch":{const u=!!c;l=st(ov,{open:u,onChange:()=>{n(s,!u)}});break}}return[(()=>{var u=dm();return ct(u,()=>s.text),u})(),l]}})),o}})};var vm=X("<img style=width:500px;margin-top:20px>");const fm=t=>st(Wr,{get title(){return O("screenshot",t.locale)},width:540,get buttons(){return[{type:"confirm",children:O("save",t.locale),onClick:()=>{const r=document.createElement("a");r.download="screenshot",r.href=t.url,document.body.appendChild(r),r.click(),r.remove()}}]},get onClose(){return t.onClose},get children(){var r=vm();return ve(()=>Fe(r,"src",t.url)),r}}),pm={AO:[{paramNameKey:"params_1",precision:0,min:1,default:5},{paramNameKey:"params_2",precision:0,min:1,default:34}],BIAS:[{paramNameKey:"BIAS1",precision:0,min:1,styleKey:"lines[0].color"},{paramNameKey:"BIAS2",precision:0,min:1,styleKey:"lines[1].color"},{paramNameKey:"BIAS3",precision:0,min:1,styleKey:"lines[2].color"},{paramNameKey:"BIAS4",precision:0,min:1,styleKey:"lines[3].color"},{paramNameKey:"BIAS5",precision:0,min:1,styleKey:"lines[4].color"}],BOLL:[{paramNameKey:"period",precision:0,min:1,default:20},{paramNameKey:"standard_deviation",precision:2,min:1,default:2}],BRAR:[{paramNameKey:"period",precision:0,min:1,default:26}],BBI:[{paramNameKey:"params_1",precision:0,min:1,default:3},{paramNameKey:"params_2",precision:0,min:1,default:6},{paramNameKey:"params_3",precision:0,min:1,default:12},{paramNameKey:"params_4",precision:0,min:1,default:24}],CCI:[{paramNameKey:"params_1",precision:0,min:1,default:20}],CR:[{paramNameKey:"params_1",precision:0,min:1,default:26},{paramNameKey:"params_2",precision:0,min:1,default:10},{paramNameKey:"params_3",precision:0,min:1,default:20},{paramNameKey:"params_4",precision:0,min:1,default:40},{paramNameKey:"params_5",precision:0,min:1,default:60}],DMA:[{paramNameKey:"params_1",precision:0,min:1,default:10},{paramNameKey:"params_2",precision:0,min:1,default:50},{paramNameKey:"params_3",precision:0,min:1,default:10}],DMI:[{paramNameKey:"params_1",precision:0,min:1,default:14},{paramNameKey:"params_2",precision:0,min:1,default:6}],EMV:[{paramNameKey:"params_1",precision:0,min:1,default:14},{paramNameKey:"params_2",precision:0,min:1,default:9}],EMA:[{paramNameKey:"EMA1",precision:0,min:1,styleKey:"lines[0].color"},{paramNameKey:"EMA2",precision:0,min:1,styleKey:"lines[1].color"},{paramNameKey:"EMA3",precision:0,min:1,styleKey:"lines[2].color"},{paramNameKey:"EMA4",precision:0,min:1,styleKey:"lines[3].color"},{paramNameKey:"EMA5",precision:0,min:1,styleKey:"lines[4].color"}],MTM:[{paramNameKey:"params_1",precision:0,min:1,default:12},{paramNameKey:"params_2",precision:0,min:1,default:6}],MA:[{paramNameKey:"MA1",precision:0,min:1,styleKey:"lines[0].color"},{paramNameKey:"MA2",precision:0,min:1,styleKey:"lines[1].color"},{paramNameKey:"MA3",precision:0,min:1,styleKey:"lines[2].color"},{paramNameKey:"MA4",precision:0,min:1,styleKey:"lines[3].color"},{paramNameKey:"MA5",precision:0,min:1,styleKey:"lines[4].color"}],MACD:[{paramNameKey:"params_1",precision:0,min:1,default:12},{paramNameKey:"params_2",precision:0,min:1,default:26},{paramNameKey:"params_2",precision:0,min:1,default:9}],OBV:[{paramNameKey:"params_1",precision:0,min:1,default:30}],PVT:[],PSY:[{paramNameKey:"params_1",precision:0,min:1,default:12},{paramNameKey:"params_2",precision:0,min:1,default:6}],ROC:[{paramNameKey:"params_1",precision:0,min:1,default:12},{paramNameKey:"params_2",precision:0,min:1,default:6}],RSI:[{paramNameKey:"RSI1",precision:0,min:1,styleKey:"lines[0].color"},{paramNameKey:"RSI2",precision:0,min:1,styleKey:"lines[1].color"},{paramNameKey:"RSI3",precision:0,min:1,styleKey:"lines[2].color"},{paramNameKey:"RSI4",precision:0,min:1,styleKey:"lines[3].color"},{paramNameKey:"RSI5",precision:0,min:1,styleKey:"lines[4].color"}],SMA:[{paramNameKey:"params_1",precision:0,min:1,default:12},{paramNameKey:"params_2",precision:0,min:1,default:2}],KDJ:[{paramNameKey:"params_1",precision:0,min:1,default:9},{paramNameKey:"params_2",precision:0,min:1,default:3},{paramNameKey:"params_3",precision:0,min:1,default:3}],SAR:[{paramNameKey:"params_1",precision:0,min:1,default:2},{paramNameKey:"params_2",precision:0,min:1,default:2},{paramNameKey:"params_3",precision:0,min:1,default:20}],TRIX:[{paramNameKey:"params_1",precision:0,min:1,default:12},{paramNameKey:"params_2",precision:0,min:1,default:9}],VOL:[{paramNameKey:"MA1",precision:0,min:1,styleKey:"lines[0].color"},{paramNameKey:"MA2",precision:0,min:1,styleKey:"lines[1].color"},{paramNameKey:"MA3",precision:0,min:1,styleKey:"lines[2].color"},{paramNameKey:"MA4",precision:0,min:1,styleKey:"lines[3].color"},{paramNameKey:"MA5",precision:0,min:1,styleKey:"lines[4].color"}],VR:[{paramNameKey:"params_1",precision:0,min:1,default:26},{paramNameKey:"params_2",precision:0,min:1,default:6}],WR:[{paramNameKey:"WR1",precision:0,min:1,styleKey:"lines[0].color"},{paramNameKey:"WR2",precision:0,min:1,styleKey:"lines[1].color"},{paramNameKey:"WR3",precision:0,min:1,styleKey:"lines[2].color"},{paramNameKey:"WR4",precision:0,min:1,styleKey:"lines[3].color"},{paramNameKey:"WR5",precision:0,min:1,styleKey:"lines[4].color"}]};var gm=X("<div class=klinecharts-pro-indicator-setting-modal-content>"),mm=X("<span>");const ym=t=>{const[r,e]=Tt(Ae.clone(t.params.calcParams)),a=i=>pm[i];return st(Wr,{get title(){return t.params.indicatorName},width:360,get buttons(){return[{type:"confirm",children:O("confirm",t.locale),onClick:()=>{const i=a(t.params.indicatorName),n=[];Ae.clone(r()).forEach((o,s)=>{!Ae.isValid(o)||o===""?"default"in i[s]&&n.push(i[s].default):n.push(o)}),t.onConfirm(n),t.onClose()}}]},get onClose(){return t.onClose},get children(){var i=gm();return ct(i,()=>a(t.params.indicatorName).map((n,o)=>[(()=>{var s=mm();return ct(s,()=>O(n.paramNameKey,t.locale)),s})(),st(Cs,{style:{width:"200px"},get value(){return r()[o]??""},get precision(){return n.precision},get min(){return n.min},onChange:s=>{const l=Ae.clone(r());l[o]=s,e(l)}})])),i}})};var _m=X('<svg viewBox="0 0 1024 1024"><path d="M945.066667 898.133333l-189.866667-189.866666c55.466667-64 87.466667-149.333333 87.466667-241.066667 0-204.8-168.533333-373.333333-373.333334-373.333333S96 264.533333 96 469.333333 264.533333 842.666667 469.333333 842.666667c91.733333 0 174.933333-34.133333 241.066667-87.466667l189.866667 189.866667c6.4 6.4 14.933333 8.533333 23.466666 8.533333s17.066667-2.133333 23.466667-8.533333c8.533333-12.8 8.533333-34.133333-2.133333-46.933334zM469.333333 778.666667C298.666667 778.666667 160 640 160 469.333333S298.666667 160 469.333333 160 778.666667 298.666667 778.666667 469.333333 640 778.666667 469.333333 778.666667z">'),Cm=X("<img alt=symbol>"),bm=X("<li><div><span>");const xm=t=>{const[r,e]=Tt(""),[a]=J0(r,t.datafeed.searchSymbols.bind(t.datafeed));return st(Wr,{get title(){return O("symbol_search",t.locale)},width:460,get onClose(){return t.onClose},get children(){return[st(Cs,{class:"klinecharts-pro-symbol-search-modal-input",get placeholder(){return O("symbol_code",t.locale)},get suffix(){return _m()},get value(){return r()},onChange:i=>{const n=`${i}`;e(n)}}),st(Ia,{class:"klinecharts-pro-symbol-search-modal-list",get loading(){return a.loading},get dataSource(){return a()??[]},renderItem:i=>(()=>{var n=bm(),o=n.firstChild,s=o.firstChild;return n.$$click=()=>{t.onSymbolSelected(i),t.onClose()},ct(o,st(be,{get when(){return i.logo},get children(){var l=Cm();return ve(()=>Fe(l,"src",i.logo)),l}}),s),ct(s,()=>i.shortName??i.ticker,null),ct(s,()=>`${i.name?`(${i.name})`:""}`,null),ct(n,()=>i.exchange??"",null),ve(()=>Fe(s,"title",i.name??"")),n})()})]}})};Ge(["click"]);var wm=X('<i class="icon-close klinecharts-pro-load-icon">'),Lm=X("<div class=klinecharts-pro-content><div class=klinecharts-pro-widget>");const oa=()=>Ae||{formatDate:(t,r,e)=>{const a=new Date(r);switch(e){case"HH:mm":return a.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit",hour12:!1});case"MM-DD HH:mm":return a.toLocaleDateString("zh-CN",{month:"2-digit",day:"2-digit"})+" "+a.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit",hour12:!1});case"YYYY-MM-DD HH:mm":return a.toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit"})+" "+a.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit",hour12:!1});case"YYYY-MM-DD":return a.toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit"});case"YYYY-MM":return a.toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit"});case"YYYY":return a.getFullYear().toString();default:return a.toLocaleString("zh-CN")}},isString:t=>typeof t=="string",clone:t=>N8(t),formatValue:(t,r,e)=>{const a=r.split(".");let i=t;for(const n of a)if(i&&typeof i=="object"&&n in i)i=i[n];else return e;return i}};function sa(t,r,e,a){return r==="VOL"&&(a={...a}),(t==null?void 0:t.createIndicator({name:r,onClick:i=>(console.log("Indicator onClick event:",i),!1),createTooltipDataSource:({indicator:i})=>{const n=[];return i.visible?n.push({id:"invisible",position:"middle",marginLeft:8,marginTop:3,marginRight:0,marginBottom:0,paddingLeft:0,paddingTop:0,paddingRight:0,paddingBottom:0,type:"icon_font",content:{family:"icomoon",code:""},size:14,color:"#76808F",activeColor:"#76808F",backgroundColor:"transparent",activeBackgroundColor:"rgba(22, 119, 255, 0.15)",borderRadius:0}):n.push({id:"visible",position:"middle",marginLeft:8,marginTop:3,marginRight:0,marginBottom:0,paddingLeft:0,paddingTop:0,paddingRight:0,paddingBottom:0,type:"icon_font",content:{family:"icomoon",code:""},size:14,color:"#76808F",activeColor:"#76808F",backgroundColor:"transparent",activeBackgroundColor:"rgba(22, 119, 255, 0.15)",borderRadius:0}),n.push({id:"setting",position:"middle",marginLeft:6,marginTop:3,marginBottom:0,marginRight:0,paddingLeft:0,paddingTop:0,paddingRight:0,paddingBottom:0,type:"icon_font",content:{family:"icomoon",code:""},size:14,color:"#76808F",activeColor:"#76808F",backgroundColor:"transparent",activeBackgroundColor:"rgba(22, 119, 255, 0.15)",borderRadius:0}),n.push({id:"close",position:"middle",marginLeft:6,marginTop:3,marginRight:0,marginBottom:0,paddingLeft:0,paddingTop:0,paddingRight:0,paddingBottom:0,type:"icon_font",content:{family:"icomoon",code:""},size:14,color:"#76808F",activeColor:"#76808F",backgroundColor:"transparent",activeBackgroundColor:"rgba(22, 119, 255, 0.15)",borderRadius:0}),{features:n}}},e,a))??null}const km=t=>{let r,e=null,a,i=!1,n=null;const[o,s]=Tt(t.theme),[l,c]=Tt(t.styles),[u,d]=Tt(t.locale),[v,h]=Tt(t.symbol),[f,p]=Tt(t.period),[g,m]=Tt(!1),[b,x]=Tt([...t.mainIndicators]),[L,_]=Tt({}),[M,w]=Tt(!1),[k,C]=Tt({key:t.timezone,text:Zn(t.timezone,t.locale)}),[y,T]=Tt(!1),[I,E]=Tt(),[A,F]=Tt(""),[N,B]=Tt(t.drawingBarVisible),[Y,K]=Tt(!1),[$,ut]=Tt(!1),[rt,z]=Tt({visible:!1,indicatorName:"",paneId:"",calcParams:[]}),[tt,mt]=Tt(!1);t.ref({setTheme:s,getTheme:()=>o(),setStyles:c,getStyles:()=>e.getStyles(),setLocale:d,getLocale:()=>u(),setTimezone:V=>{C({key:V,text:Zn(t.timezone,u())})},getTimezone:()=>k().key,setSymbol:h,getSymbol:()=>v(),setPeriod:p,getPeriod:()=>f(),getChart:()=>e});const ft=()=>{e==null||e.resize()},yt=(V,q,dt)=>{let J=q,_t=J;switch(V.timespan){case"minute":{J=J-J%(60*1e3),_t=J-dt*V.multiplier*60*1e3;break}case"hour":{J=J-J%(60*60*1e3),_t=J-dt*V.multiplier*60*60*1e3;break}case"day":{const Lt=new Date(J);J=new Date(Lt.getFullYear(),Lt.getMonth(),Lt.getDate()).getTime(),_t=J-dt*V.multiplier*24*60*60*1e3;break}case"week":{const Lt=new Date(J).getDay(),Ht=Lt===0?6:Lt-1;J=J-Ht*60*60*24;const jt=new Date(J);J=new Date(`${jt.getFullYear()}-${jt.getMonth()+1}-${jt.getDate()}`).getTime(),_t=dt*V.multiplier*7*24*60*60*1e3;break}case"month":{const Lt=new Date(J),Ht=Lt.getFullYear(),jt=Lt.getMonth()+1;J=new Date(`${Ht}-${jt}-01`).getTime(),_t=dt*V.multiplier*30*24*60*60*1e3;const Dt=new Date(_t);_t=new Date(`${Dt.getFullYear()}-${Dt.getMonth()+1}-01`).getTime();break}case"year":{const Lt=new Date(J).getFullYear();J=new Date(`${Lt}-01-01`).getTime(),_t=dt*V.multiplier*365*24*60*60*1e3;const Ht=new Date(_t);_t=new Date(`${Ht.getFullYear()}-01-01`).getTime();break}}return[_t,J]};return zo(()=>{var V,q,dt;if(window.addEventListener("resize",ft),e=S0(r,{formatter:{formatDate:({timestamp:et,template:nt,type:Pt})=>{if(!et||et<=0||!Number.isFinite(et))return console.warn("无效的时间戳:",et),"--";const Et=new Date(et),Yt=f();if(isNaN(Et.getTime()))return console.warn("无效的日期对象:",et,Et),"--";const ie=ne=>{const ee=Et.getFullYear(),Ft=String(Et.getMonth()+1).padStart(2,"0"),oe=String(Et.getDate()).padStart(2,"0"),le=String(Et.getHours()).padStart(2,"0"),Ut=String(Et.getMinutes()).padStart(2,"0");if(isNaN(ee)||isNaN(parseInt(Ft))||isNaN(parseInt(oe)))return console.warn("日期字段解析失败:",{timestamp:et,year:ee,month:Ft,day:oe}),"--";switch(ne){case"HH:mm":return`${le}:${Ut}`;case"MM-DD HH:mm":return`${Ft}-${oe} ${le}:${Ut}`;case"YYYY-MM-DD HH:mm":return`${ee}-${Ft}-${oe} ${le}:${Ut}`;case"YYYY-MM-DD":return`${ee}-${Ft}-${oe}`;case"YYYY-MM":return`${ee}-${Ft}`;case"YYYY":return ee.toString();default:return`${ee}-${Ft}-${oe} ${le}:${Ut}`}};switch(Yt.timespan){case"minute":return ie(Pt==="xAxis"?"HH:mm":"YYYY-MM-DD HH:mm");case"hour":return ie(Pt==="xAxis"?"MM-DD HH:mm":"YYYY-MM-DD HH:mm");case"day":case"week":return ie("YYYY-MM-DD");case"month":return ie(Pt==="xAxis"?"YYYY-MM":"YYYY-MM-DD");case"year":return ie(Pt==="xAxis"?"YYYY":"YYYY-MM-DD")}return ie("YYYY-MM-DD HH:mm")}}}),e){const et=e.getDom("candle_pane","main");if(et){let Pt=document.createElement("div");if(Pt.className="klinecharts-pro-watermark",oa().isString(t.watermark)){const Et=t.watermark.replace(/(^\s*)|(\s*$)/g,"");Pt.innerHTML=Et}else Pt.appendChild(t.watermark);et.appendChild(Pt)}const nt=e.getDom("candle_pane","yAxis");a=document.createElement("span"),a.className="klinecharts-pro-price-unit",nt==null||nt.appendChild(a)}b().forEach(et=>{sa(e,et,!0,{id:"candle_pane"})});const J={};t.subIndicators.forEach(et=>{const nt=sa(e,et,!0);nt&&(J[et]=nt)}),_(J);const _t=((q=(V=t.datafeed)==null?void 0:V.constructor)==null?void 0:q.name)==="DefaultDatafeed";let Lt=null;_t?typeof((dt=t.datafeed)==null?void 0:dt.getHistoryKLineData)=="function"&&(Lt={getBars:({type:et,timestamp:nt,symbol:Pt,period:Et,callback:Yt})=>{if(et!=="backward"){Yt([],{backward:!1,forward:!1});return}if(!nt){Yt([],{backward:!1,forward:!1});return}const ie=t.symbol,ne=t.period,ee=nt-1,Ft=ee-500*60*1e3;t.datafeed.getHistoryKLineData(ie,ne,Ft,ee).then(oe=>{const le=oe.length>=100;Yt(oe,{backward:le,forward:!1})}).catch(()=>{Yt([],{backward:!1,forward:!1})})}}):Lt={getBars:({type:et,timestamp:nt,symbol:Pt,period:Et,callback:Yt})=>{if(et!=="backward"){Yt([],{backward:!1,forward:!1});return}if(i){Yt([],{backward:!1,forward:!1});return}if(!nt){Yt([],{backward:!1,forward:!1});return}i=!0,(async()=>{var ie;try{const ne=f(),ee=v(),[Ft]=yt(ne,nt-1,500),oe=nt-1;if(!t||!((ie=t.datafeed)!=null&&ie.getHistoryKLineData)){Yt([],{backward:!1,forward:!1});return}const le=await t.datafeed.getHistoryKLineData(ee,ne,Ft,oe),Ut=le.length>0&&le.length>=100;Yt(le,{backward:Ut,forward:!1})}catch{Yt([],{backward:!1,forward:!1})}finally{i=!1}})()}};try{typeof(e==null?void 0:e.setDataLoader)=="function"&&Lt&&(e==null||e.setDataLoader(Lt))}catch{}let Ht=0,jt=!1,Dt=-1;const Xt=1e3,qt=setInterval(()=>{if(!(!e||jt||i))try{const et=e;if(!et.getVisibleRange||!et.getDataList)return;const nt=et.getVisibleRange(),Pt=et.getDataList();if(!nt||!Pt||Pt.length===0)return;const Et=nt.from<=2,Yt=nt.from<=0&&nt.to>=Pt.length-1,ie=Pt.length<Math.abs(nt.to-nt.from)+10,ne=Date.now(),ee=ne-Ht>=Xt,Ft=Dt>nt.from||Dt===-1;if(Dt=nt.from,(Et&&Ft||Yt||ie)&&ee){Ht=ne,jt=!0;const oe={visibleRange:{...nt},dataLength:Pt.length},le=Pt[0].timestamp;Lt==null||Lt.getBars({type:"backward",timestamp:le,symbol:null,period:null,callback:(Ut,Qe)=>{try{if(Ut&&Ut.length>0){const we=et.getDataList(),fe=[...Ut,...we];et.applyNewData(fe,{backward:Ut.length>=50,forward:!1}),setTimeout(()=>{try{const xr=Ut.length+oe.visibleRange.from;et.scrollToDataIndex(xr,!0)}catch{}},100)}}catch{}finally{setTimeout(()=>{jt=!1},500)}}})}}catch{jt=!1}},300);wa(()=>{qt&&clearInterval(qt)}),e==null||e.subscribeAction("onCandleTooltipFeatureClick",et=>{if(et.indicatorName)switch(et.featureId||et.iconId||et.id){case"visible":{e==null||e.overrideIndicator({name:et.indicatorName,visible:!0,paneId:et.paneId});break}case"invisible":{e==null||e.overrideIndicator({name:et.indicatorName,visible:!1,paneId:et.paneId});break}case"setting":{const nt=e==null?void 0:e.getIndicators({paneId:et.paneId,name:et.indicatorName});if(nt&&nt.length>0){const Pt=nt[0];z({visible:!0,indicatorName:et.indicatorName,paneId:et.paneId,calcParams:Pt.calcParams})}break}case"close":{if(et.paneId==="candle_pane"){const nt=[...b()];e==null||e.removeIndicator({paneId:et.paneId,name:et.indicatorName}),nt.splice(nt.indexOf(et.indicatorName),1),x(nt)}else{const nt={...L()};e==null||e.removeIndicator({paneId:et.paneId,name:et.indicatorName}),delete nt[et.indicatorName],_(nt)}break}}}),e==null||e.subscribeAction("onIndicatorTooltipFeatureClick",et=>{const nt=et.indicator,Pt=et.feature,Et=et.paneId;if(nt&&Pt)switch(Pt.id){case"visible":{e==null||e.overrideIndicator({name:nt.name,visible:!0,paneId:Et});break}case"invisible":{e==null||e.overrideIndicator({name:nt.name,visible:!1,paneId:Et});break}case"setting":{z({visible:!0,indicatorName:nt.name,paneId:Et,calcParams:nt.calcParams});break}case"close":{if(Et==="candle_pane"){const Yt=[...b()];e==null||e.removeIndicator({paneId:Et,name:nt.name}),Yt.splice(Yt.indexOf(nt.name),1),x(Yt)}else{const Yt={...L()};e==null||e.removeIndicator({paneId:Et,name:nt.name}),delete Yt[nt.name],_(Yt)}break}}})}),wa(()=>{n&&(n.abort(),n=null),window.removeEventListener("resize",ft),M0(r)}),er(()=>{const V=v();V!=null&&V.priceCurrency?(a.innerHTML=V==null?void 0:V.priceCurrency.toLocaleUpperCase(),a.style.display="flex"):a.style.display="none",e==null||e.setSymbol({ticker:(V==null?void 0:V.ticker)||"",pricePrecision:(V==null?void 0:V.pricePrecision)??2,volumePrecision:(V==null?void 0:V.volumePrecision)??0})}),er(V=>{var q;if(t&&t.datafeed){console.log("currentDataRequest"),n&&(n.abort(),n=null),V&&(q=t.datafeed)!=null&&q.unsubscribe&&t.datafeed.unsubscribe(V.symbol,V.period);const dt=v(),J=f();n=new AbortController;const _t=n;return ut(!0),(async()=>{var Lt,Ht;try{if(_t.signal.aborted)return;const[jt,Dt]=yt(J,new Date().getTime(),500);if(!((Lt=t.datafeed)!=null&&Lt.getHistoryKLineData)){console.error("props.datafeed.getHistoryKLineData 未定义"),ut(!1);return}const Xt=await t.datafeed.getHistoryKLineData(dt,J,jt,Dt);if(_t.signal.aborted||(e==null||e.applyNewData(Xt,{backward:!0,forward:!1}),_t.signal.aborted))return;(Ht=t.datafeed)!=null&&Ht.subscribe&&t.datafeed.subscribe(dt,J,qt=>{e==null||e.updateData(qt)})}catch(jt){(jt==null?void 0:jt.name)!=="AbortError"&&console.error("获取历史数据时出错:",jt)}finally{_t.signal.aborted||ut(!1)}})(),{symbol:dt,period:J}}return V}),er(()=>{const V=o();e==null||e.setStyles(V);const q=V==="dark"?"#929AA5":"#76808F";e==null||e.setStyles({indicator:{tooltip:{features:[{id:"visible",position:"middle",marginLeft:8,marginTop:3,marginRight:0,marginBottom:0,paddingLeft:0,paddingTop:0,paddingRight:0,paddingBottom:0,type:"icon_font",content:{family:"icomoon",code:""},size:14,color:q,activeColor:q,backgroundColor:"transparent",activeBackgroundColor:"rgba(22, 119, 255, 0.15)",borderRadius:0},{id:"invisible",position:"middle",marginLeft:8,marginTop:3,marginRight:0,marginBottom:0,paddingLeft:0,paddingTop:0,paddingRight:0,paddingBottom:0,type:"icon_font",content:{family:"icomoon",code:""},size:14,color:q,activeColor:q,backgroundColor:"transparent",activeBackgroundColor:"rgba(22, 119, 255, 0.15)",borderRadius:0},{id:"setting",position:"middle",marginLeft:6,marginTop:3,marginBottom:0,marginRight:0,paddingLeft:0,paddingTop:0,paddingRight:0,paddingBottom:0,type:"icon_font",content:{family:"icomoon",code:""},size:14,color:q,activeColor:q,backgroundColor:"transparent",activeBackgroundColor:"rgba(22, 119, 255, 0.15)",borderRadius:0},{id:"close",position:"middle",marginLeft:6,marginTop:3,marginRight:0,marginBottom:0,paddingLeft:0,paddingTop:0,paddingRight:0,paddingBottom:0,type:"icon_font",content:{family:"icomoon",code:""},size:14,color:q,activeColor:q,backgroundColor:"transparent",activeBackgroundColor:"rgba(22, 119, 255, 0.15)",borderRadius:0}]}}})}),er(()=>{e==null||e.setLocale(u())}),er(()=>{e==null||e.setTimezone(k().key)}),er(()=>{l()&&(e==null||e.setStyles(l()),E(oa().clone(e.getStyles())))}),[wm(),st(be,{get when(){return Y()},get children(){return st(xm,{get locale(){return t.locale},get datafeed(){return t.datafeed},onSymbolSelected:V=>{h(V)},onClose:()=>{K(!1)}})}}),st(be,{get when(){return g()},get children(){return st(sm,{get locale(){return t.locale},get mainIndicators(){return b()},get subIndicators(){return L()},onClose:()=>{m(!1)},onMainIndicatorChange:V=>{const q=[...b()];V.added?(sa(e,V.name,!0,{id:"candle_pane"}),q.push(V.name)):(e==null||e.removeIndicator({paneId:"candle_pane",name:V.name}),q.splice(q.indexOf(V.name),1)),x(q)},onSubIndicatorChange:V=>{const q={...L()};if(V.added){const dt=sa(e,V.name);dt&&(q[V.name]=dt)}else V.paneId&&(e==null||e.removeIndicator({paneId:V.paneId,name:V.name}),delete q[V.name]);_(q)}})}}),st(be,{get when(){return M()},get children(){return st(cm,{get locale(){return t.locale},get timezone(){return k()},onClose:()=>{w(!1)},onConfirm:C})}}),st(be,{get when(){return y()},get children(){return st(hm,{get locale(){return t.locale},get currentStyles(){return oa().clone(e.getStyles())},onClose:()=>{T(!1)},onChange:V=>{e==null||e.setStyles(V)},onRestoreDefault:V=>{const q={};V.forEach(dt=>{const J=dt.key;ii(q,J,oa().formatValue(I(),J))}),e==null||e.setStyles(q)}})}}),st(be,{get when(){return A().length>0},get children(){return st(fm,{get locale(){return t.locale},get url(){return A()},onClose:()=>{F("")}})}}),st(be,{get when(){return rt().visible},get children(){return st(ym,{get locale(){return t.locale},get params(){return rt()},onClose:()=>{z({visible:!1,indicatorName:"",paneId:"",calcParams:[]})},onConfirm:V=>{const q=rt();e==null||e.overrideIndicator({name:q.indicatorName,calcParams:V,paneId:q.paneId})}})}}),st(Ip,{get locale(){return t.locale},get symbol(){return v()},get spread(){return N()},get period(){return f()},get periods(){return t.periods},onMenuClick:async()=>{try{await ec(()=>B(!N())),e==null||e.resize()}catch{}},onSymbolClick:()=>{K(!Y())},onPeriodChange:p,onIndicatorClick:()=>{m(V=>!V)},onTimezoneClick:()=>{w(V=>!V)},onSettingClick:()=>{T(V=>!V)},onScreenshotClick:()=>{if(e){const V=e.getConvertPictureUrl(!0,"jpeg",t.theme==="dark"?"#151517":"#ffffff");F(V)}},onOrderFlowClick:async()=>{var V;if(!e)return;const q=tt();try{if(q)typeof e.clearOrderFlowData=="function"?e.clearOrderFlowData():typeof e.setOrderFlowData=="function"&&e.setOrderFlowData([]),e.setCandleVisible(!0),mt(!1);else{const dt=v();if(!((V=t.datafeed)!=null&&V.getOrderFlowData))return;const J=await t.datafeed.getOrderFlowData(dt);typeof e.setOrderFlowData=="function"&&(e.setOrderFlowData(J),e.setCandleVisible(!1),mt(!0))}}catch{}}}),(()=>{var V=Lm(),q=V.firstChild;ct(V,st(be,{get when(){return $()},get children(){return st(ys,{})}}),q),ct(V,st(be,{get when(){return N()},get children(){return st(om,{get locale(){return t.locale},onDrawingItemClick:J=>{e==null||e.createOverlay(J)},onModeChange:J=>{e==null||e.overrideOverlay({mode:J})},onLockChange:J=>{e==null||e.overrideOverlay({lock:J})},onVisibleChange:J=>{e==null||e.overrideOverlay({visible:J})},onRemoveClick:J=>{e==null||e.removeOverlay({groupId:J})}})}}),q);var dt=r;return typeof dt=="function"?bi(dt,q):r=q,ve(()=>Fe(q,"data-drawing-bar-visible",N())),V})()]};var Sm=X('<svg class=logo width=992px height=618px viewBox="0 0 992 618"version=1.1 xmlns=http://www.w3.org/2000/svg xmlns:xlink=http://www.w3.org/1999/xlink><title>编组</title><defs><polygon id=path-1 points="0 0 992 0 992 127.017578 0 127.017578"></polygon></defs><g id=页面-1 stroke=none stroke-width=1 fill=none fill-rule=evenodd><g id=编组><g transform="translate(0, 490.9824)"><mask id=mask-2 fill=white><use href=#path-1></use></mask><g id=Clip-1></g><g id=PandaAI-Trade mask=url(#mask-2) fill=#444444 fill-rule=nonzero><g transform="translate(9.1679, 13.6664)"><path d="M73.0736523,46.3481979 C73.0736523,49.1571796 72.1507011,51.5046857 70.3047989,53.3907163 C68.4588966,55.2767468 66.1314547,56.2197621 63.322473,56.2197621 L16.6131203,56.2197621 L16.6131203,90.0479273 L0,90.0479273 L0,0 L73.0736523,0 L73.0736523,46.3481979 Z M53.8120635,40.9308761 C54.6146297,40.9308761 55.2767468,40.6499779 55.7984149,40.0881816 C56.3200829,39.5263852 56.5809169,38.8843323 56.5809169,38.1620227 L56.5809169,15.1685011 L16.6131203,15.1685011 L16.6131203,40.9308761 L53.8120635,40.9308761 Z"id=形状></path><path d="M150.721932,24.9196804 L150.721932,80.296748 C150.721932,83.1057297 149.798981,85.4331717 147.953078,87.2790739 C146.107176,89.1249762 143.779734,90.0479273 140.970753,90.0479273 L90.1683123,90.0479273 L90.1683123,50.2005156 L134.229196,50.2005156 L134.229196,39.365872 L97.8729478,39.365872 L97.8729478,24.9196804 L150.721932,24.9196804 Z M131.580728,75.9628906 C132.303038,75.9628906 132.925026,75.6819924 133.446694,75.1201961 C133.968362,74.5583997 134.229196,73.9163468 134.229196,73.1940372 L134.229196,64.2855524 L106.420278,64.2855524 L106.420278,75.9628906 L131.580728,75.9628906 Z"id=形状></path><path d="M184.670482,90.0479273 L184.670482,40.3289514 L209.349393,40.3289514 C210.151959,40.3289514 210.814076,40.5897854 211.335744,41.1114535 C211.857412,41.6331215 212.118246,42.2551103 212.118246,42.9774199 L212.118246,90.0479273 L228.009057,90.0479273 L228.009057,34.6708597 C228.009057,31.861878 227.066041,29.5344361 225.180011,27.6885338 C223.29398,25.8426316 220.946474,24.9196804 218.137492,24.9196804 L168.779671,24.9196804 L168.779671,90.0479273 L184.670482,90.0479273 Z"id=路径></path><path d="M291.451915,11.3161834 L307.824265,11.3161834 L307.824265,80.296748 C307.824265,83.1057297 306.901314,85.4331717 305.055412,87.2790739 C303.209509,89.1249762 300.882067,90.0479273 298.073086,90.0479273 L247.029876,90.0479273 L247.029876,25.1604503 L291.451915,25.1604503 L291.451915,11.3161834 Z M288.803446,75.240581 C289.525756,75.240581 290.147744,74.979747 290.669412,74.4580789 C291.191081,73.9364109 291.451915,73.2742938 291.451915,72.4717276 L291.451915,39.9677966 L263.402226,39.9677966 L263.402226,75.240581 L288.803446,75.240581 Z"id=形状></path><path d="M384.14831,24.9196804 L384.14831,80.296748 C384.14831,83.1057297 383.225359,85.4331717 381.379457,87.2790739 C379.533555,89.1249762 377.206113,90.0479273 374.397131,90.0479273 L323.594691,90.0479273 L323.594691,50.2005156 L367.655575,50.2005156 L367.655575,39.365872 L331.299326,39.365872 L331.299326,24.9196804 L384.14831,24.9196804 Z M365.007107,75.9628906 C365.729416,75.9628906 366.351405,75.6819924 366.873073,75.1201961 C367.394741,74.5583997 367.655575,73.9163468 367.655575,73.1940372 L367.655575,64.2855524 L339.846656,64.2855524 L339.846656,75.9628906 L365.007107,75.9628906 Z"id=形状></path><path d="M456.258883,58.9886155 L421.588023,58.9886155 L415.929932,90.0479273 L400.159506,90.0479273 L417.013396,0.12038493 L452.647335,0.12038493 C455.13529,0.12038493 457.282155,0.862758662 459.087929,2.34750613 C460.893703,3.83225359 461.997231,5.77847662 462.398515,8.18617521 L478.40971,90.0479273 L462.27813,90.0479273 L456.258883,58.9886155 Z M424.356877,43.8201144 L453.369645,43.8201144 L447.831938,15.0481162 L429.653814,15.0481162 L424.356877,43.8201144 Z"id=形状></path><path d="M514.164034,90.0479273 C516.973016,90.0479273 519.300458,89.1249762 521.14636,87.2790739 C522.992262,85.4331717 523.915214,83.1057297 523.915214,80.296748 L523.915214,0 L507.422478,0 L507.422478,90.0479273 L514.164034,90.0479273 Z"id=路径></path><path d="M685.231019,0 L685.231019,5.41732183 C685.231019,8.22630352 684.308068,10.5537455 682.462166,12.3996477 C680.616264,14.24555 678.288822,15.1685011 675.47984,15.1685011 L655.977481,15.1685011 L655.977481,90.0479273 L639.725516,90.0479273 L639.725516,15.1685011 L611.073903,15.1685011 L611.073903,0 L685.231019,0 Z"id=路径></path><path d="M709.308005,25.5216051 C706.499023,25.5216051 704.171581,26.4445562 702.325679,28.2904585 C700.479777,30.1363607 699.556826,32.4638027 699.556826,35.2727844 L699.556826,90.0479273 L716.049561,90.0479273 L716.049561,43.5793445 C716.049561,42.7767783 716.310395,42.1146612 716.832063,41.5929932 C717.353731,41.0713251 718.015848,40.8104911 718.818415,40.8104911 L743.256555,40.8104911 L743.256555,25.5216051 L709.308005,25.5216051 Z"id=路径></path><path d="M815.728283,24.9196804 L815.728283,80.296748 C815.728283,83.1057297 814.805332,85.4331717 812.959429,87.2790739 C811.113527,89.1249762 808.786085,90.0479273 805.977104,90.0479273 L755.174663,90.0479273 L755.174663,50.2005156 L799.235548,50.2005156 L799.235548,39.365872 L762.879299,39.365872 L762.879299,24.9196804 L815.728283,24.9196804 Z M796.587079,75.9628906 C797.309389,75.9628906 797.931377,75.6819924 798.453045,75.1201961 C798.974714,74.5583997 799.235548,73.9163468 799.235548,73.1940372 L799.235548,64.2855524 L771.426629,64.2855524 L771.426629,75.9628906 L796.587079,75.9628906 Z"id=形状></path><path d="M878.208061,11.3161834 L894.580412,11.3161834 L894.580412,80.296748 C894.580412,83.1057297 893.657461,85.4331717 891.811558,87.2790739 C889.965656,89.1249762 887.638214,90.0479273 884.829232,90.0479273 L833.786022,90.0479273 L833.786022,25.1604503 L878.208061,25.1604503 L878.208061,11.3161834 Z M875.559593,75.240581 C876.281902,75.240581 876.903891,74.979747 877.425559,74.4580789 C877.947227,73.9364109 878.208061,73.2742938 878.208061,72.4717276 L878.208061,39.9677966 L850.158373,39.9677966 L850.158373,75.240581 L875.559593,75.240581 Z"id=形状></path><path d="M975.238315,53.4509087 C975.238315,56.2598904 974.295299,58.5873324 972.409269,60.4332347 C970.523238,62.2791369 968.175732,63.202088 965.36675,63.202088 L929.010502,63.202088 L929.010502,74.9998111 L975.238315,74.9998111 L975.238315,80.296748 C975.238315,83.1057297 974.295299,85.4331717 972.409269,87.2790739 C970.523238,89.1249762 968.175732,90.0479273 965.36675,90.0479273 L912.638151,90.0479273 L912.638151,25.5216051 L975.238315,25.5216051 L975.238315,53.4509087 Z M956.097111,49.8393608 C956.81942,49.8393608 957.461473,49.5785268 958.02327,49.0568588 C958.585066,48.5351908 958.865964,47.913202 958.865964,47.1908924 L958.865964,39.245487 L929.010502,39.245487 L929.010502,49.8393608 L956.097111,49.8393608 Z"id=形状></path></g></g></g><path d="M491.846918,213.063281 L489.366601,222.635023 L476.827173,265.14217 L444.337622,363.998857 L424.991819,425.628459 L458.844662,444.99233 C461.367845,446.435788 495.432494,464.010479 495.707338,464.010479 C495.939316,464.010479 532.59775,445.360765 534.879709,444.081337 L570.438768,424.152195 L567.315468,417.972916 L532.575897,317.701371 C532.143879,316.414371 500.961307,218.838795 500.961307,218.772342 C500.961307,217.407955 498.579328,211.956293 497.643851,211.179046 C494.995434,208.979371 493.827978,209.358742 491.846918,213.063281"id=Fill-2 fill=#444444></path><path d="M480.578243,2.28089145 L435.35931,12.8527915 C411.438663,17.9107829 393.884808,22.8081096 391.542333,25.0776025 C390.917,25.6840904 340.602112,124.849489 331.635652,143.149273 L275.635618,257.433969 L286.658783,277.754258 L319.626579,336.367403 L347.02942,382.006248 C350.70577,386.954887 362.832513,394.729877 384.181228,405.824147 L416.581686,421.588627 L408.062372,395.449923 L383.669363,324.021462 C377.846375,306.362654 368.945474,275.05156 369.221998,273.19929 C369.763281,269.57298 385.068797,259.754774 430.813883,233.690094 L491.685458,199.006725 L491.685458,99.4299912 C491.685458,21.237634 491.062647,-0.114777823 488.786572,0 L480.578243,2.28089145 Z"id=Fill-4 fill=#444444></path><path d="M500.995263,97.6583904 C501.009888,135.65347 502.077324,197.477383 502.735436,198.4338 L518.352777,208.659136 L578.065302,242.910981 C616.724128,265.279534 623.86334,270.246678 623.86334,274.771364 C623.86334,275.235693 610.353133,320.123369 609.07221,323.91539 L584.5422,394.011428 L575.563973,420.95009 L609.670647,405.079622 L643.014987,388.585002 L672.8889,337.5273 L710.012971,272.512806 L717.26397,258.555173 L683.934759,190.124663 L626.220104,72.9580981 C602.0036,24.5601951 601.769101,24.2035365 592.558054,21.5395321 L545.310153,10.3317029 L504.149158,0.581631795 C501.474685,-0.445444113 500.967022,15.1995885 500.995263,97.6583904"id=Fill-6 fill=#444444>');const Mm=Sm(),Tm=()=>Ae||(console.warn("klinecharts.utils not available, using fallback functions"),{isString:t=>typeof t=="string"});class Em{constructor(r){if(on(this,"_container"),on(this,"_chartApi",null),Tm().isString(r.container)){if(this._container=document.getElementById(r.container),!this._container)throw new Error("Container is null")}else this._container=r.container;this._container.classList.add("klinecharts-pro"),this._container.setAttribute("data-theme",r.theme??"light"),vc(()=>{const e=this;return st(km,{ref:a=>{e._chartApi=a},get styles(){return r.styles??{}},get watermark(){return r.watermark??Mm},get theme(){return r.theme??"light"},get locale(){return r.locale??"zh-CN"},get drawingBarVisible(){return r.drawingBarVisible??!0},get symbol(){return r.symbol},get period(){return r.period},get periods(){return r.periods??[{multiplier:1,timespan:"minute",text:"1m"},{multiplier:5,timespan:"minute",text:"5m"},{multiplier:15,timespan:"minute",text:"15m"},{multiplier:1,timespan:"hour",text:"1h"},{multiplier:1,timespan:"day",text:"1d"}]},get timezone(){return r.timezone??"Asia/Shanghai"},get mainIndicators(){return r.mainIndicators??["MA"]},get subIndicators(){return r.subIndicators??["VOL"]},get datafeed(){return r.datafeed}})},this._container)}setTheme(r){var e;(e=this._container)==null||e.setAttribute("data-theme",r),this._chartApi.setTheme(r)}getTheme(){return this._chartApi.getTheme()}setStyles(r){this._chartApi.setStyles(r)}getStyles(){return this._chartApi.getStyles()}setLocale(r){this._chartApi.setLocale(r)}getLocale(){return this._chartApi.getLocale()}setTimezone(r){this._chartApi.setTimezone(r)}getTimezone(){return this._chartApi.getTimezone()}setSymbol(r){this._chartApi.setSymbol(r)}getSymbol(){return this._chartApi.getSymbol()}setPeriod(r){this._chartApi.setPeriod(r)}getPeriod(){return this._chartApi.getPeriod()}getChart(){return this._chartApi.getChart()}}_a?X0.forEach(t=>{_a(t)}):console.warn("registerOverlay method not found in klinecharts");const bs="http://api.pandaai.online",xs="token",la=ta();function ws(t,r){const e={},a=localStorage.getItem(xs);a&&(e.Authorization=`${a}`);let i="";return r&&(i=`?${new URLSearchParams(Object.entries(r).reduce((o,[s,l])=>(o[s]=l.toString(),o),{})).toString()}`),fetch(bs+t+i,{headers:e,method:"GET"})}function Fi(t,r){const e={"Content-Type":"application/json"},a=localStorage.getItem(xs);return a&&(e.Authorization=`${a}`),fetch(bs+t,{headers:e,method:"POST",body:JSON.stringify(r)})}async function Ls(t){return t?(la.type=t.type,la.quotationType=t.type):(la.type=void 0,la.quotationType="future"),(await ws("/instrument/getQuotation",t)).json()}async function ks(t,r,e){return(await Fi("/orderFlow/getHistoryMarketData",{symbolCode:t,cycle:r,baseBarCount:e})).json()}async function Ss(t,r){const e={underlyingSymbol:t,...r};return(await Fi("/instrument/addInstrument",e)).json()}async function Aa(t){return(await ws("/instrument/queryInstrument",t)).json()}async function Ms(t,r){const e={underlyingSymbol:t,...r};return(await Fi("/instrument/deleteInstrument",e)).json()}const Im="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAkCAYAAADo6zjiAAAAAXNSR0IArs4c6QAAA1hJREFUWEftmEtoE0EYx//f2NZHSUGKiIiNIIrQgyCigooP8KAeRLRFEN9QLFJIZrelIMh6kUJ3pj140IJP0INR8SRIEWpVFFRQsPgGLXsQRdRoL7bOZyYkksY0D2ykh+5pyUy+/e33/M8S/tPV0tIyq6qq6j4RvfZ9f1P6sZT9/Pb29pkdHR1fxpvLdd0NzHzL2g2FQlM9z/tp70cBOI6zA8BlAOeVUvvHE6JYgKMJqGMAHiqllk94ACnlPGauD4KgNxaL/coHXBYPSCkfE9FSZm7UWsf+O4DjOO8AhAE0K6VOTkiAhoaGKRYsHA6vZ+Zee2+MmREEwU8btuwqKCkJC3nAdd1OZnbzeOY+tbW1hYaHh+uSNUnUTESHATwzxuwUQnAoFHrled5ILiOFAKSUd4loFYA3AASAuSk7bwEstr9RhpGcoMx8SWu96x8AViqlKgFwpg0pZT8RrbEAdmEIwM0U4TwAP4joDTNvZuZ7WuvV/whQkf1/13X7mHltEoCIbvu+vy57k+M4I8z8IA3Q1NRUWVNTs52Za1IhO87MtQAuArBvFI/H41d7enqG7XoqBNYD4wOQatV56x1Ag1LqSlkApJQHiOg0gOvMfIeIVjDzNCHEK2PMQiLayswHtdZnygrAzHu11heykmoPEZ2fBJj0wKQHcnjAzoIjtlwBLEk1sH4ADoA5JXXCdB8otgwdx7kB4I8CztHOv6dnwdeEtIoJIazEmp/oonEALwHszpwFpQJEIpH5FRUVG5nZjn07/aIpCDtxR4joqQX4AGB2npl9TSm1PdXZkp2wWA9k2hxTE0YikTlEtMhuFkLsBWDl+AtjzCGrB4jose/7dlra4ZIG2K+1PpfVCfcR0dnMHCgKIHOT4zh5FVHGLPgMwHrO6sHKhIj5xMzTAdSWFSAajS4TQvQBqB4jZEPGmHVdXV2Pcsz/ok5GBTWh53liYGAgqSXr6uqstAobYw4HQXCqvr6ePc8zueDKci4opAnHPQdyKKaizwVSyiVE9CRRit8GBwdr0yepssrybOBoNLqWmT92d3c/T6+NApBSJkUFEcV832/M0xuSS6WEYCxbf30faG1tXVBdXf1+rLNAVtkWHYKiAQq99UQDOAFgmxBiS2dnp02wkq/fFReAx7X4uMUAAAAASUVORK5CYII=",Am={class:"header"},Dm={class:"dropdown-menu"},Pm={class:"filter-right"},Fm=Re({__name:"nav",emits:["view-change","filter-change"],setup(t,{emit:r}){const e=R(null),a=R(!1),i=R("market"),n=R("all");function o(u){var h;const d=u.target,v=(h=e.value)==null?void 0:h.querySelector(".dropdown-menu");v!=null&&v.contains(d)||e.value&&!e.value.contains(d)&&(a.value=!1)}function s(u){i.value=u,a.value=!1,u==="optional"&&(n.value="all"),c("view-change",u)}function l(u){n.value=u,c("filter-change",u)}const c=r;return Ke(()=>{document.addEventListener("click",o)}),Ar(()=>{document.removeEventListener("click",o)}),(u,d)=>(G(),Q("div",Am,[S("div",{class:"title",ref_key:"titleRef",ref:e},[S("span",{class:"title-text",onClick:d[0]||(d[0]=v=>a.value=!a.value)},[d[6]||(d[6]=S("img",{src:Im,alt:"market",class:"market-icon",width:"16px"},null,-1)),Tr(" "+it(i.value==="market"?"行情-期货":"行情-股票")+" ",1),S("span",{class:se(["dropdown-arrow",{active:a.value}])},d[5]||(d[5]=[S("svg",{t:"1742451321748",class:"icon",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"32302",width:"16",height:"16"},[S("path",{d:"M722.438 510.815c-0.246-0.246-0.513-0.454-0.766-0.69l-299.893-299.892c-14.231-14.24-37.291-14.24-51.529 0-14.241 14.231-14.241 37.291 0 51.529l274.851 274.859-274.849 274.851c-14.241 14.237-14.241 37.301 0 51.532 7.116 7.119 16.436 10.682 25.764 10.682 9.321 0 18.644-3.563 25.763-10.682l299.905-299.901c0.246-0.234 0.51-0.438 0.751-0.678 7.129-7.13 10.685-16.469 10.674-25.804 0.006-9.337-3.55-18.676-10.674-25.804z",fill:"#ffffff","p-id":"32303"})],-1)]),2)]),ui(S("div",Dm,[S("div",{class:se(["dropdown-item",{active:i.value==="market"}]),onClick:d[1]||(d[1]=v=>s("market"))},"行情-期货",2),S("div",{class:se(["dropdown-item",{active:i.value==="optional"}]),onClick:d[2]||(d[2]=v=>s("optional"))},"行情-股票",2)],512),[[Xn,a.value]])],512),S("div",Pm,[S("span",{class:se(["filter-item",{active:n.value==="all"}]),onClick:d[3]||(d[3]=v=>l("all"))},"全部",2),S("span",{class:se(["filter-item",{active:n.value==="optional"}]),onClick:d[4]||(d[4]=v=>l("optional"))},"自选",2)])]))}}),Rm=Te(Fm,[["__scopeId","data-v-a54a866e"]]),Ri=Kn("orderflow",()=>{const t=Is(null);return{tick:t,setTick:i=>{t.value=i},getTick:()=>t.value,removeTick:()=>{t.value&&(t.value.destructor(),t.value=null)}}}),Bi=Kn("orderflow2",()=>{const t=R("sa"),r=R("SA509"),e=R(1),a=R("CZCE"),i=R(0),n=R(0),o=R(0),s=R(0),l=R(0),c=R(0),u=R(!1);return{contract:t,mainContract:r,period:e,exchange:a,priceChangeRatio:i,priceDifference:n,lastPrice:o,volume:s,openInterest:l,preSettlement:c,isShowAIPredict:u}}),Bm={class:"marketlist-container"},Nm={class:"marketlist-wrapper"},Om={class:"marketlist-header"},Vm=["onClick"],zm={class:"col2 code",style:{"padding-left":"5px",display:"flex","align-items":"center",gap:"5px"}},$m=["onClick"],Wm={class:"col2 name"},Ym={class:"col2 price"},Zm=Re({__name:"MarketList",setup(t,{expose:r}){const e=R("all"),a=di("child_futureListHeight"),i=sr();ta(),r({activeFilter:e});const n=R("code"),o=R(!0),s=R({}),l=R({}),c=rr(()=>Object.values(s.value).filter(y=>!isNaN(Number(y.lastPrice))&&!isNaN(Number(y.priceDifference))&&!isNaN(Number(y.priceChangeRatio))).map(y=>({...y,lastPrice:Number(y.lastPrice.toFixed(2)),priceDifference:Number(y.priceDifference.toFixed(2)),priceChangeRatio:Number((y.priceChangeRatio*100).toFixed(2))}))),u=rr(()=>{let C;return e.value==="optional"?C=Object.values(l.value).filter(y=>!isNaN(Number(y.lastPrice))&&!isNaN(Number(y.priceDifference))&&!isNaN(Number(y.priceChangeRatio))).map(y=>({...y,lastPrice:Number(y.lastPrice.toFixed(2)),priceDifference:Number(y.priceDifference.toFixed(2)),priceChangeRatio:Number((y.priceChangeRatio*100).toFixed(2))})):C=c.value,C.sort((y,T)=>{if(n.value==="name"){const A=v[y.underlyingSymbol]||"",F=v[T.underlyingSymbol]||"";return o.value?A.localeCompare(F,"zh"):F.localeCompare(A,"zh")}if(n.value==="code")return o.value?y.code.localeCompare(T.code):T.code.localeCompare(y.code);const I=y[n.value],E=T[n.value];return o.value?Number(I)-Number(E):Number(E)-Number(I)})});function d(C){n.value===C?o.value=!o.value:(n.value=C,o.value=!0)}const v={ih:"上证50主连",if:"沪深300主连",ic:"中证500主连",im:"中证1000主连",ts:"2年期国债",tf:"5年期国债",t:"10年期国债",tl:"30年期国债",sc:"原油主连",nr:"20号胶",lu:"低硫燃料油",bc:"国际铜",ec:"欧线集运主连",au:"沪金主连",ag:"沪银主连",cu:"沪铜主连",al:"沪铝主连",zn:"沪锌主连",pb:"沪铅主连",rb:"螺纹钢主连",ru:"橡胶主连",br:"合成橡胶主连",ni:"沪镍主连",sn:"沪锡主连",sp:"纸浆主连",fu:"燃油主连",bu:"沥青主连",ss:"不锈钢主连",hc:"热轧卷板",ao:"氧化铝主连",a:"豆一主连",b:"豆二主连",c:"玉米主连",cs:"玉米淀粉主连",m:"豆粕主连",i:"铁矿石主连",pg:"液化石油气主连",y:"豆油主连",p:"棕榈油主连",jd:"鸡蛋主连",l:"塑料主连",pp:"聚丙烯主连",v:"PVC主连",eb:"苯乙烯主连",eg:"乙二醇主连",j:"焦炭主连",jm:"焦煤主连",lh:"生猪主连",lg:"原木主连",ap:"苹果主连",sr:"白糖主连",oi:"菜籽油主连",cf:"棉花主连",cy:"棉纱主连",ta:"PTA主连",px:"对二甲苯主连",pr:"瓶片主连",ma:"甲醇主连",fg:"玻璃主连",sa:"纯碱主连",sh:"烧碱主连",rm:"菜粨主连",sf:"硅铁主连",sm:"锰硅主连",ur:"尿素主连",pk:"花生主连",cj:"红枣主连",pf:"短纤主连",lc:"碳酸锂主连",si:"工业硅主连",ps:"多晶硅主连"},h={ih:.2,if:.2,ic:.2,im:.2,ts:.005,tf:.005,t:.005,tl:.01,sc:.1,nr:5,lu:1,bc:10,ec:.1,au:.02,ag:1,cu:10,al:5,zn:5,pb:5,rb:1,ru:5,br:5,ni:10,sn:10,sp:2,fu:1,bu:1,ss:5,hc:1,ao:1,a:1,b:1,c:1,cs:1,m:1,i:.5,pg:1,y:2,p:2,jd:1,l:1,pp:1,v:1,eb:1,eg:1,j:.5,jm:.5,lh:5,lg:.5,ap:1,sr:1,oi:1,cf:5,cy:5,ta:2,px:1,pr:1,ma:1,fg:1,sa:1,sh:1,rm:1,sf:2,sm:2,ur:1,pk:2,cj:5,pf:2,lc:20,si:5,ps:5};function f(){const C=new Date,y=C.getTime()+C.getTimezoneOffset()*6e4,T=new Date(y+8*60*60*1e3),I=T.getDay(),E=T.getHours(),A=T.getMinutes();if(I===0)return!1;if(I===6){if(E<2||E===2&&A<=35)return!0}else if(E===8&&A>=50||E>8&&E<15||E===15&&A<=20||E===20&&A>=50||E>20||E<2||E===2&&A<=40)return!0;return!1}const p=R("");function g(C=!1){if(!(!C&&!f()))return Ls().then(y=>{if(y.code==="200"){s.value=y.data;const T=Object.values(y.data);T.sort((E,A)=>{if(n.value==="name"){const B=v[E.underlyingSymbol]||"",Y=v[A.underlyingSymbol]||"";return o.value?B.localeCompare(Y,"zh"):Y.localeCompare(B,"zh")}if(n.value==="code")return o.value?E.code.localeCompare(A.code):A.code.localeCompare(E.code);const F=E[n.value],N=A[n.value];return o.value?Number(F)-Number(N):Number(N)-Number(F)}),T.length>0&&(p.value=T[0].code);const I=s.value[L.contract];I&&(L.lastPrice=I.lastPrice,L.priceDifference=I.priceDifference,L.priceChangeRatio=I.priceChangeRatio,L.volume=I.volume,L.openInterest=I.openInterest)}})}let m;Ke(async()=>{await g(!0),m=setInterval(g,1e3),p.value&&await i.klineCharthandleContractChange(p.value),Aa().then(C=>{C.code=="200"&&(l.value=C.data)})}),Ar(()=>{clearInterval(m)});const b=C=>{const y=typeof C=="string"?parseFloat(C):C;return isNaN(y)?"":y>0?"up":y<0?"down":""},x=Ri(),L=Bi();function _(C){L.contract=C,L.mainContract=s.value[C].code,L.exchange=s.value[C].exchange,L.priceChangeRatio=s.value[C].priceChangeRatio,L.priceDifference=s.value[C].priceDifference,L.lastPrice=s.value[C].lastPrice,L.volume=s.value[C].volume,L.openInterest=s.value[C].openInterest,L.preSettlement=s.value[C].preSettlement,ks(s.value[C].code,1,200).then(y=>{if(y.code=="200"){const T=x.getTick();T&&(T.updateBaseData(y.data.baseBar,h[C],s.value[C].code),L.isShowAIPredict=!1,T.ClearChart(1),T.DrawChart(1))}})}function M(C){C in l.value?k(C):w(C)}function w(C){Ss(C).then(y=>{y.code=="200"&&Aa().then(T=>{T.code=="200"&&(l.value=T.data)})})}function k(C){Ms(C).then(y=>{y.code=="200"&&delete l.value[C]})}return(C,y)=>(G(),Q("div",Bm,[S("div",Nm,[S("div",Om,[S("span",{class:"col code",onClick:y[0]||(y[0]=T=>d("code"))},"商品代码"+it(n.value==="code"?o.value?"↑":"↓":""),1),S("span",{class:"col name",onClick:y[1]||(y[1]=T=>d("name"))},"名称"+it(n.value==="name"?o.value?"↑":"↓":""),1),S("span",{class:"col price",onClick:y[2]||(y[2]=T=>d("lastPrice"))},"最新价"+it(n.value==="lastPrice"?o.value?"↑":"↓":""),1),S("span",{class:"col change",onClick:y[3]||(y[3]=T=>d("priceDifference"))},"涨跌"+it(n.value==="priceDifference"?o.value?"↑":"↓":""),1),S("span",{class:"col changePercent",onClick:y[4]||(y[4]=T=>d("priceChangeRatio"))},"涨跌幅"+it(n.value==="priceChangeRatio"?o.value?"↑":"↓":""),1)]),S("div",{class:"future-list",style:_e({"max-height":`calc(${pe(a)}vh - 120px)`})},[(G(!0),Q(Ce,null,De(u.value,T=>ui((G(),Q("div",{key:T.code,class:"marketlist-row",onClick:I=>_(T.underlyingSymbol)},[S("span",zm,[S("span",{class:se(["optional-svg",{added:T.underlyingSymbol in l.value}]),onClick:Gn(I=>M(T.underlyingSymbol),["stop"])},y[5]||(y[5]=[S("svg",{t:"1743647836070",class:"icon",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"1646",width:"32",height:"32"},[S("path",{d:"M818.56 50.432a128 128 0 0 1 128 128v667.136a128 128 0 0 1-179.84 117.056L512 849.792l-254.72 112.832a128 128 0 0 1-179.84-117.12V178.496a128 128 0 0 1 128-128h613.12zM528 242.176a32 32 0 0 0-40.384 6.848l-3.328 4.736-56.512 97.28-109.952 23.68a32 32 0 0 0-20.544 48l3.456 4.608 75.008 83.776-11.456 111.872a32 32 0 0 0 39.296 34.368l5.504-1.856L512 610.048l102.848 45.44a32 32 0 0 0 44.8-26.752v-5.76l-11.456-111.872 75.008-83.84a32 32 0 0 0-11.52-50.88l-5.568-1.728-109.952-23.68-56.512-97.28a32 32 0 0 0-11.52-11.52z",fill:"#E1E2E3","p-id":"1647"})],-1)]),10,$m),Tr(" "+it(T.code),1)]),S("span",Wm,it(v[T.underlyingSymbol]),1),S("span",Ym,it(T.lastPrice),1),S("span",{class:se(["col2 change",b(T.priceDifference)])},it(T.priceDifference),3),S("span",{class:se(["col2 changePercent",b(T.priceChangeRatio)])},it(T.priceChangeRatio)+"% ",3)],8,Vm)),[[Xn,T.underlyingSymbol in v]])),128))],4)])]))}}),Hm=Te(Zm,[["__scopeId","data-v-60d834f9"]]),jm={class:"marketlist-container"},Xm={class:"marketlist-wrapper"},Km={class:"marketlist-header"},Gm=["onClick"],Um={class:"col2 code",style:{"padding-left":"5px",display:"flex","align-items":"center",gap:"5px"}},Qm=["onClick"],qm={class:"col2 name"},Jm={class:"col2 price"},ty=Re({__name:"StockList",setup(t,{expose:r}){const e=R("all"),a=di("child_futureListHeight"),i=sr(),n=ta();r({activeFilter:e});const o=R("code"),s=R(!0),l=R({}),c=R({}),u=rr(()=>Object.entries(l.value).filter(([C,y])=>!isNaN(Number(y.lastPrice))&&!isNaN(Number(y.priceDifference))&&!isNaN(Number(y.priceChangeRatio))).map(([C,y])=>({...y,code:C,lastPrice:Number(y.lastPrice.toFixed(2)),priceDifference:Number(y.priceDifference.toFixed(2)),priceChangeRatio:Number((y.priceChangeRatio*100).toFixed(2))}))),d=rr(()=>{let k;return e.value==="optional"?k=Object.entries(c.value).filter(([C,y])=>!isNaN(Number(y.lastPrice))&&!isNaN(Number(y.priceDifference))&&!isNaN(Number(y.priceChangeRatio))).map(([C,y])=>({...y,code:C,lastPrice:Number(y.lastPrice.toFixed(2)),priceDifference:Number(y.priceDifference.toFixed(2)),priceChangeRatio:Number((y.priceChangeRatio*100).toFixed(2))})):k=u.value,k.sort((C,y)=>{if(o.value==="name"||o.value==="code"){const E=C[o.value]||"",A=y[o.value]||"";return s.value?E.localeCompare(A,"zh"):A.localeCompare(E,"zh")}const T=C[o.value],I=y[o.value];return s.value?Number(T)-Number(I):Number(I)-Number(T)})});function v(k){o.value===k?s.value=!s.value:(o.value=k,s.value=!0)}function h(){const k=new Date,C=k.getTime()+k.getTimezoneOffset()*6e4,y=new Date(C+8*60*60*1e3),T=y.getDay(),I=y.getHours(),E=y.getMinutes();if(T===0)return!1;if(T===6){if(I<2||I===2&&E<=35)return!0}else if(I===8&&E>=50||I>8&&I<15||I===15&&E<=20||I===20&&E>=50||I>20||I<2||I===2&&E<=40)return!0;return!1}const f=R("");function p(k=!1){if(!(!k&&!h()))return Ls({type:"stock"}).then(C=>{if(C.code==="200"){l.value=C.data;const y=Object.values(C.data);y.sort((I,E)=>{if(o.value==="name"){const N=oldContract[I.underlyingSymbol]||"",B=oldContract[E.underlyingSymbol]||"";return s.value?N.localeCompare(B,"zh"):B.localeCompare(N,"zh")}if(o.value==="code")return s.value?I.code.localeCompare(E.code):E.code.localeCompare(I.code);const A=I[o.value],F=E[o.value];return s.value?Number(A)-Number(F):Number(F)-Number(A)}),y.length>0&&(f.value=y[0].code),n.symbol={exchange:"",market:"stocks",name:"000016.SZ",shortName:"000016.SZ",ticker:"000016.SZ",priceCurrency:"",type:"stock"};const T=l.value[x.contract];T&&(x.lastPrice=T.lastPrice,x.priceDifference=T.priceDifference,x.priceChangeRatio=T.priceChangeRatio,x.volume=T.volume,x.openInterest=T.openInterest)}})}let g;Ke(async()=>{await p(!0),g=setInterval(p,1e3),f.value&&await i.klineCharthandleContractChange(f.value),Aa({quotationType:"stock"}).then(k=>{k.code=="200"&&(c.value=k.data)})}),Ar(()=>{clearInterval(g)});const m=k=>{const C=typeof k=="string"?parseFloat(k):k;return isNaN(C)?"":C>0?"up":C<0?"down":""},b=Ri(),x=Bi();function L(k){const C=l.value[k];C&&(x.contract=k,x.mainContract=k,x.exchange=C.exchange||"",x.priceChangeRatio=C.priceChangeRatio,x.priceDifference=C.priceDifference,x.lastPrice=C.lastPrice,x.volume=C.volume,x.openInterest=C.openInterest,x.preSettlement=C.preSettlement,ks(k,1,200).then(y=>{if(y.code=="200"){const T=b.getTick();T&&(T.updateBaseData(y.data.baseBar,.01,k),x.isShowAIPredict=!1,T.ClearChart(1),T.DrawChart(1))}}))}function _(k){k in c.value?w(k):M(k)}function M(k){Ss(k,{quotationType:"stock"}).then(C=>{C.code=="200"&&Aa({quotationType:"stock"}).then(y=>{y.code=="200"&&(c.value=y.data)})})}function w(k){Ms(k,{quotationType:"stock"}).then(C=>{C.code=="200"&&delete c.value[k]})}return(k,C)=>(G(),Q("div",jm,[S("div",Xm,[S("div",Km,[S("span",{class:"col code",onClick:C[0]||(C[0]=y=>v("code"))},"商品代码"+it(o.value==="code"?s.value?"↑":"↓":""),1),S("span",{class:"col name",onClick:C[1]||(C[1]=y=>v("name"))},"名称"+it(o.value==="name"?s.value?"↑":"↓":""),1),S("span",{class:"col price",onClick:C[2]||(C[2]=y=>v("lastPrice"))},"最新价"+it(o.value==="lastPrice"?s.value?"↑":"↓":""),1),S("span",{class:"col change",onClick:C[3]||(C[3]=y=>v("priceDifference"))},"涨跌"+it(o.value==="priceDifference"?s.value?"↑":"↓":""),1),S("span",{class:"col changePercent",onClick:C[4]||(C[4]=y=>v("priceChangeRatio"))},"涨跌幅"+it(o.value==="priceChangeRatio"?s.value?"↑":"↓":""),1)]),S("div",{class:"future-list",style:_e({"max-height":`calc(${pe(a)}vh - 120px)`})},[(G(!0),Q(Ce,null,De(d.value,y=>(G(),Q("div",{key:y.code,class:"marketlist-row",onClick:T=>L(y.code)},[S("span",Um,[S("span",{class:se(["optional-svg",{added:y.code in c.value}]),onClick:Gn(T=>_(y.code),["stop"])},C[5]||(C[5]=[S("svg",{t:"1743647836070",class:"icon",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"1646",width:"32",height:"32"},[S("path",{d:"M818.56 50.432a128 128 0 0 1 128 128v667.136a128 128 0 0 1-179.84 117.056L512 849.792l-254.72 112.832a128 128 0 0 1-179.84-117.12V178.496a128 128 0 0 1 128-128h613.12zM528 242.176a32 32 0 0 0-40.384 6.848l-3.328 4.736-56.512 97.28-109.952 23.68a32 32 0 0 0-20.544 48l3.456 4.608 75.008 83.776-11.456 111.872a32 32 0 0 0 39.296 34.368l5.504-1.856L512 610.048l102.848 45.44a32 32 0 0 0 44.8-26.752v-5.76l-11.456-111.872 75.008-83.84a32 32 0 0 0-11.52-50.88l-5.568-1.728-109.952-23.68-56.512-97.28a32 32 0 0 0-11.52-11.52z",fill:"#E1E2E3","p-id":"1647"})],-1)]),10,Qm),Tr(" "+it(y.code),1)]),S("span",qm,it(y.name),1),S("span",Jm,it(y.lastPrice),1),S("span",{class:se(["col2 change",m(y.priceDifference)])},it(y.priceDifference),3),S("span",{class:se(["col2 changePercent",m(y.priceChangeRatio)])},it(y.priceChangeRatio)+"% ",3)],8,Gm))),128))],4)])]))}}),ey=Te(ty,[["__scopeId","data-v-851e3898"]]),ry={class:"list-container"},ay=Re({__name:"ListContainer",setup(t){const r=R("market"),e=R(),a=R();function i(o){r.value=o}function n(o){r.value==="market"&&e.value?e.value.activeFilter=o:r.value==="optional"&&a.value&&(a.value.activeFilter=o)}return(o,s)=>(G(),Q("div",ry,[kt(Rm,{onViewChange:i,onFilterChange:n}),r.value==="market"?(G(),Ua(Hm,{key:0,ref_key:"marketListRef",ref:e},null,512)):(G(),Ua(ey,{key:1,ref_key:"stockListRef",ref:a},null,512))]))}}),iy=Te(ay,[["__scopeId","data-v-e0d3373b"]]),ny={class:"aiprediction-container"},oy={class:"aiprediction-wrapper"},sy=["onClick"],ly={key:0},cy={key:1},uy={key:0,class:"model-options"},dy=["onClick"],hy=Re({__name:"AiPrediction",setup(t){const r=Ri(),e=Bi(),a=["SVM","RandomForest","LSTM","MLP","Transformer","XGBoost"],i={LSTM:[{value:"LSTM_SET_1",label:"2个layer，每个layer有50个units"},{value:"LSTM_SET_2",label:"2个layer，每个layer有100个units"},{value:"LSTM_SET_3",label:"2个layer，每个layer有25个units"}],MLP:[{value:"MLP_SET_1",label:"1个hidden layer，64个神经元"},{value:"MLP_SET_2",label:"1个hidden layer，64个神经元"},{value:"MLP_SET_3",label:"1个hidden layer，64个神经元"}],RandomForest:[{value:"RF_SET_1",label:"15个estimators，max_depth无限制"},{value:"RF_SET_2",label:"25个estimators，max_depth为5"},{value:"RF_SET_3",label:"10个estimators，max_depth为3"}],SVM:[{value:"SVM_SET_1",label:"RBF核，C=1.0，epsilon=0.1"},{value:"SVM_SET_2",label:"RBF核，C=10.0，epsilon=0.01"},{value:"SVM_SET_3",label:"RBF核，C=0.1，epsilon=0.5"}],Transformer:[{value:"TRANSFORMER_SET_1",label:"Head_size为64，单层结构，dropout是10%"},{value:"TRANSFORMER_SET_2",label:"Head_size为128，layer为2，dropout为20%"},{value:"TRANSFORMER_SET_3",label:"Head_size为32，layer为1，dropout为5%"}],XGBoost:[{value:"XGBOOST_SET_1",label:"n_estimators为5，最大深度为1，学习率为0.1，行采样比例为0.5，列采样比例为0.5"},{value:"XGBOOST_SET_2",label:"n_estimators为10，最大深度为2，学习率为0.15，行采样比例为0.5，列采样比例为0.6"},{value:"XGBOOST_SET_3",label:"n_estimators为15，最大深度为3，学习率为0.2，行采样比例为0.6，列采样比例为0.5"}]},n=R(0),o=R(1),s=R(null);function l(h){n.value=h}function c(h){o.value=h}Pe(n,h=>{if(!s.value)return;const p=s.value.querySelectorAll(".card")[h],g=s.value,m=g.getBoundingClientRect(),b=p.getBoundingClientRect(),x=b.left-m.left-m.width/2+b.width/2;g.scrollBy({left:x,behavior:"smooth"})}),Ke(()=>{l(2)});function u(h){const f=Math.abs(h-n.value);return{margin:"-12px",transform:`scale(${1.3-f*.14})`,zIndex:6-f}}function d(){e.isShowAIPredict=!0;const h=r.getTick();if(h){h.ifDrawAIPredict=!0;const f=i[a[n.value]][o.value].value;h.modelSetting=f,h.exchange=e.exchange,h.StartUpdateAIPredictData(!0)}}function v(){e.isShowAIPredict=!1;const h=r.getTick();h&&(h.ifDrawAIPredict=!1,h.ClearChart(1),h.DrawChart(2))}return(h,f)=>(G(),Q("div",ny,[S("div",oy,[f[5]||(f[5]=S("div",{class:"title"}," Ai预测 ",-1)),S("div",{class:"card-container",ref_key:"container",ref:s},[f[1]||(f[1]=S("div",{class:"spacer"},null,-1)),(G(),Q(Ce,null,De(a,(p,g)=>S("div",{class:se(["card",{"selected-card":n.value===g}]),key:g,style:_e(u(g)),onClick:m=>l(g)},[p==="RandomForest"?(G(),Q("span",ly,f[0]||(f[0]=[Tr("Random"),S("br",null,null,-1),Tr("Forest")]))):(G(),Q("span",cy,it(p),1))],14,sy)),64)),f[2]||(f[2]=S("div",{class:"spacer"},null,-1))],512),f[6]||(f[6]=S("div",{class:"sub-title"}," 预测模型 ",-1)),i[a[n.value]]?(G(),Q("div",uy,[(G(!0),Q(Ce,null,De(i[a[n.value]],(p,g)=>(G(),Q("div",{key:p.value,class:se(["option-item",{"selected-option":o.value===g}]),onClick:m=>c(g)},it(p.label),11,dy))),128))])):ue("",!0),pe(e).isShowAIPredict?ue("",!0):(G(),Q("div",{key:1,class:"start-btn",onClick:d},f[3]||(f[3]=[S("div",null,[S("svg",{width:"16px",height:"15px",viewBox:"0 0 16 15",xmlns:"http://www.w3.org/2000/svg"},[S("g",{fill:"#2767EE","fill-rule":"nonzero"},[S("path",{d:"M6.212,12 L0,12 L0,0 L15,0 L15,6.258 C14.691518,6.0047443 14.3561779,5.78610256 14,5.606 L14,1 L1,1 L1,9 L6.212,9 C6.1189172,9.32684284 6.05631633,9.66160683 6.025,10 L1,10 L1,11 L6.025,11 C6.056,11.344 6.12,11.677 6.212,12 Z M4,13 L4,14 L7.257,14 C7.0040793,13.6912725 6.78546229,13.3559704 6.605,13 L4,13 Z M7.685,13.438 L8.392,14.145 L9.806,12.731 L9.099,12.024 L7.685,13.438 Z M13.341,9.196 L14.755,7.782 L14.048,7.075 L12.634,8.489 L13.341,9.196 Z M7.685,7.782 L9.099,9.196 L9.806,8.489 L8.392,7.075 L7.685,7.782 L7.685,7.782 Z M12.634,12.731 L14.048,14.145 L14.755,13.438 L13.341,12.024 L12.634,12.731 Z M11,15 L12,15 L12,13 L11,13 L11,15 Z M11,8 L12,8 L12,6 L11,6 L11,8 Z M9,10 L7,10 L7,11 L9,11 L9,10 Z M14,10 L14,11 L16,11 L16,10 L14,10 Z"})])])],-1),S("div",null,"启动",-1)]))),pe(e).isShowAIPredict?(G(),Q("div",{key:2,class:"stop-btn",onClick:v},f[4]||(f[4]=[S("div",{class:"rotate-svg"},[S("svg",{t:"1747576575240",class:"icon",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"1505",width:"16",height:"16"},[S("path",{d:"M512 0a51.2 51.2 0 0 1 51.2 51.2v153.6a51.2 51.2 0 0 1-102.4 0V51.2a51.2 51.2 0 0 1 51.2-51.2z m0 768a51.2 51.2 0 0 1 51.2 51.2v153.6a51.2 51.2 0 0 1-102.4 0v-153.6a51.2 51.2 0 0 1 51.2-51.2z m443.392-512a51.2 51.2 0 0 1-18.7392 69.9392l-133.0176 76.8a51.2 51.2 0 1 1-51.2-88.6784l133.0176-76.8A51.2 51.2 0 0 1 955.392 256zM290.304 640a51.2 51.2 0 0 1-18.7392 69.9392l-133.0176 76.8a51.2 51.2 0 1 1-51.2-88.6784l133.0176-76.8a51.2 51.2 0 0 1 69.9392 18.7392zM955.392 768a51.2 51.2 0 0 1-69.9392 18.7392l-133.0176-76.8a51.2 51.2 0 0 1 51.2-88.6784l133.0176 76.8A51.2 51.2 0 0 1 955.392 768zM290.304 384a51.2 51.2 0 0 1-69.9392 18.7392l-133.0176-76.8a51.2 51.2 0 1 1 51.2-88.6784l133.0176 76.8A51.2 51.2 0 0 1 290.304 384z",fill:"#ffffff","p-id":"1506"})])],-1),S("div",null,"停止",-1)]))):ue("",!0)])]))}}),vy=Te(hy,[["__scopeId","data-v-aecc3abd"]]),fy={class:"toppanel-container"},py={class:"toppanel-wrapper"},gy={key:0,class:"item"},my={key:1,class:"item"},yy=Re({__name:"index",props:{currentCustomPanelIndex:{},currentCustomPanelIndexModifiers:{}},emits:["update:currentCustomPanelIndex"],setup(t){const r=As(t,"currentCustomPanelIndex");return(e,a)=>(G(),Q("div",fy,[S("div",py,[r.value==1?(G(),Q("div",gy,[kt(iy)])):ue("",!0),r.value==2?(G(),Q("div",my,[kt(vy)])):ue("",!0)])]))}}),_y=Te(yy,[["__scopeId","data-v-9f840650"]]),Cy={class:"header"},by={class:"date-wrapper"},xy={style:{width:"0",height:"0",overflow:"hidden",position:"absolute","z-index":"99999999"}},wy={key:0,class:"es"},Ly=["onClick"],ky={__name:"table-right",setup(t){const r=sr(),e=di("child_dailyPositionHeight"),a=R([]),i=R([]),n=R(!1),o=R(1),s=R(10),l=R(0),c=R(1),u=R(new Set),d=M=>M==null?"¥0.00":`¥${(M/100).toFixed(2)}`;Pe(()=>[r.timeRange,r.startDate,r.endDate],async([M,w,k])=>{w&&k&&(f.value=k,await v())},{immediate:!0}),Pe(()=>r.rightTableData,async M=>{M&&M.node_output&&await v()},{immediate:!0});const v=async()=>{var M;if(console.log("Fetching position data with:",{rightTableData:r.rightTableData,nodeOutput:(M=r.rightTableData)==null?void 0:M.node_output,selectedDate:f.value,isInitialized:n.value}),!r.rightTableData||!r.rightTableData.node_output){console.log("Missing required data for fetch");return}try{await r.fetchBacktestPosition(r.rightTableData.node_output,1,1e4,""),r.backtestPosition&&r.backtestPosition.data&&(i.value=r.backtestPosition.data.items||[],u.value=new Set(i.value.map(w=>typeof w.gmt_create=="string"&&w.gmt_create.length===8?`${w.gmt_create.slice(0,4)}-${w.gmt_create.slice(4,6)}-${w.gmt_create.slice(6,8)}`:m(new Date(w.gmt_create)))),(!f.value||!n.value)&&u.value.size>0&&(f.value=Array.from(u.value)[0],n.value=!0),h())}catch(w){console.error("获取持仓数据失败",w)}},h=()=>{if(console.log("Filtering data with date:",f.value),console.log("Available data:",i.value),!f.value||!i.value){a.value=[],l.value=0;return}const M=i.value.filter(C=>{const y=typeof C.gmt_create=="string"&&C.gmt_create.length===8?`${C.gmt_create.slice(0,4)}-${C.gmt_create.slice(4,6)}-${C.gmt_create.slice(6,8)}`:m(new Date(C.gmt_create)),T=m(new Date(f.value));return console.log("Comparing dates:",{itemDate:y,selectedDateStr:T,original:C.gmt_create}),y===T});console.log("Filtered data:",M),l.value=M.length,c.value=Math.ceil(l.value/s.value);const w=(o.value-1)*s.value,k=w+s.value;a.value=M.slice(w,k),console.log("Final position data:",a.value)},f=R(""),p=R(null);Pe(f,()=>{o.value=1,h()});const g=()=>{var M;(M=p.value)==null||M.focus()},m=M=>{if(!M)return"";const w=new Date(M),k=w.getFullYear(),C=String(w.getMonth()+1).padStart(2,"0"),y=String(w.getDate()).padStart(2,"0");return`${k}-${C}-${y}`},b=M=>{const w=m(M);return!u.value.has(w)},x=M=>{if(!M)return"";const w=M.substring(0,4),k=M.substring(4,6),C=M.substring(6,8);return`${w}-${k}-${C}`},L=async M=>{const w=M.indexOf("."),k=M.substring(0,w);await r.fetchBacktestTrade(r.rightTableData.node_output,1,1e4);const C=r.backtestTrade.data.items,y=C.filter(E=>E.business===0),T=C.filter(E=>E.business===1),I={buyFigure:y.map(E=>({timestamp:new Date(x(E.gmt_create)).getTime(),value:E.price})),sellFigure:T.map(E=>({timestamp:new Date(x(E.gmt_create)).getTime(),value:E.price}))};console.log("overlay",I),r.klineCharthandleContractChange(k,I)},_=()=>{const M=document.querySelectorAll('[id^="el-popper-container-"]');console.log("找到popper元素数量:",M.length),M.forEach(w=>{w.style.position="relative",w.style.zIndex="99999999999999999",console.log(`已设置元素 ${w.id} 的样式`)})};return Ke(async()=>{console.log("Component mounted"),_(),await v()}),(M,w)=>{const k=Zr("el-icon"),C=Zr("el-date-picker");Zr("el-pagination");const y=Zr("el-table-column"),T=Zr("el-table");return G(),Q("div",{class:"table-right",style:_e({maxHeight:pe(e)+"vh"})},[S("div",Cy,[S("div",by,[S("span",{class:"date-text",onClick:g},"每日持仓"),f.value?(G(),Q("span",{key:0,class:"selected-date",onClick:g},"（"+it(m(f.value))+"）",1)):ue("",!0),kt(k,{class:"arrow-icon",onClick:g},{default:ur(()=>[kt(pe(Ds))]),_:1}),S("div",xy,[kt(C,{modelValue:f.value,"onUpdate:modelValue":w[0]||(w[0]=I=>f.value=I),type:"date",placeholder:"请选择日期",size:"small",clearable:!1,class:"table-right-date-picker",ref_key:"datePicker",ref:p,"disabled-date":b},null,8,["modelValue"])])]),a.value.length>0?(G(),Q("div",wy,"已持仓"+it(l.value),1)):ue("",!0),ue("",!0)]),kt(T,{data:a.value,border:"",style:{width:"100%"},"max-height":`calc(${pe(e)}vh - 20px)`,class:"custom-table-r"},{default:ur(()=>[kt(y,{fixed:"",prop:"contract_name",label:"标的"}),kt(y,{width:"100",prop:"contract_code",label:"代码"},{default:ur(I=>[S("span",{class:"contract-code",onClick:E=>L(I.row.contract_code)},it(I.row.contract_code),9,Ly)]),_:1}),kt(y,{label:"最新价"},{default:ur(I=>[S("span",null,it(d(I.row.last_price)),1)]),_:1}),kt(y,{label:"仓位"},{default:ur(I=>[S("span",{class:se(I.row.direction===0?"position-long":"position-short")},it(I.row.direction===0?"多":"空")+" "+it(Math.abs(I.row.position)),3)]),_:1}),kt(y,{label:"开仓均价"},{default:ur(I=>[S("span",null,it(d(I.row.price)),1)]),_:1}),kt(y,{fixed:"right",label:"累积盈亏"},{default:ur(I=>[S("span",{class:se(["profit",I.row.accumulate_profit>0?"profit-positive":"profit-negative"])},it(d(I.row.accumulate_profit)),3)]),_:1})]),_:1},8,["data","max-height"])],4)}}},Sy=Te(ky,[["__scopeId","data-v-dc4d211b"]]),My={class:"foot-head"},Ty={class:"menu"},Ey=["onClick"],Iy=Re({__name:"foot-head",props:{activeTab:{},tabs:{}},emits:["tabChange"],setup(t,{emit:r}){const e=r,a=i=>{e("tabChange",i)};return(i,n)=>(G(),Q("div",My,[ue("",!0),S("div",Ty,[(G(!0),Q(Ce,null,De(i.tabs,(o,s)=>(G(),Q("div",{key:o.key,class:se(["menu-item",{active:i.activeTab===o.key}]),onClick:l=>a(o.key)},it(o.label),11,Ey))),128))]),ue("",!0)]))}}),Ay=Te(Iy,[["__scopeId","data-v-e19dc403"]]),ca="http://api.pandaai.online",cr=ta();class Dy{constructor(r){Yr(this,"_apiKey");Yr(this,"_subscriptions",new Map);Yr(this,"_currentSubscription",null);Yr(this,"_debounceTimer",null);this._apiKey=r}async searchSymbols(r){try{console.log("searchSymbols.kDataStore.type",cr.type),console.log("searchSymbols.kDataStore.quotationType",cr.quotationType);const e=await fetch(`${ca}/historyData/querySymobl?type=${cr.type}`);if(!e.ok)return console.error(`API 请求失败: ${e.status} ${e.statusText}`),[];const a=await e.json();if(console.log("API 返回结果:",a),a.code!=="200"||!a.data)return console.error("API 返回错误:",a.message),[];const i=Object.values(a.data);console.log(`获取到 ${i.length} 个合约`);let n=i;if(r&&r.trim()){const o=r.toLowerCase();n=i.filter(s=>s.symbol.includes(o)||s.tradingCode.toLowerCase().includes(o)),console.log(`搜索 "${r}" 匹配到 ${n.length} 个合约`)}return n.map(o=>({ticker:o.orderBookID||"",name:o.tradingCode||o.code||"",shortName:o.symbol||"",exchange:o.exchange||"",market:"futures",pricePrecision:2,volumePrecision:0,priceCurrency:"CNY",type:"future"}))}catch(e){return console.error("搜索代码时发生错误:",e),[]}}async getHistoryKLineData(r,e,a,i){console.log("getHistoryKLineData",r,e,a,i),a=a-5*24*60*60*1e3,console.log("getHistoryKLineData.kDataStore.type",cr.type),console.log("getHistoryKLineData.kDataStore.quotationType",cr.quotationType);const o=await(await fetch(`${ca}/historyData/queryHistoryData?startDate=${a}&endDate=${i}&symbol=${r.ticker.toUpperCase()}&period=${e.text}&quotationType=${cr.quotationType}`)).json();return o.data=o.data.reverse(),await(o.data||[]).map(s=>({timestamp:parseInt(s.date),open:s.open,high:s.high,low:s.low,close:s.close,volume:s.volume,turnover:s.volume}))}async subscribe(r,e,a){const i=`${r.ticker}_${e.text}`;console.log(`订阅数据: ${i}`),this._debounceTimer&&(clearTimeout(this._debounceTimer),this._debounceTimer=null),this._debounceTimer=setTimeout(async()=>{try{if(this._subscriptions.has(i)){console.log(`已存在订阅 ${i}，先取消再重新订阅`);const o=this._subscriptions.get(i);o&&clearInterval(o),this._subscriptions.delete(i)}this._subscriptions.forEach((o,s)=>{s!==i&&(console.log(`取消其他订阅: ${s}`),clearInterval(o))}),this._subscriptions.clear(),this._currentSubscription=i;const n=setInterval(async()=>{if(this._currentSubscription!==i){console.log(`订阅 ${i} 已过期，停止数据获取`);return}try{const o={symbolCode:r.ticker||"SA509",cycle:1,baseBarCount:1};let s=localStorage.getItem("token");s="***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";const l=await fetch(`${ca}/instrument/queryLiveData?quotation=${r.ticker}&quotationType=${cr.quotationType}&period=${e.text}`,{method:"GET",headers:{"Content-Type":"application/json",Authorization:`${s}`}});if(!l.ok)throw new Error(`API请求失败: ${l.status} ${l.statusText}`);const c=await l.json();if(c.code!=="200")throw new Error(`API返回错误: ${c.code} - ${c.message}`);if(Object.keys(c.data).length===0)return;const u=Object.keys(c.data)[0],d=c.data[u],v={timestamp:this.parseTickTime(d.ticktime),open:d.open,high:d.high,low:d.low,close:d.close,volume:d.volume};this._currentSubscription===i&&a(v)}catch(o){console.error(`订阅 ${i} 数据获取失败:`,o)}},1e3);this._subscriptions.set(i,n),console.log(`✅ 订阅创建成功: ${i}`)}catch(n){console.error(`创建订阅失败: ${i}`,n)}},300)}unsubscribe(r,e){const a=`${r.ticker}_${e.text}`;if(console.log(`取消订阅: ${a}`),this._debounceTimer&&(clearTimeout(this._debounceTimer),this._debounceTimer=null),this._subscriptions.has(a)){const i=this._subscriptions.get(a);i&&(clearInterval(i),console.log(`✅ 已取消订阅: ${a}`)),this._subscriptions.delete(a)}this._currentSubscription===a&&(this._currentSubscription=null)}unsubscribeAll(){console.log("取消所有订阅"),this._debounceTimer&&(clearTimeout(this._debounceTimer),this._debounceTimer=null),this._subscriptions.forEach((r,e)=>{clearInterval(r),console.log(`✅ 已取消订阅: ${e}`)}),this._subscriptions.clear(),this._currentSubscription=null}async getOrderFlowData(r){try{console.log(`📡 调用PandaAI订单流API: ${r.ticker}`);const e={symbolCode:r.ticker||"SA509",cycle:1,baseBarCount:1e4};let a=localStorage.getItem("token");a="***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";const i=await fetch(`${ca}/instrument/queryLiveData?quotation=${r.ticker}&quotationType=${cr.quotationType}&period=${period.text}`,{method:"GET",headers:{"Content-Type":"application/json",Authorization:`${a}`}});if(!i.ok)throw new Error(`API请求失败: ${i.status} ${i.statusText}`);const n=await i.json();if(n.code!=="200")throw new Error(`API返回错误: ${n.code} - ${n.message}`);const o=this.convertPandaApiDataToOrderFlow(n.data,r);return console.log(`✅ 转换完成，共${o.length}条订单流数据`),o}catch(e){return console.error("❌ 获取订单流数据失败:",e),console.warn("🔄 使用模拟数据作为降级方案"),this.generateMockOrderFlowData(r)}}convertPandaApiDataToOrderFlow(r,e){const a=Object.keys(r)[0],i=r[a];if(i.baseBar=[r[a]],!i)return console.warn("⚠️ API数据格式不正确，使用模拟数据"),this.generateMockOrderFlowData(e);const n=[];for(const o of r.baseBar)try{const s=this.parseTickTime(o.ticktime),l=[],c=o.price||[],u=o.Ask||[],d=o.Bid||[];for(let h=0;h<c.length;h++){const f=c[h],p=u[h]||0,g=d[h]||0;f&&(p>0||g>0)&&l.push({price:Number(f.toFixed(2)),buyVolume:g,sellVolume:p,totalVolume:g+p})}l.sort((h,f)=>h.price-f.price);const v={timestamp:s,priceLevels:l,tickSize:.5,open:o.open,high:o.high,low:o.low,close:o.close,volume:o.volume,delta:o.delta};n.push(v)}catch(s){console.error("❌ 转换单个Bar数据失败:",s,o)}return n}parseTickTime(r){try{if(!r)return Date.now();const e=new Date(r);return isNaN(e.getTime())?(console.warn(`⚠️ 无法解析时间格式: ${r}，使用当前时间`),Date.now()):e.getTime()}catch(e){return console.error("❌ 时间解析失败:",e,r),Date.now()}}generateMockOrderFlowData(r){console.log(`🔄 生成 ${r.ticker} 的模拟订单流数据`);const e=[],a=Date.now()-5*24*60*60*1e3;for(let i=100;i>=0;i--){const n=a-i*60*1e3,o=100+Math.random()*20,s=[];for(let l=0;l<10;l++){const c=o+(l-5)*.1,u=Math.floor(Math.random()*1e3),d=Math.floor(Math.random()*1e3);s.push({price:Number(c.toFixed(2)),buyVolume:u,sellVolume:d,totalVolume:u+d})}e.push({timestamp:n,priceLevels:s,tickSize:.01})}return console.log("mockData",e),e}}const Py={key:0,class:"table-container"},Fy={class:"table-wrapper"},Ry={class:"custom-table"},By=["title"],Ny={class:"name-cell table-cell"},Oy=["onBlur","onKeyup"],Vy=["title"],zy=["onClick"],$y={class:"table-cell"},Wy={class:"table-cell"},Yy={style:{"text-align":"right",width:"190px"}},Zy=["onClick"],Hy=["onClick"],jy={key:0,class:"loading-spinner"},Xy={class:"pagination"},Ky={class:"page-nav"},Gy=["disabled"],Uy={style:{transform:"rotate(180deg)"},viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"32302",width:"16",height:"16"},Qy={key:0,class:"dots"},qy=["onClick"],Jy=["disabled"],t_={style:{transform:"rotate(0deg)"},viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"32302",width:"16",height:"16"},e_={key:1,class:"table-container"},r_={key:2,class:"table-container"},a_={__name:"tableb",props:{tableData:{type:Array,default:()=>[]}},emits:["apply"],setup(t,{expose:r,emit:e}){const a=Un(),i=e,n=sr(),o=Qn(),s=E=>{E?o.push(`/editor?workflow_id=${E}`):o.push("/editor")},l=R(null),c=R(""),u=R(null),d=t,v=rr(()=>!d.tableData||!d.tableData.data||!d.tableData.data.workflows?[]:d.tableData.data.workflows.map(E=>({id:E._id,name:E.name,createTime:zi(E.create_at),modifyTime:zi(E.update_at),item:E}))),h=R([]);Pe(v,E=>{h.value=E},{immediate:!0});const f=R(1),p=R(10),g=rr(()=>h.value.length),m=rr(()=>Math.ceil(g.value/p.value)),b=rr(()=>{const E=(f.value-1)*p.value,A=E+p.value;return h.value.slice(E,A)}),x=rr(()=>{const E=[],A=m.value,F=f.value;if(A<=5){for(let N=1;N<=A;N++)E.push(N);return E}if(E.push(1),F>3&&E.push("..."),F===1||F===2)E.includes(2)||E.push(2),E.includes(3)||E.push(3);else if(F===A||F===A-1)for(let N=A-2;N<A;N++)E.includes(N)||E.push(N);else E.push(F-1,F,F+1);return F<A-2&&E.push("..."),E.includes(A)||E.push(A),[...new Set(E)]}),L=E=>{E!=="..."&&(f.value=E)},_=()=>{f.value>1&&f.value--},M=()=>{f.value<m.value&&f.value++},w=R({}),k=(E,A)=>{w.value[A]=!0,i("apply",{workflow_id:E._id,title:E.name,feature_tag:"factor",locator:"task_id"}),setTimeout(()=>{w.value[A]=!1},5e3),console.log("应用项目：",E)},C=E=>{E?w.value[E]=!1:Object.keys(w.value).forEach(A=>{w.value[A]=!1})};Pe(v,()=>{C()},{deep:!0}),r({resetLoadingState:C});const y=async E=>{var A;l.value=E.id,c.value=E.name,await Je(),(A=u.value)==null||A.focus()},T=async E=>{if(!c.value.trim()){a.error("名称不能为空"),c.value=E.name;return}if(c.value===E.name){l.value=null;return}try{await rl(E.id,c.value);const A=h.value.findIndex(F=>F.id===E.id);A!==-1&&(h.value[A].name=c.value),l.value=null,a.success("名称修改成功")}catch(A){console.error("重命名失败:",A),c.value=E.name,a.error("名称修改失败，请稍后重试")}},I=()=>{l.value=null,c.value=""};return(E,A)=>h.value.length>0?(G(),Q("div",Py,[S("div",Fy,[S("table",Ry,[A[3]||(A[3]=S("thead",null,[S("tr",null,[S("th",null,"ID"),S("th",null,"名称"),S("th",null,"创建时间"),S("th",null,"修改时间"),S("th")])],-1)),S("tbody",null,[(G(!0),Q(Ce,null,De(b.value,(F,N)=>(G(),Q("tr",{key:F.id},[S("td",{class:"table-cell",title:F.id},it(F.id),9,By),S("td",Ny,[l.value===F.id?ui((G(),Q("input",{key:0,"onUpdate:modelValue":A[0]||(A[0]=B=>c.value=B),type:"text",class:"edit-input",onBlur:B=>T(F),onKeyup:[Vi(B=>T(F),["enter"]),Vi(I,["esc"])],ref_for:!0,ref_key:"editInput",ref:u},null,40,Oy)),[[Ps,c.value]]):(G(),Q(Ce,{key:1},[S("span",{class:"table-cell",title:F.name},it(F.name),9,Vy),F.owner!=="*"?(G(),Q("span",{key:0,class:"copy-icon",onClick:B=>y(F)},A[2]||(A[2]=[Fs('<svg width="12px" height="12px" viewBox="0 0 12 12" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" data-v-19fd2e59><title data-v-19fd2e59>修改名称</title><g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" data-v-19fd2e59><g id="超级图表-工作流" transform="translate(-364, -729)" fill="#2767EE" fill-rule="nonzero" data-v-19fd2e59><g id="编辑" transform="translate(364, 729)" data-v-19fd2e59><path d="M5.98554217,5.31036145 L10.9359036,0.361445783 C11.1310843,0.16626506 11.4477108,0.16626506 11.6428916,0.361445783 C11.8380723,0.556626506 11.8380723,0.873253012 11.6428916,1.06843373 L6.69253012,6.0173494 C6.4973494,6.21253012 6.18072289,6.21253012 5.98554217,6.0173494 C5.79036145,5.82216867 5.79036145,5.50554217 5.98554217,5.31036145 Z M10.9995181,5.83373494 C10.9995181,5.55759036 11.2236145,5.33349398 11.499759,5.33349398 C11.7759036,5.33349398 12,5.55759036 12,5.83373494 L12,9.50024096 C12,10.8809639 10.8809639,12 9.50024096,12 L2.49975904,12 C1.11903614,12 0,10.8809639 0,9.50024096 L0,2.49975904 C0,1.11903614 1.11903614,0 2.49975904,0 L6.16626506,0 C6.44240964,0 6.66650602,0.224096386 6.66650602,0.500240964 C6.66650602,0.776385542 6.44240964,1.00048193 6.16626506,1.00048193 L2.49975904,1.00048193 C1.6713253,1.00048193 1.00048193,1.67277108 1.00048193,2.49975904 L1.00048193,9.50024096 C1.00048193,10.3286747 1.67277108,11.0009639 2.49975904,11.0009639 L9.50024096,11.0009639 C10.3286747,11.0009639 11.0009639,10.3286747 11.0009639,9.50024096 L11.0009639,5.83373494 L10.9995181,5.83373494 Z" id="形状" data-v-19fd2e59></path></g></g></g></svg>',1)]),8,zy)):ue("",!0)],64))]),S("td",$y,it(F.createTime),1),S("td",Wy,it(F.modifyTime),1),S("td",Yy,[S("button",{class:se(["apply-btn"]),onClick:B=>s(F.id),style:{"margin-right":"10px"}}," 编辑 ",8,Zy),S("button",{style:{width:"100px"},class:se(["apply-btn",{loading:w.value[F.id]}]),onClick:B=>k(F.item,F.id)},[Tr(it(w.value[F.id]?"正在应用":"应用")+" ",1),w.value[F.id]?(G(),Q("span",jy)):ue("",!0)],10,Hy)])]))),128))])])]),S("div",Xy,[S("div",Ky,[S("button",{disabled:f.value===1,onClick:_},[(G(),Q("svg",Uy,A[4]||(A[4]=[S("path",{"data-v-da8ede72":"",d:"M722.438 510.815c-0.246-0.246-0.513-0.454-0.766-0.69l-299.893-299.892c-14.231-14.24-37.291-14.24-51.529 0-14.241 14.231-14.241 37.291 0 51.529l274.851 274.859-274.849 274.851c-14.241 14.237-14.241 37.301 0 51.532 7.116 7.119 16.436 10.682 25.764 10.682 9.321 0 18.644-3.563 25.763-10.682l299.905-299.901c0.246-0.234 0.51-0.438 0.751-0.678 7.129-7.13 10.685-16.469 10.674-25.804 0.006-9.337-3.55-18.676-10.674-25.804z",fill:"#ffffff","p-id":"32303"},null,-1)])))],8,Gy),(G(!0),Q(Ce,null,De(x.value,(F,N)=>(G(),Q(Ce,{key:N},[F==="..."?(G(),Q("span",Qy,"...")):(G(),Q("button",{key:1,class:se({active:F===f.value}),onClick:B=>L(F)},it(F),11,qy))],64))),128)),S("button",{disabled:f.value===m.value,onClick:M},[(G(),Q("svg",t_,A[5]||(A[5]=[S("path",{"data-v-da8ede72":"",d:"M722.438 510.815c-0.246-0.246-0.513-0.454-0.766-0.69l-299.893-299.892c-14.231-14.24-37.291-14.24-51.529 0-14.241 14.231-14.241 37.291 0 51.529l274.851 274.859-274.849 274.851c-14.241 14.237-14.241 37.301 0 51.532 7.116 7.119 16.436 10.682 25.764 10.682 9.321 0 18.644-3.563 25.763-10.682l299.905-299.901c0.246-0.234 0.51-0.438 0.751-0.678 7.129-7.13 10.685-16.469 10.674-25.804 0.006-9.337-3.55-18.676-10.674-25.804z",fill:"#ffffff","p-id":"32303"},null,-1)])))],8,Jy)])])])):h.value.length===0&&!pe(n).loading.factorList?(G(),Q("div",e_,[kt(Jn,{type:"no-data",title:"暂无数据，快创建自己的工作流吧。"},{default:ur(()=>[S("div",{class:"content-btn active",onClick:A[1]||(A[1]=F=>s())},"创建工作流")]),_:1})])):(G(),Q("div",r_,[kt(Oe,{visible:pe(n).loading.factorList},null,8,["visible"])]))}},i_=Te(a_,[["__scopeId","data-v-19fd2e59"]]),n_={class:"factor-analysis"},o_={class:"metrics-bar"},s_={class:"metric"},l_={class:"metric"},c_={class:"metric"},u_={class:"metric"},d_={class:"value"},h_={__name:"earnings",props:{taskId:{type:String,required:!0}},setup(t){const r=sr(),e=t,a=R({annualized_ratio:0,maximum_drawdown:0,return_ratio:0,sharpe_ratio:0}),i=v=>{const h=parseFloat(v);return h>0?"#f92855":h<0?"#2dc08e":"#fff"},n=R(null);let o=null,s=null;const l=R({}),c=()=>{if(o=Ne(n.value),Object.keys(l.value).length===0){console.log("data为空");return}let v=JSON.parse(JSON.stringify(l.value));v.simple_return_chart.y[0].data=l.value.simple_return_chart.y[0].data.map(f=>Bs(f*100)),v.simple_return_chart.x[0].data=l.value.simple_return_chart.x[0].data.map(f=>Ns(new Date(f))),console.log("data:",v.simple_return_chart.y[0].data.length);const h={backgroundColor:"transparent",title:{},tooltip:{trigger:"axis",axisPointer:{type:"cross",label:{backgroundColor:"#1e1e1e"}},formatter:function(f){const p=f[0].axisValue,g=f[0].value;return`${p}<br/>${g}%`}},grid:{left:"3%",right:"2%",bottom:"5%",top:"10%",containLabel:!0},xAxis:[{type:"category",data:v.simple_return_chart.x[0].data,onZero:!0,show:!1,axisPointer:{type:"none"},lineStyle:{color:"#333",width:0}},{type:"category",data:v.simple_return_chart.x[0].data,boundaryGap:!1,axisTick:{alignWithLabel:!0,show:!0,length:5,lineStyle:{color:"#777"}},axisLabel:{color:"#777",fontSize:10},axisLine:{show:!0,lineStyle:{color:"#444",width:1}},position:"bottom"}],yAxis:{type:"value",min:Math.min(0,Math.min(...v.simple_return_chart.y[0].data)),max:Math.max(0,Math.max(...v.simple_return_chart.y[0].data)),splitNumber:6,axisLabel:{color:"#858585",formatter:"{value}%",fontSize:10},axisLine:{show:!1,lineStyle:{color:"#333"}},splitLine:{show:!0,lineStyle:{type:"dashed",width:1,color:"rgba(255, 255, 255, 0.05)"}},axisTick:{alignWithLabel:!0,show:!1,length:5,lineStyle:{color:"rgba(255, 255, 255, 0.05)"}}},dataZoom:[{type:"slider",xAxisIndex:[0,1],start:0,end:100,backgroundColor:"rgba(134, 27, 206, 0.3)",borderColor:"rgba(134, 27, 206, 0.3)",bottom:10,height:10},{type:"inside",xAxisIndex:[0,1],start:0,end:100}],series:[{type:"line",showSymbol:!1,data:v.simple_return_chart.y[0].data,lineStyle:{color:"rgba(134, 27, 206, 1)",width:1},areaStyle:{color:new Rs(0,0,0,1,[{offset:0,color:"rgba(134, 27, 206, 0.3)"},{offset:1,color:"rgba(31, 83, 182, 0.1)"}])}}]};o.setOption(h),window.addEventListener("resize",u)},u=()=>{o==null||o.resize()},d=async()=>{if(!e.taskId)return;const v=await r.getFactorEarnings(e.taskId);console.log("查看单个因子收益数据:",v),v.data&&(a.value=v.data.one_group_data);const h=await r.getFactorEarningsChart(e.taskId);console.log("查看单个因子收益数据图标:",h),h.data&&(l.value=h.data,c())};return Pe(()=>e.taskId,async()=>{await d()}),Ke(async()=>{await d(),s=new ResizeObserver(()=>{o==null||o.resize()}),n.value&&s.observe(n.value.parentElement)}),Ar(()=>{window.removeEventListener("resize",u),s&&(s.disconnect(),s=null),o==null||o.dispose(),o=null}),(v,h)=>(G(),Q("div",n_,[S("div",o_,[S("div",s_,[h[0]||(h[0]=S("span",{class:"label"},"分组盈亏",-1)),S("span",{class:"value",style:_e({color:i(a.value.return_ratio)})},it(a.value.return_ratio),5)]),S("div",l_,[h[1]||(h[1]=S("span",{class:"label"},"夏普比率",-1)),S("span",{class:"value",style:_e({color:i(a.value.sharpe_ratio)})},it(a.value.sharpe_ratio),5)]),S("div",c_,[h[2]||(h[2]=S("span",{class:"label"},"年化收益",-1)),S("span",{class:"value",style:_e({color:i(a.value.annualized_ratio)})},it(a.value.annualized_ratio),5)]),S("div",u_,[h[3]||(h[3]=S("span",{class:"label"},"最大回撤",-1)),S("span",d_,it(a.value.maximum_drawdown),1)])]),S("div",{ref_key:"echartsRef",ref:n,class:"chart-container"},null,512)]))}},v_=Te(h_,[["__scopeId","data-v-17daf2f9"]]),f_={class:"table-container"},p_={class:"table-wrapper"},g_={key:0,class:"fixed-column"},m_={class:"scroll-area"},y_={style:{display:"flex",position:"sticky",top:"0","z-index":"99",background:"#121212",width:"max-content"}},__=["title"],C_={__name:"TablesScroll",props:{tableData:{type:Array,required:!0},headers:{type:Array,required:!0},isFixed:{type:Boolean,default:!0},columnWidth:{type:[Number,String],default:0},customHeader:{type:Array,default:[]}},setup(t){qn(s=>({"7a2b27e2":`${t.columnWidth}px`}));const r=t,e=R(null),a=R(null),i=s=>!isNaN(s)&&Number.isFinite(Number(s))&&String(s).includes(".")?Number(s).toFixed(2):s,n=s=>s?s.replace(/(\d{4})(\d{2})(\d{2})/g,"$1-$2-$3"):"-",o=()=>{e.value&&a.value&&r.isFixed&&(console.log("-------------"),e.value.addEventListener("scroll",()=>{console.log(e.value.scrollTop),a.value.style.top=`-${e.value.scrollTop}px`}))};return Ke(()=>{o()}),(s,l)=>(G(),Q("div",f_,[S("div",p_,[r.isFixed?(G(),Q("div",g_,[S("div",{class:"fixed-header",style:_e({width:`${t.customHeader.length>0?t.customHeader[0].width:t.columnWidth}px`})},it(t.customHeader.length>0?t.customHeader[0].key:r.headers[0]),5),S("div",{class:"fixed-body",ref_key:"fixedBody",ref:a},[(G(!0),Q(Ce,null,De(r.tableData,(c,u)=>(G(),Q("div",{key:u,class:"fixed-cell",style:_e({width:`${t.customHeader.length>0?t.customHeader[0].width:t.columnWidth}px`})},it(n(c[r.headers[0]])),5))),128))],512)])):ue("",!0),S("div",m_,[S("div",{class:"scroll-body",ref_key:"scrollBody",ref:e},[S("div",y_,[(G(!0),Q(Ce,null,De([...r.headers].splice(r.isFixed?1:0),(c,u)=>(G(),Q("div",{key:u,class:"header-cell",style:_e({width:`${t.customHeader.length>0?t.customHeader[u+1].width:t.columnWidth}px`}),title:c},it(t.customHeader.length>0?t.customHeader[u+1].key:c),13,__))),128))]),(G(!0),Q(Ce,null,De(r.tableData,(c,u)=>(G(),Q("div",{key:u,class:"table-row"},[(G(!0),Q(Ce,null,De([...r.headers].splice(r.isFixed?1:0),(d,v)=>(G(),Q("div",{key:v,class:"table-cell",style:_e({width:`${t.customHeader.length>0?t.customHeader[v+1].width:t.columnWidth}px`})},it(i(c[d])),5))),128))]))),128))],512)])])]))}},jn=Te(C_,[["__scopeId","data-v-9a0230d5"]]),b_={class:"factor-deep"},x_={class:"factor-deep-header"},w_={class:"factor-item"},L_={class:"factor-item-title"},k_={class:"factor-item-content"},S_={style:{display:"flex","justify-content":"space-between"}},M_={class:"factor-deep-content",style:{"flex-shrink":"0","flex-grow":"1","flex-basis":"368px","margin-top":"20px"}},T_={class:"data-card",style:{height:"100%"}},E_={class:"data-item"},I_={class:"data-item"},A_={class:"data-item"},D_={class:"data-item"},P_={class:"value"},F_={key:0,class:"value"},R_={key:1,class:"label"},B_={class:"factor-item",style:{margin:"0","margin-top":"20px",width:"auto","flex-shrink":"0","flex-grow":"1","flex-basis":"460px","margin-left":"20px"}},N_={class:"factor-item-title"},O_={class:"factor-item-content"},V_={style:{width:"100%",height:"400%"}},z_={class:"factor-item",style:{"margin-left":"20px"}},$_={class:"factor-item-title"},W_={class:"factor-item-content"},Y_={style:{width:"100%",height:"400px"}},Z_={class:"factor-item-container"},H_={class:"factor-item"},j_={class:"factor-item-title"},X_={class:"factor-item-content"},K_={class:"factor-item"},G_={class:"factor-item-title"},U_={class:"factor-item-content"},Q_={class:"factor-item"},q_={class:"factor-item-title"},J_={class:"factor-item-content"},tC={class:"factor-item"},eC={class:"factor-item-title"},rC={class:"factor-item-content"},aC={class:"factor-item"},iC={class:"factor-item-title"},nC={class:"factor-item-content"},oC={class:"factor-item"},sC={class:"factor-item-title"},lC={class:"factor-item-content"},cC={class:"factor-item"},uC={class:"factor-item-title"},dC={class:"factor-item-content"},hC={class:"factor-item"},vC={class:"factor-item-title"},fC={class:"factor-item-content"},pC={class:"factor-item"},gC={class:"factor-item-title"},mC={class:"factor-item-content"},yC={class:"factor-item"},_C={class:"factor-item-title"},CC={class:"factor-item-content"},ce="#7d7d7d",bC=Re({__name:"deepAnalysis",props:{factorId:{type:String,required:!0},taskId:{type:String,requfactorIdired:!0},into:{type:Boolean,required:!0,default:!1}},setup(t){const r=D=>"",e=D=>D,a=D=>!isNaN(D)&&Number.isFinite(Number(D))&&String(D).includes(".")?Number(D).toFixed(2):D,i=t;Qn();const n=R(!1),o=D=>{const Qt=parseFloat(D);return Qt>0?"#ff4851":Qt<0?"#2fae34":"#333"},s=R({annualized_ratio:0,maximum_drawdown:0,return_ratio:0,sharpe_ratio:0}),l=async()=>{var bt,xt,It,Mt,Rt,Bt,Nt,Ot;const{json:D,httpController:Qt}=await Vs(i.taskId||r());s.value={annualized_ratio:e((xt=(bt=D==null?void 0:D.data)==null?void 0:bt.one_group_data)==null?void 0:xt.annualized_ratio),maximum_drawdown:e((Mt=(It=D==null?void 0:D.data)==null?void 0:It.one_group_data)==null?void 0:Mt.maximum_drawdown),return_ratio:e((Bt=(Rt=D==null?void 0:D.data)==null?void 0:Rt.one_group_data)==null?void 0:Bt.return_ratio),sharpe_ratio:e((Ot=(Nt=D==null?void 0:D.data)==null?void 0:Nt.one_group_data)==null?void 0:Ot.sharpe_ratio)}};let c=null;const u=R(null),d=R(""),v=R({}),h=async()=>{var xt,It,Mt,Rt,Bt,Nt,Ot,Zt,zt,At,ht,lt,at,H,Z,W;const{json:D,httpController:Qt}=await zs((i==null?void 0:i.taskId)||r());if(D.code==="200"&&(d.value=(It=(xt=D==null?void 0:D.data)==null?void 0:xt.return_chart)==null?void 0:It.title,v.value=(Mt=D==null?void 0:D.data)==null?void 0:Mt.return_chart,u.value)){if(c=Ne(u.value),Object.keys(v.value).length==0)return;var bt={code:"200",message:"查询成功",data:{task_id:"bbf3bbc030a94849bea4810058eec027",return_chart:{title:"python 5 groups return",x:[{name:"date",data:(Bt=(Rt=v.value)==null?void 0:Rt.x[0])==null?void 0:Bt.data}],y:(Nt=v.value)==null?void 0:Nt.y}}};const j=(At=(zt=(Zt=(Ot=bt==null?void 0:bt.data)==null?void 0:Ot.return_chart)==null?void 0:Zt.x[0])==null?void 0:zt.data)==null?void 0:At.map(Ct=>Ct.split(" ")[0]),gt=(at=(lt=(ht=bt==null?void 0:bt.data)==null?void 0:ht.return_chart)==null?void 0:lt.y)==null?void 0:at.map(Ct=>Ct.name),ot=["#FF6B6B","#4ECDC4","#45B7D1","#96CEB4","#FFCC5C","#FF6F69","#88D8B0","#6C88C4","#FFA07A","#98FB98","#87CEEB","#DDA0DD"].sort(()=>Math.random()-.5),Kt=(W=(Z=(H=bt==null?void 0:bt.data)==null?void 0:H.return_chart)==null?void 0:Z.y)==null?void 0:W.map((Ct,Wt)=>{var Ee;return(Ee=Ct==null?void 0:Ct.data)==null?void 0:Ee.map((pr,gr)=>({value:[gr,Wt,Number(pr).toFixed(2)],itemStyle:{color:ot[Wt%ot.length]}}))}).flat(),$t={title:{left:"center"},tooltip:{show:!1,axisPointer:{show:!1}},grid3D:{viewControl:{projection:"orthographic",autoRotate:!0,distance:200,beta:45,alpha:25},boxWidth:70,boxHeight:70,boxDepth:200,light:{main:{intensity:1.2}},top:"0%",bottom:"10%"},xAxis3D:{type:"category",data:j==null?void 0:j.map(Ct=>Ct.split(" ")[0]),name:"",axisLabel:{interval:Math.floor((j==null?void 0:j.length)/10),rotate:45,margin:20,textStyle:{fontSize:10,color:ce}},nameTextStyle:{fontSize:14,margin:30}},yAxis3D:{type:"category",data:gt,name:"",axisLabel:{color:ce}},zAxis3D:{type:"value",name:"",axisLabel:{color:ce}},dataZoom:[{type:"slider",xAxisIndex:0,start:0,end:100},{type:"inside",xAxisIndex:0,start:0,end:100}],series:[{type:"bar3D",data:Kt,shading:"lambert",label:{show:!1}}]};c.setOption($t)}},f=R("分组收益"),p=R([]),g=R({}),m=async()=>{var bt,xt;const{json:D,httpController:Qt}=await $s((i==null?void 0:i.taskId)||r());D.code==="200"&&(g.value=(bt=D==null?void 0:D.data)==null?void 0:bt.group_return_analysis,p.value=Object.keys((xt=D==null?void 0:D.data)==null?void 0:xt.group_return_analysis[0]))},b=R("最新数据"),x=R([]),L=R({}),_=R([{key:"时间",width:130},{key:"股票代码",width:100},{key:"名称",width:100},{key:"因子值",width:80}]),M=async()=>{var bt,xt;const{json:D,httpController:Qt}=await Ws((i==null?void 0:i.taskId)||r());D.code==="200"&&(L.value=(bt=D==null?void 0:D.data)==null?void 0:bt.last_date_top_factor,x.value=Object.keys((xt=D==null?void 0:D.data)==null?void 0:xt.last_date_top_factor[0]))};let w=null;const k=R(null),C=R(""),y=R({}),T=async()=>{var xt,It,Mt,Rt,Bt,Nt,Ot,Zt;const{json:D,httpController:Qt}=await Ys((i==null?void 0:i.taskId)||r());if(D.code==="200"&&(C.value=(It=(xt=D==null?void 0:D.data)==null?void 0:xt.ic_decay_chart)==null?void 0:It.title,y.value=(Mt=D==null?void 0:D.data)==null?void 0:Mt.ic_decay_chart,k.value)){if(w=Ne(k.value),Object.keys(y.value).length==0)return;const zt=(Bt=(Rt=y.value)==null?void 0:Rt.x[0])==null?void 0:Bt.data,At=(Zt=(Ot=(Nt=y.value)==null?void 0:Nt.y[0])==null?void 0:Ot.data)==null?void 0:Zt.map((ht,lt)=>({value:ht,itemStyle:{color:ht>0?"#ff0000":"#3498db"}}));var bt={title:{},tooltip:{trigger:"axis",formatter:function(ht){return ht.map(lt=>{let at=lt.value;const H=lt.color;if(Array.isArray(at)){const Z=at.map(W=>W==null||isNaN(W)?"--":Number(W).toFixed(4));return`<span style="color:${H}">${lt.seriesName}</span>: ${Z[0]}, ${Z[1]}`}return at=at==null||isNaN(at)?"--":Number(at).toFixed(4),`<span style="color:${H}">${lt.seriesName}</span>: ${at}`}).join("<br/>")}},grid:{left:"3%",right:"4%",bottom:"15%",top:"15%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:0,start:0,end:100}],xAxis:[{type:"category",data:zt,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(ht,lt){const at=zt.length,H=40,Z=w.getWidth(),W=Math.floor(Z/H),j=Math.floor((at-1)/(W-1));return(at-1-ht)%j===0}},show:!1},{type:"category",data:zt,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(ht,lt){const at=zt.length,H=40,Z=w.getWidth(),W=Math.floor(Z/H),j=Math.floor((at-1)/(W-1));return(at-1-ht)%j===0}},position:"bottom"}],yAxis:[{type:"value",splitLine:{show:!0,lineStyle:{type:"dashed",color:ce}}}],series:[{name:"IC值",type:"bar",data:At,label:{show:!1,position:"bottom",formatter:function(ht){return ht.value?ht.value.toFixed(2):""}}},{name:"IC趋势",type:"line",smooth:!1,showSymbol:!1,data:y.value.y[0].data,lineStyle:{color:"#ff0000",width:0}}]};w.setOption(bt)}};let I=null;const E=R(null),A=R(""),F=R({}),N=async()=>{var xt,It,Mt,Rt,Bt,Nt,Ot,Zt,zt;const{json:D,httpController:Qt}=await Zs((i==null?void 0:i.taskId)||r());if(D.code==="200"&&(console.log("json",D),A.value=(It=(xt=D==null?void 0:D.data)==null?void 0:xt.ic_den_chart)==null?void 0:It.title,F.value=(Mt=D==null?void 0:D.data)==null?void 0:Mt.ic_den_chart,E.value)){if(I=Ne(E.value),Object.keys(F.value).length==0)return;var bt={title:{},tooltip:{trigger:"axis",formatter:function(At){return At.map(ht=>{let lt=ht.value;const at=ht.color;if(Array.isArray(lt)){const H=lt.map(Z=>Z==null||isNaN(Z)?"--":Number(Z).toFixed(4));return`<span style="color:${at}">${ht.seriesName}</span>: ${H[0]}, ${H[1]}`}return lt=lt==null||isNaN(lt)?"--":Number(lt).toFixed(4),`<span style="color:${at}">${ht.seriesName}</span>: ${lt}`}).join("<br/>")}},legend:{data:["Histogram","Density Curve"],top:"0px",textStyle:{color:ce}},grid:{left:"3%",right:"4%",bottom:"15%",top:"15%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:0,start:0,end:100},{type:"inside",xAxisIndex:0,start:0}],xAxis:{type:"value",boundaryGap:!1,axisLine:{onZero:!1},splitLine:{show:!0,lineStyle:{type:"dashed",color:ce}}},yAxis:{type:"value",axisLine:{onZero:!1,show:!1},axisTick:{show:!1},position:"left",splitLine:{show:!0,lineStyle:{type:"dashed",color:ce}}},series:[{name:"Histogram",type:"bar",data:(Nt=(Bt=(Rt=F.value)==null?void 0:Rt.y[0])==null?void 0:Bt.data)==null?void 0:Nt.map((At,ht)=>{var lt,at;return[(at=(lt=F.value)==null?void 0:lt.x[0])==null?void 0:at.data[ht],At]}),itemStyle:{color:"rgba(135, 206, 235, 0.6)"}},{name:"Density Curve",type:"line",smooth:!0,data:(zt=(Zt=(Ot=F.value)==null?void 0:Ot.y[1])==null?void 0:Zt.data)==null?void 0:zt.map((At,ht)=>{var lt,at;return[(at=(lt=F.value)==null?void 0:lt.x[0])==null?void 0:at.data[ht],At]}),itemStyle:{color:"#1E90FF"},lineStyle:{width:2}}]};I.setOption(bt)}};let B=null;const Y=R(null),K=R(""),$=R({}),ut=async()=>{var xt,It,Mt,Rt,Bt,Nt,Ot,Zt,zt,At,ht,lt,at;const{json:D,httpController:Qt}=await Hs((i==null?void 0:i.taskId)||r());if(D.code==="200"&&(K.value=(It=(xt=D==null?void 0:D.data)==null?void 0:xt.ic_seq_chart)==null?void 0:It.title,$.value=(Mt=D==null?void 0:D.data)==null?void 0:Mt.ic_seq_chart,console.log("echartsRefICSequenceData",$.value),Y.value)){if(B=Ne(Y.value),Object.keys($.value).length==0)return;var bt={title:{left:"center"},tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter:function(H){return H.map(Z=>{let W=Z.value;const j=Z.color;if(Array.isArray(W)){const gt=W.map(ot=>ot==null||isNaN(ot)?"--":Number(ot).toFixed(4));return`<span style="color:${j}">${Z.seriesName}</span>: ${gt[0]}, ${gt[1]}`}return W=W==null||isNaN(W)?"--":Number(W).toFixed(4),`<span style="color:${j}">${Z.seriesName}</span>: ${W}`}).join("<br/>")}},legend:{data:(Bt=(Rt=$.value)==null?void 0:Rt.y)==null?void 0:Bt.map(H=>H.name),top:"0px",selected:{IC:!0,Cum_IC:!1},textStyle:{color:ce}},grid:{left:"3%",right:"4%",bottom:"2%",top:"15%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:[0,1],start:0,end:100}],xAxis:[{type:"category",data:(Zt=(Ot=(Nt=$.value)==null?void 0:Nt.x[0])==null?void 0:Ot.data)==null?void 0:Zt.map(H=>H.split(" ")[0]),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(H,Z){var $t,Ct,Wt;const W=(Wt=(Ct=($t=$.value)==null?void 0:$t.x[0])==null?void 0:Ct.data)==null?void 0:Wt.length,j=40,gt=B.getWidth(),ot=Math.floor(gt/j),Kt=Math.floor((W-1)/(ot-1));return(W-1-H)%Kt===0}},show:!1},{type:"category",data:(ht=(At=(zt=$.value)==null?void 0:zt.x[0])==null?void 0:At.data)==null?void 0:ht.map(H=>H.split(" ")[0]),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(H,Z){var $t,Ct,Wt;const W=(Wt=(Ct=($t=$.value)==null?void 0:$t.x[0])==null?void 0:Ct.data)==null?void 0:Wt.length,j=40,gt=B.getWidth(),ot=Math.floor(gt/j),Kt=Math.floor((W-1)/(ot-1));return(W-1-H)%Kt===0}},position:"bottom"}],yAxis:{type:"value",axisLabel:{interval:1e3},splitLine:{show:!0,lineStyle:{type:"dashed",color:ce}}},series:(at=(lt=$.value)==null?void 0:lt.y)==null?void 0:at.map(H=>({name:H.name,data:H.data,type:H.name==="IC"?"bar":"line",itemStyle:{color:H.name==="IC"?"#3498db":"#e74c3c"},label:{show:!1,position:"top",formatter:function(Z){return Z.data.toFixed(3)}}}))};B.setOption(bt)}};let rt=null;const z=R(null),tt=R(""),mt=R({}),ft=async()=>{var xt,It,Mt,Rt,Bt,Nt,Ot,Zt,zt,At,ht,lt,at;const{json:D,httpController:Qt}=await js((i==null?void 0:i.taskId)||r());if(D.code==="200"&&(tt.value=(It=(xt=D==null?void 0:D.data)==null?void 0:xt.ic_self_correlation_chart)==null?void 0:It.title,mt.value=(Mt=D==null?void 0:D.data)==null?void 0:Mt.ic_self_correlation_chart,z.value)){if(rt=Ne(z.value),Object.keys(mt.value).length==0)return;var bt={title:{},tooltip:{trigger:"axis",formatter:function(H){return H.map(Z=>{let W=Z.value;const j=Z.color;if(Array.isArray(W)){const gt=W.map(ot=>ot==null||isNaN(ot)?"--":Number(ot).toFixed(4));return`<span style="color:${j}">${Z.seriesName}</span>: ${gt[0]}, ${gt[1]}`}return W=W==null||isNaN(W)?"--":Number(W).toFixed(4),`<span style="color:${j}">${Z.seriesName}</span>: ${W}`}).join("<br/>")}},legend:{data:["自相关系数","下限(95%置信区间)","上限(95%置信区间)"],top:"0px",textStyle:{color:ce}},grid:{left:"3%",right:"4%",bottom:"15%",top:"15%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:[0,1],start:0,end:100}],xAxis:[{type:"category",data:(Bt=(Rt=mt.value)==null?void 0:Rt.x[0])==null?void 0:Bt.data,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(H,Z){var $t,Ct,Wt;const W=(Wt=(Ct=($t=mt.value)==null?void 0:$t.x[0])==null?void 0:Ct.data)==null?void 0:Wt.length,j=40,gt=rt.getWidth(),ot=Math.floor(gt/j),Kt=Math.floor((W-1)/(ot-1));return(W-1-H)%Kt===0}},show:!1},{type:"category",data:(Ot=(Nt=mt.value)==null?void 0:Nt.x[0])==null?void 0:Ot.data,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(H,Z){var $t,Ct,Wt;const W=(Wt=(Ct=($t=mt.value)==null?void 0:$t.x[0])==null?void 0:Ct.data)==null?void 0:Wt.length,j=40,gt=rt.getWidth(),ot=Math.floor(gt/j),Kt=Math.floor((W-1)/(ot-1));return(W-1-H)%Kt===0}},position:"bottom"}],yAxis:{type:"value",min:-1,max:1,interval:.2,splitLine:{show:!0,lineStyle:{type:"dashed",color:ce}}},series:[{name:"自相关系数",type:"line",data:(zt=(Zt=mt.value)==null?void 0:Zt.y[0])==null?void 0:zt.data,symbol:"circle",symbolSize:8,lineStyle:{width:2},itemStyle:{color:"#3498db"},label:{show:!1,position:"top",formatter:function(H){return H.value.toFixed(3)}}},{name:"下限(95%置信区间)",type:"line",data:(ht=(At=mt.value)==null?void 0:At.y[1])==null?void 0:ht.data,lineStyle:{type:"dashed",width:1},itemStyle:{color:"#95a5a6"},symbol:"none"},{name:"上限(95%置信区间)",type:"line",data:(at=(lt=mt.value)==null?void 0:lt.y[2])==null?void 0:at.data,lineStyle:{type:"dashed",width:1},itemStyle:{color:"#95a5a6"},symbol:"none"}]};rt.setOption(bt)}};let yt=null;const V=R(null),q=R(""),dt=R({}),J=async()=>{var xt,It,Mt,Rt,Bt,Nt,Ot,Zt,zt,At;const{json:D,httpController:Qt}=await Xs((i==null?void 0:i.taskId)||r());if(D.code==="200"&&(q.value=(It=(xt=D==null?void 0:D.data)==null?void 0:xt.rank_ic_decay_chart)==null?void 0:It.title,dt.value=(Mt=D==null?void 0:D.data)==null?void 0:Mt.rank_ic_decay_chart,V.value)){if(yt=Ne(V.value),Object.keys(dt.value).length==0)return;const ht=(Bt=(Rt=dt.value)==null?void 0:Rt.x[0])==null?void 0:Bt.data,lt=(Zt=(Ot=(Nt=dt.value)==null?void 0:Nt.y[0])==null?void 0:Ot.data)==null?void 0:Zt.map((at,H)=>({value:at,itemStyle:{color:at>0?"#ff0000":"#3498db"}}));var bt={title:{},tooltip:{trigger:"axis",formatter:function(at){return at.map(H=>{let Z=H.value;const W=H.color;if(Array.isArray(Z)){const j=Z.map(gt=>gt==null||isNaN(gt)?"--":Number(gt).toFixed(4));return`<span style="color:${W}">${H.seriesName}</span>: ${j[0]}, ${j[1]}`}return Z=Z==null||isNaN(Z)?"--":Number(Z).toFixed(4),`<span style="color:${W}">${H.seriesName}</span>: ${Z}`}).join("<br/>")}},grid:{left:"3%",right:"4%",bottom:"15%",top:"15%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:0,start:0,end:100}],xAxis:[{type:"category",data:ht,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(at,H){const Z=ht.length,W=40,j=yt.getWidth(),gt=Math.floor(j/W),ot=Math.floor((Z-1)/(gt-1));return(Z-1-at)%ot===0}},show:!1},{type:"category",data:ht,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(at,H){const Z=ht.length,W=40,j=yt.getWidth(),gt=Math.floor(j/W),ot=Math.floor((Z-1)/(gt-1));return(Z-1-at)%ot===0}},position:"bottom"}],yAxis:[{type:"value",splitLine:{show:!0,lineStyle:{type:"dashed",color:ce}}}],series:[{name:"IC值",type:"bar",data:lt,label:{show:!1,position:"bottom",formatter:function(at){return at.value?at.value.toFixed(3):""}}},{name:"IC趋势",type:"line",smooth:!1,showSymbol:!1,data:(At=(zt=dt.value)==null?void 0:zt.y[0])==null?void 0:At.data,lineStyle:{color:"#000000",width:0}}]};yt.setOption(bt)}};let _t=null;const Lt=R(null),Ht=R(""),jt=R({}),Dt=async()=>{var xt,It,Mt,Rt,Bt,Nt,Ot,Zt,zt;const{json:D,httpController:Qt}=await Ks((i==null?void 0:i.taskId)||r());if(D.code==="200"&&(Ht.value=(It=(xt=D==null?void 0:D.data)==null?void 0:xt.rank_ic_den_chart)==null?void 0:It.title,jt.value=(Mt=D==null?void 0:D.data)==null?void 0:Mt.rank_ic_den_chart,Lt.value)){if(_t=Ne(Lt.value),Object.keys(jt.value).length==0)return;var bt={title:{},tooltip:{trigger:"axis",formatter:function(At){return At.map(ht=>{let lt=ht.value;const at=ht.color;if(Array.isArray(lt)){const H=lt.map(Z=>Z==null||isNaN(Z)?"--":Number(Z).toFixed(4));return`<span style="color:${at}">${ht.seriesName}</span>: ${H[0]}, ${H[1]}`}return lt=lt==null||isNaN(lt)?"--":Number(lt).toFixed(4),`<span style="color:${at}">${ht.seriesName}</span>: ${lt}`}).join("<br/>")}},legend:{data:["Histogram","Density Curve"],top:"0px",textStyle:{color:ce}},grid:{left:"3%",right:"4%",bottom:"15%",top:"15%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:0,start:0,end:100},{type:"inside",xAxisIndex:0,zoomOnMouseWheel:!1,start:0}],xAxis:{type:"value",boundaryGap:!1,axisLine:{onZero:!1},axisLabel:{formatter:"{value}"},splitLine:{show:!0,lineStyle:{type:"dashed",color:ce}}},yAxis:{type:"value",axisLine:{onZero:!1,show:!1},axisTick:{show:!1},position:"left",splitLine:{show:!0,lineStyle:{type:"dashed",color:ce}}},series:[{name:"Histogram",type:"bar",data:(Nt=(Bt=(Rt=jt.value)==null?void 0:Rt.y[0])==null?void 0:Bt.data)==null?void 0:Nt.map((At,ht)=>{var lt,at;return[(at=(lt=jt.value)==null?void 0:lt.x[0])==null?void 0:at.data[ht],At]}),itemStyle:{color:"rgba(135, 206, 235, 0.6)"}},{name:"Density Curve",type:"line",smooth:!0,data:(zt=(Zt=(Ot=jt.value)==null?void 0:Ot.y[1])==null?void 0:Zt.data)==null?void 0:zt.map((At,ht)=>{var lt,at;return[(at=(lt=jt.value)==null?void 0:lt.x[0])==null?void 0:at.data[ht],At]}),itemStyle:{color:"#1E90FF"},lineStyle:{width:2}}]};_t.setOption(bt)}};let Xt=null;const qt=R(null),et=R(""),nt=R({}),Pt=async()=>{var xt,It,Mt,Rt,Bt,Nt,Ot,Zt,zt,At,ht,lt,at;const{json:D,httpController:Qt}=await Gs(i.taskId||r());if(D.code==="200"&&(et.value=(It=(xt=D==null?void 0:D.data)==null?void 0:xt.rank_ic_seq_chart)==null?void 0:It.title,nt.value=(Mt=D==null?void 0:D.data)==null?void 0:Mt.rank_ic_seq_chart,qt.value)){if(Xt=Ne(qt.value),Object.keys(nt.value).length==0)return;var bt={title:{left:"center"},tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter:function(H){return H.map(Z=>{let W=Z.value;const j=Z.color;if(Array.isArray(W)){const gt=W.map(ot=>ot==null||isNaN(ot)?"--":Number(ot).toFixed(4));return`<span style="color:${j}">${Z.seriesName}</span>: ${gt[0]}, ${gt[1]}`}return W=W==null||isNaN(W)?"--":Number(W).toFixed(4),`<span style="color:${j}">${Z.seriesName}</span>: ${W}`}).join("<br/>")}},legend:{data:(Bt=(Rt=nt.value)==null?void 0:Rt.y)==null?void 0:Bt.map(H=>H.name),top:"0px",selected:{IC:!0,Cum_IC:!1},textStyle:{color:ce}},grid:{left:"3%",right:"4%",bottom:"1%",top:"15%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:[0,1],start:0,end:100}],xAxis:[{type:"category",data:(Zt=(Ot=(Nt=nt.value)==null?void 0:Nt.x[0])==null?void 0:Ot.data)==null?void 0:Zt.map(H=>H.split(" ")[0]),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(H,Z){var $t,Ct,Wt;const W=(Wt=(Ct=($t=nt.value)==null?void 0:$t.x[0])==null?void 0:Ct.data)==null?void 0:Wt.length,j=40,gt=Xt.getWidth(),ot=Math.floor(gt/j),Kt=Math.floor((W-1)/(ot-1));return(W-1-H)%Kt===0}},show:!1},{type:"category",data:(ht=(At=(zt=nt.value)==null?void 0:zt.x[0])==null?void 0:At.data)==null?void 0:ht.map(H=>H.split(" ")[0]),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(H,Z){var $t,Ct,Wt;const W=(Wt=(Ct=($t=nt.value)==null?void 0:$t.x[0])==null?void 0:Ct.data)==null?void 0:Wt.length,j=40,gt=Xt.getWidth(),ot=Math.floor(gt/j),Kt=Math.floor((W-1)/(ot-1));return(W-1-H)%Kt===0}},position:"bottom"}],yAxis:{type:"value",splitLine:{show:!0,lineStyle:{type:"dashed",color:ce}}},series:(at=(lt=nt.value)==null?void 0:lt.y)==null?void 0:at.map(H=>({name:H.name,data:H.data,type:H.name==="Rank_IC"?"bar":"line",itemStyle:{color:H.name==="IC"?"#3498db":"#e74c3c"},showSymbol:!1,label:{show:!1,position:"top",formatter:function(Z){return Z.data.toFixed(3)}}}))};Xt.setOption(bt)}};let Et=null;const Yt=R(null),ie=R(""),ne=R({}),ee=async()=>{var xt,It,Mt,Rt,Bt,Nt,Ot,Zt,zt,At,ht,lt,at;const{json:D,httpController:Qt}=await Us((i==null?void 0:i.taskId)||r());if(D.code==="200"&&(ie.value=(It=(xt=D==null?void 0:D.data)==null?void 0:xt.rank_ic_self_correlation_chart)==null?void 0:It.title,ne.value=(Mt=D==null?void 0:D.data)==null?void 0:Mt.rank_ic_self_correlation_chart,Yt.value)){if(Et=Ne(Yt.value),Object.keys(ne.value).length==0)return;var bt={title:{},tooltip:{trigger:"axis",formatter:function(H){return H.map(Z=>{let W=Z.value;const j=Z.color;if(Array.isArray(W)){const gt=W.map(ot=>ot==null||isNaN(ot)?"--":Number(ot).toFixed(4));return`<span style="color:${j}">${Z.seriesName}</span>: ${gt[0]}, ${gt[1]}`}return W=W==null||isNaN(W)?"--":Number(W).toFixed(4),`<span style="color:${j}">${Z.seriesName}</span>: ${W}`}).join("<br/>")}},legend:{data:["自相关系数","下限(95%置信区间)","上限(95%置信区间)"],top:"0px",textStyle:{color:ce}},grid:{left:"3%",right:"4%",bottom:"10%",top:"10%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:0,start:0,end:100}],xAxis:[{type:"category",data:(Bt=(Rt=ne.value)==null?void 0:Rt.x[0])==null?void 0:Bt.data,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(H,Z){var $t,Ct,Wt;const W=(Wt=(Ct=($t=ne.value)==null?void 0:$t.x[0])==null?void 0:Ct.data)==null?void 0:Wt.length,j=40,gt=Et.getWidth(),ot=Math.floor(gt/j),Kt=Math.floor((W-1)/(ot-1));return(W-1-H)%Kt===0}},show:!1},{type:"category",data:(Ot=(Nt=ne.value)==null?void 0:Nt.x[0])==null?void 0:Ot.data,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(H,Z){var $t,Ct,Wt;const W=(Wt=(Ct=($t=ne.value)==null?void 0:$t.x[0])==null?void 0:Ct.data)==null?void 0:Wt.length,j=40,gt=Et.getWidth(),ot=Math.floor(gt/j),Kt=Math.floor((W-1)/(ot-1));return(W-1-H)%Kt===0}},position:"bottom"}],yAxis:{type:"value",min:-1,max:1,interval:.2,splitLine:{show:!0,lineStyle:{type:"dashed",color:ce}}},series:[{name:"自相关系数",type:"line",data:(zt=(Zt=ne.value)==null?void 0:Zt.y[0])==null?void 0:zt.data,symbol:"circle",symbolSize:8,lineStyle:{width:2},itemStyle:{color:"#3498db"},label:{show:!1,position:"top",formatter:function(H){return H.value.toFixed(3)}}},{name:"下限(95%置信区间)",type:"line",data:(ht=(At=ne.value)==null?void 0:At.y[1])==null?void 0:ht.data,lineStyle:{type:"dashed",width:1},itemStyle:{color:"#95a5a6"},symbol:"none"},{name:"上限(95%置信区间)",type:"line",data:(at=(lt=ne.value)==null?void 0:lt.y[2])==null?void 0:at.data,lineStyle:{type:"dashed",width:1},itemStyle:{color:"#95a5a6"},symbol:"none"}]};Et.setOption(bt)}};let Ft=null;const oe=R(null),le=R(""),Ut=R({}),Qe=async()=>{var bt,xt,It,Mt,Rt,Bt,Nt,Ot,Zt,zt,At,ht,lt,at,H,Z;const{json:D,httpController:Qt}=await Qs((i==null?void 0:i.taskId)||r());if(D.code==="200"&&((It=(xt=(bt=D==null?void 0:D.data)==null?void 0:bt.return_chart)==null?void 0:xt.y)==null||It.forEach(W=>{W.data=W.data.map(j=>a(j*100))}),le.value=(Rt=(Mt=D==null?void 0:D.data)==null?void 0:Mt.return_chart)==null?void 0:Rt.title,Ut.value=(Bt=D==null?void 0:D.data)==null?void 0:Bt.return_chart,oe.value)){if(Ft=Ne(oe.value),Object.keys(Ut.value).length==0)return;const W={title:{},tooltip:{trigger:"axis",formatter:function(j){return j.map(gt=>{let ot=gt.value;const Kt=gt.color;if(Array.isArray(ot)){const $t=ot.map(Ct=>Ct==null||isNaN(Ct)?"--":Number(Ct).toFixed(4));return`<span style="color:${Kt}">${gt.seriesName}</span>: ${$t[0]}, ${$t[1]}`}return ot=ot==null||isNaN(ot)?"--":Number(ot).toFixed(4),`<span style="color:${Kt}">${gt.seriesName}</span>: ${ot}%`}).join("<br/>")}},legend:{data:(Ot=(Nt=Ut.value)==null?void 0:Nt.y)==null?void 0:Ot.map(j=>j.name),textStyle:{color:ce}},xAxis:[{type:"category",data:(At=(zt=(Zt=Ut.value)==null?void 0:Zt.x[0])==null?void 0:zt.data)==null?void 0:At.map(j=>Oi(j)),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(j,gt){var Ee,pr,gr;const ot=(gr=(pr=(Ee=Ut.value)==null?void 0:Ee.x[0])==null?void 0:pr.data)==null?void 0:gr.length,Kt=60,$t=Ft.getWidth(),Ct=Math.floor($t/Kt),Wt=Math.floor((ot-1)/(Ct-1));return(ot-1-j)%Wt===0}},show:!1},{type:"category",data:(at=(lt=(ht=Ut.value)==null?void 0:ht.x[0])==null?void 0:lt.data)==null?void 0:at.map(j=>Oi(j)),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(j,gt){var Ee,pr,gr;const ot=(gr=(pr=(Ee=Ut.value)==null?void 0:Ee.x[0])==null?void 0:pr.data)==null?void 0:gr.length,Kt=60,$t=Ft.getWidth(),Ct=Math.floor($t/Kt),Wt=Math.floor((ot-1)/(Ct-1));return(ot-1-j)%Wt===0}},position:"bottom"}],yAxis:{type:"value",axisLabel:{formatter:"{value}%"},splitLine:{show:!0,lineStyle:{type:"dashed",color:ce}}},grid:{left:"3%",right:"4%",bottom:"0%",top:"10%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:[0,1],start:0,end:100}],series:(Z=(H=Ut.value)==null?void 0:H.y)==null?void 0:Z.map(j=>({name:j.name,type:"line",data:j.data,showSymbol:!1}))};Ft.setOption(W)}};let we=null;const fe=R(null),xr=R(""),me=R({}),Le=async()=>{var xt,It,Mt,Rt,Bt,Nt,Ot,Zt,zt,At,ht,lt,at;const{json:D,httpController:Qt}=await qs((i==null?void 0:i.taskId)||r());if(D.code==="200"&&(xr.value=(It=(xt=D==null?void 0:D.data)==null?void 0:xt.excess_chart)==null?void 0:It.title,me.value=(Mt=D==null?void 0:D.data)==null?void 0:Mt.excess_chart,fe.value)){if(we=Ne(fe.value),Object.keys(me.value).length==0)return;const H=["#1f77b4","#ff7f0e","#2ca02c","#d62728","#9467bd","#0099cc","#ff00ff"];var bt={title:{},tooltip:{trigger:"axis",formatter:function(Z){return Z.map(W=>{let j=W.value;const gt=W.color;if(Array.isArray(j)){const ot=j.map(Kt=>Kt==null||isNaN(Kt)?"--":Number(Kt).toFixed(4));return`<span style="color:${gt}">${W.seriesName}</span>: ${ot[0]}, ${ot[1]}`}return j=j==null||isNaN(j)?"--":Number(j).toFixed(4),`<span style="color:${gt}">${W.seriesName}</span>: ${j}`}).join("<br/>")}},legend:{data:(Bt=(Rt=me.value)==null?void 0:Rt.y)==null?void 0:Bt.map(Z=>Z.name),top:"0px",textStyle:{color:ce,fontSize:10}},grid:{left:"3%",right:"4%",bottom:"0%",top:"10%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:[0,1],start:0,end:100}],xAxis:[{type:"category",data:(Zt=(Ot=(Nt=me.value)==null?void 0:Nt.x[0])==null?void 0:Ot.data)==null?void 0:Zt.map(Z=>Z.split(" ")[0]),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(Z,W){var Ct,Wt,Ee;const j=(Ee=(Wt=(Ct=me.value)==null?void 0:Ct.x[0])==null?void 0:Wt.data)==null?void 0:Ee.length,gt=40,ot=we.getWidth(),Kt=Math.floor(ot/gt),$t=Math.floor((j-1)/(Kt-1));return(j-1-Z)%$t===0}},show:!1},{type:"category",data:(ht=(At=(zt=me.value)==null?void 0:zt.x[0])==null?void 0:At.data)==null?void 0:ht.map(Z=>Z.split(" ")[0]),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(Z,W){var Ct,Wt,Ee;const j=(Ee=(Wt=(Ct=me.value)==null?void 0:Ct.x[0])==null?void 0:Wt.data)==null?void 0:Ee.length,gt=40,ot=we.getWidth(),Kt=Math.floor(ot/gt),$t=Math.floor((j-1)/(Kt-1));return(j-1-Z)%$t===0}},position:"bottom"}],yAxis:{type:"value",axisLabel:{formatter:Z=>(Z*100).toFixed(1)+"%"}},series:(at=(lt=me.value)==null?void 0:lt.y)==null?void 0:at.map((Z,W)=>({name:Z.name,type:"line",data:Z.data,symbol:"circle",symbolSize:6,showSymbol:!1,lineStyle:{width:(Z.name.includes("多空组合"),1)},itemStyle:{color:H[W]}}))};we.setOption(bt)}};Pe(()=>i.factorId,async()=>{i.into&&(console.log("加载mc3"),await wr(),await l(),await m(),await M(),await h(),await T(),await N(),await ut(),await ft(),await J(),await Dt(),await Pt(),await ee(),await Qe(),await Le(),await Je(),de())}),Pe(()=>i.taskId,async()=>{i.into&&(console.log("加载mc2"),await Je(),de(),await wr(),await l(),await Je(),await m(),await M(),await h(),await T(),await N(),await ut(),await ft(),await J(),await Dt(),await Pt(),await ee(),await Qe(),await Le(),await Je(),setTimeout(()=>{de()},1200))}),Pe(()=>i.into,async()=>{i.into&&(console.log("加载mc1"),await wr(),await l(),await m(),await M(),await h(),await T(),await N(),await ut(),await ft(),await J(),await Dt(),await Pt(),await ee(),await Qe(),await Le(),await Je(),de())}),Pe(()=>i.into,async()=>{await Je(),de()});const de=()=>{c==null||c.resize(),w==null||w.resize(),I==null||I.resize(),B==null||B.resize(),rt==null||rt.resize(),yt==null||yt.resize(),_t==null||_t.resize(),Xt==null||Xt.resize(),Et==null||Et.resize(),Ft==null||Ft.resize(),we==null||we.resize()};let ke=null;Ke(async()=>{var D,Qt,bt,xt,It,Mt,Rt,Bt,Nt,Ot,Zt;try{i.into&&(await wr(),await l(),await m(),await M(),await h(),await T(),await N(),await ut(),await ft(),await J(),await Dt(),await Pt(),await ee(),await Qe(),await Le(),Ni.value=!1,await Je(),ke=new ResizeObserver(()=>{de()}),[(D=u.value)==null?void 0:D.parentElement,(Qt=k.value)==null?void 0:Qt.parentElement,(bt=E.value)==null?void 0:bt.parentElement,(xt=Y.value)==null?void 0:xt.parentElement,(It=z.value)==null?void 0:It.parentElement,(Mt=V.value)==null?void 0:Mt.parentElement,(Rt=Lt.value)==null?void 0:Rt.parentElement,(Bt=qt.value)==null?void 0:Bt.parentElement,(Nt=Yt.value)==null?void 0:Nt.parentElement,(Ot=oe.value)==null?void 0:Ot.parentElement,(Zt=fe.value)==null?void 0:Zt.parentElement].forEach(At=>{At&&ke.observe(At)}),de())}catch{n.value,Ni.value=!0}}),Ar(()=>{ke&&(ke.disconnect(),ke=null),ze()});const ze=()=>{c==null||c.dispose(),w==null||w.dispose(),I==null||I.dispose(),B==null||B.dispose(),rt==null||rt.dispose(),yt==null||yt.dispose(),_t==null||_t.dispose(),Xt==null||Xt.dispose(),Et==null||Et.dispose(),Ft==null||Ft.dispose(),we==null||we.dispose()},fr=R([]),wr=async()=>{var bt;const{json:D,httpController:Qt}=await Os((i==null?void 0:i.taskId)||r());fr.value=(bt=D==null?void 0:D.data)==null?void 0:bt.factor_data_analysis},Ni=R(!0),Oi=D=>D&&D.split(" ")[0];return(D,Qt)=>(G(),Q("div",b_,[S("div",x_,[S("div",w_,[S("div",L_,it(d.value),1),S("div",k_,[S("div",{ref_key:"echartsRefEarningsBigChart",ref:oe,style:{width:"100%",height:"400px"}},[kt(Oe,{visible:!0})],512)])]),S("div",S_,[S("div",M_,[S("div",T_,[S("div",E_,[S("div",{class:"value",style:_e({color:o(s.value.return_ratio)})},it(s.value.return_ratio),5),Qt[0]||(Qt[0]=S("div",{class:"label"},"因子收益",-1))]),S("div",I_,[S("div",{class:"value",style:_e({color:o(s.value.sharpe_ratio)})},it(s.value.sharpe_ratio),5),Qt[1]||(Qt[1]=S("div",{class:"label"},"夏普比率",-1))]),S("div",A_,[S("div",{class:"value",style:_e({color:o(s.value.annualized_ratio)})},it(s.value.annualized_ratio),5),Qt[2]||(Qt[2]=S("div",{class:"label"},"年化收益",-1))]),S("div",D_,[S("div",P_,it(s.value.maximum_drawdown),1),Qt[3]||(Qt[3]=S("div",{class:"label"},"最大回撤",-1))]),(G(!0),Q(Ce,null,De(fr.value,(bt,xt)=>(G(),Q("div",{key:xt,class:"data-item"},[(G(!0),Q(Ce,null,De(Object.values(bt),(It,Mt)=>(G(),Q(Ce,{key:Mt},[Mt==1?(G(),Q("div",F_,it(It),1)):ue("",!0),Mt==0?(G(),Q("div",R_,it(It),1)):ue("",!0)],64))),128))]))),128))])]),S("div",B_,[S("div",N_,it(b.value),1),S("div",O_,[S("div",V_,[kt(jn,{tableData:L.value,headers:x.value,isFixed:!0,"custom-header":_.value},null,8,["tableData","headers","custom-header"])])])]),S("div",z_,[S("div",$_,it(f.value),1),S("div",W_,[S("div",Y_,[kt(jn,{tableData:g.value,headers:p.value,isFixed:!0,"column-width":110},null,8,["tableData","headers"])])])])]),S("div",Z_,[S("div",H_,[S("div",j_,it(C.value),1),S("div",X_,[S("div",{ref_key:"echartsRefSimDietrich",ref:k,style:{width:"100%",height:"400px"}},[kt(Oe,{visible:!0})],512)])]),S("div",K_,[S("div",G_,it(A.value),1),S("div",U_,[S("div",{ref_key:"echartsRefICDistribution",ref:E,style:{width:"100%",height:"400px"}},[kt(Oe,{visible:!0})],512)])]),S("div",Q_,[S("div",q_,it(K.value),1),S("div",J_,[S("div",{ref_key:"echartsRefICSequence",ref:Y,style:{width:"100%",height:"400px"}},[kt(Oe,{visible:!0})],512)])]),S("div",tC,[S("div",eC,it(tt.value),1),S("div",rC,[S("div",{ref_key:"echartsRefICCcorrelation",ref:z,style:{width:"100%",height:"400px"}},[kt(Oe,{visible:!0})],512)])]),S("div",aC,[S("div",iC,it(q.value),1),S("div",nC,[S("div",{ref_key:"echartsRefRankICDecay",ref:V,style:{width:"100%",height:"400px"}},[kt(Oe,{visible:!0})],512)])]),S("div",oC,[S("div",sC,it(Ht.value),1),S("div",lC,[S("div",{ref_key:"echartsRefRankICDistribution",ref:Lt,style:{width:"100%",height:"400px"}},[kt(Oe,{visible:!0})],512)])]),S("div",cC,[S("div",uC,it(et.value),1),S("div",dC,[S("div",{ref_key:"echartsRefRankICSequence",ref:qt,style:{width:"100%",height:"400px"}},[kt(Oe,{visible:!0})],512)])]),S("div",hC,[S("div",vC,it(ie.value),1),S("div",fC,[S("div",{ref_key:"echartsRefRankICCcorrelation",ref:Yt,style:{width:"100%",height:"400px"}},[kt(Oe,{visible:!0})],512)])]),S("div",pC,[S("div",gC,it(le.value),1),S("div",mC,[S("div",{ref_key:"echartsRefCurrentDeepAnalysis",ref:u,style:{width:"100%",height:"400px"}},[kt(Oe,{visible:!0})],512)])]),S("div",yC,[S("div",_C,it(xr.value),1),S("div",CC,[S("div",{ref_key:"echartsRefExcessReturnBigChart",ref:fe,style:{width:"100%",height:"400px"}},[kt(Oe,{visible:!0})],512)])])])])]))}}),xC=Te(bC,[["__scopeId","data-v-617db5de"]]),wC={class:"strategy-container"},LC={key:1,class:"strategy-container-content"},kC={class:"strategy-header"},SC={class:"strategy-content"},MC={class:"strategy-content"},TC={key:0,class:"tab-content"},EC={key:1,class:"tab-content"},IC=Re({__name:"index",setup(t){const r=Un(),e=sr(),a=R("我的工作流"),i=R(null),n=R(null),o=R(!0),s=[{label:"收益",value:"factor_overview"},{label:"深度分析",value:"factor_analysis"}];e.child_currentFootTab="factor_overview";const l=h=>{console.log("当前选中的tab:",h)},c=R(null),u=R(null),d=async h=>{var f,p,g,m,b,x;console.log("应用项目：",h);try{const L=await e.fetchLastRunId(h.workflow_id,h.feature_tag,h.locator);if(L.code===0)c.value=L.data;else if(L.code===-1001){r.warning("工作流正在运行，请等待完成后再点击应用！"),(f=n.value)==null||f.resetLoadingState();return}else if(L.code===-1002){r.error("工作流运行错误，请重新去工作流重新运行！"),(p=n.value)==null||p.resetLoadingState();return}else if(L.code===-1003){r.warning("工作流没有运行过，请去工作流运行！"),(g=n.value)==null||g.resetLoadingState();return}L.data.length>0&&(u.value=L.data[0],await e.getFactorLastDateData((m=u.value)==null?void 0:m.node_output),console.log("因子最新日期数据:",e.factorLastDateData))}catch(L){console.log("请求接口出错:",L),r.error("请求接口出错，请检查网络！"),(b=n.value)==null||b.resetLoadingState();return}(x=n.value)==null||x.resetLoadingState(),a.value=h.title,o.value=!1,e.child_currentFootTab="factor_overview"},v=()=>{o.value=!0};return Ke(async()=>{await e.fetchFactorList(),console.log("因子列表返回的数据:",e.factorList)}),(h,f)=>{var p,g;return G(),Q(Ce,null,[S("div",wC,[o.value?(G(),Ua(i_,{key:0,ref_key:"tableaRef",ref:n,onApply:d,tableData:pe(e).factorList},null,8,["tableData"])):(G(),Q("div",LC,[S("div",kC,[kt(Js,{onBack:v,backs:!0,title:a.value},null,8,["title"]),kt(tl,{"tab-list":s,tab:pe(e).child_currentFootTab,"onUpdate:tab":[f[0]||(f[0]=m=>pe(e).child_currentFootTab=m),l]},null,8,["tab"])]),S("div",SC,[S("div",MC,[pe(e).child_currentFootTab==="factor_overview"?(G(),Q("div",TC,[kt(v_,{taskId:(p=u.value)==null?void 0:p.node_output},null,8,["taskId"])])):ue("",!0),pe(e).child_currentFootTab==="factor_analysis"?(G(),Q("div",EC,[kt(xC,{into:!0,factorId:"",taskId:(g=u.value)==null?void 0:g.node_output},null,8,["taskId"])])):ue("",!0)])])]))]),kt(to,{ref_key:"logsDialog",ref:i},null,512)],64)}}}),AC=Te(IC,[["__scopeId","data-v-1c4a531a"]]),DC=Re({__name:"index",setup(t){const r=sr(),e=R(null);return R(!0),r.child_currentFootTab="trade_all",(a,i)=>(G(),Q(Ce,null,[kt(Jn,{title:"实盘功能开通请联系小助理，开通对应期货公司权限",type:"no-use"}),ue("",!0),kt(to,{ref_key:"logsDialog",ref:e},null,512)],64))}}),PC=Te(DC,[["__scopeId","data-v-cd6d393a"]]),FC={class:"strategy-container"},RC=Re({__name:"index",setup(t){const r=sr();return r.child_currentFootTab="signal_all",(e,a)=>(G(),Q("div",FC," 信号指标内容... "))}}),BC=Te(RC,[["__scopeId","data-v-960ae904"]]);V1({name:"bsCircle",draw:(t,r,e)=>{const{x:a,y:i,width:n,text:o}=r,{color:s,textColor:l}=e,c=n/2;t.beginPath(),t.arc(a,i,c,0,2*Math.PI),t.fillStyle=s||"#ff0000",t.fill(),t.strokeStyle="#ffffff",t.lineWidth=2,t.stroke(),t.fillStyle=l||"#ffffff",t.font=`bold ${c}px Arial`,t.textAlign="center",t.textBaseline="middle",t.fillText(o,a,i)},checkEventOn:(t,r)=>{const{x:e,y:a}=t,{x:i,y:n,width:o}=r,s=e-i,l=a-n,c=o/2;return Math.sqrt(s*s+l*l)<=c}});_a({name:"buyFigure",totalStep:2,createPointFigures:({coordinates:t})=>t.map(r=>({type:"bsCircle",attrs:{x:r.x,y:r.y,width:20,text:"B"},styles:{color:"#e9414d",textColor:"#ffffff"}}))});_a({name:"sellFigure",totalStep:2,createPointFigures:({coordinates:t})=>t.map(r=>({type:"bsCircle",attrs:{x:r.x,y:r.y,width:20,text:"S"},styles:{color:"#45b486",textColor:"#ffffff"}}))});console.log("figure.ts");Xl({name:"factorIndicatorDraw",shortName:"Factor",zLevel:-1,figures:[],calc:t=>t.map(r=>({volume:r.volume,close:r.close,open:r.open})),createTooltipDataSource:({indicator:t,crosshair:r})=>{const a=t.result[r.dataIndex];if(a){const i=a.open<a.close?"rgb(224, 152, 199)":"rgb(143, 211, 232)";return{legends:[{title:"",value:{text:a.volume,color:i}}]}}return{}},draw:({ctx:t,chart:r,indicator:e,bounding:a,xAxis:i})=>{const{realFrom:n,realTo:o}=r.getVisibleRange(),{gapBar:s,halfGapBar:l}=r.getBarSpace(),{result:c}=e;let u=0;for(let h=n;h<o;h++){const f=c[h];f&&(u=Math.max(u,f.volume))}const d=a.height*.4,v=$1("rect");for(let h=n;h<o;h++){const f=c[h];if(f){const p=Math.round(f.volume/u*d),g=f.open<f.close?"rgba(224, 152, 199, 0.6)":"rgba(143, 211, 232, 0.6)";new v({name:"rect",attrs:{x:i.convertToPixel(h)-l,y:a.height-p,width:s,height:p},styles:{color:g}}).draw(t)}}return!0}});const NC={class:"left-bottom-content-tab"},OC={key:0,class:"left-bottom-content-tab-item"},VC={key:1,class:"left-bottom-content-tab-item"},zC={key:2,class:"left-bottom-content-tab-item"},$C={key:3,class:"left-bottom-content-tab-item"},WC={class:"right"},YC={class:"right-table"},ZC=Re({__name:"index",setup(t){qn(z=>({d252268a:p.value+"px",d78258da:b.value+"px"}));const r=sr(),e=ta(),a=R(!0);Wa("child_isShowFuture",a);const i=R(100);Wa("child_futureListHeight",i);const n=R(0);Wa("child_dailyPositionHeight",n);const o=z=>{console.log("应用项目：",z),(z==null?void 0:z.type)==="strategy"&&(z.positionHeight?(i.value=60,n.value=z.positionHeight||0):(i.value=100,n.value=0),r.rightTableData=z.selectedApplyData||null,z.timeRange&&r.updateTimeRange(z.timeRange,z.startDate||"",z.endDate||""))};Pe(()=>r.currentFootTab,()=>{r.currentFootTab!=="strategy"&&(i.value=100,n.value=0)});let s=null;const l=R(null),c=R(null);let u=null;const d=R(1),v=[{key:"strategy",label:"策略"},{key:"factor",label:"因子"},{key:"trade",label:"实盘"}];r.currentFootTab="strategy";const h=R(null),f=R(!0),p=R(350),g=()=>{f.value=!f.value,p.value=f.value?350:0},m=R(!0),b=R(300),x=()=>{m.value=!m.value,b.value=m.value?300:0},L=R(!1),_=R(0),M=R(0),w=z=>{L.value=!0,_.value=z.clientY,M.value=b.value,window.addEventListener("mousemove",k,{capture:!0}),window.addEventListener("mouseup",C,{capture:!0}),document.body.style.cursor="s-resize",document.body.style.userSelect="none"},k=z=>{L.value&&(b.value=Math.max(50,Math.min(800,M.value-(z.clientY-_.value))),z.preventDefault(),z.stopPropagation())},C=()=>{L.value=!1,window.removeEventListener("mousemove",k,{capture:!0}),window.removeEventListener("mouseup",C,{capture:!0}),document.body.style.cursor="",document.body.style.userSelect=""},y=()=>{Je(()=>{var tt;const z=((tt=h.value)==null?void 0:tt.$el)||h.value;z&&z.addEventListener("click",T,!0)})},T=z=>{var yt;if(z.target.closest(".optional-svg"))return;const tt=z.target.closest(".marketlist-row");if(!tt)return;z.preventDefault(),z.stopPropagation();const mt=tt.querySelector(".col2.code");if(!mt)return;const ft=(yt=mt.textContent)==null?void 0:yt.trim();ft&&(console.log("点击了合约:",ft),E(ft))};let I=null;const E=async(z,tt)=>{console.log("准备切换到合约:",z),I&&clearTimeout(I),I=setTimeout(async()=>{console.log("执行合约切换:",z);try{if(s&&typeof s.setSymbol=="function"){tt&&s.setPeriod({multiplier:1,timespan:"day",text:"1d"});const mt=A(z);mt.ticker=z.replace(/\d+$/,"").toUpperCase(),console.log("symbol.ticker",mt.ticker),s.setSymbol(mt),s.getChart().setOrderFlowData([]),s.getChart().setCandleVisible(!0),console.log("====================symbol=======================",mt),tt&&(s.getChart().createOverlay({name:"buyFigure",points:tt==null?void 0:tt.buyFigure}),s.getChart().createOverlay({name:"sellFigure",points:tt==null?void 0:tt.sellFigure})),s.getChart().createIndicator("factorIndicatorDraw",!1,{id:"candle_pane"})}console.log("✅ 合约切换完成:",z)}catch(mt){console.error("❌ 合约切换失败:",mt)}},300)};r.klineCharthandleContractChange=E;const A=z=>{const tt={BABA2503:"BABA",IF2503:"IF"},mt=z;return tt[z]?{exchange:"XNYS",market:"stocks",ticker:tt[z],name:tt[z],shortName:tt[z],priceCurrency:"CNY",type:"stock"}:{exchange:"XNYS",market:"stocks",ticker:mt,name:mt,shortName:mt,priceCurrency:"CNY",type:"stock"}},F=()=>{if(s){const z=s.getChart();z&&z.resize()}},N=z=>{r.currentFootTab=z,console.log("当前选中的tab:",z)},B=R(!1),Y=R(0),K=R(0),$=z=>{B.value=!0,Y.value=z.clientX,K.value=p.value,window.addEventListener("mousemove",ut,{capture:!0}),window.addEventListener("mouseup",rt,{capture:!0}),document.body.style.cursor="w-resize",document.body.style.userSelect="none"},ut=z=>{B.value&&(p.value=Math.max(50,Math.min(800,K.value+(Y.value-z.clientX))),z.preventDefault(),z.stopPropagation())},rt=()=>{B.value=!1,window.removeEventListener("mousemove",ut,{capture:!0}),window.removeEventListener("mouseup",rt,{capture:!0}),document.body.style.cursor="",document.body.style.userSelect=""};return Pe(b,z=>{}),Ke(async()=>{try{const z=b.value/window.innerHeight*100;l.value&&(s=new Em({container:l.value,symbol:e.symbol,period:{multiplier:15,timespan:"minute",text:"1m"},datafeed:new Dy("Q0aVaOIdjbaHHu5UZM5MQ0XcOEU3mSIJ")}),s.setTheme("dark"),r.klineChartRef=s,u=new ResizeObserver(()=>{F()}),c.value&&u.observe(c.value)),y()}catch(z){console.error("Failed to load klinecharts:",z)}}),Ar(()=>{var tt;if(I&&(clearTimeout(I),I=null),s&&s.getDatafeed){const mt=s.getDatafeed();mt&&typeof mt.unsubscribeAll=="function"&&mt.unsubscribeAll()}u&&(u.disconnect(),u=null);const z=((tt=h.value)==null?void 0:tt.$el)||h.value;z&&z.removeEventListener("click",T,!0),window.removeEventListener("mousemove",k,{capture:!0}),window.removeEventListener("mouseup",C,{capture:!0}),window.removeEventListener("mousemove",ut,{capture:!0}),window.removeEventListener("mouseup",rt,{capture:!0})}),(z,tt)=>(G(),Q("div",{class:se(["kline-container",{dragging:L.value}])},[S("div",{ref_key:"leftTopRef",ref:c,class:"left-top"},[S("div",{ref_key:"chartContainerRef",ref:l,class:"chart-container"},null,512)],512),S("div",{class:se(["left-bottom",{dragging:L.value}])},[S("div",{class:"left-bottom-toggle-btn",onClick:x},[(G(),Q("svg",{style:_e({transform:m.value?"rotate(90deg)":"rotate(270deg)"}),viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"32302",width:"16",height:"16"},tt[0]||(tt[0]=[S("path",{"data-v-da8ede72":"",d:"M722.438 510.815c-0.246-0.246-0.513-0.454-0.766-0.69l-299.893-299.892c-14.231-14.24-37.291-14.24-51.529 0-14.241 14.231-14.241 37.291 0 51.529l274.851 274.859-274.849 274.851c-14.241 14.237-14.241 37.301 0 51.532 7.116 7.119 16.436 10.682 25.764 10.682 9.321 0 18.644-3.563 25.763-10.682l299.905-299.901c0.246-0.234 0.51-0.438 0.751-0.678 7.129-7.13 10.685-16.469 10.674-25.804 0.006-9.337-3.55-18.676-10.674-25.804z",fill:"#777777","p-id":"32303"},null,-1)]),4))]),S("div",{class:se(["left-bottom-drag",{dragging:L.value}]),onMousedown:w},tt[1]||(tt[1]=[S("span",null,null,-1),S("span",null,null,-1),S("span",null,null,-1)]),34),S("div",{class:"left-bottom-content",style:_e({opacity:m.value?1:0})},[kt(Ay,{onTabChange:N,activeTab:pe(r).currentFootTab,tabs:v},null,8,["activeTab"]),S("div",NC,[pe(r).currentFootTab==="strategy"?(G(),Q("div",OC,[kt(el,{onApply:o})])):ue("",!0),pe(r).currentFootTab==="factor"?(G(),Q("div",VC,[kt(AC)])):ue("",!0),pe(r).currentFootTab==="trade"?(G(),Q("div",zC,[kt(PC)])):ue("",!0),pe(r).currentFootTab==="indicator"?(G(),Q("div",$C,[kt(BC)])):ue("",!0)])],4)],2),S("div",WC,[S("div",{class:"right-toggle-btn",onClick:g},[(G(),Q("svg",{style:_e({transform:f.value?"rotate(0deg)":"rotate(180deg)"}),viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"32302",width:"16",height:"16"},tt[2]||(tt[2]=[S("path",{"data-v-da8ede72":"",d:"M722.438 510.815c-0.246-0.246-0.513-0.454-0.766-0.69l-299.893-299.892c-14.231-14.24-37.291-14.24-51.529 0-14.241 14.231-14.241 37.291 0 51.529l274.851 274.859-274.849 274.851c-14.241 14.237-14.241 37.301 0 51.532 7.116 7.119 16.436 10.682 25.764 10.682 9.321 0 18.644-3.563 25.763-10.682l299.905-299.901c0.246-0.234 0.51-0.438 0.751-0.678 7.129-7.13 10.685-16.469 10.674-25.804 0.006-9.337-3.55-18.676-10.674-25.804z",fill:"#777777","p-id":"32303"},null,-1)]),4))]),S("div",{class:se(["right-drag",{dragging:B.value}]),onMousedown:$},tt[3]||(tt[3]=[S("span",null,null,-1),S("span",null,null,-1),S("span",null,null,-1)]),34),S("div",{class:"CustomTopPanel",style:_e({opacity:f.value?1:0})},[kt(_y,{ref_key:"customTopPanelRef",ref:h,currentCustomPanelIndex:d.value},null,8,["currentCustomPanelIndex"])],4),S("div",YC,[kt(Sy)])])],2))}}),HC=Te(ZC,[["__scopeId","data-v-68a15203"]]),jC={class:"about"},QC=Re({__name:"ChartsView",setup(t){return(r,e)=>(G(),Q("div",jC,[kt(HC)]))}});export{QC as default};
