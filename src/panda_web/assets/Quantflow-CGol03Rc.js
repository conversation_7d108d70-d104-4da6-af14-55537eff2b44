import{H as Yt}from"./Header-BeOjax6s.js";import{d as gt,v as xt,g as Pt,c as w,o as A,F as ze,k as Je,i as je,b as e,l as St,a as Me,n as Pe,x as mt,t as H,_ as nt,r,f as rt,y as lt,z as Ft,A as bt,h as Be,u as Lt,B as Wt,C as wt,D as Ot,G as Zt,e as Qt,H as Xe,I as qe,J as et,K as Vt,j as Ye,L as Jt,M as Bt,N as yt,O as Ht,P as Mt,Q as $t,R as Xt,S as jt,m as Kt,T as ea,U as ta,V as aa,W as ht,X as Ge,Y as oa,Z as sa,w as la,$ as na,a0 as ia}from"./main-CZkS48P-.js";import{a as ra}from"./index-DjmxrE12.js";import{l as st,G as ca,a as da,b as ua,c as fa,d as ha,e as ga,f as pa,g as va,h as Aa,i as ma,j as ya,k as wa,m as xa,n as ba,o as ka,p as _a,q as Gt,_ as qt,s as Ca}from"./index-d1VHI6YI.js";const La="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAhNJREFUOE+Nkz2IU0EQx2c2TxJ5CHKihWcqfdl9hBgMAcVO7MUqCAqiJ2Ihp2IhaBPsUgg2lhaHV5gcaKdYaHFXqBA0CSRvkmcX5U5E1HydIewb80ISnsGPbLXLzv83s7P/QZhZ9Xr9NDOfBYBjALAXAL4xc0UIsdZut1fS6fQgKMHJoVQqLUYikccAcJiZHwkhXgohtgaDwQIiHkfE88y8bRhGxrKs9xPdCDAWv0HEcrfbvZBKpb7MVtZsNnd2Op0cIl7UWp+Mx+Nv/ZgRgIg2EPFHLBY7hYjerDh4JqIcAJwDAKWUauP4zSu9Xu/QnzLPwgqFQiiRSPg9yUsp7yIRrTHzZ9u2r/4rc/DOcZzLiHhTKSV9QBMRl6WUT+cFuK57UGv9QWu9xwf8DIVCJyzLej0voFwum+FwuOP3wQdsIuKSlPLZvADXdQ9orZue5+1Hx3FeAEDRtu078wJqtdoZIcQ9pdSi/wuXmDlrmqYVjUa354EQ0ToAvFNKXcdisbjDNM0aADy3bXv5fwAiugIAuX6/H08mkx9HRnJd98jQXRtD7z+oVCq3M5mM/huIiL4CwKpS6trUif6m0Wgc9TzvCTN/F0LcF0K8Mk1zs9Vq7WbmWD6fX89msx4R+S58CABLSqnV6TCNLb1raOkb42mMBarYQsS0lPLTOG4K+Q0QLLtarS4YhrEPANoT4cxM+JBbvwBBieor6fEbMAAAAABJRU5ErkJggg==",Ea="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAATCAYAAAByUDbMAAAAAXNSR0IArs4c6QAAAwxJREFUOE+VVE1oHVUU/r47M4lN/YmCiFAQ6qbiokVQBEGooC66svRnJQgqQle+/LRm5r3kZmbuKOlLpAlSqtiNq9KiLkRQlIggiLpQFyIIRUGQKJSYUqK58+7xnUde0Pja0Fnec+53v59zhrj+x7IsD5OcBHAAwLcicrrZbL4LQAZd4/ZDEaFz7giAVhfoGoAyjuPP67p+HECzC7gbQJFl2SWS/wHdArPWmiRJeiAArgKYzbLso+2POeee7jKbAXCbgnrvL1lrg/ZRQeI4PmqMaYUQ/uwym221Wh/fQH6vVBTFU8aYGZJ3hBCKuq4vqi/nSe4zxkxPTU19shPIAKZPqgoAP6k/V0nuTdP0j5sF6vdXVXW3iPzCqqqciBzT1NI0ff9mAYuiOGyMmSN5oReAc+6giLxO8kqn02lMT09/txOoc+6A3gFwJ8lGlmXLXFhY2DU2NrauQQwNDT2vAYjIB8PDw83JycnfB3h0j44LgEMhhJlOp/O2ptloNHZpAD8A+JJklmXZb9ba25MkaQJ4TkTaKysrZ5aWlv5eXFwcXltba5AcA3Dee19Za9ecc/eKiAPwqI7GrXEcT5F8sUv5jPd+3lr7V1EU95M8TfIhAMsADgL4xnt/0lp72Vp7S5Ik4yLyMoA367p+dWtoy7K8j+QcgEdE5JVms3lBJeZ5vj+Kov26TlmWfa9nZVkeJ/kagK+896estT/3htY59xmAi977c9bauqqqxzaN3dgM4+u+b3mePxxFkZo+pKanafqFtTaOouilKIqOMM/zB6Momie5R0TGN1dIH3lWgxaR5RDCG1EUnQDwBAD19h1ddl0tkvMi8qt6uSXTOXcIQFtELhtjxtM0/XFiYmL36OjoSWPMMyGE91ZXV+fa7fa1qqr2hRCUwF6S2vthT+a/o1fKcRyfIJnqEG5sbMxaa6/0e6y1d8VxrPt4XFl778+qNf36/35BWtBLSZJYAMdEpBoZGXlrfX39BZU46JEbgvWLRVE8QLJNUr36lOSEyr/edgxktr1ZZ0pnb6cV+wccUGjIODRIkAAAAABJRU5ErkJggg==",Ia="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACYAAAAgCAYAAAB+ZAqzAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAJqADAAQAAAABAAAAIAAAAAALstX7AAAEgUlEQVRYCc2YWWiUVxTHO0uiVhP3qi20oZIggoK+1FICxaTSjULzkEJ9sNWHSMTRYGfLMk2zTGactmnGVhKopH2opXmwUE0LDaUREVREcCFqwIWCGtHWMhO7JDOZ/s7nvWEy5svWzCQXzpx7zzn3nP937j6WJ6ZYGhoanrFYLCWJROJN+GrcrFSu+pBdQ3YMfqS6uvrWVEJYJtuprq5und1udxP0Hfrax+kfA+B3sVgs6PP5Lo5jO0I9YWB+v/+loaEhD4HewIPud4r6EUCehu6KZ/QroBeolkCbREZBnei0Wq2BysrKk49EY//qAKZWAHodpx4MCpVRAn4UWRPDJMBMC8O9CZDS9y1IxzqBTAD+aNoxyXiETUdHh623t7dUOV2vlDHAfKu+umdEh3EafNxa+roxexcyhp/2eerBgoKCjtLS0niqC/0Vhry2tnYu8+c9ADkRPK+M/4IfstlsH3s8nt+UbEosEAg8G4/HP6DzDuhJ5eQ6IEPMw6+I/492bAATQNnZ2Q7mUAWgVirlAzp8QYbCpP2e7jAdnAwuJ5aDWLvwt1h8EquPWM0DAwNhAWhRX9GFrkAMKLfp0EyG2txud/SRKD2/wWAwhwyWAaqCCE+rKFeJvcXS2Nj4M4JXoDsA+hDEX4N4QBllhIXD4TnRaHQbAD8Cg4xYlwDrpzIf2lxVVfVrRpCYBKmvr9/McP6C+qGtqKjoZSqyc28pLi6OFBYWXuru7n5slaBPW5GMEXc7AT4nYwvgx03nGEPaypBKNtNWxpxjEhUApquSZdyC/v50opvQqkwOKADTuY9xEjzHUO0j5sT2sWRwUjfb+VEdxnGQfe1/7/z4usAqDExo5xdQqcXkrPxBnGb8rEwFJ20Azq7bRSrIWXcfSwXIRE7/DZZhkvOqion9JRO7LhVEJtqcQH7iy2r1gaHNjmAPoD6T4PBlmQAxWgxiP4VcqBVMc60I5IYppT4/P19O+RkpbBtlBG6U4ILJSvqMOxmNRE9Pj1ybZ6So2EM6uIVJvBdszUpwhXqAc/IbToGYNkonJ46dS+pWNXJrJBb1CiNbgCsHkB/ZQgXiJu39AGyn4/B1V+mmhcmNor+/fzsgXDjMU04jtL1s3AcNYCIEwKKsrKzdVPdAS0VGuQN9Mjg42IZ+Wm4a+FlAHJlPcmaugqT8DrUQ5wD6P0UwDEwaUkKh0HwytZNqcsc/yGALdMDr9T4wDCf509TUtJhs7Ibkw5eo7saHM5StTqfzYbLLx4BppaQ6Eom8Dxh5duUpeRR+EGrmtms8cJXclLH0V6CU1V4O5SjDGwDcn5ub2+5wOP5VshHMFJi2IrV2Ui/vQS9kTE743wA+xEPi05qamhvaNpnLI4eXkBMAsmnOU7rL8ABDdhi/Yy6ucYHpYDiycld7m7acEBu0HH4WOgPJ68oGXw6YF+EbIe3/HDI/l87v8TO8JaA3LbqjqcFoCobnNeSSQf23wWhmAuA4FGLYfxrNYCzZlIBphwBcRZZeZchkiGUl22lL5q6R3U6Xy9WnbSfL/wNxuVVL2KmRUgAAAABJRU5ErkJggg==",Sa={class:"category-items"},Ra=["onClick","onDragstart","onDragend","draggable"],Da={class:"node-icon"},Na={key:0,src:Ia,alt:"icon",width:"11px",height:"11px"},Ba={key:0,class:"item-count"},Ct=30,Ma=gt({__name:"CategoryItem",props:{item:{type:Object,required:!0},level:{type:Number,default:0},selectedNodeId:{type:String,default:null},expandedCategories:{type:Array,default:()=>[]}},emits:["toggle-category","drag-start","drag-end"],setup($,{emit:F}){const I=$,p=F,k=xt(()=>{var q;return((q=I.item.children)==null?void 0:q.filter(X=>X.object_type==="group"||X.object_type==="plugin"))||[]}),o=q=>q===k.value.length-1,he=q=>q<k.value.length-1,x=q=>{if(!q.children)return 0;let X=0;for(const Le of q.children)Le.object_type==="plugin"?X++:Le.object_type==="group"&&Le.children&&(X+=x(Le));return X},ee=()=>({left:`${I.level*Ct+8}px`}),B=()=>({left:`${I.level*Ct+9.5}px`,width:`${Ct-2}px`}),l=()=>({marginLeft:`${I.level*Ct+30}px`}),Ce=q=>{p("toggle-category",q)},Ae=(q,X)=>{q.dataTransfer&&(q.dataTransfer.setData("node-type",X.name),q.dataTransfer.effectAllowed="copy"),p("drag-start",X)};return(q,X)=>{const Le=Pt("category-item",!0);return A(),w("div",Sa,[(A(!0),w(ze,null,Je(k.value,(V,Ie)=>{var xe,We;return A(),w("div",{key:V.id,class:je(["node-container",{"is-group":V.object_type==="group","is-plugin":V.object_type==="plugin","is-last":o(Ie),"has-children":V.object_type==="group"&&((xe=V.children)==null?void 0:xe.length)}])},[e("div",{class:je(["vertical-line",{full:he(Ie),partial:!he(Ie)}]),style:Pe(ee())},null,6),e("div",{class:"horizontal-line",style:Pe(B())},null,4),e("div",{class:je(V.object_type==="group"?"group-content":"plugin-content"),style:Pe(l()),onClick:mt(U=>V.object_type==="group"?Ce(V.id):null,["stop"]),onDragstart:U=>V.object_type==="plugin"?Ae(U,V):null,onDragend:U=>V.object_type==="plugin"?q.$emit("drag-end"):null,draggable:V.object_type==="plugin"},[e("div",Da,[V.object_type==="group"?(A(),w("img",Na)):(A(),w("span",{key:1,class:je(["node-icon-indicator",{"is-selected":$.selectedNodeId===V.id}])},null,2))]),e("span",{class:"node-name",style:Pe({color:$.selectedNodeId===V.id?"#fff":"#858585"})},H(V.display_name||V.name),5),V.object_type==="group"&&((We=V.children)!=null&&We.length)?(A(),w("span",Ba,H(x(V)),1)):Me("",!0)],46,Ra),V.object_type==="group"&&$.expandedCategories.includes(V.id)?(A(),St(Le,{key:0,item:V,level:$.level+1,"selected-node-id":$.selectedNodeId,"expanded-categories":$.expandedCategories,onToggleCategory:X[0]||(X[0]=U=>q.$emit("toggle-category",U)),onDragStart:X[1]||(X[1]=U=>q.$emit("drag-start",U)),onDragEnd:X[2]||(X[2]=U=>q.$emit("drag-end"))},null,8,["item","level","selected-node-id","expanded-categories"])):Me("",!0)],2)}),128))])}}}),Ta=nt(Ma,[["__scopeId","data-v-ac1a95ba"]]),Ua={class:"side-nav-container"},Fa={class:"search-container"},Wa={class:"search-input"},Oa={class:"nav-content"},Va={key:0,class:"empty-state"},Ga=["onClick"],qa={class:"category-name"},za={key:0,class:"item-count"},Ya=gt({__name:"Aside",props:{defaultExpanded:{type:Boolean,default:!0}},emits:["categories-loading-state"],setup($,{emit:F}){const I=$,p=F,k=r(!0),o=r(!1),x=r([]),ee=r(null),B=r([]),l=U=>{if(!x.value.includes(U.id))return 0;let M=0;const b=(i,P)=>{i.object_type==="group"&&x.value.includes(i.id)&&(M=Math.max(M,P),i.children&&i.children.forEach(y=>b(y,P+1)))};return b(U,1),M},Ce=U=>{if(!U.children)return 0;let M=0;for(const b of U.children)b.object_type==="plugin"?M++:b.object_type==="group"&&b.children&&(M+=Ce(b));return M};rt(async()=>{p("categories-loading-state",!0),k.value=!0;let U=!1;try{const M=await ra();M&&M.length>0?(B.value=M,x.value=M.map(b=>b.id),U=!0):M&&M.length===0&&(B.value=[],x.value=[],U=!0)}catch(M){console.error("加载节点分类失败 (Aside.vue):",M)}finally{k.value=!1,U&&p("categories-loading-state",!1)}I.defaultExpanded&&(x.value.length===0&&!U||x.value.length===0&&U&&B.value.length)});const Ae=()=>{o.value=!o.value},q=U=>{const M=x.value.indexOf(U);M===-1?x.value.push(U):x.value.splice(M,1)},X=U=>{U&&(ee.value=U.id)},Le=()=>{ee.value=null},V=r(""),Ie=xt(()=>{if(!V.value.trim())return B.value;const U=V.value.toLowerCase(),M=b=>b.map(i=>{if(i.object_type==="group"){const P=M(i.children||[]);return P.length>0||i.name.toLowerCase().includes(U)?{...i,children:P}:null}else if(i.object_type==="plugin")return i.name.toLowerCase().includes(U)||i.display_name&&i.display_name.toLowerCase().includes(U)?i:null;return null}).filter(i=>i!==null);return M(B.value)}),xe=()=>{V.value.trim()},We=()=>{V.value=""};return(U,M)=>(A(),w("div",Ua,[e("div",{class:je(["side-nav",{collapsed:o.value}])},[o.value?Me("",!0):(A(),w(ze,{key:0},[e("div",Fa,[e("div",Wa,[M[2]||(M[2]=e("img",{src:La,alt:"icon",class:"search-icon",width:"14px",height:"14px"},null,-1)),lt(e("input",{type:"text","onUpdate:modelValue":M[0]||(M[0]=b=>V.value=b),onInput:xe,placeholder:"搜索目录"},null,544),[[Ft,V.value]]),V.value?(A(),w("button",{key:0,class:"clear-button",onClick:We},M[1]||(M[1]=[e("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none"},[e("path",{d:"M18 6L6 18M6 6l12 12",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round"})],-1)]))):Me("",!0)])]),e("div",Oa,[V.value&&Ie.value.length===0?(A(),w("div",Va,[M[3]||(M[3]=e("svg",{class:"empty-icon",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none"},[e("path",{d:"M15.5 15.5L19 19",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round"}),e("path",{d:"M5 11a6 6 0 1012 0 6 6 0 00-12 0z",stroke:"currentColor","stroke-width":"2"})],-1)),M[4]||(M[4]=e("p",null,"未找到匹配的节点或目录",-1)),e("button",{class:"clear-search",onClick:We},"清除搜索")])):(A(!0),w(ze,{key:1},Je(V.value?Ie.value:B.value,(b,i)=>{var P;return A(),w("div",{key:b.id,class:"nav-category"},[e("div",{class:"category-header",onClick:y=>q(b.id)},[M[5]||(M[5]=e("span",{class:"category-icon"},[e("img",{src:Ea,alt:"icon"})],-1)),e("span",qa,H(b.name),1),(P=b.children)!=null&&P.length?(A(),w("span",za,H(Ce(b)),1)):Me("",!0)],8,Ga),lt(Be(Ta,{item:b,level:0,"selected-node-id":ee.value,"expanded-categories":x.value,"max-expanded-level":l(b),onToggleCategory:q,onDragStart:X,onDragEnd:Le},null,8,["item","selected-node-id","expanded-categories","max-expanded-level"]),[[bt,x.value.includes(b.id)]])])}),128))])],64))],2),e("div",{class:"fixed-fold_arrow",onClick:Ae},[(A(),w("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",style:Pe({transform:o.value?"rotate(90deg)":"rotate(270deg)"})},M[6]||(M[6]=[e("path",{d:"M7 14l5-5 5 5",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)]),4))])]))}}),Pa=nt(Ya,[["__scopeId","data-v-24627af2"]]),Et="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAAYtJREFUWEfV2dFxgzAMAFDZS2SHbEG26D8cPx2nP9zx3y2aLbIDGcLOqcW9lGBLlgym+cQGPxQsccIAALRtezLGfFpr34dhuOGxWr++78/OuQ/v/ds4jpOZcV8AcAaAu7W2qYWccWg5AcDNe38xXdfhgeYpYlWQC1zgXE1kYFdkxDB57xuD1JrIGM5ae8FH7RtYC0nh0PULTCCncDcldzYH9wLcC8nFrQK3RubgosCtkLm4JLA0UoIjgaWQUhwLqEVqcGygFKnFZQFzkSVw2UAushROBKSQOO6cC69MofiIq9GfUpdTymJRmq+B73NqnDiCYeUI8vk+xZELFxFHkIFU49QRTDyPOFQfeOi/+NCbJJXnqqcZThLmzOGmtKxdnLNwztwUlg2ULCg5Z4llATULac5l5UHtAlTtptosyQiWwBEVh0zmUWBJnAa5CtwCJ0W+ALfESZD/p/WxR+SWOY6zZqr9Ru4wbrlKzaOQsQbmLjjOM7nWAt4VRyCvyyZ6FVwE+dNEx8Ejf4Z4AOhffFtCFuoVAAAAAElFTkSuQmCC",Za=["onClick"],Qa=["src","alt"],Ja={class:"toolbar-text"},Tt=1.1,Ha=gt({__name:"Toolbar",props:{isRunning:{type:Boolean,default:!1},progress:{type:Number,default:0},graph:{type:Object,required:!0,default:null},isGraphReady:{type:Boolean,default:!1},canvas:{type:null,required:!1}},setup($){function F(y,ae=!1){y||(y=window.location.href);const z={},_=y.indexOf("?");if(_!==-1){const v=y.indexOf("#",_),R=v!==-1?y.substring(_+1,v):y.substring(_+1);I(R,z)}if(ae){const v=y.indexOf("#");if(v!==-1&&v<y.length-1){const R=y.substring(v+1),J=R.indexOf("?");if(J!==-1){const c=R.substring(J+1);I(c,z)}}}return z}function I(y,ae){if(!y)return;const z=y.split("&");for(const _ of z){const[v,R]=_.split("=",2);v&&(ae[decodeURIComponent(v)]=R!==void 0?decodeURIComponent(R):"")}}const p=Lt(),k=Wt(),o=$,he=[{id:1,src:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAUCAYAAACAl21KAAAAAXNSR0IArs4c6QAAAQ9JREFUOE/llKFOxEAURe+j00AQfAceFjTBEAwWAX9AQtDTZGdJp7UIVBUJCDyiCjQsfAC+P4Agm5DOzGWbVCC6Jaliw/P35L1zJyNpmt6IyCmAFQybQPJWrLWe5FhEXgFcAXgDcPcL84TkSETOAWzPl7hsQARwqLUurbXPAEqt9aQPNM+M28xunucHIYTyH4GeAGy0wvs0bZH8TJJkr9NRnucj7/2FiKz2UUh+NQ0nSTLtBA15RksCIilZlp21whdeGkKYVVV1XRRF3XmaMWZdKfUgIk1zC4fkzDl3ZIz5WBJHf6d+ksfOucchGyml9kXkvvlGXgDsDIH8yEzFGLMWx/Gm9z4aAouiyNd1/f4N+DkenOMZoNsAAAAASUVORK5CYII=",import.meta.url).href,alt:"save",text:"保存"},{id:2,src:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAATCAYAAACdkl3yAAAAAXNSR0IArs4c6QAAAU1JREFUOE+d1DFLw1AQB/D/JRFEcHZxdNbVVRB0dXJ1cxHXgi8v2DQ3pDjpVlc3v4E4SBc/gLoJIgT3Uhyq9M48oRpLapO8KSR3P97dvTxi5hUiYlVdB+Dhd4mIhNbam8K7mY/EzC5wU1X7RCRFaDwep1EU3VeFPgCcGWNOqiTMinE70vzjqTGmXREiAC7nz6oNMfMTgDtjzFERbALZfCixql6GYXg4wWpDrp4kSdr5YKIi1ggqw0qhTqez63nedoXm7wNYJaLrUoiZzwEczINUdYGIFgEMG5cWx/GG7/u3AN5FZKsRNI1Ya19qQ2WIa0FtiJmf8xMQuHLcTiZ9bALtBEHw0Gq13orDqA3999O+AshEJPV9/+caUVUdDAb9brc7nHcMvnuUJMkegCsiWppOEJFja+1FJcgFpWm6PBqN1qZvyCzLHnu93mcV6Au5hdZ8w5yWBAAAAABJRU5ErkJggg==",import.meta.url).href,alt:"export",text:"导出"},{id:3,src:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAATCAYAAACdkl3yAAAAAXNSR0IArs4c6QAAAUZJREFUOE+l1LFKxEAQBuB/JsgVclyv1wh2ng9gc1iIb2Bn63VayaWYJEo2G7hWbOxs9QkUBFtbrS19AgURTDJeCkO8M9yS23KZ/WY2mR2C4zLG7DNzAoBrRwoielFVIUcHcRzveJ7n1yFVZSIaAnhyhpoSWmtTAKdNULmvLtVaa8+mceezEFlrLwHsishWW4iSJLkioiMAkYiYNlCFTD9gHARBWa7Tql+tNVJmqqA0TW9V9QDAG4CbRWUURfEQhuHdb1wFWWvfAXRV9YuIvhdBAK5F5GQOMsZsMPMjgNU8z/eiKHp2wKqQP79/GWyuj9pi/zZkDctEZNPlik2djclkspZl2baI3C8FuRyuxzRW1ASNx+Nur9cbElH1PvM8Z2YuR8u68xgxxhwz88VsIlX9BHDoDI1Go5V+vz+YnZCdTufV9/2PHyuOmQ4RpZomAAAAAElFTkSuQmCC",import.meta.url).href,alt:"import",text:"导入"},{id:4,src:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAUCAYAAACAl21KAAAAAXNSR0IArs4c6QAAAWZJREFUOE/tlLFLAzEYxd872m46u+jg4iTuOtgiCqKbg7P+A06l0OTK0SSF4uQ/oLODowiKWAfdRXBwcNDFWbe23GdTpNij1x6dvSW8fMkv+R65R6R8zrkygP1E+VwpdTxqC9NA1to9ADuJ+qXW+mIkyBizS3IxDZhlXkTe6Jx7AbAEIAAQA5AsmxNrXvutGWOKQRDcxXFcCsOwNQUIU4OiKJrN5/NHnU7nJIqir6lBjUZjXURaJIvVavU+FWSMOfAthmF49tt+Ug/ZkQrqneg9g9a65EFJnfR1LMgDetcegP7qfxBgrb31nmitN/yY1Jk9qtfryx5Qq9We/ZjUmUGTfpORIGvtKskHAFtKqZtJEF93zm0CuBaRNa31Y/8dNZvNmW63+yEiTyLig6s9AVYgWSa5ksvl5iuVyvcg2Jxz2yJySnIuy41E5JPkoVLqyq8fSkgR8fm0ICKFcTCSbaXUO8lBdv0AzkZJXg8QSYAAAAAASUVORK5CYII=",import.meta.url).href,alt:"delete",text:"清空"},{id:5,src:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAATCAYAAACdkl3yAAAAAXNSR0IArs4c6QAAAq9JREFUOE+VlF2oTUEUx/9r9t6lxFXIffEV+Ug3H0+IB99SyleEkkIePAnlnNl3t9z9cepe3ngg9+ESykcSijwoIlKkKDc8E4mEOLP3LOZ2jo7jIPM2s9b81pq1/msILVaWZXOstWsAzAAwiog+isgLAFfzPL/MzF+br1HjQZZlU6y1R4hoIYBnInILwGsiaiOiDhGZD+BVURT7oig623j3JyhN0yUAzgHoF5HdYRjebY7a3d3dbow5AGAHgG6t9f66zwCoq6urw/O8OwAuGWO2MXO11ZPrZ2mabgLQB2C/1vqQO3cgStP0AYBPxpjFzJz/DdIA2wOgYq2d1tnZ+ZyyLFslIueLopgeRdHTZkiapldqkZ802phZBUHwWEQehWG4xWVzSkTawzBc1CITZ7cisi4MwwstguwSkSzP85HO8SWA41rryv+C4jierJR69qO2Mx3oCxHtLJfLJxvefw3AhNp+oms5gM8iYqy1m6MoeuRsPT09g6vV6icAKxzoHYCS1vpYU1fG1vYZgNMAnjiQ53m9pVLpvbMx84ggCN5aaxdQkiQPlVI3y+Wy68Jvgv1bjZIkmUdEt40x411GLuJ6Y8wkZrYtClrUin2xhe0ggJVa68lUK5hr7Tat9Ylm5yRJ1uZ5foOZPza1v933/edExE6UA8pO0/Twj9HYaK2d7cT1L0Eys+/7/nUiGmOM6XBDPABi5kFBENwEMFoptbpUKjmlt1zMPMz3/TNEtLQoillRFD2uj8jABWYeWncQkT6lVG+1Wr1fr1ulUhlnrd0gInuJ6BuA4SJyJs/z7c7nl2+kNncbRSQkoqkA3PC+AdAGYIiIfCCio8aYzPO8uUqpi3VYM+jnc+I4nkpEM5RSo5wYrbX9eZ7fa/wZ4jhe7mDuJ/gj6F8FbxDvMgBbvwM3PmOICE4pBgAAAABJRU5ErkJggg==",import.meta.url).href,alt:"zoom-in",text:"放大"},{id:6,src:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAUCAYAAACAl21KAAAAAXNSR0IArs4c6QAAAplJREFUOE+dlE1IVFEUx//nzXtJIIgyjQsLCVoJrVqEK7PaBC5Moq9FC11Glm2Sd+80N9+7zmQRFLWwiKKIomlTqwwypI9VC1u0CGpRWYhGjEmRvY+TV2ZkHM2P7vZ/zu+d87/3/wjLnGw2WzszM1PvOM7U6OjoRD6fj/5VTpWCUmqj4zgnmbmDiBrL9Gkiegzgiuu6I5V9C0Ba6+MAsgDGmfk2Mz+3LGs8iqIay7K2EtF+AK1ElC8UCl0DAwPTJeA8SGt9AUA3M58Ow/C8UurPUmt4nrfbsqxbzDxh23ZLb2/vlKmbA3me12VZ1lVmPiilzC/nm9F8399ERK8AvBFCtM2BcrlcTRRFH5j5upTy1EqQkt7X19ecSCReElG767qPyPf9EwAyYRg2KqV+rBZk6rTWZvqUEKKFtNbDzDwmpTyyFoip7e/vb2fmB0RUb0BfmTknpbxUAvm+3xDHcaoSnEgkAiHE29kN2GjFp/KZmZsN6Dczd0kp75QatdZjABqWmjAMw52ZTOaZ0Xp6etYnk8lfANoMyDSdE0JcLDUqpeqqqqrqKkFRFAVSyo9lk5vb+1Sa6AkRfXNd9/B/eLSPme8GQbDBTHTU+BYEwWal1Pe1wLTWD4mo2nXdXaSUqnYc5z2A+0KI7tWCtNatAJ4C2COEGJp72b7vHyIiY3anEOLmSrBsNrsljuMXs7kbEUIcmI9IEXaGiNLMfDYMQ08pZW5j0fF9fy8RXQNQG8dxUzqdfrcAVHypnQBMeA3kXmX6AZj0bwNwg5m3E9E6Zm6VUn5Z6n+UtG37GBF1zPrWBMAyH2HmcQBDAC5LKV8rpVK2bQ+XYItA5bsMDg46k5OTqTAMfyqlCpV7lsGiZUErmV7M24Y4jnf8BdONMaMxI63zAAAAAElFTkSuQmCC",import.meta.url).href,alt:"zoom-out",text:"缩小"},{id:7,src:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAAAXNSR0IArs4c6QAAAfxJREFUOE+Vk7+LE0EUx79v3d1OEEEQQYsTLI3VidilUVCwipwKeoV/gcURM7NxSGY2iURFQQ4CCt5ZCHYWNlqLhRaCFjbKKiJIIIU/kE12nswRwxKSvcuUM+995vu+7z3CNscYcyrLsn69Xn9bFEpFj8aYiwA2APxh5tNSylfz4ueCxpAHAL4D8ADsLYLNBDWbzcOe571n5sue551j5i/M/IuI1pIk2dfr9YbTymaClFKe7/sHpZRJHMePHUgIUVNKLSmlPs0qr9Ajl5AH7chsrfUdItovhLiQT5gH0lr3iKjvlLr4LUVxHN+11q74vl+uVqsf8iBjzCoR/ajVas+n7o8BeAngoRBijbTWt4joGhGtM/MEYq1NsyzbVEr9dYB2u31oNBqdJaK8HQ52lZnbZIx5DeA4gDcAfud+TQFcEUK49sMNJoDrUz6FAE4w8ztSSu0JguCFgwwGgzPdbjcPc4Cj1tqfURR9zkOUUmEQBE8BHAFQ3pL5H+ZKk1KuTnmx6dovpRT5e631fSIqO4hTPam30+nsTtP0QBRFH3fStUajUbLWflNK9SddK5qPhecoD2u1WkvW2ifD4fB8GIZ6vCLPXGeTJFledEU2iOgkgK8A3AgsM/NtKWVjoRWpVCq7SqXSIyK65BKZ+cY8yLYejWH3ACRSyptFXv4DBbYHOWLuLHQAAAAASUVORK5CYII=",import.meta.url).href,alt:"move",text:"移动"},{id:8,src:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAATCAYAAAByUDbMAAAAAXNSR0IArs4c6QAAAldJREFUOE+tlDtoFFEUhv+zswMGk3WDiFoIoo0YhMVGhVhEURGNqLC9KYQo2CQI456bcLOZGbZSUTE++qCFRiRNQCtT+EC0EbERH51aaBpBduZ377IJmxCXjXrLuf/55p7HfwQtDkkZGxtb53nemiRJvlprv7fSy3KXcRwfIzkA4ACA1U2a9wCmPM+7EgTBp6Wxi2BhGG4CMCkivSS/iMg0ybciMiciG0juB7CX5E8Aw8aYG83ABVilUumpVquPRKQrTVNNkmTCWvtr6d/L5XKP53lXAfQBuKmqg9baHMl8HRbH8do0TV+ISCeAg6r6ulVtisWiVygULgE4R7IsIq4cWodFUXQNwKCI9JVKpSetQPN31tqM7/vTAA43vu2TKIrWA/hMctIYc6odkNO41LLZ7IyI7F6AjY+Pn85kMrdI7jLGPG8HZq1d5fv+XQB75vUkT0oYhrdFpKiq3QDYDuxPGpema/8WY8z2fwG5WAe7B6Cgqlv/B+yy62RHR0f30NCQG8a/Pq5m/SLyUEROlEqlByslubFK0/TNyMjIhDQ68wHAR1XdtRJYGIa9IuLm0qhqNO+AMySvk7xgjKm0A7TW5n3ff0bSzdu2IAh+1GFu1cRxPAWgn+R5Y8zFVsAoijbWmuZKshPAUVWdqXezyR6dvu/fAXCkthkeO89Vq9VZa23apMlns1nnEm34eEBVXUz9LFpBDb8NkyyJSL6xht4BmKttCPeaHTUr+wCeJklydnR09FVzBssuxyAIunO53PE0TQ+JyGYAXQC+1crwkuR9Y8zscmX4DWS98ucU6bk+AAAAAElFTkSuQmCC",import.meta.url).href,alt:"refresh",text:"刷新"}],x=xt(()=>o.isGraphReady?he:he.filter(y=>y.id<=4)),ee=r(null),B=r(!1),l=y=>!!((y===3||y===4)&&o.isRunning);function Ce(y,ae){if(l(y)){y===3?k.warning("工作流运行时无法导入 JSON 文件"):y===4&&k.warning("工作流运行时无法删除节点");return}switch(y){case 1:q();break;case 2:Le();break;case 3:V();break;case 4:xe();break;case 5:We();break;case 6:U();break;case 7:M();break;case 8:b();break;default:console.warn(`No action defined for id: ${y}`)}}const Ae=()=>{const ae=(o.graph.graph||o.graph).serialize(),z={format_version:"2.0",name:p.title?p.title:`workflow-${new Date().toISOString().replace(/[:]/g,"-").slice(0,19)}`,description:"",litegraph:ae,nodes:[],links:[]};p.title=z.name;const _=F(window.location.href);return _.workflow_id&&(z.id=_.workflow_id),console.log("graphData.nodes:",ae.nodes),ae.nodes&&Array.isArray(ae.nodes)&&(z.nodes=ae.nodes.map(v=>{const R=Object.keys(v.properties),J={};return R.forEach(c=>{var we;const m=(we=v.inputs.find(me=>me.name===c))==null?void 0:we.fieldName,S=v.properties[c];J[m]=S}),ae.links.find(c=>v.id===c[1]),{uuid:v.flags.uuid,title:v.title||"",name:v.type||"",type:v.type||"",litegraph_id:v.id||0,positionX:v.pos?v.pos[0]:0,positionY:v.pos?v.pos[1]:0,width:v.size?v.size[0]:0,height:v.size?v.size[1]:0,static_input_data:J||{},output_db_id:null}})),ae.links&&Array.isArray(ae.links)&&(z.links=ae.links.map(v=>{var Oe,Ue;const[R,J,c,m,S,we]=v,me=ae.nodes.find(He=>He.id===J),ke=ae.nodes.find(He=>He.id===m);return console.log("sourceNode,targetNode:",me,ke),{uuid:Ot(),litegraph_id:J,status:1,previous_node_uuid:me.flags.uuid,next_node_uuid:ke.flags.uuid,output_field_name:(Oe=ke.inputs[S])==null?void 0:Oe.fieldName,input_field_name:(Ue=me.outputs[c])==null?void 0:Ue.fieldName}})),z};async function q(){try{const y=JSON.stringify(Ae());if(console.log("jsonString:",Ae()),Ae().nodes.length===0){k.warning("当前工作区域为空，不可保存空白工作流！");return}const ae=localStorage.getItem("token"),z=await fetch("http://localhost:8000/api/workflow/save",{method:"POST",headers:{uid:"0","quantflow-auth":"2","Content-Type":"application/json",Authorization:`${ae}`},body:y});if(z.ok){localStorage.setItem("savedGraph",JSON.stringify(y));const _=await z.json();if(console.log("data:",_),k.success(p.owner==="*"?"模版工作流克隆成功！":"保存成功！"),p.workflow_id=_.data.workflow_id,p.owner="",p.workflow_id){const J=new URL(window.location.href);J.searchParams.set("workflow_id",p.workflow_id),window.history.replaceState({},"",J)}const v={uid:"0","quantflow-auth":"2","Content-Type":"application/json",Authorization:`${ae}`},R=await fetch(`http://localhost:8000/api/workflow/query?workflow_id=${_.data.workflow_id}`,{headers:v});if(R.status===404)return console.log("工作流不存在!"),null;if(R.ok){const J=await R.json();console.log("data:",J),p.title=J.name}}else throw new Error(`请求失败: ${z.status} ${z.statusText}`)}catch(y){console.error("保存图表时出错:",y),k.error("保存失败！")}}const X=y=>{(y.metaKey||y.ctrlKey)&&y.key==="s"&&(y.preventDefault(),q())};function Le(){const y=JSON.stringify(Ae()),ae=new Blob([y],{type:"application/json"}),z=URL.createObjectURL(ae),_=document.createElement("a");_.href=z,_.download=`graph-export-${new Date().toISOString().slice(0,10)}.json`,document.body.appendChild(_),_.click(),document.body.removeChild(_),URL.revokeObjectURL(z),console.log("图表已成功导出",y)}function V(){var y;if(p.owner==="*"){k.warning("当前为模版,不支持导入覆盖，请先点击保存按钮创建个人工作流后，再进行导入!");return}if(o.isRunning){k.warning("工作流运行时无法导入 JSON 文件");return}(y=ee.value)==null||y.click()}async function Ie(y){var _;if(o.isRunning){k.warning("工作流运行时无法导入 JSON 文件");return}console.log("开始导入图形");const ae=y.target;if(!((_=ae.files)!=null&&_.length))return;const z=ae.files[0];try{const v=await z.text(),R=JSON.parse(v).litegraph,J=o.graph.graph||o.graph,c=o.canvas;if(!J||!c)return console.error("Graph or Canvas not initialized"),!1;J.clear();try{J.configure(R),J.nodes.forEach(ue=>{ue._fullTitle?ue.title=ue.truncateTitle(ue._fullTitle):(ue._fullTitle=ue.title||"",ue.title=ue.truncateTitle(ue.title||""))}),c.setZoom(1),c.ds.offset=[0,0];let m=1/0,S=-1/0,we=1/0,me=-1/0;J.nodes.forEach(ue=>{m=Math.min(m,ue.pos[0]),S=Math.max(S,ue.pos[0]+ue.size[0]),we=Math.min(we,ue.pos[1]),me=Math.max(me,ue.pos[1]+ue.size[1])});const ke=S-m,Oe=me-we,Ue=m+ke/2,He=we+Oe/2,it=c.canvas.getBoundingClientRect(),Ze=it.width,Re=it.height;return c.ds.offset=[Ze/2-Ue,Re/2-He],J.setDirtyCanvas(!0),c.draw(!0,!0),k.success("导入成功！"),ae.value="",!0}catch(m){return console.error("Error configuring graph:",m),k.error("导入失败：图形数据格式不正确"),!1}}catch(v){return console.error("Import failed:",v),k.error("导入失败，请检查文件格式是否正确"),!1}}function xe(){if(p.owner==="*"){k.warning("当前为模版,不支持清空!");return}if(o.isRunning){k.warning("工作流运行时无法删除节点");return}try{const y=o.graph.graph||o.graph;confirm("确定要清空当前图表吗？此操作不可撤销。")&&(y.clear(),y.setDirtyCanvas(!0,!0),console.log("图表已清空"),k.success("图表已清空"))}catch(y){console.error("清空图表时出错:",y)}}function We(){if(o.canvas)try{const y=o.canvas.ds.scale||1;o.canvas.setZoom(y*Tt),o.canvas.draw(!0,!0)}catch(y){console.error("Zoom in error:",y)}}function U(){if(o.canvas)try{const y=o.canvas.ds.scale||1;o.canvas.setZoom(y/Tt),o.canvas.draw(!0,!0)}catch(y){console.error("Zoom out error:",y)}}function M(){o.canvas&&(B.value=!B.value,o.canvas.drag_mode=B.value,B.value?o.canvas.canvas.style.cursor="grab":o.canvas.canvas.style.cursor="default")}function b(){if(o.canvas)try{o.canvas.setZoom(1),o.canvas.ds.offset=[0,0],o.canvas.draw(!0,!0)}catch(y){console.error("Reset view error:",y)}}const i=()=>{B.value&&(o.canvas.canvas.style.cursor="grabbing")},P=()=>{B.value&&(o.canvas.canvas.style.cursor="grab")};return rt(()=>{window.addEventListener("keydown",X),o.canvas&&(o.canvas.canvas.addEventListener("mousedown",i),o.canvas.canvas.addEventListener("mouseup",P))}),wt(()=>{window.removeEventListener("keydown",X),o.canvas&&(o.canvas.canvas.removeEventListener("mousedown",i),o.canvas.canvas.removeEventListener("mouseup",P))}),(y,ae)=>(A(),w(ze,null,[(A(!0),w(ze,null,Je(x.value,z=>(A(),w("div",{class:je(["toolbar-item",{disabled:l(z.id)}]),key:z.id,onClick:_=>Ce(z.id,z.text)},[e("img",{src:z.src,alt:z.alt,width:"17px",height:"19px"},null,8,Qa),e("div",Ja,H(z.text),1)],10,Za))),128)),e("input",{type:"file",ref_key:"fileInput",ref:ee,style:{display:"none"},accept:".json",onChange:Ie},null,544)],64))}}),$a=nt(Ha,[["__scopeId","data-v-d3f84579"]]),Xa={class:"table-container"},ja={class:"table-wrapper"},Ka={key:0,class:"fixed-column"},eo={class:"scroll-area"},to={style:{display:"flex",position:"sticky",top:"0","z-index":"99",background:"#292C38",width:"max-content"}},ao=["title"],oo={__name:"TablesScroll",props:{tableData:{type:Array,required:!0},headers:{type:Array,required:!0},isFixed:{type:Boolean,default:!0},columnWidth:{type:[Number,String],default:0},customHeader:{type:Array,default:[]}},setup($){Zt(x=>({"25f58ff8":`${$.columnWidth}px`}));const F=$,I=r(null),p=r(null),k=x=>!isNaN(x)&&Number.isFinite(Number(x))&&String(x).includes(".")?Number(x).toFixed(2):x,o=x=>x?x.replace(/(\d{4})(\d{2})(\d{2})/g,"$1-$2-$3"):"-",he=()=>{I.value&&p.value&&F.isFixed&&(console.log("-------------"),I.value.addEventListener("scroll",()=>{console.log(I.value.scrollTop),p.value.style.top=`-${I.value.scrollTop}px`}))};return rt(()=>{he()}),(x,ee)=>(A(),w("div",Xa,[e("div",ja,[F.isFixed?(A(),w("div",Ka,[e("div",{class:"fixed-header",style:Pe({width:`${$.customHeader.length>0?$.customHeader[0].width:$.columnWidth}px`})},H($.customHeader.length>0?$.customHeader[0].key:F.headers[0]),5),e("div",{class:"fixed-body",ref_key:"fixedBody",ref:p},[(A(!0),w(ze,null,Je(F.tableData,(B,l)=>(A(),w("div",{key:l,class:"fixed-cell",style:Pe({width:`${$.customHeader.length>0?$.customHeader[0].width:$.columnWidth}px`})},H(o(B[F.headers[0]])),5))),128))],512)])):Me("",!0),e("div",eo,[e("div",{class:"scroll-body",ref_key:"scrollBody",ref:I},[e("div",to,[(A(!0),w(ze,null,Je([...F.headers].splice(F.isFixed?1:0),(B,l)=>(A(),w("div",{key:l,class:"header-cell",style:Pe({width:`${$.customHeader.length>0?$.customHeader[l+1].width:$.columnWidth}px`}),title:B},H($.customHeader.length>0?$.customHeader[l+1].key:B),13,ao))),128))]),(A(!0),w(ze,null,Je(F.tableData,(B,l)=>(A(),w("div",{key:l,class:"table-row"},[(A(!0),w(ze,null,Je([...F.headers].splice(F.isFixed?1:0),(Ce,Ae)=>(A(),w("div",{key:Ae,class:"table-cell",style:Pe({width:`${$.customHeader.length>0?$.customHeader[Ae+1].width:$.columnWidth}px`})},H(k(B[Ce])),5))),128))]))),128))],512)])])]))}},Ut=nt(oo,[["__scopeId","data-v-f22c5882"]]),so={class:"factor-deep"},lo={class:"factor-deep-header"},no={class:"factor-item"},io={class:"factor-item-title"},ro={class:"factor-item-content"},co={style:{display:"flex","justify-content":"space-between"}},uo={class:"factor-deep-content",style:{"flex-shrink":"0","flex-basis":"368px","margin-top":"20px"}},fo={class:"data-card",style:{height:"100%"}},ho={class:"data-item"},go={class:"data-item"},po={class:"data-item"},vo={class:"data-item"},Ao={class:"value"},mo={key:0,class:"value"},yo={key:1,class:"label"},wo={class:"factor-item",style:{margin:"0","margin-top":"20px",width:"auto","flex-shrink":"0","flex-grow":"1","flex-basis":"460px","margin-left":"20px"}},xo={class:"factor-item-title"},bo={class:"factor-item-content"},ko={style:{width:"100%",height:"400px"}},_o={class:"factor-item",style:{"margin-left":"20px"}},Co={class:"factor-item-title"},Lo={class:"factor-item-content"},Eo={style:{width:"100%",height:"400px"}},Io={class:"factor-item-container"},So={class:"factor-item"},Ro={class:"factor-item-title"},Do={class:"factor-item-content"},No={class:"factor-item"},Bo={class:"factor-item-title"},Mo={class:"factor-item-content"},To={class:"factor-item"},Uo={class:"factor-item-title"},Fo={class:"factor-item-content"},Wo={class:"factor-item"},Oo={class:"factor-item-title"},Vo={class:"factor-item-content"},Go={class:"factor-item"},qo={class:"factor-item-title"},zo={class:"factor-item-content"},Yo={class:"factor-item"},Po={class:"factor-item-title"},Zo={class:"factor-item-content"},Qo={class:"factor-item"},Jo={class:"factor-item-title"},Ho={class:"factor-item-content"},$o={class:"factor-item"},Xo={class:"factor-item-title"},jo={class:"factor-item-content"},Ko={class:"factor-item"},es={class:"factor-item-title"},ts={class:"factor-item-content"},as={class:"factor-item"},os={class:"factor-item-title"},ss={class:"factor-item-content"},Te="#7d7d7d",ls=gt({__name:"FactorDeep",props:{factorId:{type:String,required:!0},taskId:{type:String,requfactorIdired:!0},into:{type:Boolean,required:!0,default:!1}},setup($){const F=t=>"",I=t=>t,p=(t,be)=>{if(typeof t=="number")return parseFloat(t.toFixed(be));if(typeof t=="string"&&!isNaN(t)&&t.includes("."))return parseFloat(Number(t).toFixed(be));if(Array.isArray(t))return t.map(O=>p(O));if(typeof t=="object"&&t!==null){const O={};for(const Y in t)t.hasOwnProperty(Y)&&(O[Y]=p(t[Y]));return O}return t},k=t=>!isNaN(t)&&Number.isFinite(Number(t))&&String(t).includes(".")?Number(t).toFixed(2):t,o=$;Qt();const he=r(!1),x=t=>{const be=parseFloat(t);return be>0?"#ff4851":be<0?"#2fae34":"#333"},ee=r({annualized_ratio:0,maximum_drawdown:0,return_ratio:0,sharpe_ratio:0}),B=async()=>{var O,Y,oe,te,ie,re,ce,de;const{json:t,httpController:be}=await ca(o.taskId||F());ee.value={annualized_ratio:I((Y=(O=t==null?void 0:t.data)==null?void 0:O.one_group_data)==null?void 0:Y.annualized_ratio),maximum_drawdown:I((te=(oe=t==null?void 0:t.data)==null?void 0:oe.one_group_data)==null?void 0:te.maximum_drawdown),return_ratio:I((re=(ie=t==null?void 0:t.data)==null?void 0:ie.one_group_data)==null?void 0:re.return_ratio),sharpe_ratio:I((de=(ce=t==null?void 0:t.data)==null?void 0:ce.one_group_data)==null?void 0:de.sharpe_ratio)}};let l=null;const Ce=r(null),Ae=r(""),q=r({}),X=async()=>{var Y,oe,te,ie,re,ce,de,pe,ve,K,Z,C,D,h,f,u;const{json:t,httpController:be}=await da((o==null?void 0:o.taskId)||F());if(t.code==="200"&&(Ae.value=(oe=(Y=t==null?void 0:t.data)==null?void 0:Y.return_chart)==null?void 0:oe.title,q.value=(te=t==null?void 0:t.data)==null?void 0:te.return_chart,Ce.value)){if(l=et(Ce.value),Object.keys(q.value).length==0)return;var O={code:"200",message:"查询成功",data:{task_id:"bbf3bbc030a94849bea4810058eec027",return_chart:{title:"python 5 groups return",x:[{name:"date",data:(re=(ie=q.value)==null?void 0:ie.x[0])==null?void 0:re.data}],y:(ce=q.value)==null?void 0:ce.y}}};const g=(K=(ve=(pe=(de=O==null?void 0:O.data)==null?void 0:de.return_chart)==null?void 0:pe.x[0])==null?void 0:ve.data)==null?void 0:K.map(G=>G.split(" ")[0]),N=(D=(C=(Z=O==null?void 0:O.data)==null?void 0:Z.return_chart)==null?void 0:C.y)==null?void 0:D.map(G=>G.name),L=["#FF6B6B","#4ECDC4","#45B7D1","#96CEB4","#FFCC5C","#FF6F69","#88D8B0","#6C88C4","#FFA07A","#98FB98","#87CEEB","#DDA0DD"].sort(()=>Math.random()-.5),fe=(u=(f=(h=O==null?void 0:O.data)==null?void 0:h.return_chart)==null?void 0:f.y)==null?void 0:u.map((G,ne)=>{var Qe;return(Qe=G==null?void 0:G.data)==null?void 0:Qe.map((vt,At)=>({value:[At,ne,Number(vt).toFixed(2)],itemStyle:{color:L[ne%L.length]}}))}).flat(),le={title:{left:"center"},tooltip:{show:!1,axisPointer:{show:!1}},grid3D:{viewControl:{projection:"orthographic",autoRotate:!0,distance:200,beta:45,alpha:25},boxWidth:70,boxHeight:70,boxDepth:200,light:{main:{intensity:1.2}},top:"0%",bottom:"10%"},xAxis3D:{type:"category",data:g==null?void 0:g.map(G=>G.split(" ")[0]),name:"",axisLabel:{interval:Math.floor((g==null?void 0:g.length)/10),rotate:45,margin:20,textStyle:{fontSize:10,color:Te}},nameTextStyle:{fontSize:14,margin:30}},yAxis3D:{type:"category",data:N,name:"",axisLabel:{color:Te}},zAxis3D:{type:"value",name:"",axisLabel:{color:Te}},dataZoom:[{type:"slider",xAxisIndex:0,start:0,end:100},{type:"inside",xAxisIndex:0,start:0,end:100}],series:[{type:"bar3D",data:fe,shading:"lambert",label:{show:!1}}]};l.setOption(le)}},Le=r("分组收益"),V=r([]),Ie=r({}),xe=async()=>{var O,Y;const{json:t,httpController:be}=await ua((o==null?void 0:o.taskId)||F());t.code==="200"&&(Ie.value=(O=t==null?void 0:t.data)==null?void 0:O.group_return_analysis,V.value=Object.keys((Y=t==null?void 0:t.data)==null?void 0:Y.group_return_analysis[0]))},We=r("最新数据"),U=r([]),M=r({}),b=r([{key:"时间",width:130},{key:"股票代码",width:100},{key:"名称",width:100},{key:"因子值",width:80}]),i=async()=>{var O,Y;const{json:t,httpController:be}=await fa((o==null?void 0:o.taskId)||F());t.code==="200"&&(M.value=(O=t==null?void 0:t.data)==null?void 0:O.last_date_top_factor,U.value=Object.keys((Y=t==null?void 0:t.data)==null?void 0:Y.last_date_top_factor[0]))};let P=null;const y=r(null),ae=r(""),z=r({}),_=async()=>{var Y,oe,te,ie,re,ce,de,pe,ve;const{json:t,httpController:be}=await ha((o==null?void 0:o.taskId)||F());if(t.code==="200"&&(ae.value=(oe=(Y=t==null?void 0:t.data)==null?void 0:Y.ic_decay_chart)==null?void 0:oe.title,console.log("还需处理-----------------",p((te=t==null?void 0:t.data)==null?void 0:te.ic_decay_chart,3)),z.value=(ie=t==null?void 0:t.data)==null?void 0:ie.ic_decay_chart,y.value)){if(P=et(y.value),Object.keys(z.value).length==0)return;const K=(ce=(re=z.value)==null?void 0:re.x[0])==null?void 0:ce.data,Z=(ve=(pe=(de=z.value)==null?void 0:de.y[0])==null?void 0:pe.data)==null?void 0:ve.map((C,D)=>({value:C,itemStyle:{color:C>0?"#ff0000":"#3498db"}}));var O={title:{},tooltip:{trigger:"axis",formatter:function(C){return C.map(D=>{let h=D.value;const f=D.color;if(Array.isArray(h)){const u=h.map(g=>g==null||isNaN(g)?"--":Number(g).toFixed(4));return`<span style="color:${f}">${D.seriesName}</span>: ${u[0]}, ${u[1]}`}return h=h==null||isNaN(h)?"--":Number(h).toFixed(4),`<span style="color:${f}">${D.seriesName}</span>: ${h}`}).join("<br/>")}},grid:{left:"3%",right:"4%",bottom:"15%",top:"15%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:0,start:0,end:100}],xAxis:[{type:"category",data:K,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(C,D){const h=K.length,f=40,u=P.getWidth(),g=Math.floor(u/f),N=Math.floor((h-1)/(g-1));return(h-1-C)%N===0}},show:!1},{type:"category",data:K,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(C,D){const h=K.length,f=40,u=P.getWidth(),g=Math.floor(u/f),N=Math.floor((h-1)/(g-1));return(h-1-C)%N===0}},position:"bottom"}],yAxis:[{type:"value",splitLine:{show:!0,lineStyle:{type:"dashed",color:Te}}}],series:[{name:"IC值",type:"bar",data:Z,label:{show:!1,position:"bottom",formatter:function(C){return C.value?C.value.toFixed(2):""}}},{name:"IC趋势",type:"line",smooth:!1,showSymbol:!1,data:z.value.y[0].data,lineStyle:{color:"#ff0000",width:0}}]};P.setOption(O)}};let v=null;const R=r(null),J=r(""),c=r({}),m=async()=>{var Y,oe,te,ie,re,ce,de,pe,ve;const{json:t,httpController:be}=await ga((o==null?void 0:o.taskId)||F());if(t.code==="200"&&(console.log("json",t),J.value=(oe=(Y=t==null?void 0:t.data)==null?void 0:Y.ic_den_chart)==null?void 0:oe.title,c.value=(te=t==null?void 0:t.data)==null?void 0:te.ic_den_chart,R.value)){if(v=et(R.value),Object.keys(c.value).length==0)return;var O={title:{},tooltip:{trigger:"axis",formatter:function(K){return K.map(Z=>{let C=Z.value;const D=Z.color;if(Array.isArray(C)){const h=C.map(f=>f==null||isNaN(f)?"--":Number(f).toFixed(4));return`<span style="color:${D}">${Z.seriesName}</span>: ${h[0]}, ${h[1]}`}return C=C==null||isNaN(C)?"--":Number(C).toFixed(4),`<span style="color:${D}">${Z.seriesName}</span>: ${C}`}).join("<br/>")}},legend:{data:["Histogram","Density Curve"],top:"0px",textStyle:{color:Te}},grid:{left:"3%",right:"4%",bottom:"15%",top:"15%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:0,start:0,end:100},{type:"inside",xAxisIndex:0,start:0}],xAxis:{type:"value",boundaryGap:!1,axisLine:{onZero:!1},splitLine:{show:!0,lineStyle:{type:"dashed",color:Te}}},yAxis:{type:"value",axisLine:{onZero:!1,show:!1},axisTick:{show:!1},position:"left",splitLine:{show:!0,lineStyle:{type:"dashed",color:Te}}},series:[{name:"Histogram",type:"bar",data:(ce=(re=(ie=c.value)==null?void 0:ie.y[0])==null?void 0:re.data)==null?void 0:ce.map((K,Z)=>{var C,D;return[(D=(C=c.value)==null?void 0:C.x[0])==null?void 0:D.data[Z],K]}),itemStyle:{color:"rgba(135, 206, 235, 0.6)"}},{name:"Density Curve",type:"line",smooth:!0,data:(ve=(pe=(de=c.value)==null?void 0:de.y[1])==null?void 0:pe.data)==null?void 0:ve.map((K,Z)=>{var C,D;return[(D=(C=c.value)==null?void 0:C.x[0])==null?void 0:D.data[Z],K]}),itemStyle:{color:"#1E90FF"},lineStyle:{width:2}}]};v.setOption(O)}};let S=null;const we=r(null),me=r(""),ke=r({}),Oe=async()=>{var Y,oe,te,ie,re,ce,de,pe,ve,K,Z,C,D;const{json:t,httpController:be}=await pa((o==null?void 0:o.taskId)||F());if(t.code==="200"&&(me.value=(oe=(Y=t==null?void 0:t.data)==null?void 0:Y.ic_seq_chart)==null?void 0:oe.title,ke.value=(te=t==null?void 0:t.data)==null?void 0:te.ic_seq_chart,console.log("echartsRefICSequenceData",ke.value),we.value)){if(S=et(we.value),Object.keys(ke.value).length==0)return;var O={title:{left:"center"},tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter:function(h){return h.map(f=>{let u=f.value;const g=f.color;if(Array.isArray(u)){const N=u.map(L=>L==null||isNaN(L)?"--":Number(L).toFixed(4));return`<span style="color:${g}">${f.seriesName}</span>: ${N[0]}, ${N[1]}`}return u=u==null||isNaN(u)?"--":Number(u).toFixed(4),`<span style="color:${g}">${f.seriesName}</span>: ${u}`}).join("<br/>")}},legend:{data:(re=(ie=ke.value)==null?void 0:ie.y)==null?void 0:re.map(h=>h.name),top:"0px",selected:{IC:!0,Cum_IC:!1},textStyle:{color:Te}},grid:{left:"3%",right:"4%",bottom:"2%",top:"15%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:[0,1],start:0,end:100}],xAxis:[{type:"category",data:(pe=(de=(ce=ke.value)==null?void 0:ce.x[0])==null?void 0:de.data)==null?void 0:pe.map(h=>h.split(" ")[0]),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(h,f){var le,G,ne;const u=(ne=(G=(le=ke.value)==null?void 0:le.x[0])==null?void 0:G.data)==null?void 0:ne.length,g=40,N=S.getWidth(),L=Math.floor(N/g),fe=Math.floor((u-1)/(L-1));return(u-1-h)%fe===0}},show:!1},{type:"category",data:(Z=(K=(ve=ke.value)==null?void 0:ve.x[0])==null?void 0:K.data)==null?void 0:Z.map(h=>h.split(" ")[0]),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(h,f){var le,G,ne;const u=(ne=(G=(le=ke.value)==null?void 0:le.x[0])==null?void 0:G.data)==null?void 0:ne.length,g=40,N=S.getWidth(),L=Math.floor(N/g),fe=Math.floor((u-1)/(L-1));return(u-1-h)%fe===0}},position:"bottom"}],yAxis:{type:"value",axisLabel:{interval:1e3},splitLine:{show:!0,lineStyle:{type:"dashed",color:Te}}},series:(D=(C=ke.value)==null?void 0:C.y)==null?void 0:D.map(h=>({name:h.name,data:h.data,type:h.name==="IC"?"bar":"line",itemStyle:{color:h.name==="IC"?"#3498db":"#e74c3c"},label:{show:!1,position:"top",formatter:function(f){return f.data.toFixed(3)}}}))};S.setOption(O)}};let Ue=null;const He=r(null),it=r(""),Ze=r({}),Re=async()=>{var Y,oe,te,ie,re,ce,de,pe,ve,K,Z,C,D;const{json:t,httpController:be}=await va((o==null?void 0:o.taskId)||F());if(t.code==="200"&&(it.value=(oe=(Y=t==null?void 0:t.data)==null?void 0:Y.ic_self_correlation_chart)==null?void 0:oe.title,Ze.value=(te=t==null?void 0:t.data)==null?void 0:te.ic_self_correlation_chart,He.value)){if(Ue=et(He.value),Object.keys(Ze.value).length==0)return;var O={title:{},tooltip:{trigger:"axis",formatter:function(h){return h.map(f=>{let u=f.value;const g=f.color;if(Array.isArray(u)){const N=u.map(L=>L==null||isNaN(L)?"--":Number(L).toFixed(4));return`<span style="color:${g}">${f.seriesName}</span>: ${N[0]}, ${N[1]}`}return u=u==null||isNaN(u)?"--":Number(u).toFixed(4),`<span style="color:${g}">${f.seriesName}</span>: ${u}`}).join("<br/>")}},legend:{data:["自相关系数","下限(95%置信区间)","上限(95%置信区间)"],top:"0px",textStyle:{color:Te}},grid:{left:"3%",right:"4%",bottom:"15%",top:"15%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:[0,1],start:0,end:100}],xAxis:[{type:"category",data:(re=(ie=Ze.value)==null?void 0:ie.x[0])==null?void 0:re.data,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(h,f){var le,G,ne;const u=(ne=(G=(le=Ze.value)==null?void 0:le.x[0])==null?void 0:G.data)==null?void 0:ne.length,g=40,N=Ue.getWidth(),L=Math.floor(N/g),fe=Math.floor((u-1)/(L-1));return(u-1-h)%fe===0}},show:!1},{type:"category",data:(de=(ce=Ze.value)==null?void 0:ce.x[0])==null?void 0:de.data,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(h,f){var le,G,ne;const u=(ne=(G=(le=Ze.value)==null?void 0:le.x[0])==null?void 0:G.data)==null?void 0:ne.length,g=40,N=Ue.getWidth(),L=Math.floor(N/g),fe=Math.floor((u-1)/(L-1));return(u-1-h)%fe===0}},position:"bottom"}],yAxis:{type:"value",min:-1,max:1,interval:.2,splitLine:{show:!0,lineStyle:{type:"dashed",color:Te}}},series:[{name:"自相关系数",type:"line",data:(ve=(pe=Ze.value)==null?void 0:pe.y[0])==null?void 0:ve.data,symbol:"circle",symbolSize:8,lineStyle:{width:2},itemStyle:{color:"#3498db"},label:{show:!1,position:"top",formatter:function(h){return h.value.toFixed(3)}}},{name:"下限(95%置信区间)",type:"line",data:(Z=(K=Ze.value)==null?void 0:K.y[1])==null?void 0:Z.data,lineStyle:{type:"dashed",width:1},itemStyle:{color:"#95a5a6"},symbol:"none"},{name:"上限(95%置信区间)",type:"line",data:(D=(C=Ze.value)==null?void 0:C.y[2])==null?void 0:D.data,lineStyle:{type:"dashed",width:1},itemStyle:{color:"#95a5a6"},symbol:"none"}]};Ue.setOption(O)}};let ue=null;const tt=r(null),Ve=r(""),Ke=r({}),at=async()=>{var Y,oe,te,ie,re,ce,de,pe,ve,K;const{json:t,httpController:be}=await Aa((o==null?void 0:o.taskId)||F());if(t.code==="200"&&(Ve.value=(oe=(Y=t==null?void 0:t.data)==null?void 0:Y.rank_ic_decay_chart)==null?void 0:oe.title,Ke.value=(te=t==null?void 0:t.data)==null?void 0:te.rank_ic_decay_chart,tt.value)){if(ue=et(tt.value),Object.keys(Ke.value).length==0)return;const Z=(re=(ie=Ke.value)==null?void 0:ie.x[0])==null?void 0:re.data,C=(pe=(de=(ce=Ke.value)==null?void 0:ce.y[0])==null?void 0:de.data)==null?void 0:pe.map((D,h)=>({value:D,itemStyle:{color:D>0?"#ff0000":"#3498db"}}));var O={title:{},tooltip:{trigger:"axis",formatter:function(D){return D.map(h=>{let f=h.value;const u=h.color;if(Array.isArray(f)){const g=f.map(N=>N==null||isNaN(N)?"--":Number(N).toFixed(4));return`<span style="color:${u}">${h.seriesName}</span>: ${g[0]}, ${g[1]}`}return f=f==null||isNaN(f)?"--":Number(f).toFixed(4),`<span style="color:${u}">${h.seriesName}</span>: ${f}`}).join("<br/>")}},grid:{left:"3%",right:"4%",bottom:"15%",top:"15%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:0,start:0,end:100}],xAxis:[{type:"category",data:Z,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(D,h){const f=Z.length,u=40,g=ue.getWidth(),N=Math.floor(g/u),L=Math.floor((f-1)/(N-1));return(f-1-D)%L===0}},show:!1},{type:"category",data:Z,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(D,h){const f=Z.length,u=40,g=ue.getWidth(),N=Math.floor(g/u),L=Math.floor((f-1)/(N-1));return(f-1-D)%L===0}},position:"bottom"}],yAxis:[{type:"value",splitLine:{show:!0,lineStyle:{type:"dashed",color:Te}}}],series:[{name:"IC值",type:"bar",data:C,label:{show:!1,position:"bottom",formatter:function(D){return D.value?D.value.toFixed(3):""}}},{name:"IC趋势",type:"line",smooth:!1,showSymbol:!1,data:(K=(ve=Ke.value)==null?void 0:ve.y[0])==null?void 0:K.data,lineStyle:{color:"#000000",width:0}}]};ue.setOption(O)}};let $e=null;const ct=r(null),a=r(""),s=r({}),n=async()=>{var Y,oe,te,ie,re,ce,de,pe,ve;const{json:t,httpController:be}=await ma((o==null?void 0:o.taskId)||F());if(t.code==="200"&&(a.value=(oe=(Y=t==null?void 0:t.data)==null?void 0:Y.rank_ic_den_chart)==null?void 0:oe.title,s.value=(te=t==null?void 0:t.data)==null?void 0:te.rank_ic_den_chart,ct.value)){if($e=et(ct.value),Object.keys(s.value).length==0)return;var O={title:{},tooltip:{trigger:"axis",formatter:function(K){return K.map(Z=>{let C=Z.value;const D=Z.color;if(Array.isArray(C)){const h=C.map(f=>f==null||isNaN(f)?"--":Number(f).toFixed(4));return`<span style="color:${D}">${Z.seriesName}</span>: ${h[0]}, ${h[1]}`}return C=C==null||isNaN(C)?"--":Number(C).toFixed(4),`<span style="color:${D}">${Z.seriesName}</span>: ${C}`}).join("<br/>")}},legend:{data:["Histogram","Density Curve"],top:"0px",textStyle:{color:Te}},grid:{left:"3%",right:"4%",bottom:"15%",top:"15%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:0,start:0,end:100},{type:"inside",xAxisIndex:0,zoomOnMouseWheel:!1,start:0}],xAxis:{type:"value",boundaryGap:!1,axisLine:{onZero:!1},axisLabel:{formatter:"{value}"},splitLine:{show:!0,lineStyle:{type:"dashed",color:Te}}},yAxis:{type:"value",axisLine:{onZero:!1,show:!1},axisTick:{show:!1},position:"left",splitLine:{show:!0,lineStyle:{type:"dashed",color:Te}}},series:[{name:"Histogram",type:"bar",data:(ce=(re=(ie=s.value)==null?void 0:ie.y[0])==null?void 0:re.data)==null?void 0:ce.map((K,Z)=>{var C,D;return[(D=(C=s.value)==null?void 0:C.x[0])==null?void 0:D.data[Z],K]}),itemStyle:{color:"rgba(135, 206, 235, 0.6)"}},{name:"Density Curve",type:"line",smooth:!0,data:(ve=(pe=(de=s.value)==null?void 0:de.y[1])==null?void 0:pe.data)==null?void 0:ve.map((K,Z)=>{var C,D;return[(D=(C=s.value)==null?void 0:C.x[0])==null?void 0:D.data[Z],K]}),itemStyle:{color:"#1E90FF"},lineStyle:{width:2}}]};$e.setOption(O)}};let d=null;const T=r(null),E=r(""),Q=r({}),se=async()=>{var Y,oe,te,ie,re,ce,de,pe,ve,K,Z,C,D;const{json:t,httpController:be}=await ya(o.taskId||F());if(t.code==="200"&&(E.value=(oe=(Y=t==null?void 0:t.data)==null?void 0:Y.rank_ic_seq_chart)==null?void 0:oe.title,Q.value=(te=t==null?void 0:t.data)==null?void 0:te.rank_ic_seq_chart,T.value)){if(d=et(T.value),Object.keys(Q.value).length==0)return;var O={title:{left:"center"},tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter:function(h){return h.map(f=>{let u=f.value;const g=f.color;if(Array.isArray(u)){const N=u.map(L=>L==null||isNaN(L)?"--":Number(L).toFixed(4));return`<span style="color:${g}">${f.seriesName}</span>: ${N[0]}, ${N[1]}`}return u=u==null||isNaN(u)?"--":Number(u).toFixed(4),`<span style="color:${g}">${f.seriesName}</span>: ${u}`}).join("<br/>")}},legend:{data:(re=(ie=Q.value)==null?void 0:ie.y)==null?void 0:re.map(h=>h.name),top:"0px",selected:{IC:!0,Cum_IC:!1},textStyle:{color:Te}},grid:{left:"3%",right:"4%",bottom:"1%",top:"15%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:[0,1],start:0,end:100}],xAxis:[{type:"category",data:(pe=(de=(ce=Q.value)==null?void 0:ce.x[0])==null?void 0:de.data)==null?void 0:pe.map(h=>h.split(" ")[0]),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(h,f){var le,G,ne;const u=(ne=(G=(le=Q.value)==null?void 0:le.x[0])==null?void 0:G.data)==null?void 0:ne.length,g=40,N=d.getWidth(),L=Math.floor(N/g),fe=Math.floor((u-1)/(L-1));return(u-1-h)%fe===0}},show:!1},{type:"category",data:(Z=(K=(ve=Q.value)==null?void 0:ve.x[0])==null?void 0:K.data)==null?void 0:Z.map(h=>h.split(" ")[0]),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(h,f){var le,G,ne;const u=(ne=(G=(le=Q.value)==null?void 0:le.x[0])==null?void 0:G.data)==null?void 0:ne.length,g=40,N=d.getWidth(),L=Math.floor(N/g),fe=Math.floor((u-1)/(L-1));return(u-1-h)%fe===0}},position:"bottom"}],yAxis:{type:"value",splitLine:{show:!0,lineStyle:{type:"dashed",color:Te}}},series:(D=(C=Q.value)==null?void 0:C.y)==null?void 0:D.map(h=>({name:h.name,data:h.data,type:h.name==="Rank_IC"?"bar":"line",itemStyle:{color:h.name==="IC"?"#3498db":"#e74c3c"},showSymbol:!1,label:{show:!1,position:"top",formatter:function(f){return f.data.toFixed(3)}}}))};d.setOption(O)}};let j=null;const ge=r(null),De=r(""),_e=r({}),Ne=async()=>{var Y,oe,te,ie,re,ce,de,pe,ve,K,Z,C,D;const{json:t,httpController:be}=await wa((o==null?void 0:o.taskId)||F());if(t.code==="200"&&(De.value=(oe=(Y=t==null?void 0:t.data)==null?void 0:Y.rank_ic_self_correlation_chart)==null?void 0:oe.title,_e.value=(te=t==null?void 0:t.data)==null?void 0:te.rank_ic_self_correlation_chart,ge.value)){if(j=et(ge.value),Object.keys(_e.value).length==0)return;var O={title:{},tooltip:{trigger:"axis",formatter:function(h){return h.map(f=>{let u=f.value;const g=f.color;if(Array.isArray(u)){const N=u.map(L=>L==null||isNaN(L)?"--":Number(L).toFixed(4));return`<span style="color:${g}">${f.seriesName}</span>: ${N[0]}, ${N[1]}`}return u=u==null||isNaN(u)?"--":Number(u).toFixed(4),`<span style="color:${g}">${f.seriesName}</span>: ${u}`}).join("<br/>")}},legend:{data:["自相关系数","下限(95%置信区间)","上限(95%置信区间)"],top:"0px",textStyle:{color:Te}},grid:{left:"3%",right:"4%",bottom:"10%",top:"10%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:0,start:0,end:100}],xAxis:[{type:"category",data:(re=(ie=_e.value)==null?void 0:ie.x[0])==null?void 0:re.data,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(h,f){var le,G,ne;const u=(ne=(G=(le=_e.value)==null?void 0:le.x[0])==null?void 0:G.data)==null?void 0:ne.length,g=40,N=j.getWidth(),L=Math.floor(N/g),fe=Math.floor((u-1)/(L-1));return(u-1-h)%fe===0}},show:!1},{type:"category",data:(de=(ce=_e.value)==null?void 0:ce.x[0])==null?void 0:de.data,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(h,f){var le,G,ne;const u=(ne=(G=(le=_e.value)==null?void 0:le.x[0])==null?void 0:G.data)==null?void 0:ne.length,g=40,N=j.getWidth(),L=Math.floor(N/g),fe=Math.floor((u-1)/(L-1));return(u-1-h)%fe===0}},position:"bottom"}],yAxis:{type:"value",min:-1,max:1,interval:.2,splitLine:{show:!0,lineStyle:{type:"dashed",color:Te}}},series:[{name:"自相关系数",type:"line",data:(ve=(pe=_e.value)==null?void 0:pe.y[0])==null?void 0:ve.data,symbol:"circle",symbolSize:8,lineStyle:{width:2},itemStyle:{color:"#3498db"},label:{show:!1,position:"top",formatter:function(h){return h.value.toFixed(3)}}},{name:"下限(95%置信区间)",type:"line",data:(Z=(K=_e.value)==null?void 0:K.y[1])==null?void 0:Z.data,lineStyle:{type:"dashed",width:1},itemStyle:{color:"#95a5a6"},symbol:"none"},{name:"上限(95%置信区间)",type:"line",data:(D=(C=_e.value)==null?void 0:C.y[2])==null?void 0:D.data,lineStyle:{type:"dashed",width:1},itemStyle:{color:"#95a5a6"},symbol:"none"}]};j.setOption(O)}};let W=null;const ye=r(null),ot=r(""),Se=r({}),Fe=async()=>{var O,Y,oe,te,ie,re,ce,de,pe,ve,K,Z,C,D,h,f;const{json:t,httpController:be}=await xa((o==null?void 0:o.taskId)||F());if(t.code==="200"&&((oe=(Y=(O=t==null?void 0:t.data)==null?void 0:O.return_chart)==null?void 0:Y.y)==null||oe.forEach(u=>{u.data=u.data.map(g=>k(g*100))}),ot.value=(ie=(te=t==null?void 0:t.data)==null?void 0:te.return_chart)==null?void 0:ie.title,Se.value=(re=t==null?void 0:t.data)==null?void 0:re.return_chart,ye.value)){if(W=et(ye.value),Object.keys(Se.value).length==0)return;const u={title:{},tooltip:{trigger:"axis",formatter:function(g){return g.map(N=>{let L=N.value;const fe=N.color;if(Array.isArray(L)){const le=L.map(G=>G==null||isNaN(G)?"--":Number(G).toFixed(4));return`<span style="color:${fe}">${N.seriesName}</span>: ${le[0]}, ${le[1]}`}return L=L==null||isNaN(L)?"--":Number(L).toFixed(4),`<span style="color:${fe}">${N.seriesName}</span>: ${L}%`}).join("<br/>")}},legend:{data:(de=(ce=Se.value)==null?void 0:ce.y)==null?void 0:de.map(g=>g.name),textStyle:{color:Te}},xAxis:[{type:"category",data:(K=(ve=(pe=Se.value)==null?void 0:pe.x[0])==null?void 0:ve.data)==null?void 0:K.map(g=>Nt(g)),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(g,N){var Qe,vt,At;const L=(At=(vt=(Qe=Se.value)==null?void 0:Qe.x[0])==null?void 0:vt.data)==null?void 0:At.length,fe=60,le=W.getWidth(),G=Math.floor(le/fe),ne=Math.floor((L-1)/(G-1));return(L-1-g)%ne===0}},show:!1},{type:"category",data:(D=(C=(Z=Se.value)==null?void 0:Z.x[0])==null?void 0:C.data)==null?void 0:D.map(g=>Nt(g)),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(g,N){var Qe,vt,At;const L=(At=(vt=(Qe=Se.value)==null?void 0:Qe.x[0])==null?void 0:vt.data)==null?void 0:At.length,fe=60,le=W.getWidth(),G=Math.floor(le/fe),ne=Math.floor((L-1)/(G-1));return(L-1-g)%ne===0}},position:"bottom"}],yAxis:{type:"value",axisLabel:{formatter:"{value}%"},splitLine:{show:!0,lineStyle:{type:"dashed",color:Te}}},grid:{left:"3%",right:"4%",bottom:"0%",top:"10%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:[0,1],start:0,end:100}],series:(f=(h=Se.value)==null?void 0:h.y)==null?void 0:f.map(g=>({name:g.name,type:"line",data:g.data,showSymbol:!1}))};W.setOption(u)}};let Ee=null;const dt=r(null),pt=r(""),ut=r({}),kt=async()=>{var Y,oe,te,ie,re,ce,de,pe,ve,K,Z,C,D;const{json:t,httpController:be}=await ba((o==null?void 0:o.taskId)||F());if(t.code==="200"&&(pt.value=(oe=(Y=t==null?void 0:t.data)==null?void 0:Y.excess_chart)==null?void 0:oe.title,ut.value=(te=t==null?void 0:t.data)==null?void 0:te.excess_chart,dt.value)){if(Ee=et(dt.value),Object.keys(ut.value).length==0)return;const h=["#1f77b4","#ff7f0e","#2ca02c","#d62728","#9467bd","#0099cc","#ff00ff"];var O={title:{},tooltip:{trigger:"axis",formatter:function(f){return f.map(u=>{let g=u.value;const N=u.color;if(Array.isArray(g)){const L=g.map(fe=>fe==null||isNaN(fe)?"--":Number(fe).toFixed(4));return`<span style="color:${N}">${u.seriesName}</span>: ${L[0]}, ${L[1]}`}return g=g==null||isNaN(g)?"--":Number(g).toFixed(4),`<span style="color:${N}">${u.seriesName}</span>: ${g}`}).join("<br/>")}},legend:{data:(re=(ie=ut.value)==null?void 0:ie.y)==null?void 0:re.map(f=>f.name),top:"0px",textStyle:{color:Te,fontSize:10}},grid:{left:"3%",right:"4%",bottom:"0%",top:"10%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:[0,1],start:0,end:100}],xAxis:[{type:"category",data:(pe=(de=(ce=ut.value)==null?void 0:ce.x[0])==null?void 0:de.data)==null?void 0:pe.map(f=>f.split(" ")[0]),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(f,u){var G,ne,Qe;const g=(Qe=(ne=(G=ut.value)==null?void 0:G.x[0])==null?void 0:ne.data)==null?void 0:Qe.length,N=40,L=Ee.getWidth(),fe=Math.floor(L/N),le=Math.floor((g-1)/(fe-1));return(g-1-f)%le===0}},show:!1},{type:"category",data:(Z=(K=(ve=ut.value)==null?void 0:ve.x[0])==null?void 0:K.data)==null?void 0:Z.map(f=>f.split(" ")[0]),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(f,u){var G,ne,Qe;const g=(Qe=(ne=(G=ut.value)==null?void 0:G.x[0])==null?void 0:ne.data)==null?void 0:Qe.length,N=40,L=Ee.getWidth(),fe=Math.floor(L/N),le=Math.floor((g-1)/(fe-1));return(g-1-f)%le===0}},position:"bottom"}],yAxis:{type:"value",axisLabel:{formatter:f=>(f*100).toFixed(1)+"%"}},series:(D=(C=ut.value)==null?void 0:C.y)==null?void 0:D.map((f,u)=>({name:f.name,type:"line",data:f.data,symbol:"circle",symbolSize:6,showSymbol:!1,lineStyle:{width:(f.name.includes("多空组合"),1)},itemStyle:{color:h[u]}}))};Ee.setOption(O)}};Xe(()=>o.factorId,async()=>{o.into&&(console.log("加载mc3"),await _t(),await B(),await xe(),await i(),await X(),await _(),await m(),await Oe(),await Re(),await at(),await n(),await se(),await Ne(),await Fe(),await kt(),await qe(),ft())}),Xe(()=>o.taskId,async()=>{o.into&&(console.log("加载mc2"),await qe(),ft(),await _t(),await B(),await qe(),await xe(),await i(),await X(),await _(),await m(),await Oe(),await Re(),await at(),await n(),await se(),await Ne(),await Fe(),await kt(),await qe(),setTimeout(()=>{ft()},1200))}),Xe(()=>o.into,async()=>{o.into&&(console.log("加载mc1"),await _t(),await B(),await xe(),await i(),await X(),await _(),await m(),await Oe(),await Re(),await at(),await n(),await se(),await Ne(),await Fe(),await kt(),await qe(),ft())}),Xe(()=>o.into,async()=>{await qe(),ft()});const ft=()=>{l==null||l.resize(),P==null||P.resize(),v==null||v.resize(),S==null||S.resize(),Ue==null||Ue.resize(),ue==null||ue.resize(),$e==null||$e.resize(),d==null||d.resize(),j==null||j.resize(),W==null||W.resize(),Ee==null||Ee.resize()},zt=()=>{l==null||l.dispose(),P==null||P.dispose(),v==null||v.dispose(),S==null||S.dispose(),Ue==null||Ue.dispose(),ue==null||ue.dispose(),$e==null||$e.dispose(),d==null||d.dispose(),j==null||j.dispose(),W==null||W.dispose(),Ee==null||Ee.dispose()},Rt=r([]),_t=async()=>{var O;const{json:t,httpController:be}=await ka((o==null?void 0:o.taskId)||F());Rt.value=(O=t==null?void 0:t.data)==null?void 0:O.factor_data_analysis},Dt=r(!0);rt(async()=>{try{o.into&&(await _t(),await B(),await xe(),await i(),await X(),await _(),await m(),await Oe(),await Re(),await at(),await n(),await se(),await Ne(),await Fe(),await kt(),Dt.value=!1,await qe(),ft())}catch{he.value,Dt.value=!0}window.addEventListener("resize",ft)}),wt(()=>{window.removeEventListener("resize",ft),zt()});const Nt=t=>t&&t.split(" ")[0];return(t,be)=>(A(),w("div",so,[e("div",lo,[e("div",no,[e("div",io,H(Ae.value),1),e("div",ro,[e("div",{ref_key:"echartsRefEarningsBigChart",ref:ye,style:{width:"100%",height:"400px"}},[Be(st,{visible:!0})],512)])]),e("div",co,[e("div",uo,[e("div",fo,[e("div",ho,[e("div",{class:"value",style:Pe({color:x(ee.value.return_ratio)})},H(ee.value.return_ratio),5),be[0]||(be[0]=e("div",{class:"label"},"因子收益",-1))]),e("div",go,[e("div",{class:"value",style:Pe({color:x(ee.value.sharpe_ratio)})},H(ee.value.sharpe_ratio),5),be[1]||(be[1]=e("div",{class:"label"},"夏普比率",-1))]),e("div",po,[e("div",{class:"value",style:Pe({color:x(ee.value.annualized_ratio)})},H(ee.value.annualized_ratio),5),be[2]||(be[2]=e("div",{class:"label"},"年化收益",-1))]),e("div",vo,[e("div",Ao,H(ee.value.maximum_drawdown),1),be[3]||(be[3]=e("div",{class:"label"},"最大回撤",-1))]),(A(!0),w(ze,null,Je(Rt.value,(O,Y)=>(A(),w("div",{key:Y,class:"data-item"},[(A(!0),w(ze,null,Je(Object.values(O),(oe,te)=>(A(),w(ze,{key:te},[te==1?(A(),w("div",mo,H(oe),1)):Me("",!0),te==0?(A(),w("div",yo,H(oe),1)):Me("",!0)],64))),128))]))),128))])]),e("div",wo,[e("div",xo,H(We.value),1),e("div",bo,[e("div",ko,[Be(Ut,{tableData:M.value,headers:U.value,isFixed:!0,"custom-header":b.value},null,8,["tableData","headers","custom-header"])])])]),e("div",_o,[e("div",Co,H(Le.value),1),e("div",Lo,[e("div",Eo,[Be(Ut,{tableData:Ie.value,headers:V.value,isFixed:!0,"column-width":110},null,8,["tableData","headers"])])])])]),e("div",Io,[e("div",So,[e("div",Ro,H(ae.value),1),e("div",Do,[e("div",{ref_key:"echartsRefSimDietrich",ref:y,style:{width:"100%",height:"400px"}},[Be(st,{visible:!0})],512)])]),e("div",No,[e("div",Bo,H(J.value),1),e("div",Mo,[e("div",{ref_key:"echartsRefICDistribution",ref:R,style:{width:"100%",height:"400px"}},[Be(st,{visible:!0})],512)])]),e("div",To,[e("div",Uo,H(me.value),1),e("div",Fo,[e("div",{ref_key:"echartsRefICSequence",ref:we,style:{width:"100%",height:"400px"}},[Be(st,{visible:!0})],512)])]),e("div",Wo,[e("div",Oo,H(it.value),1),e("div",Vo,[e("div",{ref_key:"echartsRefICCcorrelation",ref:He,style:{width:"100%",height:"400px"}},[Be(st,{visible:!0})],512)])]),e("div",Go,[e("div",qo,H(Ve.value),1),e("div",zo,[e("div",{ref_key:"echartsRefRankICDecay",ref:tt,style:{width:"100%",height:"400px"}},[Be(st,{visible:!0})],512)])]),e("div",Yo,[e("div",Po,H(a.value),1),e("div",Zo,[e("div",{ref_key:"echartsRefRankICDistribution",ref:ct,style:{width:"100%",height:"400px"}},[Be(st,{visible:!0})],512)])]),e("div",Qo,[e("div",Jo,H(E.value),1),e("div",Ho,[e("div",{ref_key:"echartsRefRankICSequence",ref:T,style:{width:"100%",height:"400px"}},[Be(st,{visible:!0})],512)])]),e("div",$o,[e("div",Xo,H(De.value),1),e("div",jo,[e("div",{ref_key:"echartsRefRankICCcorrelation",ref:ge,style:{width:"100%",height:"400px"}},[Be(st,{visible:!0})],512)])]),e("div",Ko,[e("div",es,H(ot.value),1),e("div",ts,[e("div",{ref_key:"echartsRefCurrentDeepAnalysis",ref:Ce,style:{width:"100%",height:"400px"}},[Be(st,{visible:!0})],512)])]),e("div",as,[e("div",os,H(pt.value),1),e("div",ss,[e("div",{ref_key:"echartsRefExcessReturnBigChart",ref:dt,style:{width:"100%",height:"400px"}},[Be(st,{visible:!0})],512)])])])])]))}}),ns=nt(ls,[["__scopeId","data-v-62815ad4"]]),is=gt({__name:"FactorCorrelation",props:{taskId:{type:String,requfactorIdired:!0}},setup($){const F=$,I=r(null);return rt(()=>{I.value&&_a(F.taskId).then(p=>{const k=p.json.data.factor_correlation_chart,o=Object.keys(k[0]),he=[...o].reverse(),x=k.flatMap((l,Ce)=>Object.entries(l).map(([Ae,q],X)=>{const Le=Number(q.toFixed(4));return[X,he.indexOf(o[Ce]),Le]})),ee={tooltip:{position:"top"},grid:{height:"50%",top:"10%"},xAxis:{type:"category",data:o,splitArea:{show:!0}},yAxis:{type:"category",data:he,splitArea:{show:!0}},visualMap:{min:0,max:1,calculable:!0,orient:"horizontal",left:"center",bottom:"15%"},series:[{name:"Factor Correlation",type:"heatmap",data:x,label:{show:!0},emphasis:{itemStyle:{shadowBlur:10,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};et(I.value).setOption(ee)})}),(p,k)=>(A(),w("div",{ref_key:"chartRef",ref:I,style:{width:"100%",height:"500px"}},null,512))}}),rs={class:"logs-header"},cs={class:"controls"},ds={class:"filter-group"},us=["value"],fs={class:"filter-group"},hs={width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},gs={key:0,d:"M3 4h13M3 8h9M3 12h5M19 20V8M15 16l4 4 4-4"},ps={key:1,d:"M3 4h13M3 8h9M3 12h5M19 20V8M15 12l4-4 4 4"},vs={class:"filter-group"},As={class:"logs-table"},ms={class:"log-message"},ys={key:0,class:"metadata-container"},ws=["onClick"],xs={class:"toggle-text"},bs={class:"log-metadata"},ks={class:"logs-footer"},_s={class:"pagination-info"},Cs={key:0},Ls={class:"pagination-controls"},Es=["disabled"],Is={width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",style:{transform:"rotate(270deg)"}},Ss=["disabled"],Rs={width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",style:{transform:"rotate(90deg)"}},Ds=gt({__name:"Logs",props:{isCollapsed:{type:Boolean,default:!0},logCount:{type:Number,default:0}},setup($){const F=r(null);Vt();const I=Lt(),p=$,k=r([]),o=r(!1),he=r("ALL"),x=r("asc"),ee=r(50),B=r(1),l=r([void 0]),Ce=r(void 0),Ae=r(!1),q=r(null),X=r(p.isCollapsed);Xe(()=>p.isCollapsed,c=>{c||(X.value=c)});const Le=r(new Set),V=r(50);let Ie=!1;const xe=c=>{var S;Ie=!0,document.body.style.cursor="ns-resize",document.body.style.userSelect="none";const m=(S=c.target.closest(".logs-container"))==null?void 0:S.getBoundingClientRect();m&&(m.height,c.clientY,document.addEventListener("mousemove",We),document.addEventListener("mouseup",U))},We=c=>{if(!Ie)return;const m=window.innerHeight,S=c.clientY,me=(m-S)/m*100;V.value=Math.min(Math.max(me,20),80)},U=()=>{Ie=!1,document.body.style.cursor="",document.body.style.userSelect="",document.removeEventListener("mousemove",We),document.removeEventListener("mouseup",U)},M=async(c,m=!1)=>{if(!I.workflow_run_id||!I.workflow_id)return k.value=[],m&&(B.value=1,l.value=[void 0],Ce.value=void 0,Ae.value=!1,q.value=null),!1;o.value=!0;try{const S=localStorage.getItem("token"),we={workflow_run_id:I.workflow_run_id,workflow_id:I.workflow_id,limit:Number(ee.value)};c&&(we.last_sequence=c),he.value!=="ALL"&&(we.log_level=he.value);const me=await Gt.get("http://localhost:8000/api/workflow/run/log",{params:we,headers:{uid:"0","quantflow-auth":"2","Content-Type":"application/json",Authorization:`${S}`}});if(me.status===200&&me.data&&me.data.data){const ke=me.data.data;return k.value=ke.logs||[],Ce.value=ke.next_sequence,Ae.value=ke.has_more??!1,q.value=ke.total_count,m&&(B.value=1,l.value=[void 0]),!0}else return console.error("Failed to fetch logs:",me),k.value=[],m&&(B.value=1,l.value=[void 0],Ce.value=void 0),Ae.value=!1,!1}catch(S){return console.error("Error fetching logs:",S),k.value=[],m&&(B.value=1,l.value=[void 0],Ce.value=void 0),Ae.value=!1,!1}finally{o.value=!1}},b=xt(()=>{let c=[...k.value];return he.value!=="ALL"&&(c=c.filter(m=>m.level===he.value)),c.sort((m,S)=>{const we=new Date(m.timestamp).getTime(),me=new Date(S.timestamp).getTime();return x.value==="asc"?we-me:me-we}),c}),i=xt(()=>["ALL","INFO","ERROR","WARNING","DEBUG"]),P=async()=>{if(Ae.value&&Ce.value!=null&&!o.value){const c=Ce.value;await M(c,!1)&&(B.value++,l.value.length<B.value?l.value.push(c):l.value[B.value-1]=c)}},y=async()=>{if(B.value>1&&!o.value){const c=B.value-1,m=l.value[c-1];await M(m,!1)&&(B.value=c)}},ae=()=>{x.value=x.value==="asc"?"desc":"asc",qe(()=>{R()})},z=c=>{if(!c)return"N/A";try{return new Date(c).toLocaleString()}catch{return c}},_=()=>{M(void 0,!0)},v=()=>{X.value=!X.value,X.value||(V.value=50)},R=()=>{const c=document.querySelector(".logs-content-area");c&&(x.value==="asc"?c.scrollTop=c.scrollHeight:c.scrollTop=0)};Xe(()=>k.value,()=>{qe(()=>{R()})},{deep:!0}),Xe(()=>p.logCount,async(c,m)=>{c!==m&&I.workflow_run_id&&await M(void 0,!0)},{immediate:!1}),rt(async()=>{await M(void 0,!0)});const J=(c,m)=>{Le.value.has(c)?Le.value.delete(c):Le.value.add(c),qe(()=>{var we,me,ke;const S=(we=m.target)==null?void 0:we.nextElementSibling;if(S){const Oe=S.getBoundingClientRect().height;console.log("height:",Oe),(ke=F.value)==null||ke.scrollTo({top:((me=F.value)==null?void 0:me.scrollHeight)+Oe,behavior:"smooth"})}})};return(c,m)=>(A(),w("div",{class:je(["logs-container",{collapsed:X.value,dragging:Ye(Ie)}]),style:Pe({height:V.value+"%"})},[e("div",{class:"drag-handle",onMousedown:xe},null,32),e("div",{class:"toggle-button",onClick:v},[(A(),w("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",style:Pe({transform:X.value?"rotate(0deg)":"rotate(180deg)"})},m[2]||(m[2]=[e("path",{d:"M7 14l5-5 5 5",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)]),4))]),e("div",rs,[m[7]||(m[7]=Jt('<div class="title" data-v-cd54df2d><svg width="24" height="24" viewBox="0 0 24 24" fill="none" data-v-cd54df2d><circle cx="12" cy="12" r="9" stroke="currentColor" stroke-width="2" data-v-cd54df2d></circle><path d="M8 9H16" stroke="currentColor" stroke-width="2" stroke-linecap="round" data-v-cd54df2d></path><path d="M8 12H14" stroke="currentColor" stroke-width="2" stroke-linecap="round" data-v-cd54df2d></path><path d="M8 15H12" stroke="currentColor" stroke-width="2" stroke-linecap="round" data-v-cd54df2d></path></svg><span data-v-cd54df2d>日志内容</span></div>',1)),e("div",cs,[e("div",ds,[m[3]||(m[3]=e("label",{for:"level-filter"},"级别:",-1)),lt(e("select",{id:"level-filter","onUpdate:modelValue":m[0]||(m[0]=S=>he.value=S),onChange:_},[(A(!0),w(ze,null,Je(i.value,S=>(A(),w("option",{key:S,value:S},H(S),9,us))),128))],544),[[Bt,he.value]])]),e("div",fs,[m[4]||(m[4]=e("label",{for:"sort-order"},"时间:",-1)),e("button",{onClick:ae,class:"sort-button"},[(A(),w("svg",hs,[x.value==="desc"?(A(),w("path",gs)):(A(),w("path",ps))]))])]),e("div",vs,[m[6]||(m[6]=e("label",{for:"limit-per-page"},"每页显示:",-1)),lt(e("select",{id:"limit-per-page","onUpdate:modelValue":m[1]||(m[1]=S=>ee.value=S),onChange:_},m[5]||(m[5]=[e("option",{value:"50"},"50",-1),e("option",{value:"100"},"100",-1)]),544),[[Bt,ee.value]])])])]),e("div",{class:"logs-content-area",ref_key:"logsContentArea",ref:F},[e("table",As,[m[9]||(m[9]=e("thead",null,[e("tr",null,[e("th",{class:"timestamp-col"},"时间"),e("th",{class:"level-col"},"级别"),e("th",{class:"message-col"},"消息")])],-1)),e("tbody",null,[(A(!0),w(ze,null,Je(b.value,S=>(A(),w("tr",{key:S._id,class:je(["log-entry",`log-level-${S.level.toLowerCase()}`])},[e("td",null,H(z(S.timestamp)),1),e("td",null,[e("span",{class:je(["log-level-badge",`log-level-badge-${S.level.toLowerCase()}`])},H(S.level),3)]),e("td",ms,[yt(H(S.message)+" ",1),S.error_detail&&Object.keys(S.error_detail).length>0?(A(),w("div",ys,[e("button",{class:"metadata-toggle",onClick:we=>J(S._id,we)},[e("span",xs,H(Le.value.has(S._id)?"收起详情":"查看详情"),1),(A(),w("svg",{width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",class:je({rotate:Le.value.has(S._id)})},m[8]||(m[8]=[e("path",{d:"M6 9l6 6 6-6",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)]),2))],8,ws),lt(e("div",bs,[e("pre",null,H(S.error_detail),1)],512),[[bt,Le.value.has(S._id)]])])):Me("",!0)])],2))),128))])])],512),e("div",ks,[e("div",_s,[e("span",null,"第 "+H(B.value)+" 页",1),q.value!==null?(A(),w("span",Cs," / 总 "+H(q.value)+" 条记录",1)):Me("",!0),e("span",null," (已加载 "+H(k.value.length)+" 条)",1)]),e("div",Ls,[e("button",{onClick:y,disabled:B.value===1||o.value,class:"pagination-button pagination-button-prev"},[(A(),w("svg",Is,m[10]||(m[10]=[e("path",{d:"M7 14l5-5 5 5",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)]))),m[11]||(m[11]=yt(" 上一页 "))],8,Es),e("button",{onClick:P,disabled:!Ae.value||o.value,class:"pagination-button pagination-button-next"},[m[13]||(m[13]=yt(" 下一页 ")),(A(),w("svg",Rs,m[12]||(m[12]=[e("path",{d:"M7 14l5-5 5 5",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)])))],8,Ss)])])],6))}}),Ns=nt(Ds,[["__scopeId","data-v-cd54df2d"]]),Bs={class:"alert"},Ms={class:"box"},Ts={__name:"alert",props:{show:{type:Boolean,default:!1}},emits:["close"],setup($,{emit:F}){const I=$,p=F,k=()=>{p("close")};return(o,he)=>lt((A(),w("div",Bs,[e("div",Ms,[e("img",{onClick:k,class:"close-btn",src:qt}),Ht(o.$slots,"default",{},void 0,!0)])],512)),[[bt,I.show]])}},It=nt(Ts,[["__scopeId","data-v-34167be4"]]),Us="/quantflow/assets/chat-dI4p2fsV.png",Fs="data:image/png;base64,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",Ws="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAAAXNSR0IArs4c6QAAAvdJREFUWEfNmEtoFEEQhv/qhXWJ2XiRgAHxICRHkdxEwT3N1qwQMYL4OKlnQRIUjHrxAT6C4NncfCAYY8DtmTkloHgL4tGABxEiiJeYGNaF7dIOPZKsmzibDZtpmNNUV31dXV1V3YSUDWqWJ4qi7cYYD4APYC+AHvdZVXPu+wRAK6Uiz/N+NmMjMZDWej8RXQPAAHIJjVQABCJyw/f990nm/BeoXC7vUUrdAnAKgJU3AN4R0SsRmTHGzHV0dFjPYGlpqUcp1UNE/SJyFMABAAqAAHhqjBkplUqf1wNbF0hrzUT0DMAOABUReZjJZEY9z/uWZLVRFHXXarUhIrrgvDovIid93w/Wmr8mUBiGQyJy165QRMYzmcxFz/O+JAGpl4miaHetVntARIPWw0R0qVgsjjbS1RDIwdy3riaiq8Vi8fZGQOrnhGF4RURu2q0nouFGUP8AuW16bSc59z7fDJhYh9b6hAsDEZEj9du3CsgF8AcbM0Q0slmeqV+Q1nqEiKyn5o0x+1YG+iqgIAgeAzhtY8b3/eOb6ZkGUC9cTD1h5jPx/79ALs/MAPillOrdaAAnXYQNdGPMLIBtItIf56m/QGEYTtjcYU+W7/uXkypuRU5rfceeOAATzHzM6loGcuXgO4CsUmpX0jzTCoyz222M+QqgqpTaacvMMlAQBJZuHMBbZj7UqqFm5gdB8AbAQQCDzPxyGSgMwzERObtWbmjGQLOyK3LeGDOfjz00BeAwgAIzTzertBX5IAisXWt/mpkLMdDHP8Wv1xjTVyqVbOS3bZTL5V6llLU/y8x9MdACgM5cLpcvFAqLbaOxrpma6qxUKtb+IjPnY6AfAPLVarVrYGDA/mzbmJyczGezWWt/gZm7UrtlqQvqRwDObcWx11oPE9E9AKuOfboSY+pKh8vW6SmuFih17Ycrsulp0CxQ6lpYt3X2LpaOJj+uF6m6BtVBpeOiGEOl6iodQ6XqsWFlD2LzlFLquogUt/Q5pr4xSs2DVbs6tt8B3hFDpWW0GwAAAABJRU5ErkJggg==",Os="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAYCAYAAADkgu3FAAAAAXNSR0IArs4c6QAAAydJREFUSEu11UtoE0EYB/Dv221VpAejXutBXT3I4kHBbhSxtvWFiA8Si9hLqRZfVeoDn1hFsT7RKooP9CIqqagItphNrIJQtW2QVEGxerB60UMDvjA7830ym6b2mUZLBkIg2Znf/L/5hkX4j2GFggeANGicV7g/3emY7oPJ57xh+xAL3sMSAAgPP1tctDedNf4JssIPj4DEnSARWDKob5RQ3bi8aNdgWNqQFbKPAcF2Fgpw04CbKvE5/ry4aEcqLC3IGwqeJAmV7uIC3iLBZAWwhFaWYCZgPvWiZN7WgbBBISscPM0CNiOhWiwIcdpHrD9Xi0tJpk76CZYw303KeKaptHBLf1hKyBt+WMMCNyWScL1HF8ti8exJ5EAUCECLS+NrB7V7crLuImsLVSlZ4Nnm9QUVvbH+IWbMC4fOIcF6tVMkfDBKc1bUL1r02wrYJhFE1aI6SaOxZEHbxJq64Z5h2beZcXFnSc+3VBRsBEROgn0hZrQa7AvgQHlnC9//9rPD99rvj6tJCpKdiXTIMhpL8tvU71MCgWEjv3hqmbQlIBlIwMXItoJ1SawnxIzesH2JJJSpMyGCe+KTx99SPt1J7sy6bpuSVOkQhBBGpGyBC6kx7WJzNnyPBYBgqfofBFxp2T13rcL+QlVVmjVr5mWQXMrqIcl3nM+ji7sjbiIFCYyqEgnuCSUx7ojdRIIVLN11rkbg6ZouKC8UPAMSKlQSFlw7Iid71eP8fNH7UK1rtilUIgEgRZYR2ZAoXfcxp6oh65tON1iCzz0zwTVdkDcY/EgCckHCrdxfsdW1fr+6HX2GCznoNgOR3i+kJvkCAf1965jrQFjMEtq7oBl1dh4wG+N+xG4MhCRKY5tIEEWJIOO6Eansmyi5O58voL8bP3YlEb8Z9ML2juRCDrrNQEJLCXWf++/QWZUoUTqG1ImGDiUTOZoR2TVw6YYGnbJNkIn2ZtYzDDlaVN1+Vs1QlalE1bbJEqPufZNahiE3kTqBTEIHbZOpE1LNUJ2h0k09+GiK7vArdY90SROaqgs/DPRWHVLXqdveZox5whL55dH82QB/3zmpwD/qfMD8dywLsQAAAABJRU5ErkJggg==",Vs={key:0,class:"factor-chat"},Gs={class:"container"},qs={class:"chat-header",style:{padding:"5px 0",display:"flex","justify-content":"space-between","align-items":"center"}},zs={class:"assistant-type-select",style:{"margin-right":"16px"}},Ys={style:{"margin-left":"4px","vertical-align":"middle"},width:"12",height:"12",viewBox:"0 0 24 24"},Ps={key:0,class:"dropdown-list",style:{position:"absolute",right:"0",top:"100%",background:"#222",border:"1px solid #333","border-radius":"4px","min-width":"100px","z-index":"100"}},Zs=["onClick"],Qs={class:"chat-item-content"},Js={class:"chat-item-header"},Hs={class:"chat-item-header-time"},$s={key:0,src:Us,alt:"chat",width:"16",height:"16",style:{"margin-left":"10px"}},Xs={class:"chat-item-content-text"},js=["innerHTML"],Ks={class:"chat-input"},el={key:0,src:Fs,alt:"checkbox",width:"16",height:"16",style:{"margin-right":"3px"}},tl={key:1,src:Ws,alt:"checkbox-unselected",width:"16",height:"16",style:{"margin-right":"3px"}},al={class:"dropdown-arrow",width:"14",height:"14",viewBox:"0 0 24 24",style:{"vertical-align":"middle",position:"relative",top:"2px"}},ol={key:0,class:"modes-select-dropdown"},sl=["onClick"],ll={key:0,width:"16",height:"16",viewBox:"0 0 24 24",style:{"margin-right":"6px"}},nl={key:0,src:Os,alt:"send",width:"14",height:"14"},il={key:1,xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"currentColor"},rl={__name:"Chat",props:{visible:{type:Boolean,default:!0}},emits:["update:visible","apply-code","close-chat"],setup($,{emit:F}){const I=Lt(),p=r(!0),k=r(""),o=r(""),he=r([{type:"receive",content:"你好，我是PandaAI Chat，很高兴认识你！"}]),x=r(null),ee=r(null),B=r(!1),l=r(null);Mt({start:!1,end:!1});const Ce=[{value:"code",label:"通用代码助手"},{value:"backtest",label:"回测代码助手"},{value:"factor",label:"因子构建代码助手"}];console.log("msms:",I.assistantTypeMap);const Ae=xt(()=>{const _=Ce.find(v=>v.value===I.assistantTypeMap.get(I.id));return _?_.label:""}),q=r(!1),X=r(null);function Le(_){I.assistantTypeMap.set(I.id,_),q.value=!1}const V=async()=>{if(o.value.trim()==""&&!B.value){ee.value.className="sendEmpty",await new Promise(_=>setTimeout(_,300)),ee.value.className="";return}if(B.value){B.value=!1,ee.value.disabled=!1,console.log("点击了暂停"),l.value&&l.value.abort(),he.value.pop(),he.value.pop();return}if(o.value.trim()){B.value=!0,ee.value.disabled=!0;const _={type:"send",content:o.value};he.value.push(_),o.value="",qe(()=>{x.value&&(x.value.scrollTop=x.value.scrollHeight),x.value&&x.value.dispatchEvent(new Event("refresh-highlight"))});const v={message:p.value?k.value+`
`+_.content:_.content};xe.value&&(v.session_id=xe.value),p.value&&(v.original_code=I.original_code),v.model=b.value,await We(v),B.value=!1,ee.value.disabled=!1,qe(()=>{x.value&&(x.value.scrollTop=x.value.scrollHeight),x.value&&x.value.dispatchEvent(new Event("refresh-highlight"))})}},Ie=r(null),xe=r(""),We=async _=>{clearInterval(Ie.value),l.value=new AbortController;const v=setTimeout(()=>l.value.abort(),1e3*50),R=Mt({type:"receive",content:"正在思考..."});he.value.push(R),qe(()=>{x.value&&(x.value.scrollTop=x.value.scrollHeight),x.value&&x.value.dispatchEvent(new Event("refresh-highlight"))});try{let J=!1;const c=r(".");console.log("------1------"),Ie.value=setInterval(()=>{c.value=c.value==="..."?".":c.value+".",R.content="正在处理"+c.value,J||(qe(()=>{x.value&&(x.value.scrollTop=x.value.scrollHeight),x.value&&x.value.dispatchEvent(new Event("refresh-highlight"))}),J=!0)},300);let m="";I.assistantTypeMap.get(I.id)==="backtest"?m="/pandaApi/quantflow/api/chat/backtest-assistant-stream":I.assistantTypeMap.get(I.id)==="factor"?m="/pandaApi/quantflow/api/chat/factor-assistant-stream":I.assistantTypeMap.get(I.id)==="code"&&(m="/pandaApi/quantflow/api/chat/code-assistant-stream");const S=localStorage.getItem("token"),we=await fetch(m,{method:"POST",headers:{"Content-Type":"application/json",uid:"0",Authorization:`${S}`},body:JSON.stringify(_),signal:l.value.signal});if(clearInterval(v),we.status!==200)throw new Error("服务器错误，请稍后再试！");const me=we.body.getReader(),ke=new TextDecoder;let Oe=!1,Ue=!0,He=0,it=1,Ze="",Re="";for(;!Oe;){const ue=new Promise((Ke,at)=>{setTimeout(()=>{at(new Error("Read-timed-out"))},36e5)}),tt=await Promise.race([me.read(),ue]);Ue&&(Ue=!1,R.content="",clearInterval(Ie.value));const{value:Ve}=tt;if(Oe=tt.done,Ve){const Ke=ke.decode(Ve,{stream:!0}),at=/^data:\s*(\{.*}$)/gm,$e=Ke.match(at);if($e){for(const ct of $e)try{const a=JSON.parse(ct.replace("data: ",""));if(console.log("---m---",a),console.log("---typeChange---",Re),Object.keys(a).includes("status")&&(Re&&Re==="reasoning"&&(R.content+="</pre>"),Re&&Re==="content"&&(R.content+="</pre>"),Re="status",R.content+=`<li style='font-size:12px;color:#ddd'>${a.status}</li>`),Object.keys(a).includes("reasoning")&&(Re!=="reasoning"&&(R.content+=`<pre class='reasoning' style="color:#aaa; white-space: pre-wrap;font-family: sans-serif;">`,Re="reasoning"),R.content+=a.reasoning),Object.keys(a).includes("content")&&(Re!=="content"&&(R.content+=`<pre class='content' style="color:#aaa; white-space: pre-wrap;font-family: sans-serif;">`,Re="content"),R.content+=a.content),Object.keys(a).includes("code")&&a.code){const s="codeId"+Date.now();Re!=="code"&&(R.content+="<div class='final_result_code'>",Re="code");const n=a.code;I.codeMap.set(s,n),R.content+=`<pre><code class='language-python'>${n}</code></pre>
                                 <span class='codeApply' data-id='${s}'>应用</span>
                                 <span class='codeCopy'  data-id='${s}'>复制</span>
                                 </div>`}Object.keys(a).includes("explanation")&&(R.content+=`<pre  style='font-size:12px;color:#ddd;white-space: pre-wrap;'>${ea.parse(a.explanation)}</pre>`),Object.keys(a).includes("session_id")&&(xe.value=a.session_id)}catch(a){console.log(a)}qe(()=>{x.value&&(x.value.scrollTop=x.value.scrollHeight),x.value&&x.value.dispatchEvent(new Event("refresh-highlight"))})}}R.content=R.content.replace(/<span class=['"]lod['"][^>]*><\/span>/g,""),R.content+="<span class='lod'></span>"}Oe&&(R.content=R.content.replace(/<span class=['"]lod['"][^>]*><\/span>/g,""),qe(()=>{const ue=document.querySelectorAll(".reasoning");for(let Ve=0;Ve<ue.length;Ve++)ue[Ve].style.display="none";const tt=document.querySelectorAll(".content");for(let Ve=0;Ve<tt.length;Ve++)tt[Ve].style.display="none"}))}catch(J){J.name==="AbortError"||J.name==="Read-timed-out"?R.content="网络超时，请稍后再试！":(console.log("error",(J==null?void 0:J.message)||J),R.content="网络超时，请稍后再试!"),clearInterval(Ie.value),B.value=!1,ee.value.disabled=!1}};rt(()=>{});function U(_){q.value&&X.value&&!X.value.contains(_.target)&&(q.value=!1)}$t(()=>{setTimeout(()=>{const _=document.querySelectorAll(".reasoning");for(let R=0;R<_.length;R++)_[R].style.display="block";const v=document.querySelectorAll(".content");for(let R=0;R<v.length;R++)v[R].style.display="none";x.value&&(x.value.scrollTop=x.value.scrollHeight)},60)}),rt(()=>{document.addEventListener("click",U)}),wt(()=>{document.removeEventListener("click",U)});const M=[{item:"DeepSeek-R1",value:"DeepSeek-R1"},{item:"DeepSeek-V3",value:"DeepSeek-V3"}],b=r(M[1].value),i=r(!1),P=r(null);function y(_){b.value=_,i.value=!1}function ae(_){i.value=!i.value,qe(()=>{i.value&&document.addEventListener("click",z,!0)})}function z(_){P.value&&!P.value.contains(_.target)&&(i.value=!1,document.removeEventListener("click",z,!0))}return Xe(i,_=>{_||document.removeEventListener("click",z,!0)}),(_,v)=>{var J;const R=Xt("highlight");return $.visible?(A(),w("div",Vs,[e("div",Gs,[e("div",qs,[v[4]||(v[4]=e("span",null,"AI 助手",-1)),e("div",zs,[e("div",{class:"dropdown",ref_key:"dropdownRef",ref:X,onClick:v[0]||(v[0]=c=>q.value=!q.value),style:{position:"relative",cursor:"pointer"}},[e("span",null,H(Ae.value),1),(A(),w("svg",Ys,v[3]||(v[3]=[e("path",{d:"M7 10l5 5 5-5z",fill:"#aaa"},null,-1)]))),q.value?(A(),w("div",Ps,[(A(),w(ze,null,Je(Ce,c=>e("div",{key:c.value,onClick:mt(m=>Le(c.value),["stop"]),style:{padding:"6px 16px","text-align":"center","text-indent":"0",color:"#fff",cursor:"pointer","white-space":"nowrap"}},H(c.label),9,Zs)),64))])):Me("",!0)],512)])]),lt((A(),w("div",{class:"chat-content",ref_key:"chatItemContent",ref:x},[(A(!0),w(ze,null,Je(he.value,(c,m)=>(A(),w("div",{class:je(["chat-item",{user:c.type==="send",ai:c.type==="receive"}]),key:m},[e("div",Qs,[e("div",Js,[e("span",Hs,[c.type==="receive"?(A(),w("img",$s)):Me("",!0),yt(" "+H(c.type==="send"?"你":"PandaAI 助手"),1)])]),e("div",Xs,[e("div",{innerHTML:c.content},null,8,js)])])],2))),128))])),[[R]]),e("div",Ks,[e("label",null,[lt(e("input",{type:"checkbox","onUpdate:modelValue":v[1]||(v[1]=c=>p.value=c)},null,512),[[bt,!1],[jt,p.value]]),p.value?(A(),w("img",el)):(A(),w("img",tl)),v[5]||(v[5]=e("span",null,"引用当前编辑器代码",-1))]),e("div",{class:"modes-select",ref_key:"modelDropdownRef",ref:P},[e("div",{class:"modes-select-label",onClick:ae},[e("span",null,H(((J=M.find(c=>c.value===b.value))==null?void 0:J.item)||"请选择模型"),1),(A(),w("svg",al,v[6]||(v[6]=[e("path",{d:"M7 10l5 5 5-5",stroke:"#52BBFE","stroke-width":"2",fill:"none","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)])))]),i.value?(A(),w("div",ol,[(A(),w(ze,null,Je(M,c=>e("div",{key:c.value,onClick:mt(m=>y(c.value),["stop"]),class:je(["modes-select-option",{selected:b.value===c.value}])},[b.value===c.value?(A(),w("svg",ll,v[7]||(v[7]=[e("path",{d:"M5 13l4 4L19 7",stroke:"#52BBFE","stroke-width":"2",fill:"none","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)]))):Me("",!0),e("span",null,H(c.item),1)],10,sl)),64))])):Me("",!0)],512),lt(e("textarea",{ref_key:"textarea",ref:ee,"onUpdate:modelValue":v[2]||(v[2]=c=>o.value=c),placeholder:"请在这里输入提问...",onKeydown:Kt(V,["enter"])},null,544),[[Ft,o.value]]),e("button",{onClick:V,class:je({send:!B.value})},[B.value?(A(),w("svg",il,v[8]||(v[8]=[e("rect",{x:"6",y:"6",width:"12",height:"12",rx:"1"},null,-1)]))):(A(),w("img",nl))],2)])])])):Me("",!0)}}},cl=nt(rl,[["__scopeId","data-v-d87fb98b"]]),dl={key:0,class:"alert-container"},ul={class:"alert-header"},fl={class:"alert-content"},hl={key:1,class:"alert-container"},gl={class:"alert-header"},pl={class:"alert-content"},vl={key:2,class:"alert-container"},Al={class:"alert-header"},ml={class:"alert-content"},yl={class:"content-container"},wl={class:"toolbar-container"},xl={class:"run-container"},bl={key:0,style:{"font-size":"12px",color:"#fff",position:"absolute",top:"-26px",transform:"scale(0.8)"}},kl={key:0,class:"drag-overlay"},_l={class:"monaco_ed"},Cl={class:"alert"},Ll={class:"box"},El={style:{width:"100%",height:"calc(100% - 100px)",display:"flex",position:"relative"}},Il={key:0,class:"diff-action-buttons"},Sl={class:"chartCode",style:{width:"35%",height:"100%","background-color":"#222",position:"relative"}},Rl=gt({__name:"Editor",setup($){const F=r(null),I=r(!1);let p=null,k=null,o=null;const he=r(null),x=Vt(),ee=ta(),B=aa(),l=Lt(),Ce=()=>{l.codeStatus=!1,l.id="",I.value=!1};Xe(()=>l.codeStatus,()=>{setTimeout(()=>{k==null||k.setValue(l.code.get(l.id)||""),o==null||o.setValue(l.code.get(l.id)||"")},30)}),wt(()=>{p&&p.dispose(),k&&k.dispose(),o&&o.dispose()});const Ae=r(!1);function q(){if(I.value&&p&&"getModel"in p&&k&&o){const a=p.getModel(),s=a&&a.modified?a.modified.getValue():"";setTimeout(()=>{l.code.set(l.id,s),k&&k.setValue(s)},60),Ae.value=!0,I.value=!1}}function X(){if(I.value&&p&&"getModel"in p&&k&&o){const a=p.getModel(),s=a&&a.original?a.original.getValue():"";a&&a.modified&&a.modified.setValue(s),Ae.value=!1,I.value=!1}}const Le=()=>{if(l.node.node){let a="";if(I.value&&p&&"getModel"in p){const n=p.getModel();Ae.value?a=n&&n.modified?n.modified.getValue():"":a=n&&n.original?n.original.getValue():""}else p&&"getValue"in p&&(a=p.getValue());l.node.node.properties[l.node.title]=a,l.code.set(l.id,a);const s=l.node.node.widgets.find(n=>n.name===l.node.title);s.value=a,console.log("workflowStore.node.widget",s),l.codeStatus=!1,Ae.value=!1,I.value=!1}};Xe(()=>ee.isBoxShow,async()=>{console.log("---2333-----"),ee.isBoxShow&&(await qe(),console.log("strategyAnalysisRef.value",he.value),he.value&&(console.log("---23334-----"),he.value.handleApply({workflow_id:ee.id,title:l.title,feature_tag:"backtest",locator:"backtest_id",last_run_id:l.workflow_run_id},!1)))});function V(a,s=!1){a||(a=window.location.href);const n={},d=a.indexOf("?");if(d!==-1){const T=a.indexOf("#",d),E=T!==-1?a.substring(d+1,T):a.substring(d+1);Ie(E,n)}if(s){const T=a.indexOf("#");if(T!==-1&&T<a.length-1){const E=a.substring(T+1),Q=E.indexOf("?");if(Q!==-1){const se=E.substring(Q+1);Ie(se,n)}}}return n}function Ie(a,s){if(!a)return;const n=a.split("&");for(const d of n){const[T,E]=d.split("=",2);T&&(s[decodeURIComponent(T)]=E!==void 0?decodeURIComponent(E):"")}}const xe=Wt(),We=r(!1),U=r(null);let M=r(null),b,i;const P=r(!1),y=r(0),ae=r(!1);let z=0,_=0,v=1;const R=()=>{_+=.03*v,_>=1?(_=1,v=-1):_<=0&&(_=0,v=1),i&&i.draw(!0,!0),requestAnimationFrame(R)};R();const J=()=>{const a=U.value,s=Math.max(window.devicePixelRatio,1),{width:n,height:d}=a.getBoundingClientRect();a.width=Math.round(n*s),a.height=Math.round(d*s),a.getContext("2d").scale(s,s),i.scale_offset=[1,1],i.ds.scale=1,i.dirty_canvas=!0,i.dirty_bgcanvas=!0,i==null||i.draw(!0,!0)},c=a=>{var s;if(P.value){a.preventDefault();return}(s=a.dataTransfer)!=null&&s.types.includes("Files")&&(a.preventDefault(),z++,ae.value=!0)},m=a=>{var n,d;if(P.value){a.preventDefault();return}(((n=a.dataTransfer)==null?void 0:n.types.includes("node-type"))||(d=a.dataTransfer)!=null&&d.types.includes("Files"))&&(a.preventDefault(),a.dataTransfer.dropEffect="copy")},S=a=>{var s;if(P.value){a.preventDefault();return}(s=a.dataTransfer)!=null&&s.types.includes("Files")&&(a.preventDefault(),z--,z<=0&&(ae.value=!1,z=0))},we=async a=>{var n,d,T,E,Q,se,j;if(l.owner==="*"){xe.warning("当前为模版,不支持导入覆盖，请先点击保存按钮创建个人工作流后，再进行导入!");return}if(P.value){if(a.preventDefault(),(n=a.dataTransfer)!=null&&n.types.includes("Files")){const ge=a.dataTransfer.files;ge!=null&&ge.length&&ge[0].name.toLowerCase().endsWith(".json")&&xe.warning("工作流运行时无法导入 JSON 文件")}return}if((d=a.dataTransfer)==null?void 0:d.types.includes("node-type")){const ge=(T=a.dataTransfer)==null?void 0:T.getData("node-type");if(console.log("nodeType:",ge),ge&&b){const De=(E=U.value)==null?void 0:E.getBoundingClientRect();if(De){const _e=a.clientX-De.left,Ne=a.clientY-De.top,W=i.ds.scale||1,ye=i.ds.offset||[0,0],ot=[(_e-ye[0])/W,(Ne-ye[1])/W],Se=Ge.createNode(ge);if(Se){Se.pos=ot;let Fe=0;for(const Ee of b.nodes)Ee.order&&Ee.order>Fe&&(Fe=Ee.order);Se.order=Fe+1e3,b.add(Se),b.setDirtyCanvas(!0),i.draw(!0,!0)}}}return}if((Q=a.dataTransfer)!=null&&Q.types.includes("Files")){a.preventDefault(),ae.value=!1,z=0;const ge=a.dataTransfer.files;if(!(ge!=null&&ge.length))return;const De=ge[0];if(!De.name.toLowerCase().endsWith(".json")){It("请拖入 JSON 文件");return}try{const _e=await De.text(),Ne=JSON.parse(_e).litegraph;if(!b||!i){console.error("Graph or GraphCanvas not initialized");return}b.clear();try{if(b.configure(Ne),b.nodes.forEach(W=>{W._fullTitle?W.title=W.truncateTitle(W._fullTitle):(W._fullTitle=W.title||"",W.title=W.truncateTitle(W.title||""))}),b.nodes.length>0){const W=b.nodes[0],ye=U.value.getBoundingClientRect(),ot=ye.width,Se=ye.height;i.setZoom(1);const Fe=W.pos[0],Ee=W.pos[1],dt=((se=W.size)==null?void 0:se[0])||0,pt=((j=W.size)==null?void 0:j[1])||0;i.ds.offset=[ot/2-(Fe+dt/2),Se/2-(Ee+pt/2)],i.setDirty(!0,!0)}else i.setZoom(1),i.ds.offset=[0,0];i.draw(!0,!0),console.log("Graph imported successfully")}catch(W){console.error("Error configuring graph:",W),It("导入失败：图形数据格式不正确")}}catch(_e){console.error("Import failed:",_e),It("导入失败，请检查文件格式是否正确")}}},me=()=>{const a=getComputedStyle(document.documentElement),s=d=>a.getPropertyValue(d).trim();Ge.NODE_DEFAULT_BGCOLOR=s("--node-bg"),Ge.NODE_TEXT_COLOR=s("--node-text"),Ge.BACKGROUND_IMAGE_COLOR=s("--node-bg"),Ge.GRID_COLOR=s("--grid-color");const n=document.documentElement.classList.contains("dark");Ge.LGraphCanvas.DEFAULT_BACKGROUND_IMAGE,Ge.LGraphCanvas.link_type_colors={tezheng:"#339966",mubiao:"#CC3366",hangqing:"#3366CC",out1:"#CC6600",out2:"#0ff",target1:"#0ff",target2:"#f56c6c"}},ke=r(!1);Xe(ke,a=>{a&&i.visible_nodes.forEach(s=>{s.inputs.forEach((n,d)=>{})})}),Xe(P,a=>{!b||!i||(a?(i.allow_dragnodes=!1,i.allow_reconnect_links=!1,i.allow_linking=!1,i.allow_select_nodes=!1,b.nodes.forEach(s=>{s.inputs&&s.inputs.forEach(n=>{n.locked=!0}),s.outputs&&s.outputs.forEach(n=>{n.locked=!0}),s.widgets&&s.widgets.forEach(n=>{n.disabled=!0,n.element&&(n.element.style.opacity="0.5",n.element.style.cursor="crosshair",n.element.disabled=!0),console.log("widget:",n)})})):(i.allow_dragnodes=!0,i.allow_dragcanvas=!0,i.allow_reconnect_links=!0,i.allow_linking=!0,i.allow_select_nodes=!0,b.nodes.forEach(s=>{s.inputs&&s.inputs.forEach(n=>{n.locked=!1}),s.outputs&&s.outputs.forEach(n=>{n.locked=!1}),s.widgets&&s.widgets.forEach(n=>{n.disabled=!1,n.element&&(n.element.style.opacity="1",n.element.style.cursor="auto",n.element.disabled=!1)})})),i.draw(!0,!0))});const Oe=async a=>{try{const n={uid:"0","quantflow-auth":"2","Content-Type":"application/json",Authorization:`${localStorage.getItem("token")}`},d=await fetch(`http://localhost:8000/api/workflow/query?workflow_id=${a}`,{headers:n});if(d.status===404)return xe.error("工作流不存在!"),null;if(d.ok){const T=await d.json();return l.title=T.name,l.owner=T.owner,console.log("获取workflow数据成功:",T),T.last_run_id&&(l.workflow_run_id=T.last_run_id,at(null,"auto")),T}else throw new Error(`请求失败: ${d.status} ${d.statusText}`)}catch(s){return console.error("获取workflow数据失败:",s),null}},Ue=a=>{try{if(!a){console.error("获取workflow数据为空:",a);return}const s=a.litegraph;if(!b||!i){console.error("Graph or GraphCanvas not initialized");return}b.clear();try{if(b.configure(s),b.nodes.forEach(n=>{n._fullTitle?n.title=n.truncateTitle(n._fullTitle):(n._fullTitle=n.title||"",n.title=n.truncateTitle(n.title||""))}),b.nodes.length>0){let n=1/0,d=-1/0,T=1/0,E=-1/0;b.nodes.forEach(Ee=>{var dt,pt;n=Math.min(n,Ee.pos[0]),d=Math.max(d,Ee.pos[0]+(((dt=Ee.size)==null?void 0:dt[0])||0)),T=Math.min(T,Ee.pos[1]),E=Math.max(E,Ee.pos[1]+(((pt=Ee.size)==null?void 0:pt[1])||0))});const Q=d-n,se=E-T,j=U.value.getBoundingClientRect(),ge=j.width,De=j.height,_e=100,Ne=(ge-_e*2)/Q,W=(De-_e*2)/se,ye=Math.min(Ne,W,1);i.setZoom(ye);const ot=(n+d)/2,Se=(T+E)/2,Fe=window.devicePixelRatio||1;i.ds.offset=[ge/2/Fe-ot*ye+350,De/2/Fe-Se*ye+100],i.setDirty(!0,!0)}else i.setZoom(1),i.ds.offset=[0,0];i.draw(!0,!0),console.log("Graph imported successfully")}catch(n){console.error("Error configuring graph:",n),console.log("api工作流数据导入失败：图形数据格式不正确",n)}}catch(s){console.error("Import failed:",s),console.log("api工作流数据导入失败，请检查文件格式是否正确",s)}};rt(async()=>{var T;(T=document.querySelector(".chartCode"))==null||T.addEventListener("click",E=>{try{const Q=E.target.closest(".codeApply"),se=E.target.closest(".codeCopy");if(Q){console.log("应用");const j=l.codeMap.get(Q.dataset.id);l.original_code=j,console.log("应用code:",j),I.value=!0,setTimeout(()=>{o==null||o.setValue(j)},400)}if(se){console.log("复制");const j=l.codeMap.get(se.dataset.id);console.log("复制code:",j),navigator.clipboard.writeText(j)}}catch(Q){console.log("e1:",Q)}}),I.value?(p=ht.createDiffEditor(F.value,{automaticLayout:!0,theme:"vs-dark",readOnly:!1,renderSideBySide:!1,ignoreTrimWhitespace:!1}),k.onDidChangeModelContent(()=>{console.log("内容改变2"),l.original_code=p.getValue()}),setTimeout(()=>{p&&l.code.get(l.id)&&(k=ht.createModel(l.code.get(l.id),"python"),o=ht.createModel(l.code.get(l.id),"python"),k&&o&&p.setModel({original:k,modified:o}))},60)):(p=ht.create(F.value,{automaticLayout:!0,theme:"vs-dark",readOnly:!1,language:"python",value:l.code.get(l.id)||""}),k=p.getModel(),p&&p.onDidChangeModelContent(()=>{console.log("内容改变：",p.getValue()),l.original_code=p.getValue(),l.code.set(l.id,p.getValue())}));const a=V(window.location.href);if(console.log("params:",a),console.log("params.workflow_id:",a.workflow_id),a.workflow_id===""||!a.workflow_id?(l.workflow_id="",l.owner="",l.workflow_run_id="",l.title=""):l.workflow_id=a.workflow_id,console.log("onMounted"),!U.value)return;me(),Ge.LGraphCanvas.DEFAULT_BACKGROUND_IMAGE="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAIAAAD/gAIDAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAQBJREFUeNrs1rEKwjAUhlETUkj3vP9rdmr1Ysammk2w5wdxuLgcMHyptfawuZX4pJSWZTnfnu/lnIe/jNNxHHGNn//HNbbv+4dr6V+11uF527arU7+u63qfa/bnmh8sWLBgwYJlqRf8MEptXPBXJXa37BSl3ixYsGDBMliwFLyCV/DeLIMFCxYsWLBMwSt4Be/NggXLYMGCBUvBK3iNruC9WbBgwYJlsGApeAWv4L1ZBgsWLFiwYJmCV/AK3psFC5bBggULloJX8BpdwXuzYMGCBctgwVLwCl7Be7MMFixYsGDBsu8FH1FaSmExVfAxBa/gvVmwYMGCZbBg/W4vAQYA5tRF9QYlv/QAAAAASUVORK5CYII=",Ge.pointerListenerAdd(U.value,"move",E=>{ke.value=i.pointer.dragStarted}),b=new Ge.LGraph,i=new Ge.LGraphCanvas(U.value,b),M.value=b,i.high_quality=!0,i.render_connections_border=!0,i.render_curved_connections=!0,i.links_render_mode=Ge.SPLINE_LINK,i.render_canvas_border=!1,i.render_connection_arrows=!1,i.render_curved_connections=!1,i.links_render_mode=Ge.SPLINE_LINK,i.render_shadows=!0,i.zoom_modify_alpha=!0;const s={canvas:i,graph:b,canvasContainer:U.value.parentElement};oa(s);const n=()=>{i&&me()},d=new MutationObserver(()=>{n()});if(d.observe(document.documentElement,{attributes:!0,attributeFilter:["class"]}),i.setZoom(Math.max(window.devicePixelRatio,1)),i.ds.offset=[0,0],i.allow_dragcanvas=!0,i.allow_dragnodes=!0,i.allow_interaction=!0,i.allow_searchbox=!1,i.drag_mode=!1,i.allow_reconnect_links=!0,i.allow_zoom=!0,i.onShowNodeCreationDialog=()=>!1,i.onDrawForeground=E=>{for(const Q of i.visible_nodes){const se=Q._pos,j=Q.size,ge=Ge.NODE_TITLE_HEIGHT,De=Q.flags.collapsed?ge:j[1]+ge,_e=6,Ne=15;if(E.strokeStyle="rgba(39,103,238,0)",E.lineWidth=0,Q.status==="success")E.strokeStyle="#33FF00",E.lineWidth=2;else if(Q.status==="failed")E.strokeStyle="#FF0033",E.lineWidth=7;else if(Q.status==="running"){const Ee=0+_*.6;E.strokeStyle=`rgba(51, 102, 255,${Ee})`,E.lineWidth=4}E.beginPath();const W=se[0]-_e,ye=se[1]-ge-_e;let ot=j[0];Q.flags.collapsed&&(E.save(),E.restore(),ot=Math.max(Q.width));const Se=ot+_e*2,Fe=De+_e*2;E.moveTo(W+Ne,ye),E.lineTo(W+Se-Ne,ye),E.quadraticCurveTo(W+Se,ye,W+Se,ye+Ne),E.lineTo(W+Se,ye+Fe-Ne),E.quadraticCurveTo(W+Se,ye+Fe,W+Se-Ne,ye+Fe),E.lineTo(W+Ne,ye+Fe),E.quadraticCurveTo(W,ye+Fe,W,ye+Fe-Ne),E.lineTo(W,ye+Ne),E.quadraticCurveTo(W,ye,W+Ne,ye),E.stroke()}},J(),window.addEventListener("resize",J),b.runStep(),n(),b.start(),We.value=!0,b.change(),i.draw(!0,!0),Ge.NODE_BOX_OUTLINE_COLOR="rgba(39,103,238,0.65)",Ge.WIDGET_BGCOLOR="rgba(16,18,19,0.8)",Ge.WIDGET_SECONDARY_TEXT_COLOR="#858585",Ge.NODE_WIDGET_HEIGHT=25,await new Promise(E=>setTimeout(E,500)),a.workflow_id){const E=await Oe(a.workflow_id);Ue(E)}wt(()=>{d.disconnect(),window.removeEventListener("resize",J),b&&(b.stop(),We.value=!1)}),window.addEventListener("keydown",He)}),wt(()=>{window.removeEventListener("keydown",He)});function He(a){(a.ctrlKey||a.metaKey)&&a.key==="s"&&(a.preventDefault(),Le())}const it=()=>{const a=b.serialize(),s={format_version:"2.0",name:l.title?l.title:`workflow-${new Date().toISOString().replace(/[:]/g,"-").slice(0,19)}`,description:"",litegraph:a,nodes:[],links:[]};l.title=s.name;const n=V(window.location.href);return n.workflow_id&&(s.id=n.workflow_id),console.log("graphData.nodes:",a.nodes),a.nodes&&Array.isArray(a.nodes)&&(s.nodes=a.nodes.map(d=>{const T=Object.keys(d.properties),E={};return T.forEach(Q=>{var ge;const se=(ge=d.inputs.find(De=>De.name===Q))==null?void 0:ge.fieldName,j=d.properties[Q];E[se]=j}),a.links.find(Q=>d.id===Q[1]),{uuid:d.flags.uuid,title:d.title||"",name:d.type||"",type:d.type||"",litegraph_id:d.id||0,positionX:d.pos?d.pos[0]:0,positionY:d.pos?d.pos[1]:0,width:d.size?d.size[0]:0,height:d.size?d.size[1]:0,static_input_data:E||{},output_db_id:null}})),a.links&&Array.isArray(a.links)&&(s.links=a.links.map(d=>{var Ne,W;const[T,E,Q,se,j,ge]=d,De=a.nodes.find(ye=>ye.id===E),_e=a.nodes.find(ye=>ye.id===se);return console.log("sourceNode,targetNode:",De,_e),{uuid:Ot(),litegraph_id:E,status:1,previous_node_uuid:De.flags.uuid,next_node_uuid:_e.flags.uuid,output_field_name:(Ne=_e.inputs[j])==null?void 0:Ne.fieldName,input_field_name:(W=De.outputs[Q])==null?void 0:W.fieldName}})),s},Ze=async()=>{if(it().nodes.length===0){xe.warning("当前工作区域为空，不可保存空白工作流！");return}const a=JSON.stringify(it());try{const s=localStorage.getItem("token"),n=await fetch("http://localhost:8000/api/workflow/save",{method:"POST",headers:{uid:"0","quantflow-auth":"2","Content-Type":"application/json",Authorization:`${s}`},body:a});if(n.ok){const d=await n.json();console.log("保存的workflow_id:",d);const T=d.data&&d.data.workflow_id;l.workflow_id=T;const E=new URL(window.location.href);if(E.searchParams.set("workflow_id",T),window.history.replaceState({},"",E),console.log("保存的的workflow_id:",T),l.owner==="*"&&xe.success("模版工作流克隆成功！正在运行中...",{timeout:2e3}),l.owner="",l.workflow_id){const j=new URL(window.location.href);j.searchParams.set("workflow_id",l.workflow_id),window.history.replaceState({},"",j)}const Q={uid:"0","quantflow-auth":"2","Content-Type":"application/json",Authorization:`${s}`},se=await fetch(`http://localhost:8000/api/workflow/query?workflow_id=${d.data.workflow_id}`,{headers:Q});if(se.status===404)return console.log("工作流不存在!"),null;if(se.ok){const j=await se.json();console.log("data:",j),l.title=j.name}return T}else throw new Error(`请求失败: ${n.status} ${n.statusText}`)}catch(s){return console.error("保存图表时出错:",s),xe.error("运行失败，请重试！"),null}},Re=r(0),ue=async a=>{try{Re.value=-1;const s=localStorage.getItem("token"),n=await fetch("http://localhost:8000/api/workflow/run",{method:"POST",headers:{uid:"0","quantflow-auth":"2","Content-Type":"application/json",Authorization:`${s}`},body:JSON.stringify({workflow_id:a})});if(console.log("运行模式:","LOCAL"),n.ok){const d=await n.json();console.log("运行结果:",d);const T=d.data&&d.data.workflow_run_id;return l.workflow_run_id=T,T}else throw new Error(`请求失败: ${n.status} ${n.statusText}`)}catch(s){return console.error("运行图表时出错:",s),xe.error("运行失败，请重试！"),null}},tt=async a=>{console.log("nodeStatus:",a),b.nodes.forEach(s=>{s.status=null}),a.success_node_ids&&a.success_node_ids.forEach(s=>{const n=b.nodes.find(d=>d.flags.uuid===s);n&&(n.status="success",i.draw(!0,!0))}),a.running_node_ids&&a.running_node_ids.forEach(s=>{const n=b.nodes.find(d=>d.flags.uuid===s);n&&(n.status="running",i.draw(!0,!0))}),a.failed_node_ids&&a.failed_node_ids.forEach(s=>{const n=b.nodes.find(d=>d.flags.uuid===s);n&&(n.status="failed",i.draw(!0,!0))})},Ve=r(!0),Ke=async(a,s,n,d,T)=>{try{Re.value++;const E=localStorage.getItem("token"),Q=await Gt.get("http://localhost:8000/api/workflow/run",{params:{workflow_run_id:a,last_log_id:s},headers:{uid:"0","quantflow-auth":"2","Content-Type":"application/json",Authorization:`${E}`}});if(Q.status===200){const se=Q.data;console.log("轮询结果:",se),tt(se.data);const j=se.data&&se.data.progress;y.value=j,j<100&&P.value?l.pollTimer=setTimeout(Ke.bind(this,a,s,n,d,T),d):(Ve.value=!0,T==="click"?xe.success("运行完成！"):T==="auto"&&xe.success("获取运行结果成功！"),console.log("轮询结束:",se.data),l.pollData={...se.data},P.value=!1,clearTimeout(l.pollTimer));const ge=se.data&&se.data.status;if(ge>=2&&(P.value=!1,y.value=j,clearTimeout(l.pollTimer),ge==3)){const De=se.data.last_error_message+`
`+se.data.last_error_stacktrace;T!=="auto"&&(Ve.value=!1),setTimeout(()=>{Ve.value=!0},300)}ge>2&&T==="auto"&&b.nodes.forEach(De=>{De.status=null})}else throw new Error(`请求失败: ${Q.status} ${Q.statusText}`)}catch(E){console.error("轮询工作流状态时出错:",E),P.value=!1,clearTimeout(l.pollTimer)}},at=async(a,s)=>{if(a&&ct(a),a){if(l.workflow_id=await Ze(),console.log("workflow_id:",l.workflow_id),l.workflow_id){const n=await ue(l.workflow_id);l.workflow_run_id=n,console.log("workflow_run_id11:",l.workflow_run_id),Ke(n,0,l.user_id,2e3,"click"),P.value=!0,y.value=0,l.pollData=null}}else Ke(l.workflow_run_id,0,l.user_id,2e3,"auto"),P.value=!0,y.value=0,l.pollData=null},$e=async a=>{a&&ct(a);try{P.value=!1,y.value=0,clearTimeout(l.pollTimer),await new Promise(d=>setTimeout(d,500)),b.nodes.forEach(d=>{d.status=null}),i.allow_dragnodes=!0,i.allow_dragcanvas=!0,i.allow_reconnect_links=!0,i.allow_linking=!0,i.allow_select_nodes=!0,b.nodes.forEach(d=>{d.inputs&&d.inputs.forEach(T=>{T.locked=!1}),d.outputs&&d.outputs.forEach(T=>{T.locked=!1}),d.widgets&&d.widgets.forEach(T=>{T.disabled=!1})}),b.setDirtyCanvas(!0),i.draw(!0,!0);const s=localStorage.getItem("token"),n=await fetch("http://localhost:8000/api/workflow/run/terminate",{method:"POST",headers:{uid:"0","quantflow-auth":"2","Content-Type":"application/json",Authorization:`${s}`},body:JSON.stringify({workflow_run_id:l.workflow_run_id})});if(n.ok)console.log("停止运行成功"),xe.success("停止运行成功");else throw new Error(`请求失败: ${n.status} ${n.statusText}`)}catch(s){console.error("停止运行时出错:",s),xe.error("停止运行失败")}},ct=a=>{const s=a.currentTarget;s.classList.remove("animating-ripple"),s.offsetWidth,s.classList.add("animating-ripple"),setTimeout(()=>{s.classList.remove("animating-ripple")},600)};return Xe(I,async a=>{try{p&&(p.dispose(),p=null),k&&(k.dispose(),k=null),o&&(o.dispose(),o=null)}catch(s){console.log(s)}try{a?(p=ht.createDiffEditor(F.value,{automaticLayout:!0,theme:"vs-dark",readOnly:!1,renderSideBySide:!1,ignoreTrimWhitespace:!1}),console.log("workflowStore.code:",l.code),k=ht.createModel(l.code.get(l.id)||"","python"),o=ht.createModel(l.code.get(l.id)||"","python"),p.setModel({original:k,modified:o})):(p=ht.create(F.value,{automaticLayout:!0,theme:"vs-dark",readOnly:!1,language:"python",value:l.code.get(l.id)||""}),k=p.getModel(),setTimeout(()=>{p.setValue(l.code.get(l.id))},30))}catch(s){console.log("e2：",s)}try{p&&p.onDidChangeModelContent(()=>{console.log("内容改成3"),l.original_code=p.getValue(),l.code.set(l.id,p.getValue())})}catch(s){console.log(s)}}),(a,s)=>(A(),w(ze,null,[Ye(x).isBoxShow?(A(),w("div",dl,[e("div",ul,[e("img",{class:"close-btn",src:Et,alt:"close",style:{width:"16px",height:"16px",cursor:"pointer"},onClick:s[0]||(s[0]=(...n)=>Ye(x).closeBox&&Ye(x).closeBox(...n))})]),e("div",fl,[Be(ns,{into:!0,factorId:"",taskId:Ye(x).id},null,8,["taskId"])])])):Me("",!0),Ye(ee).isBoxShow?(A(),w("div",hl,[e("div",gl,[e("img",{class:"close-btn",src:Et,alt:"close",style:{width:"16px",height:"16px",cursor:"pointer"},onClick:s[1]||(s[1]=(...n)=>Ye(ee).closeBox&&Ye(ee).closeBox(...n))})]),e("div",pl,[Be(Ca,{ref_key:"strategyAnalysisRef",ref:he},null,512)])])):Me("",!0),Ye(B).isBoxShow?(A(),w("div",vl,[e("div",Al,[e("img",{class:"close-btn",src:Et,alt:"close",style:{width:"16px",height:"16px",cursor:"pointer"},onClick:s[2]||(s[2]=(...n)=>Ye(B).closeBox&&Ye(B).closeBox(...n))})]),e("div",ml,[Be(is,{taskId:Ye(B).id},null,8,["taskId"])])])):Me("",!0),e("div",{class:"graph-editor litegraph .litegraph-editor",onDragenter:mt(c,["prevent"]),onDragover:mt(m,["prevent"]),onDragleave:mt(S,["prevent"]),onDrop:mt(we,["prevent"])},[e("div",yl,[e("canvas",{ref_key:"canvas",ref:U},null,512),e("div",wl,[Be($a,{"is-running":P.value,progress:y.value,graph:Ye(b),"is-graph-ready":We.value,canvas:Ye(i)},null,8,["is-running","progress","graph","is-graph-ready","canvas"])]),e("div",xl,[P.value?(A(),w("div",bl,H(y.value.toFixed(2))+"%",1)):Me("",!0),P.value?(A(),w("div",{key:1,class:"progress-bar",style:Pe({width:y.value+"%"})},null,4)):Me("",!0),P.value?(A(),w("button",{key:3,class:"run-button stop-run-button",onClick:s[4]||(s[4]=n=>$e(n))},s[6]||(s[6]=[e("svg",{class:"loading-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[e("path",{d:"M6 19h4V5H6v14zm8-14v14h4V5h-4z",fill:"currentColor"})],-1)]))):(A(),w("button",{key:2,class:"run-button",onClick:s[3]||(s[3]=n=>at(n))},s[5]||(s[5]=[e("svg",{class:"play-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[e("path",{d:"M8 5.14v14.72a1 1 0 001.5.86l11-7.36a1 1 0 000-1.72l-11-7.36a1 1 0 00-1.5.86z",fill:"currentColor"})],-1)])))]),Be(Ns,{logCount:Re.value,isCollapsed:Ve.value},null,8,["logCount","isCollapsed"]),Be(sa,{name:"fade"},{default:la(()=>[ae.value?(A(),w("div",kl,s[7]||(s[7]=[e("div",{class:"drag-message"},[e("svg",{class:"upload-icon",width:"48",height:"48",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[e("path",{d:"M12 3L20 10H16.5V19H7.5V10H4L12 3Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})]),e("p",null,"释放以导入 JSON 文件")],-1)]))):Me("",!0)]),_:1})])],32),lt(e("div",_l,[e("div",Cl,[e("div",Ll,[e("img",{onClick:Ce,class:"close-btn",src:qt}),s[10]||(s[10]=e("div",{class:"editorMonaco-head",style:{height:"40px","line-height":"40px","text-indent":"20px",color:"#fff","font-size":"14px"}},"编辑代码",-1)),e("div",El,[e("div",{class:"editorMonaco",style:{height:"100%",width:"65%",position:"relative"},ref_key:"editorDom",ref:F},[I.value?(A(),w("div",Il,[e("button",{class:"apply-btn",onClick:q},s[8]||(s[8]=[e("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[e("path",{d:"M4 8.5L7 11.5L12 5.5",stroke:"white","stroke-width":"1.3","stroke-linecap":"round","stroke-linejoin":"round"})],-1),yt(" 应用 ")])),e("button",{class:"reject-btn",onClick:X},s[9]||(s[9]=[e("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[e("path",{d:"M4 4L12 12M12 4L4 12",stroke:"white","stroke-width":"1.3","stroke-linecap":"round"})],-1),yt(" 拒绝 ")]))])):Me("",!0)],512),e("div",Sl,[(A(),St(ia,null,[Ye(l).codeStatus?(A(),St(na(cl),{key:Ye(l).id})):Me("",!0)],1024))])]),e("div",{class:"editorMonaco-foot",style:{height:"40px","padding-top":"15px"}},[e("div",{class:"content-btn active",onClick:Le,style:{display:"inline-flex",float:"left","margin-left":"20px"}},"保存")])])])],512),[[bt,Ye(l).codeStatus]])],64))}}),Dl=nt(Rl,[["__scopeId","data-v-d1632553"]]),Nl={class:"quantflow-container"},Bl={class:"content-container"},Ml={class:"aside-container"},Tl=gt({__name:"Quantflow",setup($){const F=r(!0),I=p=>{F.value=p};return(p,k)=>(A(),w("div",Nl,[Be(Yt),k[0]||(k[0]=yt()),e("div",Bl,[e("div",Ml,[Be(Pa,{onCategoriesLoadingState:I})]),Be(Dl)])]))}}),Gl=nt(Tl,[["__scopeId","data-v-36d9fec6"]]);export{Gl as default};
