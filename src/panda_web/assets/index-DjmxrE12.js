import{aa as h}from"./main-CZkS48P-.js";const m=async()=>{try{const n=localStorage.getItem("token"),a=await fetch("http://localhost:8000/api/plugins/all",{headers:{uid:"0","quantflow-auth":"2","Content-Type":"application/json",Authorization:`${n}`}});if(!a.ok)throw new Error(`API请求失败: ${a.status}`);const e=await a.json();if(e.code==401,e.code===0&&e.data){const o=p(e.data);return u(o),o}else return console.error("API返回错误:",e.message),[]}catch(n){return console.error("获取分类失败:",n),[]}},w=n=>"";function p(n){const a=(e,o)=>{if(e.object_type==="group"){const t={...e,id:`group-${e.name.replace(/\//g,"-").replace(/\s+/g,"-").toLowerCase()}`};return e.children&&e.children.length>0&&(t.children=e.children.map((r,s)=>a(r,s))),t}else{const t=e.display_name||e.name;return{...e,id:`node-${t.replace(/\s+/g,"-").toLowerCase()}-${o}`}}};return n.map(e=>a(e))}function u(n){const a=o=>{let t=[];return o.forEach(r=>{r.object_type==="plugin"?t.push(r):r.object_type==="group"&&r.children&&(t=t.concat(a(r.children)))}),t};a(n).forEach((o,t)=>{const r=o.group||"未分类",s=o.input_schema||{properties:{}},l=o.output_schema||{properties:{}},c=o.display_name||o.name,d=c.replace(/\s+/g,"-").toLowerCase(),i={id:o.id||`node-${d}-${t}`,title:o.display_name||"",name:c,type:o.name,icon:w(o.type||""),raw_data:{...o,nodeType:o.name,group:r,inputSchema:s,outputSchema:l}};o.hasOwnProperty("box_color")&&o.box_color&&(i.box_color=o.box_color),h(o.name,i)})}const g=async(n,a)=>{try{const e=localStorage.getItem("token"),o=new URLSearchParams({page:n.toString(),limit:a.toString()}).toString(),t=await fetch(`http://localhost:8000/api/workflow/all?${o}`,{method:"GET",headers:{uid:"0","quantflow-auth":"2","Content-Type":"application/json",Authorization:`${e}`}});if(!t.ok)throw new Error(`API请求失败: ${t.status}`);const r=await t.json();if(r.code==401){const s=new URL(window.location.href);s.pathname="/quantflow",window.location.href=window.location.origin+"/login?redirectUrl="+s.href}return r.code===0&&r.data?(console.log("responseData.data",r.data),r.data):(console.error("API返回错误:",r.message),[])}catch(e){return console.error("获取列表数据失败:",e),[]}},y=async n=>{try{const a=localStorage.getItem("token"),e=await fetch("http://localhost:8000/api/workflow/delete",{method:"DELETE",headers:{uid:"0","quantflow-auth":"2","Content-Type":"application/json",Authorization:`${a}`},body:JSON.stringify({workflow_id_list:n})});if(!e.ok)throw new Error(`API请求失败: ${e.status}`);const o=await e.json();if(o.code==401){const t=new URL(window.location.href);t.pathname="/quantflow",window.location.href=window.location.origin+"/login?redirectUrl="+t.href}return o.code===0?o:(console.error("API返回错误:",o.message),[])}catch(a){return console.error("删除数据失败:",a),[]}},k=async(n,a)=>{try{const e=localStorage.getItem("token"),o=await fetch("http://localhost:8000/api/workflow/save",{method:"POST",headers:{uid:"0","quantflow-auth":"2","Content-Type":"application/json",Authorization:`${e}`},body:JSON.stringify({id:n,name:a})});if(!o.ok)throw new Error(`API请求失败: ${o.status}`);const t=await o.json();if(t.code==401){const r=new URL(window.location.href);r.pathname="/quantflow",window.location.href=window.location.origin+"/login?redirectUrl="+r.href}return t.code===0&&t.data?t.data:(console.error("API返回错误:",t.message),[])}catch(e){return console.error("重命名数据失败:",e),[]}};export{m as a,y as d,g,k as r};
