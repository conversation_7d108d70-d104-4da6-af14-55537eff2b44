import{_ as C,a1 as I,u as D,B as H,r as i,H as M,f as Q,C as b,c as a,o as l,b as t,a as p,j as w,y as x,t as B,z as O,m as g,I as P}from"./main-B0DO4AYl.js";const N="/quantflow/assets/logo-DgM1YGv4.png",h="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAYpJREFUOE+Nk08og3EYx7/PZikkB8lhMjN/zjjISSmkiDLE1LYypbSTI3bAQTs42GFlTWlRyw5Ko5aDuGkXF4w2WS5iBxfv7H0fbbMxvbb3d/z9nufzeZ6n50dQeBrGt9eJ6F1VWbEV27V85NJIYT4aje4lJtoE40aSNCPxQ0sknascMOldYJArK+TnFHH384H1STFAN+VzMGH1p2I6e9yf7lMOmPEHwRj83bIoYkARQGc+rkcyFQOhvKBvgk8RoMkUdAOwyQw8WhKgN4VmQLT3z8A/igCY9LOXdpDkBKDO2nPhnCsmQc3mq1FBnTyPe3re0rettqtaURCHQGwHo6PEnoTJYA4fMTCc3rKsgqvySTnRH3HeD94gg/W6H6DTAhN/h1DREQkpSd2eiWix3gZANPYD+auWaUSS1iLe9uUMQGeO1mjKOARQp1zPMnUE7rQ6Ixwk5d8Mi6/VKkFwgdlUZHBJEDnvEhcr8E+Isp+pbf6lixlzBPQSoGXgE0AMzCcpCTsPnrr734Iv4ZCCIF+bZs4AAAAASUVORK5CYII=",R="/quantflow/assets/avatar-Db53fH93.png",U={class:"header-container"},Y={class:"left-container"},K={key:0,class:"workflow-title"},L={class:"right-container"},z={key:0,src:h,alt:"theme-switch",style:{"padding-left":"10px"}},F={key:1,src:h,alt:"theme-switch",style:{"padding-left":"50px"}},G={key:0,class:"login-container"},V={class:"dropdown-container"},j={key:0,class:"dropdown-menu"},q={__name:"Header",setup(J){const v=I().path,s=D(),c=H();i(null);const r=i(!1),u=i(!0),d=i(!1),n=i(""),m=i(null),A=()=>{if(s.owner==="*"){c.warning("模版名称不能修改！");return}n.value=s.title,d.value=!0,P(()=>{var e;(e=m.value)==null||e.focus()})},f=async()=>{if(n.value.trim()==="")c.warning("工作流名称不能为空"),n.value=s.title;else if(s.title=n.value.trim(),s.workflow_id)try{const e=localStorage.getItem("token");if((await fetch("http://localhost:8000/api/workflow/save",{method:"POST",headers:{uid:"0","quantflow-auth":"2","Content-Type":"application/json",Authorization:e},body:JSON.stringify({id:s.workflow_id,name:n.value.trim()})})).ok)c.success("工作流名称已保存");else throw new Error("保存失败")}catch(e){c.error("保存工作流名称失败"),console.error("保存工作流名称时出错:",e)}d.value=!1},_=()=>{n.value=s.title,d.value=!1},y=()=>{r.value=!r.value},S=()=>{localStorage.removeItem("token"),window.location.href=window.location.origin,r.value=!1},k=e=>{const o=document.querySelector(".dropdown-container");o&&!o.contains(e.target)&&(r.value=!1)};function E(){}return M(u,e=>{document.documentElement.classList.toggle("dark",e),localStorage.setItem("theme",e?"dark":"light")}),Q(()=>{document.documentElement.classList.toggle("dark",u.value),document.addEventListener("click",k)}),b(()=>{document.removeEventListener("click",k)}),(e,o)=>(l(),a("div",U,[t("div",Y,[o[2]||(o[2]=t("a",{href:"/quantflow/"},[t("img",{src:N,alt:"logo"})],-1)),w(v).includes("editor")&&w(s).title?(l(),a("span",K,[o[1]||(o[1]=t("span",{class:"workflow-title-text"},"当前工作流名称：",-1)),d.value?x((l(),a("input",{key:1,ref_key:"titleInput",ref:m,"onUpdate:modelValue":o[0]||(o[0]=T=>n.value=T),onBlur:f,onKeyup:[g(f,["enter"]),g(_,["esc"])],class:"title-input"},null,544)),[[O,n.value]]):(l(),a("span",{key:0,class:"workflow-title-text-sub",onClick:A},[t("span",null,B(w(s).title),1)]))])):p("",!0)]),t("div",L,[t("div",{style:{},class:"theme-switch",onClick:E},[u.value?(l(),a("img",z)):(l(),a("img",F))]),e.__APP_TYPE__==="CLOUD"?(l(),a("div",G,[t("div",V,[t("img",{src:R,class:"user-avatar",alt:"头像",onClick:y}),r.value?(l(),a("div",j,[t("div",{class:"menu-item"},[t("a",{onClick:S},"退出登录")])])):p("",!0)])])):p("",!0)])]))}},X=C(q,[["__scopeId","data-v-57241e4a"]]);export{X as H};
