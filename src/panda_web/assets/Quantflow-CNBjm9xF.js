import{H as Gt}from"./Header-c5efEo_J.js";import{d as dt,v as ht,g as zt,c as x,o as w,F as Me,k as $e,i as et,b as e,l as Vt,a as We,n as Oe,x as ft,t as P,_ as at,r as h,f as ut,y as ct,z as qt,A as yt,h as Re,u as bt,B as Bt,C as kt,D as Tt,G as Zt,e as Qt,H as He,I as je,J as Pe,K as Ut,j as Je,L as Pt,M as Nt,N as mt,O as Yt,P as Jt,Q as $t,R as Ie,S as Ht,T as Kt,w as Xt}from"./main-qylHbfG8.js";import{a as jt}from"./index-BCchqN9w.js";import{l as Ye,G as ea,a as ta,b as aa,c as oa,d as sa,e as ra,f as ia,g as na,h as la,i as ca,j as da,k as ua,m as fa,n as ha,o as ga,p as Wt,s as pa}from"./index-u7iyfjKw.js";const va="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAhNJREFUOE+Nkz2IU0EQx2c2TxJ5CHKihWcqfdl9hBgMAcVO7MUqCAqiJ2Ihp2IhaBPsUgg2lhaHV5gcaKdYaHFXqBA0CSRvkmcX5U5E1HydIewb80ISnsGPbLXLzv83s7P/QZhZ9Xr9NDOfBYBjALAXAL4xc0UIsdZut1fS6fQgKMHJoVQqLUYikccAcJiZHwkhXgohtgaDwQIiHkfE88y8bRhGxrKs9xPdCDAWv0HEcrfbvZBKpb7MVtZsNnd2Op0cIl7UWp+Mx+Nv/ZgRgIg2EPFHLBY7hYjerDh4JqIcAJwDAKWUauP4zSu9Xu/QnzLPwgqFQiiRSPg9yUsp7yIRrTHzZ9u2r/4rc/DOcZzLiHhTKSV9QBMRl6WUT+cFuK57UGv9QWu9xwf8DIVCJyzLej0voFwum+FwuOP3wQdsIuKSlPLZvADXdQ9orZue5+1Hx3FeAEDRtu078wJqtdoZIcQ9pdSi/wuXmDlrmqYVjUa354EQ0ToAvFNKXcdisbjDNM0aADy3bXv5fwAiugIAuX6/H08mkx9HRnJd98jQXRtD7z+oVCq3M5mM/huIiL4CwKpS6trUif6m0Wgc9TzvCTN/F0LcF0K8Mk1zs9Vq7WbmWD6fX89msx4R+S58CABLSqnV6TCNLb1raOkb42mMBarYQsS0lPLTOG4K+Q0QLLtarS4YhrEPANoT4cxM+JBbvwBBieor6fEbMAAAAABJRU5ErkJggg==",Aa="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAATCAYAAAByUDbMAAAAAXNSR0IArs4c6QAAAwxJREFUOE+VVE1oHVUU/r47M4lN/YmCiFAQ6qbiokVQBEGooC66svRnJQgqQle+/LRm5r3kZmbuKOlLpAlSqtiNq9KiLkRQlIggiLpQFyIIRUGQKJSYUqK58+7xnUde0Pja0Fnec+53v59zhrj+x7IsD5OcBHAAwLcicrrZbL4LQAZd4/ZDEaFz7giAVhfoGoAyjuPP67p+HECzC7gbQJFl2SWS/wHdArPWmiRJeiAArgKYzbLso+2POeee7jKbAXCbgnrvL1lrg/ZRQeI4PmqMaYUQ/uwym221Wh/fQH6vVBTFU8aYGZJ3hBCKuq4vqi/nSe4zxkxPTU19shPIAKZPqgoAP6k/V0nuTdP0j5sF6vdXVXW3iPzCqqqciBzT1NI0ff9mAYuiOGyMmSN5oReAc+6giLxO8kqn02lMT09/txOoc+6A3gFwJ8lGlmXLXFhY2DU2NrauQQwNDT2vAYjIB8PDw83JycnfB3h0j44LgEMhhJlOp/O2ptloNHZpAD8A+JJklmXZb9ba25MkaQJ4TkTaKysrZ5aWlv5eXFwcXltba5AcA3Dee19Za9ecc/eKiAPwqI7GrXEcT5F8sUv5jPd+3lr7V1EU95M8TfIhAMsADgL4xnt/0lp72Vp7S5Ik4yLyMoA367p+dWtoy7K8j+QcgEdE5JVms3lBJeZ5vj+Kov26TlmWfa9nZVkeJ/kagK+896estT/3htY59xmAi977c9bauqqqxzaN3dgM4+u+b3mePxxFkZo+pKanafqFtTaOouilKIqOMM/zB6Momie5R0TGN1dIH3lWgxaR5RDCG1EUnQDwBAD19h1ddl0tkvMi8qt6uSXTOXcIQFtELhtjxtM0/XFiYmL36OjoSWPMMyGE91ZXV+fa7fa1qqr2hRCUwF6S2vthT+a/o1fKcRyfIJnqEG5sbMxaa6/0e6y1d8VxrPt4XFl778+qNf36/35BWtBLSZJYAMdEpBoZGXlrfX39BZU46JEbgvWLRVE8QLJNUr36lOSEyr/edgxktr1ZZ0pnb6cV+wccUGjIODRIkAAAAABJRU5ErkJggg==",ma="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACYAAAAgCAYAAAB+ZAqzAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAJqADAAQAAAABAAAAIAAAAAALstX7AAAEgUlEQVRYCc2YWWiUVxTHO0uiVhP3qi20oZIggoK+1FICxaTSjULzkEJ9sNWHSMTRYGfLMk2zTGactmnGVhKopH2opXmwUE0LDaUREVREcCFqwIWCGtHWMhO7JDOZ/s7nvWEy5svWzCQXzpx7zzn3nP937j6WJ6ZYGhoanrFYLCWJROJN+GrcrFSu+pBdQ3YMfqS6uvrWVEJYJtuprq5und1udxP0Hfrax+kfA+B3sVgs6PP5Lo5jO0I9YWB+v/+loaEhD4HewIPud4r6EUCehu6KZ/QroBeolkCbREZBnei0Wq2BysrKk49EY//qAKZWAHodpx4MCpVRAn4UWRPDJMBMC8O9CZDS9y1IxzqBTAD+aNoxyXiETUdHh623t7dUOV2vlDHAfKu+umdEh3EafNxa+roxexcyhp/2eerBgoKCjtLS0niqC/0Vhry2tnYu8+c9ADkRPK+M/4IfstlsH3s8nt+UbEosEAg8G4/HP6DzDuhJ5eQ6IEPMw6+I/492bAATQNnZ2Q7mUAWgVirlAzp8QYbCpP2e7jAdnAwuJ5aDWLvwt1h8EquPWM0DAwNhAWhRX9GFrkAMKLfp0EyG2txud/SRKD2/wWAwhwyWAaqCCE+rKFeJvcXS2Nj4M4JXoDsA+hDEX4N4QBllhIXD4TnRaHQbAD8Cg4xYlwDrpzIf2lxVVfVrRpCYBKmvr9/McP6C+qGtqKjoZSqyc28pLi6OFBYWXuru7n5slaBPW5GMEXc7AT4nYwvgx03nGEPaypBKNtNWxpxjEhUApquSZdyC/v50opvQqkwOKADTuY9xEjzHUO0j5sT2sWRwUjfb+VEdxnGQfe1/7/z4usAqDExo5xdQqcXkrPxBnGb8rEwFJ20Azq7bRSrIWXcfSwXIRE7/DZZhkvOqion9JRO7LhVEJtqcQH7iy2r1gaHNjmAPoD6T4PBlmQAxWgxiP4VcqBVMc60I5IYppT4/P19O+RkpbBtlBG6U4ILJSvqMOxmNRE9Pj1ybZ6So2EM6uIVJvBdszUpwhXqAc/IbToGYNkonJ46dS+pWNXJrJBb1CiNbgCsHkB/ZQgXiJu39AGyn4/B1V+mmhcmNor+/fzsgXDjMU04jtL1s3AcNYCIEwKKsrKzdVPdAS0VGuQN9Mjg42IZ+Wm4a+FlAHJlPcmaugqT8DrUQ5wD6P0UwDEwaUkKh0HwytZNqcsc/yGALdMDr9T4wDCf509TUtJhs7Ibkw5eo7saHM5StTqfzYbLLx4BppaQ6Eom8Dxh5duUpeRR+EGrmtms8cJXclLH0V6CU1V4O5SjDGwDcn5ub2+5wOP5VshHMFJi2IrV2Ui/vQS9kTE743wA+xEPi05qamhvaNpnLI4eXkBMAsmnOU7rL8ABDdhi/Yy6ucYHpYDiycld7m7acEBu0HH4WOgPJ68oGXw6YF+EbIe3/HDI/l87v8TO8JaA3LbqjqcFoCobnNeSSQf23wWhmAuA4FGLYfxrNYCzZlIBphwBcRZZeZchkiGUl22lL5q6R3U6Xy9WnbSfL/wNxuVVL2KmRUgAAAABJRU5ErkJggg==",ya={class:"category-items"},wa=["onClick","onDragstart","onDragend","draggable"],_a={class:"node-icon"},xa={key:0,src:ma,alt:"icon",width:"11px",height:"11px"},ba={key:0,class:"item-count"},At=30,ka=dt({__name:"CategoryItem",props:{item:{type:Object,required:!0},level:{type:Number,default:0},selectedNodeId:{type:String,default:null},expandedCategories:{type:Array,default:()=>[]}},emits:["toggle-category","drag-start","drag-end"],setup(q,{emit:F}){const Y=q,T=F,U=ht(()=>{var R;return((R=Y.item.children)==null?void 0:R.filter(H=>H.object_type==="group"||H.object_type==="plugin"))||[]}),s=R=>R===U.value.length-1,p=R=>R<U.value.length-1,Z=R=>{if(!R.children)return 0;let H=0;for(const k of R.children)k.object_type==="plugin"?H++:k.object_type==="group"&&k.children&&(H+=Z(k));return H},te=()=>({left:`${Y.level*At+8}px`}),z=()=>({left:`${Y.level*At+9.5}px`,width:`${At-2}px`}),ae=()=>({marginLeft:`${Y.level*At+30}px`}),oe=R=>{T("toggle-category",R)},Le=(R,H)=>{R.dataTransfer&&(R.dataTransfer.setData("node-type",H.name),R.dataTransfer.effectAllowed="copy"),T("drag-start",H)};return(R,H)=>{const k=zt("category-item",!0);return w(),x("div",ya,[(w(!0),x(Me,null,$e(U.value,(o,ge)=>{var Ee,Fe;return w(),x("div",{key:o.id,class:et(["node-container",{"is-group":o.object_type==="group","is-plugin":o.object_type==="plugin","is-last":s(ge),"has-children":o.object_type==="group"&&((Ee=o.children)==null?void 0:Ee.length)}])},[e("div",{class:et(["vertical-line",{full:p(ge),partial:!p(ge)}]),style:Oe(te())},null,6),e("div",{class:"horizontal-line",style:Oe(z())},null,4),e("div",{class:et(o.object_type==="group"?"group-content":"plugin-content"),style:Oe(ae()),onClick:ft(N=>o.object_type==="group"?oe(o.id):null,["stop"]),onDragstart:N=>o.object_type==="plugin"?Le(N,o):null,onDragend:N=>o.object_type==="plugin"?R.$emit("drag-end"):null,draggable:o.object_type==="plugin"},[e("div",_a,[o.object_type==="group"?(w(),x("img",xa)):(w(),x("span",{key:1,class:et(["node-icon-indicator",{"is-selected":q.selectedNodeId===o.id}])},null,2))]),e("span",{class:"node-name",style:Oe({color:q.selectedNodeId===o.id?"#fff":"#858585"})},P(o.display_name||o.name),5),o.object_type==="group"&&((Fe=o.children)!=null&&Fe.length)?(w(),x("span",ba,P(Z(o)),1)):We("",!0)],46,wa),o.object_type==="group"&&q.expandedCategories.includes(o.id)?(w(),Vt(k,{key:0,item:o,level:q.level+1,"selected-node-id":q.selectedNodeId,"expanded-categories":q.expandedCategories,onToggleCategory:H[0]||(H[0]=N=>R.$emit("toggle-category",N)),onDragStart:H[1]||(H[1]=N=>R.$emit("drag-start",N)),onDragEnd:H[2]||(H[2]=N=>R.$emit("drag-end"))},null,8,["item","level","selected-node-id","expanded-categories"])):We("",!0)],2)}),128))])}}}),Ca=at(ka,[["__scopeId","data-v-ac1a95ba"]]),La={class:"side-nav-container"},Ea={class:"search-container"},Sa={class:"search-input"},Ra={class:"nav-content"},Na={key:0,class:"empty-state"},Da=["onClick"],Ia={class:"category-name"},Fa={key:0,class:"item-count"},Ba=dt({__name:"Aside",props:{defaultExpanded:{type:Boolean,default:!0}},emits:["categories-loading-state"],setup(q,{emit:F}){const Y=q,T=F,U=h(!0),s=h(!1),Z=h([]),te=h(null),z=h([]),ae=N=>{if(!Z.value.includes(N.id))return 0;let L=0;const ce=(pe,me)=>{pe.object_type==="group"&&Z.value.includes(pe.id)&&(L=Math.max(L,me),pe.children&&pe.children.forEach(v=>ce(v,me+1)))};return ce(N,1),L},oe=N=>{if(!N.children)return 0;let L=0;for(const ce of N.children)ce.object_type==="plugin"?L++:ce.object_type==="group"&&ce.children&&(L+=oe(ce));return L};ut(async()=>{T("categories-loading-state",!0),U.value=!0;let N=!1;try{const L=await jt();L&&L.length>0?(z.value=L,Z.value=L.map(ce=>ce.id),N=!0):L&&L.length===0&&(z.value=[],Z.value=[],N=!0)}catch(L){console.error("加载节点分类失败 (Aside.vue):",L)}finally{U.value=!1,N&&T("categories-loading-state",!1)}Y.defaultExpanded&&(Z.value.length===0&&!N||Z.value.length===0&&N&&z.value.length)});const Le=()=>{s.value=!s.value},R=N=>{const L=Z.value.indexOf(N);L===-1?Z.value.push(N):Z.value.splice(L,1)},H=N=>{N&&(te.value=N.id)},k=()=>{te.value=null},o=h(""),ge=ht(()=>{if(!o.value.trim())return z.value;const N=o.value.toLowerCase(),L=ce=>ce.map(pe=>{if(pe.object_type==="group"){const me=L(pe.children||[]);return me.length>0||pe.name.toLowerCase().includes(N)?{...pe,children:me}:null}else if(pe.object_type==="plugin")return pe.name.toLowerCase().includes(N)||pe.display_name&&pe.display_name.toLowerCase().includes(N)?pe:null;return null}).filter(pe=>pe!==null);return L(z.value)}),Ee=()=>{o.value.trim()},Fe=()=>{o.value=""};return(N,L)=>(w(),x("div",La,[e("div",{class:et(["side-nav",{collapsed:s.value}])},[s.value?We("",!0):(w(),x(Me,{key:0},[e("div",Ea,[e("div",Sa,[L[2]||(L[2]=e("img",{src:va,alt:"icon",class:"search-icon",width:"14px",height:"14px"},null,-1)),ct(e("input",{type:"text","onUpdate:modelValue":L[0]||(L[0]=ce=>o.value=ce),onInput:Ee,placeholder:"搜索目录"},null,544),[[qt,o.value]]),o.value?(w(),x("button",{key:0,class:"clear-button",onClick:Fe},L[1]||(L[1]=[e("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none"},[e("path",{d:"M18 6L6 18M6 6l12 12",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round"})],-1)]))):We("",!0)])]),e("div",Ra,[o.value&&ge.value.length===0?(w(),x("div",Na,[L[3]||(L[3]=e("svg",{class:"empty-icon",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none"},[e("path",{d:"M15.5 15.5L19 19",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round"}),e("path",{d:"M5 11a6 6 0 1012 0 6 6 0 00-12 0z",stroke:"currentColor","stroke-width":"2"})],-1)),L[4]||(L[4]=e("p",null,"未找到匹配的节点或目录",-1)),e("button",{class:"clear-search",onClick:Fe},"清除搜索")])):(w(!0),x(Me,{key:1},$e(o.value?ge.value:z.value,(ce,pe)=>{var me;return w(),x("div",{key:ce.id,class:"nav-category"},[e("div",{class:"category-header",onClick:v=>R(ce.id)},[L[5]||(L[5]=e("span",{class:"category-icon"},[e("img",{src:Aa,alt:"icon"})],-1)),e("span",Ia,P(ce.name),1),(me=ce.children)!=null&&me.length?(w(),x("span",Fa,P(oe(ce)),1)):We("",!0)],8,Da),ct(Re(Ca,{item:ce,level:0,"selected-node-id":te.value,"expanded-categories":Z.value,"max-expanded-level":ae(ce),onToggleCategory:R,onDragStart:H,onDragEnd:k},null,8,["item","selected-node-id","expanded-categories","max-expanded-level"]),[[yt,Z.value.includes(ce.id)]])])}),128))])],64))],2),e("div",{class:"fixed-fold_arrow",onClick:Le},[(w(),x("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",style:Oe({transform:s.value?"rotate(90deg)":"rotate(270deg)"})},L[6]||(L[6]=[e("path",{d:"M7 14l5-5 5 5",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)]),4))])]))}}),Ta=at(Ba,[["__scopeId","data-v-24627af2"]]),Dt="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAAYtJREFUWEfV2dFxgzAMAFDZS2SHbEG26D8cPx2nP9zx3y2aLbIDGcLOqcW9lGBLlgym+cQGPxQsccIAALRtezLGfFpr34dhuOGxWr++78/OuQ/v/ds4jpOZcV8AcAaAu7W2qYWccWg5AcDNe38xXdfhgeYpYlWQC1zgXE1kYFdkxDB57xuD1JrIGM5ae8FH7RtYC0nh0PULTCCncDcldzYH9wLcC8nFrQK3RubgosCtkLm4JLA0UoIjgaWQUhwLqEVqcGygFKnFZQFzkSVw2UAushROBKSQOO6cC69MofiIq9GfUpdTymJRmq+B73NqnDiCYeUI8vk+xZELFxFHkIFU49QRTDyPOFQfeOi/+NCbJJXnqqcZThLmzOGmtKxdnLNwztwUlg2ULCg5Z4llATULac5l5UHtAlTtptosyQiWwBEVh0zmUWBJnAa5CtwCJ0W+ALfESZD/p/WxR+SWOY6zZqr9Ru4wbrlKzaOQsQbmLjjOM7nWAt4VRyCvyyZ6FVwE+dNEx8Ejf4Z4AOhffFtCFuoVAAAAAElFTkSuQmCC",Mt="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAAYtJREFUWEfV2dFxgzAMAFDZS2SHbEG26D8cPx2nP9zx3y2aLbIDGcLOqcW9lGBLlgym+cQGPxQsccIAALRtezLGfFpr34dhuOGxWr++78/OuQ/v/ds4jpOZcV8AcAaAu7W2qYWccWg5AcDNe38xXdfhgeYpYlWQC1zgXE1kYFdkxDB57xuD1JrIGM5ae8FH7RtYC0nh0PULTCCncDcldzYH9wLcC8nFrQK3RubgosCtkLm4JLA0UoIjgaWQUhwLqEVqcGygFKnFZQFzkSVw2UAushROBKSQOO6cC69MofiIq9GfUpdTymJRmq+B73NqnDiCYeUI8vk+xZELFxFHkIFU49QRTDyPOFQfeOi/+NCbJJXnqqcZThLmzOGmtKxdnLNwztwUlg2ULCg5Z4llATULac5l5UHtAlTtptosyQiWwBEVh0zmUWBJnAa5CtwCJ0W+ALfESZD/p/WxR+SWOY6zZqr9Ru4wbrlKzaOQsQbmLjjOM7nWAt4VRyCvyyZ6FVwE+dNEx8Ejf4Z4AOhffFtCFuoVAAAAAElFTkSuQmCC",Ua=["onClick"],Wa=["src","alt"],Ma={class:"toolbar-text"},It=1.1,Oa=dt({__name:"Toolbar",props:{isRunning:{type:Boolean,default:!1},progress:{type:Number,default:0},graph:{type:Object,required:!0,default:null},isGraphReady:{type:Boolean,default:!1},canvas:{type:null,required:!1}},setup(q){function F(v,se=!1){v||(v=window.location.href);const O={},de=v.indexOf("?");if(de!==-1){const E=v.indexOf("#",de),ke=E!==-1?v.substring(de+1,E):v.substring(de+1);Y(ke,O)}if(se){const E=v.indexOf("#");if(E!==-1&&E<v.length-1){const ke=v.substring(E+1),A=ke.indexOf("?");if(A!==-1){const g=ke.substring(A+1);Y(g,O)}}}return O}function Y(v,se){if(!v)return;const O=v.split("&");for(const de of O){const[E,ke]=de.split("=",2);E&&(se[decodeURIComponent(E)]=ke!==void 0?decodeURIComponent(ke):"")}}const T=bt(),U=Bt(),s=q,p=[{id:1,src:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAUCAYAAACAl21KAAAAAXNSR0IArs4c6QAAAQ9JREFUOE/llKFOxEAURe+j00AQfAceFjTBEAwWAX9AQtDTZGdJp7UIVBUJCDyiCjQsfAC+P4Agm5DOzGWbVCC6Jaliw/P35L1zJyNpmt6IyCmAFQybQPJWrLWe5FhEXgFcAXgDcPcL84TkSETOAWzPl7hsQARwqLUurbXPAEqt9aQPNM+M28xunucHIYTyH4GeAGy0wvs0bZH8TJJkr9NRnucj7/2FiKz2UUh+NQ0nSTLtBA15RksCIilZlp21whdeGkKYVVV1XRRF3XmaMWZdKfUgIk1zC4fkzDl3ZIz5WBJHf6d+ksfOucchGyml9kXkvvlGXgDsDIH8yEzFGLMWx/Gm9z4aAouiyNd1/f4N+DkenOMZoNsAAAAASUVORK5CYII=",import.meta.url).href,alt:"save",text:"保存"},{id:2,src:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAATCAYAAACdkl3yAAAAAXNSR0IArs4c6QAAAU1JREFUOE+d1DFLw1AQB/D/JRFEcHZxdNbVVRB0dXJ1cxHXgi8v2DQ3pDjpVlc3v4E4SBc/gLoJIgT3Uhyq9M48oRpLapO8KSR3P97dvTxi5hUiYlVdB+Dhd4mIhNbam8K7mY/EzC5wU1X7RCRFaDwep1EU3VeFPgCcGWNOqiTMinE70vzjqTGmXREiAC7nz6oNMfMTgDtjzFERbALZfCixql6GYXg4wWpDrp4kSdr5YKIi1ggqw0qhTqez63nedoXm7wNYJaLrUoiZzwEczINUdYGIFgEMG5cWx/GG7/u3AN5FZKsRNI1Ya19qQ2WIa0FtiJmf8xMQuHLcTiZ9bALtBEHw0Gq13orDqA3999O+AshEJPV9/+caUVUdDAb9brc7nHcMvnuUJMkegCsiWppOEJFja+1FJcgFpWm6PBqN1qZvyCzLHnu93mcV6Au5hdZ8w5yWBAAAAABJRU5ErkJggg==",import.meta.url).href,alt:"export",text:"导出"},{id:3,src:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAATCAYAAACdkl3yAAAAAXNSR0IArs4c6QAAAUZJREFUOE+l1LFKxEAQBuB/JsgVclyv1wh2ng9gc1iIb2Bn63VayaWYJEo2G7hWbOxs9QkUBFtbrS19AgURTDJeCkO8M9yS23KZ/WY2mR2C4zLG7DNzAoBrRwoielFVIUcHcRzveJ7n1yFVZSIaAnhyhpoSWmtTAKdNULmvLtVaa8+mceezEFlrLwHsishWW4iSJLkioiMAkYiYNlCFTD9gHARBWa7Tql+tNVJmqqA0TW9V9QDAG4CbRWUURfEQhuHdb1wFWWvfAXRV9YuIvhdBAK5F5GQOMsZsMPMjgNU8z/eiKHp2wKqQP79/GWyuj9pi/zZkDctEZNPlik2djclkspZl2baI3C8FuRyuxzRW1ASNx+Nur9cbElH1PvM8Z2YuR8u68xgxxhwz88VsIlX9BHDoDI1Go5V+vz+YnZCdTufV9/2PHyuOmQ4RpZomAAAAAElFTkSuQmCC",import.meta.url).href,alt:"import",text:"导入"},{id:4,src:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAUCAYAAACAl21KAAAAAXNSR0IArs4c6QAAAWZJREFUOE/tlLFLAzEYxd872m46u+jg4iTuOtgiCqKbg7P+A06l0OTK0SSF4uQ/oLODowiKWAfdRXBwcNDFWbe23GdTpNij1x6dvSW8fMkv+R65R6R8zrkygP1E+VwpdTxqC9NA1to9ADuJ+qXW+mIkyBizS3IxDZhlXkTe6Jx7AbAEIAAQA5AsmxNrXvutGWOKQRDcxXFcCsOwNQUIU4OiKJrN5/NHnU7nJIqir6lBjUZjXURaJIvVavU+FWSMOfAthmF49tt+Ug/ZkQrqneg9g9a65EFJnfR1LMgDetcegP7qfxBgrb31nmitN/yY1Jk9qtfryx5Qq9We/ZjUmUGTfpORIGvtKskHAFtKqZtJEF93zm0CuBaRNa31Y/8dNZvNmW63+yEiTyLig6s9AVYgWSa5ksvl5iuVyvcg2Jxz2yJySnIuy41E5JPkoVLqyq8fSkgR8fm0ICKFcTCSbaXUO8lBdv0AzkZJXg8QSYAAAAAASUVORK5CYII=",import.meta.url).href,alt:"delete",text:"清空"},{id:5,src:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAATCAYAAACdkl3yAAAAAXNSR0IArs4c6QAAAq9JREFUOE+VlF2oTUEUx/9r9t6lxFXIffEV+Ug3H0+IB99SyleEkkIePAnlnNl3t9z9cepe3ngg9+ESykcSijwoIlKkKDc8E4mEOLP3LOZ2jo7jIPM2s9b81pq1/msILVaWZXOstWsAzAAwiog+isgLAFfzPL/MzF+br1HjQZZlU6y1R4hoIYBnInILwGsiaiOiDhGZD+BVURT7oig623j3JyhN0yUAzgHoF5HdYRjebY7a3d3dbow5AGAHgG6t9f66zwCoq6urw/O8OwAuGWO2MXO11ZPrZ2mabgLQB2C/1vqQO3cgStP0AYBPxpjFzJz/DdIA2wOgYq2d1tnZ+ZyyLFslIueLopgeRdHTZkiapldqkZ802phZBUHwWEQehWG4xWVzSkTawzBc1CITZ7cisi4MwwstguwSkSzP85HO8SWA41rryv+C4jierJR69qO2Mx3oCxHtLJfLJxvefw3AhNp+oms5gM8iYqy1m6MoeuRsPT09g6vV6icAKxzoHYCS1vpYU1fG1vYZgNMAnjiQ53m9pVLpvbMx84ggCN5aaxdQkiQPlVI3y+Wy68Jvgv1bjZIkmUdEt40x411GLuJ6Y8wkZrYtClrUin2xhe0ggJVa68lUK5hr7Tat9Ylm5yRJ1uZ5foOZPza1v933/edExE6UA8pO0/Twj9HYaK2d7cT1L0Eys+/7/nUiGmOM6XBDPABi5kFBENwEMFoptbpUKjmlt1zMPMz3/TNEtLQoillRFD2uj8jABWYeWncQkT6lVG+1Wr1fr1ulUhlnrd0gInuJ6BuA4SJyJs/z7c7nl2+kNncbRSQkoqkA3PC+AdAGYIiIfCCio8aYzPO8uUqpi3VYM+jnc+I4nkpEM5RSo5wYrbX9eZ7fa/wZ4jhe7mDuJ/gj6F8FbxDvMgBbvwM3PmOICE4pBgAAAABJRU5ErkJggg==",import.meta.url).href,alt:"zoom-in",text:"放大"},{id:6,src:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAUCAYAAACAl21KAAAAAXNSR0IArs4c6QAAAplJREFUOE+dlE1IVFEUx//nzXtJIIgyjQsLCVoJrVqEK7PaBC5Moq9FC11Glm2Sd+80N9+7zmQRFLWwiKKIomlTqwwypI9VC1u0CGpRWYhGjEmRvY+TV2ZkHM2P7vZ/zu+d87/3/wjLnGw2WzszM1PvOM7U6OjoRD6fj/5VTpWCUmqj4zgnmbmDiBrL9Gkiegzgiuu6I5V9C0Ba6+MAsgDGmfk2Mz+3LGs8iqIay7K2EtF+AK1ElC8UCl0DAwPTJeA8SGt9AUA3M58Ow/C8UurPUmt4nrfbsqxbzDxh23ZLb2/vlKmbA3me12VZ1lVmPiilzC/nm9F8399ERK8AvBFCtM2BcrlcTRRFH5j5upTy1EqQkt7X19ecSCReElG767qPyPf9EwAyYRg2KqV+rBZk6rTWZvqUEKKFtNbDzDwmpTyyFoip7e/vb2fmB0RUb0BfmTknpbxUAvm+3xDHcaoSnEgkAiHE29kN2GjFp/KZmZsN6Dczd0kp75QatdZjABqWmjAMw52ZTOaZ0Xp6etYnk8lfANoMyDSdE0JcLDUqpeqqqqrqKkFRFAVSyo9lk5vb+1Sa6AkRfXNd9/B/eLSPme8GQbDBTHTU+BYEwWal1Pe1wLTWD4mo2nXdXaSUqnYc5z2A+0KI7tWCtNatAJ4C2COEGJp72b7vHyIiY3anEOLmSrBsNrsljuMXs7kbEUIcmI9IEXaGiNLMfDYMQ08pZW5j0fF9fy8RXQNQG8dxUzqdfrcAVHypnQBMeA3kXmX6AZj0bwNwg5m3E9E6Zm6VUn5Z6n+UtG37GBF1zPrWBMAyH2HmcQBDAC5LKV8rpVK2bQ+XYItA5bsMDg46k5OTqTAMfyqlCpV7lsGiZUErmV7M24Y4jnf8BdONMaMxI63zAAAAAElFTkSuQmCC",import.meta.url).href,alt:"zoom-out",text:"缩小"},{id:7,src:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAAAXNSR0IArs4c6QAAAfxJREFUOE+Vk7+LE0EUx79v3d1OEEEQQYsTLI3VidilUVCwipwKeoV/gcURM7NxSGY2iURFQQ4CCt5ZCHYWNlqLhRaCFjbKKiJIIIU/kE12nswRwxKSvcuUM+995vu+7z3CNscYcyrLsn69Xn9bFEpFj8aYiwA2APxh5tNSylfz4ueCxpAHAL4D8ADsLYLNBDWbzcOe571n5sue551j5i/M/IuI1pIk2dfr9YbTymaClFKe7/sHpZRJHMePHUgIUVNKLSmlPs0qr9Ajl5AH7chsrfUdItovhLiQT5gH0lr3iKjvlLr4LUVxHN+11q74vl+uVqsf8iBjzCoR/ajVas+n7o8BeAngoRBijbTWt4joGhGtM/MEYq1NsyzbVEr9dYB2u31oNBqdJaK8HQ52lZnbZIx5DeA4gDcAfud+TQFcEUK49sMNJoDrUz6FAE4w8ztSSu0JguCFgwwGgzPdbjcPc4Cj1tqfURR9zkOUUmEQBE8BHAFQ3pL5H+ZKk1KuTnmx6dovpRT5e631fSIqO4hTPam30+nsTtP0QBRFH3fStUajUbLWflNK9SddK5qPhecoD2u1WkvW2ifD4fB8GIZ6vCLPXGeTJFledEU2iOgkgK8A3AgsM/NtKWVjoRWpVCq7SqXSIyK65BKZ+cY8yLYejWH3ACRSyptFXv4DBbYHOWLuLHQAAAAASUVORK5CYII=",import.meta.url).href,alt:"move",text:"移动"},{id:8,src:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAATCAYAAAByUDbMAAAAAXNSR0IArs4c6QAAAldJREFUOE+tlDtoFFEUhv+zswMGk3WDiFoIoo0YhMVGhVhEURGNqLC9KYQo2CQI456bcLOZGbZSUTE++qCFRiRNQCtT+EC0EbERH51aaBpBduZ377IJmxCXjXrLuf/55p7HfwQtDkkZGxtb53nemiRJvlprv7fSy3KXcRwfIzkA4ACA1U2a9wCmPM+7EgTBp6Wxi2BhGG4CMCkivSS/iMg0ybciMiciG0juB7CX5E8Aw8aYG83ABVilUumpVquPRKQrTVNNkmTCWvtr6d/L5XKP53lXAfQBuKmqg9baHMl8HRbH8do0TV+ISCeAg6r6ulVtisWiVygULgE4R7IsIq4cWodFUXQNwKCI9JVKpSetQPN31tqM7/vTAA43vu2TKIrWA/hMctIYc6odkNO41LLZ7IyI7F6AjY+Pn85kMrdI7jLGPG8HZq1d5fv+XQB75vUkT0oYhrdFpKiq3QDYDuxPGpema/8WY8z2fwG5WAe7B6Cgqlv/B+yy62RHR0f30NCQG8a/Pq5m/SLyUEROlEqlByslubFK0/TNyMjIhDQ68wHAR1XdtRJYGIa9IuLm0qhqNO+AMySvk7xgjKm0A7TW5n3ff0bSzdu2IAh+1GFu1cRxPAWgn+R5Y8zFVsAoijbWmuZKshPAUVWdqXezyR6dvu/fAXCkthkeO89Vq9VZa23apMlns1nnEm34eEBVXUz9LFpBDb8NkyyJSL6xht4BmKttCPeaHTUr+wCeJklydnR09FVzBssuxyAIunO53PE0TQ+JyGYAXQC+1crwkuR9Y8zscmX4DWS98ucU6bk+AAAAAElFTkSuQmCC",import.meta.url).href,alt:"refresh",text:"刷新"}],Z=ht(()=>s.isGraphReady?p:p.filter(v=>v.id<=4)),te=h(null),z=h(!1),ae=v=>!!((v===3||v===4)&&s.isRunning);function oe(v,se){if(ae(v)){v===3?U.warning("工作流运行时无法导入 JSON 文件"):v===4&&U.warning("工作流运行时无法删除节点");return}switch(v){case 1:R();break;case 2:k();break;case 3:o();break;case 4:Ee();break;case 5:Fe();break;case 6:N();break;case 7:L();break;case 8:ce();break;default:console.warn(`No action defined for id: ${v}`)}}const Le=()=>{const se=(s.graph.graph||s.graph).serialize(),O={format_version:"2.0",name:T.title?T.title:`workflow-${new Date().toISOString().replace(/[:]/g,"-").slice(0,19)}`,description:"",litegraph:se,nodes:[],links:[]};T.title=O.name;const de=F(window.location.href);return de.workflow_id&&(O.id=de.workflow_id),console.log("graphData.nodes:",se.nodes),se.nodes&&Array.isArray(se.nodes)&&(O.nodes=se.nodes.map(E=>{const ke=Object.keys(E.properties),A={};return ke.forEach(g=>{var we;const C=(we=E.inputs.find(Ne=>Ne.name===g))==null?void 0:we.fieldName,ve=E.properties[g];A[C]=ve}),se.links.find(g=>E.id===g[1]),{uuid:E.flags.uuid,title:E.title||"",name:E.type||"",type:E.type||"",litegraph_id:E.id||0,positionX:E.pos?E.pos[0]:0,positionY:E.pos?E.pos[1]:0,width:E.size?E.size[0]:0,height:E.size?E.size[1]:0,static_input_data:A||{},output_db_id:null}})),se.links&&Array.isArray(se.links)&&(O.links=se.links.map(E=>{var Ge,De;const[ke,A,g,C,ve,we]=E,Ne=se.nodes.find(Qe=>Qe.id===A),Be=se.nodes.find(Qe=>Qe.id===C);return console.log("sourceNode,targetNode:",Ne,Be),{uuid:Tt(),litegraph_id:A,status:1,previous_node_uuid:Ne.flags.uuid,next_node_uuid:Be.flags.uuid,output_field_name:(Ge=Be.inputs[ve])==null?void 0:Ge.fieldName,input_field_name:(De=Ne.outputs[g])==null?void 0:De.fieldName}})),O};async function R(){try{const v=JSON.stringify(Le());if(console.log("jsonString:",Le()),Le().nodes.length===0){U.warning("当前工作区域为空，不可保存空白工作流！");return}const se=localStorage.getItem("token"),O=await fetch("http://localhost:8000/api/workflow/save",{method:"POST",headers:{uid:"0","quantflow-auth":"2","Content-Type":"application/json",Authorization:`${se}`},body:v});if(O.ok){localStorage.setItem("savedGraph",JSON.stringify(v));const de=await O.json();if(console.log("data:",de),U.success(T.owner==="*"?"模版工作流克隆成功！":"保存成功！"),T.workflow_id=de.data.workflow_id,T.owner="",T.workflow_id){const A=new URL(window.location.href);A.searchParams.set("workflow_id",T.workflow_id),window.history.replaceState({},"",A)}const E={uid:"0","quantflow-auth":"2","Content-Type":"application/json",Authorization:`${se}`},ke=await fetch(`http://localhost:8000/api/workflow/query?workflow_id=${de.data.workflow_id}`,{headers:E});if(ke.status===404)return console.log("工作流不存在!"),null;if(ke.ok){const A=await ke.json();console.log("data:",A),T.title=A.name}}else throw new Error(`请求失败: ${O.status} ${O.statusText}`)}catch(v){console.error("保存图表时出错:",v),U.error("保存失败！")}}const H=v=>{(v.metaKey||v.ctrlKey)&&v.key==="s"&&(v.preventDefault(),R())};function k(){const v=JSON.stringify(Le()),se=new Blob([v],{type:"application/json"}),O=URL.createObjectURL(se),de=document.createElement("a");de.href=O,de.download=`graph-export-${new Date().toISOString().slice(0,10)}.json`,document.body.appendChild(de),de.click(),document.body.removeChild(de),URL.revokeObjectURL(O),console.log("图表已成功导出",v)}function o(){var v;if(T.owner==="*"){U.warning("当前为模版,不支持导入覆盖，请先点击保存按钮创建个人工作流后，再进行导入!");return}if(s.isRunning){U.warning("工作流运行时无法导入 JSON 文件");return}(v=te.value)==null||v.click()}async function ge(v){var de;if(s.isRunning){U.warning("工作流运行时无法导入 JSON 文件");return}console.log("开始导入图形");const se=v.target;if(!((de=se.files)!=null&&de.length))return;const O=se.files[0];try{const E=await O.text(),ke=JSON.parse(E).litegraph,A=s.graph.graph||s.graph,g=s.canvas;if(!A||!g)return console.error("Graph or Canvas not initialized"),!1;A.clear();try{A.configure(ke),A.nodes.forEach(a=>{a._fullTitle?a.title=a.truncateTitle(a._fullTitle):(a._fullTitle=a.title||"",a.title=a.truncateTitle(a.title||""))}),g.setZoom(1),g.ds.offset=[0,0];let C=1/0,ve=-1/0,we=1/0,Ne=-1/0;A.nodes.forEach(a=>{C=Math.min(C,a.pos[0]),ve=Math.max(ve,a.pos[0]+a.size[0]),we=Math.min(we,a.pos[1]),Ne=Math.max(Ne,a.pos[1]+a.size[1])});const Be=ve-C,Ge=Ne-we,De=C+Be/2,Qe=we+Ge/2,it=g.canvas.getBoundingClientRect(),ze=it.width,r=it.height;return g.ds.offset=[ze/2-De,r/2-Qe],A.setDirtyCanvas(!0),g.draw(!0,!0),U.success("导入成功！"),se.value="",!0}catch(C){return console.error("Error configuring graph:",C),U.error("导入失败：图形数据格式不正确"),!1}}catch(E){return console.error("Import failed:",E),U.error("导入失败，请检查文件格式是否正确"),!1}}function Ee(){if(T.owner==="*"){U.warning("当前为模版,不支持清空!");return}if(s.isRunning){U.warning("工作流运行时无法删除节点");return}try{const v=s.graph.graph||s.graph;confirm("确定要清空当前图表吗？此操作不可撤销。")&&(v.clear(),v.setDirtyCanvas(!0,!0),console.log("图表已清空"),U.success("图表已清空"))}catch(v){console.error("清空图表时出错:",v)}}function Fe(){if(s.canvas)try{const v=s.canvas.ds.scale||1;s.canvas.setZoom(v*It),s.canvas.draw(!0,!0)}catch(v){console.error("Zoom in error:",v)}}function N(){if(s.canvas)try{const v=s.canvas.ds.scale||1;s.canvas.setZoom(v/It),s.canvas.draw(!0,!0)}catch(v){console.error("Zoom out error:",v)}}function L(){s.canvas&&(z.value=!z.value,s.canvas.drag_mode=z.value,z.value?s.canvas.canvas.style.cursor="grab":s.canvas.canvas.style.cursor="default")}function ce(){if(s.canvas)try{s.canvas.setZoom(1),s.canvas.ds.offset=[0,0],s.canvas.draw(!0,!0)}catch(v){console.error("Reset view error:",v)}}const pe=()=>{z.value&&(s.canvas.canvas.style.cursor="grabbing")},me=()=>{z.value&&(s.canvas.canvas.style.cursor="grab")};return ut(()=>{window.addEventListener("keydown",H),s.canvas&&(s.canvas.canvas.addEventListener("mousedown",pe),s.canvas.canvas.addEventListener("mouseup",me))}),kt(()=>{window.removeEventListener("keydown",H),s.canvas&&(s.canvas.canvas.removeEventListener("mousedown",pe),s.canvas.canvas.removeEventListener("mouseup",me))}),(v,se)=>(w(),x(Me,null,[(w(!0),x(Me,null,$e(Z.value,O=>(w(),x("div",{class:et(["toolbar-item",{disabled:ae(O.id)}]),key:O.id,onClick:de=>oe(O.id,O.text)},[e("img",{src:O.src,alt:O.alt,width:"17px",height:"19px"},null,8,Wa),e("div",Ma,P(O.text),1)],10,Ua))),128)),e("input",{type:"file",ref_key:"fileInput",ref:te,style:{display:"none"},accept:".json",onChange:ge},null,544)],64))}}),Ga=at(Oa,[["__scopeId","data-v-d3f84579"]]),za={class:"table-container"},Va={class:"table-wrapper"},qa={key:0,class:"fixed-column"},Za={class:"scroll-area"},Qa={style:{display:"flex",position:"sticky",top:"0","z-index":"99",background:"#292C38",width:"max-content"}},Pa=["title"],Ya={__name:"TablesScroll",props:{tableData:{type:Array,required:!0},headers:{type:Array,required:!0},isFixed:{type:Boolean,default:!0},columnWidth:{type:[Number,String],default:0},customHeader:{type:Array,default:[]}},setup(q){Zt(Z=>({"25f58ff8":`${q.columnWidth}px`}));const F=q,Y=h(null),T=h(null),U=Z=>!isNaN(Z)&&Number.isFinite(Number(Z))&&String(Z).includes(".")?Number(Z).toFixed(2):Z,s=Z=>Z?Z.replace(/(\d{4})(\d{2})(\d{2})/g,"$1-$2-$3"):"-",p=()=>{Y.value&&T.value&&F.isFixed&&(console.log("-------------"),Y.value.addEventListener("scroll",()=>{console.log(Y.value.scrollTop),T.value.style.top=`-${Y.value.scrollTop}px`}))};return ut(()=>{p()}),(Z,te)=>(w(),x("div",za,[e("div",Va,[F.isFixed?(w(),x("div",qa,[e("div",{class:"fixed-header",style:Oe({width:`${q.customHeader.length>0?q.customHeader[0].width:q.columnWidth}px`})},P(q.customHeader.length>0?q.customHeader[0].key:F.headers[0]),5),e("div",{class:"fixed-body",ref_key:"fixedBody",ref:T},[(w(!0),x(Me,null,$e(F.tableData,(z,ae)=>(w(),x("div",{key:ae,class:"fixed-cell",style:Oe({width:`${q.customHeader.length>0?q.customHeader[0].width:q.columnWidth}px`})},P(s(z[F.headers[0]])),5))),128))],512)])):We("",!0),e("div",Za,[e("div",{class:"scroll-body",ref_key:"scrollBody",ref:Y},[e("div",Qa,[(w(!0),x(Me,null,$e([...F.headers].splice(F.isFixed?1:0),(z,ae)=>(w(),x("div",{key:ae,class:"header-cell",style:Oe({width:`${q.customHeader.length>0?q.customHeader[ae+1].width:q.columnWidth}px`}),title:z},P(q.customHeader.length>0?q.customHeader[ae+1].key:z),13,Pa))),128))]),(w(!0),x(Me,null,$e(F.tableData,(z,ae)=>(w(),x("div",{key:ae,class:"table-row"},[(w(!0),x(Me,null,$e([...F.headers].splice(F.isFixed?1:0),(oe,Le)=>(w(),x("div",{key:Le,class:"table-cell",style:Oe({width:`${q.customHeader.length>0?q.customHeader[Le+1].width:q.columnWidth}px`})},P(U(z[oe])),5))),128))]))),128))],512)])])]))}},Ft=at(Ya,[["__scopeId","data-v-f22c5882"]]),Ja={class:"factor-deep"},$a={class:"factor-deep-header"},Ha={class:"factor-item"},Ka={class:"factor-item-title"},Xa={class:"factor-item-content"},ja={style:{display:"flex","justify-content":"space-between"}},eo={class:"factor-deep-content",style:{"flex-shrink":"0","flex-basis":"368px","margin-top":"20px"}},to={class:"data-card",style:{height:"100%"}},ao={class:"data-item"},oo={class:"data-item"},so={class:"data-item"},ro={class:"data-item"},io={class:"value"},no={key:0,class:"value"},lo={key:1,class:"label"},co={class:"factor-item",style:{margin:"0","margin-top":"20px",width:"auto","flex-shrink":"0","flex-grow":"1","flex-basis":"460px","margin-left":"20px"}},uo={class:"factor-item-title"},fo={class:"factor-item-content"},ho={style:{width:"100%",height:"400px"}},go={class:"factor-item",style:{"margin-left":"20px"}},po={class:"factor-item-title"},vo={class:"factor-item-content"},Ao={style:{width:"100%",height:"400px"}},mo={class:"factor-item-container"},yo={class:"factor-item"},wo={class:"factor-item-title"},_o={class:"factor-item-content"},xo={class:"factor-item"},bo={class:"factor-item-title"},ko={class:"factor-item-content"},Co={class:"factor-item"},Lo={class:"factor-item-title"},Eo={class:"factor-item-content"},So={class:"factor-item"},Ro={class:"factor-item-title"},No={class:"factor-item-content"},Do={class:"factor-item"},Io={class:"factor-item-title"},Fo={class:"factor-item-content"},Bo={class:"factor-item"},To={class:"factor-item-title"},Uo={class:"factor-item-content"},Wo={class:"factor-item"},Mo={class:"factor-item-title"},Oo={class:"factor-item-content"},Go={class:"factor-item"},zo={class:"factor-item-title"},Vo={class:"factor-item-content"},qo={class:"factor-item"},Zo={class:"factor-item-title"},Qo={class:"factor-item-content"},Po={class:"factor-item"},Yo={class:"factor-item-title"},Jo={class:"factor-item-content"},Se="#7d7d7d",$o=dt({__name:"FactorDeep",props:{factorId:{type:String,required:!0},taskId:{type:String,requfactorIdired:!0},into:{type:Boolean,required:!0,default:!1}},setup(q){const F=t=>"",Y=t=>t,T=(t,Ae)=>{if(typeof t=="number")return parseFloat(t.toFixed(Ae));if(typeof t=="string"&&!isNaN(t)&&t.includes("."))return parseFloat(Number(t).toFixed(Ae));if(Array.isArray(t))return t.map(S=>T(S));if(typeof t=="object"&&t!==null){const S={};for(const I in t)t.hasOwnProperty(I)&&(S[I]=T(t[I]));return S}return t},U=t=>!isNaN(t)&&Number.isFinite(Number(t))&&String(t).includes(".")?Number(t).toFixed(2):t,s=q;Qt();const p=h(!1),Z=t=>{const Ae=parseFloat(t);return Ae>0?"#ff4851":Ae<0?"#2fae34":"#333"},te=h({annualized_ratio:0,maximum_drawdown:0,return_ratio:0,sharpe_ratio:0}),z=async()=>{var S,I,Q,V,K,X,j,ee;const{json:t,httpController:Ae}=await ea(s.taskId||F());te.value={annualized_ratio:Y((I=(S=t==null?void 0:t.data)==null?void 0:S.one_group_data)==null?void 0:I.annualized_ratio),maximum_drawdown:Y((V=(Q=t==null?void 0:t.data)==null?void 0:Q.one_group_data)==null?void 0:V.maximum_drawdown),return_ratio:Y((X=(K=t==null?void 0:t.data)==null?void 0:K.one_group_data)==null?void 0:X.return_ratio),sharpe_ratio:Y((ee=(j=t==null?void 0:t.data)==null?void 0:j.one_group_data)==null?void 0:ee.sharpe_ratio)}};let ae=null;const oe=h(null),Le=h(""),R=h({}),H=async()=>{var I,Q,V,K,X,j,ee,fe,he,G,B,m,_,c,l,n;const{json:t,httpController:Ae}=await ta((s==null?void 0:s.taskId)||F());if(t.code==="200"&&(Le.value=(Q=(I=t==null?void 0:t.data)==null?void 0:I.return_chart)==null?void 0:Q.title,R.value=(V=t==null?void 0:t.data)==null?void 0:V.return_chart,oe.value)){if(ae=Pe(oe.value),Object.keys(R.value).length==0)return;var S={code:"200",message:"查询成功",data:{task_id:"bbf3bbc030a94849bea4810058eec027",return_chart:{title:"python 5 groups return",x:[{name:"date",data:(X=(K=R.value)==null?void 0:K.x[0])==null?void 0:X.data}],y:(j=R.value)==null?void 0:j.y}}};const u=(G=(he=(fe=(ee=S==null?void 0:S.data)==null?void 0:ee.return_chart)==null?void 0:fe.x[0])==null?void 0:he.data)==null?void 0:G.map(D=>D.split(" ")[0]),b=(_=(m=(B=S==null?void 0:S.data)==null?void 0:B.return_chart)==null?void 0:m.y)==null?void 0:_.map(D=>D.name),y=["#FF6B6B","#4ECDC4","#45B7D1","#96CEB4","#FFCC5C","#FF6F69","#88D8B0","#6C88C4","#FFA07A","#98FB98","#87CEEB","#DDA0DD"].sort(()=>Math.random()-.5),le=(n=(l=(c=S==null?void 0:S.data)==null?void 0:c.return_chart)==null?void 0:l.y)==null?void 0:n.map((D,$)=>{var Ze;return(Ze=D==null?void 0:D.data)==null?void 0:Ze.map((nt,lt)=>({value:[lt,$,Number(nt).toFixed(2)],itemStyle:{color:y[$%y.length]}}))}).flat(),J={title:{left:"center"},tooltip:{show:!1,axisPointer:{show:!1}},grid3D:{viewControl:{projection:"orthographic",autoRotate:!0,distance:200,beta:45,alpha:25},boxWidth:70,boxHeight:70,boxDepth:200,light:{main:{intensity:1.2}},top:"0%",bottom:"10%"},xAxis3D:{type:"category",data:u==null?void 0:u.map(D=>D.split(" ")[0]),name:"",axisLabel:{interval:Math.floor((u==null?void 0:u.length)/10),rotate:45,margin:20,textStyle:{fontSize:10,color:Se}},nameTextStyle:{fontSize:14,margin:30}},yAxis3D:{type:"category",data:b,name:"",axisLabel:{color:Se}},zAxis3D:{type:"value",name:"",axisLabel:{color:Se}},dataZoom:[{type:"slider",xAxisIndex:0,start:0,end:100},{type:"inside",xAxisIndex:0,start:0,end:100}],series:[{type:"bar3D",data:le,shading:"lambert",label:{show:!1}}]};ae.setOption(J)}},k=h("分组收益"),o=h([]),ge=h({}),Ee=async()=>{var S,I;const{json:t,httpController:Ae}=await aa((s==null?void 0:s.taskId)||F());t.code==="200"&&(ge.value=(S=t==null?void 0:t.data)==null?void 0:S.group_return_analysis,o.value=Object.keys((I=t==null?void 0:t.data)==null?void 0:I.group_return_analysis[0]))},Fe=h("最新数据"),N=h([]),L=h({}),ce=h([{key:"时间",width:130},{key:"股票代码",width:100},{key:"名称",width:100},{key:"因子值",width:80}]),pe=async()=>{var S,I;const{json:t,httpController:Ae}=await oa((s==null?void 0:s.taskId)||F());t.code==="200"&&(L.value=(S=t==null?void 0:t.data)==null?void 0:S.last_date_top_factor,N.value=Object.keys((I=t==null?void 0:t.data)==null?void 0:I.last_date_top_factor[0]))};let me=null;const v=h(null),se=h(""),O=h({}),de=async()=>{var I,Q,V,K,X,j,ee,fe,he;const{json:t,httpController:Ae}=await sa((s==null?void 0:s.taskId)||F());if(t.code==="200"&&(se.value=(Q=(I=t==null?void 0:t.data)==null?void 0:I.ic_decay_chart)==null?void 0:Q.title,console.log("还需处理-----------------",T((V=t==null?void 0:t.data)==null?void 0:V.ic_decay_chart,3)),O.value=(K=t==null?void 0:t.data)==null?void 0:K.ic_decay_chart,v.value)){if(me=Pe(v.value),Object.keys(O.value).length==0)return;const G=(j=(X=O.value)==null?void 0:X.x[0])==null?void 0:j.data,B=(he=(fe=(ee=O.value)==null?void 0:ee.y[0])==null?void 0:fe.data)==null?void 0:he.map((m,_)=>({value:m,itemStyle:{color:m>0?"#ff0000":"#3498db"}}));var S={title:{},tooltip:{trigger:"axis",formatter:function(m){return m.map(_=>{let c=_.value;const l=_.color;if(Array.isArray(c)){const n=c.map(u=>u==null||isNaN(u)?"--":Number(u).toFixed(4));return`<span style="color:${l}">${_.seriesName}</span>: ${n[0]}, ${n[1]}`}return c=c==null||isNaN(c)?"--":Number(c).toFixed(4),`<span style="color:${l}">${_.seriesName}</span>: ${c}`}).join("<br/>")}},grid:{left:"3%",right:"4%",bottom:"15%",top:"15%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:0,start:0,end:100}],xAxis:[{type:"category",data:G,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(m,_){const c=G.length,l=40,n=me.getWidth(),u=Math.floor(n/l),b=Math.floor((c-1)/(u-1));return(c-1-m)%b===0}},show:!1},{type:"category",data:G,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(m,_){const c=G.length,l=40,n=me.getWidth(),u=Math.floor(n/l),b=Math.floor((c-1)/(u-1));return(c-1-m)%b===0}},position:"bottom"}],yAxis:[{type:"value",splitLine:{show:!0,lineStyle:{type:"dashed",color:Se}}}],series:[{name:"IC值",type:"bar",data:B,label:{show:!1,position:"bottom",formatter:function(m){return m.value?m.value.toFixed(2):""}}},{name:"IC趋势",type:"line",smooth:!1,showSymbol:!1,data:O.value.y[0].data,lineStyle:{color:"#ff0000",width:0}}]};me.setOption(S)}};let E=null;const ke=h(null),A=h(""),g=h({}),C=async()=>{var I,Q,V,K,X,j,ee,fe,he;const{json:t,httpController:Ae}=await ra((s==null?void 0:s.taskId)||F());if(t.code==="200"&&(console.log("json",t),A.value=(Q=(I=t==null?void 0:t.data)==null?void 0:I.ic_den_chart)==null?void 0:Q.title,g.value=(V=t==null?void 0:t.data)==null?void 0:V.ic_den_chart,ke.value)){if(E=Pe(ke.value),Object.keys(g.value).length==0)return;var S={title:{},tooltip:{trigger:"axis",formatter:function(G){return G.map(B=>{let m=B.value;const _=B.color;if(Array.isArray(m)){const c=m.map(l=>l==null||isNaN(l)?"--":Number(l).toFixed(4));return`<span style="color:${_}">${B.seriesName}</span>: ${c[0]}, ${c[1]}`}return m=m==null||isNaN(m)?"--":Number(m).toFixed(4),`<span style="color:${_}">${B.seriesName}</span>: ${m}`}).join("<br/>")}},legend:{data:["Histogram","Density Curve"],top:"0px",textStyle:{color:Se}},grid:{left:"3%",right:"4%",bottom:"15%",top:"15%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:0,start:0,end:100},{type:"inside",xAxisIndex:0,start:0}],xAxis:{type:"value",boundaryGap:!1,axisLine:{onZero:!1},splitLine:{show:!0,lineStyle:{type:"dashed",color:Se}}},yAxis:{type:"value",axisLine:{onZero:!1,show:!1},axisTick:{show:!1},position:"left",splitLine:{show:!0,lineStyle:{type:"dashed",color:Se}}},series:[{name:"Histogram",type:"bar",data:(j=(X=(K=g.value)==null?void 0:K.y[0])==null?void 0:X.data)==null?void 0:j.map((G,B)=>{var m,_;return[(_=(m=g.value)==null?void 0:m.x[0])==null?void 0:_.data[B],G]}),itemStyle:{color:"rgba(135, 206, 235, 0.6)"}},{name:"Density Curve",type:"line",smooth:!0,data:(he=(fe=(ee=g.value)==null?void 0:ee.y[1])==null?void 0:fe.data)==null?void 0:he.map((G,B)=>{var m,_;return[(_=(m=g.value)==null?void 0:m.x[0])==null?void 0:_.data[B],G]}),itemStyle:{color:"#1E90FF"},lineStyle:{width:2}}]};E.setOption(S)}};let ve=null;const we=h(null),Ne=h(""),Be=h({}),Ge=async()=>{var I,Q,V,K,X,j,ee,fe,he,G,B,m,_;const{json:t,httpController:Ae}=await ia((s==null?void 0:s.taskId)||F());if(t.code==="200"&&(Ne.value=(Q=(I=t==null?void 0:t.data)==null?void 0:I.ic_seq_chart)==null?void 0:Q.title,Be.value=(V=t==null?void 0:t.data)==null?void 0:V.ic_seq_chart,console.log("echartsRefICSequenceData",Be.value),we.value)){if(ve=Pe(we.value),Object.keys(Be.value).length==0)return;var S={title:{left:"center"},tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter:function(c){return c.map(l=>{let n=l.value;const u=l.color;if(Array.isArray(n)){const b=n.map(y=>y==null||isNaN(y)?"--":Number(y).toFixed(4));return`<span style="color:${u}">${l.seriesName}</span>: ${b[0]}, ${b[1]}`}return n=n==null||isNaN(n)?"--":Number(n).toFixed(4),`<span style="color:${u}">${l.seriesName}</span>: ${n}`}).join("<br/>")}},legend:{data:(X=(K=Be.value)==null?void 0:K.y)==null?void 0:X.map(c=>c.name),top:"0px",selected:{IC:!0,Cum_IC:!1},textStyle:{color:Se}},grid:{left:"3%",right:"4%",bottom:"2%",top:"15%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:[0,1],start:0,end:100}],xAxis:[{type:"category",data:(fe=(ee=(j=Be.value)==null?void 0:j.x[0])==null?void 0:ee.data)==null?void 0:fe.map(c=>c.split(" ")[0]),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(c,l){var J,D,$;const n=($=(D=(J=Be.value)==null?void 0:J.x[0])==null?void 0:D.data)==null?void 0:$.length,u=40,b=ve.getWidth(),y=Math.floor(b/u),le=Math.floor((n-1)/(y-1));return(n-1-c)%le===0}},show:!1},{type:"category",data:(B=(G=(he=Be.value)==null?void 0:he.x[0])==null?void 0:G.data)==null?void 0:B.map(c=>c.split(" ")[0]),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(c,l){var J,D,$;const n=($=(D=(J=Be.value)==null?void 0:J.x[0])==null?void 0:D.data)==null?void 0:$.length,u=40,b=ve.getWidth(),y=Math.floor(b/u),le=Math.floor((n-1)/(y-1));return(n-1-c)%le===0}},position:"bottom"}],yAxis:{type:"value",axisLabel:{interval:1e3},splitLine:{show:!0,lineStyle:{type:"dashed",color:Se}}},series:(_=(m=Be.value)==null?void 0:m.y)==null?void 0:_.map(c=>({name:c.name,data:c.data,type:c.name==="IC"?"bar":"line",itemStyle:{color:c.name==="IC"?"#3498db":"#e74c3c"},label:{show:!1,position:"top",formatter:function(l){return l.data.toFixed(3)}}}))};ve.setOption(S)}};let De=null;const Qe=h(null),it=h(""),ze=h({}),r=async()=>{var I,Q,V,K,X,j,ee,fe,he,G,B,m,_;const{json:t,httpController:Ae}=await na((s==null?void 0:s.taskId)||F());if(t.code==="200"&&(it.value=(Q=(I=t==null?void 0:t.data)==null?void 0:I.ic_self_correlation_chart)==null?void 0:Q.title,ze.value=(V=t==null?void 0:t.data)==null?void 0:V.ic_self_correlation_chart,Qe.value)){if(De=Pe(Qe.value),Object.keys(ze.value).length==0)return;var S={title:{},tooltip:{trigger:"axis",formatter:function(c){return c.map(l=>{let n=l.value;const u=l.color;if(Array.isArray(n)){const b=n.map(y=>y==null||isNaN(y)?"--":Number(y).toFixed(4));return`<span style="color:${u}">${l.seriesName}</span>: ${b[0]}, ${b[1]}`}return n=n==null||isNaN(n)?"--":Number(n).toFixed(4),`<span style="color:${u}">${l.seriesName}</span>: ${n}`}).join("<br/>")}},legend:{data:["自相关系数","下限(95%置信区间)","上限(95%置信区间)"],top:"0px",textStyle:{color:Se}},grid:{left:"3%",right:"4%",bottom:"15%",top:"15%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:[0,1],start:0,end:100}],xAxis:[{type:"category",data:(X=(K=ze.value)==null?void 0:K.x[0])==null?void 0:X.data,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(c,l){var J,D,$;const n=($=(D=(J=ze.value)==null?void 0:J.x[0])==null?void 0:D.data)==null?void 0:$.length,u=40,b=De.getWidth(),y=Math.floor(b/u),le=Math.floor((n-1)/(y-1));return(n-1-c)%le===0}},show:!1},{type:"category",data:(ee=(j=ze.value)==null?void 0:j.x[0])==null?void 0:ee.data,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(c,l){var J,D,$;const n=($=(D=(J=ze.value)==null?void 0:J.x[0])==null?void 0:D.data)==null?void 0:$.length,u=40,b=De.getWidth(),y=Math.floor(b/u),le=Math.floor((n-1)/(y-1));return(n-1-c)%le===0}},position:"bottom"}],yAxis:{type:"value",min:-1,max:1,interval:.2,splitLine:{show:!0,lineStyle:{type:"dashed",color:Se}}},series:[{name:"自相关系数",type:"line",data:(he=(fe=ze.value)==null?void 0:fe.y[0])==null?void 0:he.data,symbol:"circle",symbolSize:8,lineStyle:{width:2},itemStyle:{color:"#3498db"},label:{show:!1,position:"top",formatter:function(c){return c.value.toFixed(3)}}},{name:"下限(95%置信区间)",type:"line",data:(B=(G=ze.value)==null?void 0:G.y[1])==null?void 0:B.data,lineStyle:{type:"dashed",width:1},itemStyle:{color:"#95a5a6"},symbol:"none"},{name:"上限(95%置信区间)",type:"line",data:(_=(m=ze.value)==null?void 0:m.y[2])==null?void 0:_.data,lineStyle:{type:"dashed",width:1},itemStyle:{color:"#95a5a6"},symbol:"none"}]};De.setOption(S)}};let a=null;const i=h(null),d=h(""),f=h({}),W=async()=>{var I,Q,V,K,X,j,ee,fe,he,G;const{json:t,httpController:Ae}=await la((s==null?void 0:s.taskId)||F());if(t.code==="200"&&(d.value=(Q=(I=t==null?void 0:t.data)==null?void 0:I.rank_ic_decay_chart)==null?void 0:Q.title,f.value=(V=t==null?void 0:t.data)==null?void 0:V.rank_ic_decay_chart,i.value)){if(a=Pe(i.value),Object.keys(f.value).length==0)return;const B=(X=(K=f.value)==null?void 0:K.x[0])==null?void 0:X.data,m=(fe=(ee=(j=f.value)==null?void 0:j.y[0])==null?void 0:ee.data)==null?void 0:fe.map((_,c)=>({value:_,itemStyle:{color:_>0?"#ff0000":"#3498db"}}));var S={title:{},tooltip:{trigger:"axis",formatter:function(_){return _.map(c=>{let l=c.value;const n=c.color;if(Array.isArray(l)){const u=l.map(b=>b==null||isNaN(b)?"--":Number(b).toFixed(4));return`<span style="color:${n}">${c.seriesName}</span>: ${u[0]}, ${u[1]}`}return l=l==null||isNaN(l)?"--":Number(l).toFixed(4),`<span style="color:${n}">${c.seriesName}</span>: ${l}`}).join("<br/>")}},grid:{left:"3%",right:"4%",bottom:"15%",top:"15%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:0,start:0,end:100}],xAxis:[{type:"category",data:B,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(_,c){const l=B.length,n=40,u=a.getWidth(),b=Math.floor(u/n),y=Math.floor((l-1)/(b-1));return(l-1-_)%y===0}},show:!1},{type:"category",data:B,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(_,c){const l=B.length,n=40,u=a.getWidth(),b=Math.floor(u/n),y=Math.floor((l-1)/(b-1));return(l-1-_)%y===0}},position:"bottom"}],yAxis:[{type:"value",splitLine:{show:!0,lineStyle:{type:"dashed",color:Se}}}],series:[{name:"IC值",type:"bar",data:m,label:{show:!1,position:"bottom",formatter:function(_){return _.value?_.value.toFixed(3):""}}},{name:"IC趋势",type:"line",smooth:!1,showSymbol:!1,data:(G=(he=f.value)==null?void 0:he.y[0])==null?void 0:G.data,lineStyle:{color:"#000000",width:0}}]};a.setOption(S)}};let re=null;const ue=h(null),_e=h(""),ie=h({}),ye=async()=>{var I,Q,V,K,X,j,ee,fe,he;const{json:t,httpController:Ae}=await ca((s==null?void 0:s.taskId)||F());if(t.code==="200"&&(_e.value=(Q=(I=t==null?void 0:t.data)==null?void 0:I.rank_ic_den_chart)==null?void 0:Q.title,ie.value=(V=t==null?void 0:t.data)==null?void 0:V.rank_ic_den_chart,ue.value)){if(re=Pe(ue.value),Object.keys(ie.value).length==0)return;var S={title:{},tooltip:{trigger:"axis",formatter:function(G){return G.map(B=>{let m=B.value;const _=B.color;if(Array.isArray(m)){const c=m.map(l=>l==null||isNaN(l)?"--":Number(l).toFixed(4));return`<span style="color:${_}">${B.seriesName}</span>: ${c[0]}, ${c[1]}`}return m=m==null||isNaN(m)?"--":Number(m).toFixed(4),`<span style="color:${_}">${B.seriesName}</span>: ${m}`}).join("<br/>")}},legend:{data:["Histogram","Density Curve"],top:"0px",textStyle:{color:Se}},grid:{left:"3%",right:"4%",bottom:"15%",top:"15%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:0,start:0,end:100},{type:"inside",xAxisIndex:0,zoomOnMouseWheel:!1,start:0}],xAxis:{type:"value",boundaryGap:!1,axisLine:{onZero:!1},axisLabel:{formatter:"{value}"},splitLine:{show:!0,lineStyle:{type:"dashed",color:Se}}},yAxis:{type:"value",axisLine:{onZero:!1,show:!1},axisTick:{show:!1},position:"left",splitLine:{show:!0,lineStyle:{type:"dashed",color:Se}}},series:[{name:"Histogram",type:"bar",data:(j=(X=(K=ie.value)==null?void 0:K.y[0])==null?void 0:X.data)==null?void 0:j.map((G,B)=>{var m,_;return[(_=(m=ie.value)==null?void 0:m.x[0])==null?void 0:_.data[B],G]}),itemStyle:{color:"rgba(135, 206, 235, 0.6)"}},{name:"Density Curve",type:"line",smooth:!0,data:(he=(fe=(ee=ie.value)==null?void 0:ee.y[1])==null?void 0:fe.data)==null?void 0:he.map((G,B)=>{var m,_;return[(_=(m=ie.value)==null?void 0:m.x[0])==null?void 0:_.data[B],G]}),itemStyle:{color:"#1E90FF"},lineStyle:{width:2}}]};re.setOption(S)}};let ne=null;const xe=h(null),M=h(""),Ce=h({}),Te=async()=>{var I,Q,V,K,X,j,ee,fe,he,G,B,m,_;const{json:t,httpController:Ae}=await da(s.taskId||F());if(t.code==="200"&&(M.value=(Q=(I=t==null?void 0:t.data)==null?void 0:I.rank_ic_seq_chart)==null?void 0:Q.title,Ce.value=(V=t==null?void 0:t.data)==null?void 0:V.rank_ic_seq_chart,xe.value)){if(ne=Pe(xe.value),Object.keys(Ce.value).length==0)return;var S={title:{left:"center"},tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter:function(c){return c.map(l=>{let n=l.value;const u=l.color;if(Array.isArray(n)){const b=n.map(y=>y==null||isNaN(y)?"--":Number(y).toFixed(4));return`<span style="color:${u}">${l.seriesName}</span>: ${b[0]}, ${b[1]}`}return n=n==null||isNaN(n)?"--":Number(n).toFixed(4),`<span style="color:${u}">${l.seriesName}</span>: ${n}`}).join("<br/>")}},legend:{data:(X=(K=Ce.value)==null?void 0:K.y)==null?void 0:X.map(c=>c.name),top:"0px",selected:{IC:!0,Cum_IC:!1},textStyle:{color:Se}},grid:{left:"3%",right:"4%",bottom:"1%",top:"15%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:[0,1],start:0,end:100}],xAxis:[{type:"category",data:(fe=(ee=(j=Ce.value)==null?void 0:j.x[0])==null?void 0:ee.data)==null?void 0:fe.map(c=>c.split(" ")[0]),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(c,l){var J,D,$;const n=($=(D=(J=Ce.value)==null?void 0:J.x[0])==null?void 0:D.data)==null?void 0:$.length,u=40,b=ne.getWidth(),y=Math.floor(b/u),le=Math.floor((n-1)/(y-1));return(n-1-c)%le===0}},show:!1},{type:"category",data:(B=(G=(he=Ce.value)==null?void 0:he.x[0])==null?void 0:G.data)==null?void 0:B.map(c=>c.split(" ")[0]),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(c,l){var J,D,$;const n=($=(D=(J=Ce.value)==null?void 0:J.x[0])==null?void 0:D.data)==null?void 0:$.length,u=40,b=ne.getWidth(),y=Math.floor(b/u),le=Math.floor((n-1)/(y-1));return(n-1-c)%le===0}},position:"bottom"}],yAxis:{type:"value",splitLine:{show:!0,lineStyle:{type:"dashed",color:Se}}},series:(_=(m=Ce.value)==null?void 0:m.y)==null?void 0:_.map(c=>({name:c.name,data:c.data,type:c.name==="Rank_IC"?"bar":"line",itemStyle:{color:c.name==="IC"?"#3498db":"#e74c3c"},showSymbol:!1,label:{show:!1,position:"top",formatter:function(l){return l.data.toFixed(3)}}}))};ne.setOption(S)}};let be=null;const Ve=h(null),Ue=h(""),qe=h({}),tt=async()=>{var I,Q,V,K,X,j,ee,fe,he,G,B,m,_;const{json:t,httpController:Ae}=await ua((s==null?void 0:s.taskId)||F());if(t.code==="200"&&(Ue.value=(Q=(I=t==null?void 0:t.data)==null?void 0:I.rank_ic_self_correlation_chart)==null?void 0:Q.title,qe.value=(V=t==null?void 0:t.data)==null?void 0:V.rank_ic_self_correlation_chart,Ve.value)){if(be=Pe(Ve.value),Object.keys(qe.value).length==0)return;var S={title:{},tooltip:{trigger:"axis",formatter:function(c){return c.map(l=>{let n=l.value;const u=l.color;if(Array.isArray(n)){const b=n.map(y=>y==null||isNaN(y)?"--":Number(y).toFixed(4));return`<span style="color:${u}">${l.seriesName}</span>: ${b[0]}, ${b[1]}`}return n=n==null||isNaN(n)?"--":Number(n).toFixed(4),`<span style="color:${u}">${l.seriesName}</span>: ${n}`}).join("<br/>")}},legend:{data:["自相关系数","下限(95%置信区间)","上限(95%置信区间)"],top:"0px",textStyle:{color:Se}},grid:{left:"3%",right:"4%",bottom:"10%",top:"10%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:0,start:0,end:100}],xAxis:[{type:"category",data:(X=(K=qe.value)==null?void 0:K.x[0])==null?void 0:X.data,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(c,l){var J,D,$;const n=($=(D=(J=qe.value)==null?void 0:J.x[0])==null?void 0:D.data)==null?void 0:$.length,u=40,b=be.getWidth(),y=Math.floor(b/u),le=Math.floor((n-1)/(y-1));return(n-1-c)%le===0}},show:!1},{type:"category",data:(ee=(j=qe.value)==null?void 0:j.x[0])==null?void 0:ee.data,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(c,l){var J,D,$;const n=($=(D=(J=qe.value)==null?void 0:J.x[0])==null?void 0:D.data)==null?void 0:$.length,u=40,b=be.getWidth(),y=Math.floor(b/u),le=Math.floor((n-1)/(y-1));return(n-1-c)%le===0}},position:"bottom"}],yAxis:{type:"value",min:-1,max:1,interval:.2,splitLine:{show:!0,lineStyle:{type:"dashed",color:Se}}},series:[{name:"自相关系数",type:"line",data:(he=(fe=qe.value)==null?void 0:fe.y[0])==null?void 0:he.data,symbol:"circle",symbolSize:8,lineStyle:{width:2},itemStyle:{color:"#3498db"},label:{show:!1,position:"top",formatter:function(c){return c.value.toFixed(3)}}},{name:"下限(95%置信区间)",type:"line",data:(B=(G=qe.value)==null?void 0:G.y[1])==null?void 0:B.data,lineStyle:{type:"dashed",width:1},itemStyle:{color:"#95a5a6"},symbol:"none"},{name:"上限(95%置信区间)",type:"line",data:(_=(m=qe.value)==null?void 0:m.y[2])==null?void 0:_.data,lineStyle:{type:"dashed",width:1},itemStyle:{color:"#95a5a6"},symbol:"none"}]};be.setOption(S)}};let Ke=null;const wt=h(null),Ct=h(""),ot=h({}),gt=async()=>{var S,I,Q,V,K,X,j,ee,fe,he,G,B,m,_,c,l;const{json:t,httpController:Ae}=await fa((s==null?void 0:s.taskId)||F());if(t.code==="200"&&((Q=(I=(S=t==null?void 0:t.data)==null?void 0:S.return_chart)==null?void 0:I.y)==null||Q.forEach(n=>{n.data=n.data.map(u=>U(u*100))}),Ct.value=(K=(V=t==null?void 0:t.data)==null?void 0:V.return_chart)==null?void 0:K.title,ot.value=(X=t==null?void 0:t.data)==null?void 0:X.return_chart,wt.value)){if(Ke=Pe(wt.value),Object.keys(ot.value).length==0)return;const n={title:{},tooltip:{trigger:"axis",formatter:function(u){return u.map(b=>{let y=b.value;const le=b.color;if(Array.isArray(y)){const J=y.map(D=>D==null||isNaN(D)?"--":Number(D).toFixed(4));return`<span style="color:${le}">${b.seriesName}</span>: ${J[0]}, ${J[1]}`}return y=y==null||isNaN(y)?"--":Number(y).toFixed(4),`<span style="color:${le}">${b.seriesName}</span>: ${y}%`}).join("<br/>")}},legend:{data:(ee=(j=ot.value)==null?void 0:j.y)==null?void 0:ee.map(u=>u.name),textStyle:{color:Se}},xAxis:[{type:"category",data:(G=(he=(fe=ot.value)==null?void 0:fe.x[0])==null?void 0:he.data)==null?void 0:G.map(u=>Rt(u)),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(u,b){var Ze,nt,lt;const y=(lt=(nt=(Ze=ot.value)==null?void 0:Ze.x[0])==null?void 0:nt.data)==null?void 0:lt.length,le=60,J=Ke.getWidth(),D=Math.floor(J/le),$=Math.floor((y-1)/(D-1));return(y-1-u)%$===0}},show:!1},{type:"category",data:(_=(m=(B=ot.value)==null?void 0:B.x[0])==null?void 0:m.data)==null?void 0:_.map(u=>Rt(u)),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(u,b){var Ze,nt,lt;const y=(lt=(nt=(Ze=ot.value)==null?void 0:Ze.x[0])==null?void 0:nt.data)==null?void 0:lt.length,le=60,J=Ke.getWidth(),D=Math.floor(J/le),$=Math.floor((y-1)/(D-1));return(y-1-u)%$===0}},position:"bottom"}],yAxis:{type:"value",axisLabel:{formatter:"{value}%"},splitLine:{show:!0,lineStyle:{type:"dashed",color:Se}}},grid:{left:"3%",right:"4%",bottom:"0%",top:"10%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:[0,1],start:0,end:100}],series:(l=(c=ot.value)==null?void 0:c.y)==null?void 0:l.map(u=>({name:u.name,type:"line",data:u.data,showSymbol:!1}))};Ke.setOption(n)}};let Xe=null;const _t=h(null),Lt=h(""),st=h({}),pt=async()=>{var I,Q,V,K,X,j,ee,fe,he,G,B,m,_;const{json:t,httpController:Ae}=await ha((s==null?void 0:s.taskId)||F());if(t.code==="200"&&(Lt.value=(Q=(I=t==null?void 0:t.data)==null?void 0:I.excess_chart)==null?void 0:Q.title,st.value=(V=t==null?void 0:t.data)==null?void 0:V.excess_chart,_t.value)){if(Xe=Pe(_t.value),Object.keys(st.value).length==0)return;const c=["#1f77b4","#ff7f0e","#2ca02c","#d62728","#9467bd","#0099cc","#ff00ff"];var S={title:{},tooltip:{trigger:"axis",formatter:function(l){return l.map(n=>{let u=n.value;const b=n.color;if(Array.isArray(u)){const y=u.map(le=>le==null||isNaN(le)?"--":Number(le).toFixed(4));return`<span style="color:${b}">${n.seriesName}</span>: ${y[0]}, ${y[1]}`}return u=u==null||isNaN(u)?"--":Number(u).toFixed(4),`<span style="color:${b}">${n.seriesName}</span>: ${u}`}).join("<br/>")}},legend:{data:(X=(K=st.value)==null?void 0:K.y)==null?void 0:X.map(l=>l.name),top:"0px",textStyle:{color:Se,fontSize:10}},grid:{left:"3%",right:"4%",bottom:"0%",top:"10%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:[0,1],start:0,end:100}],xAxis:[{type:"category",data:(fe=(ee=(j=st.value)==null?void 0:j.x[0])==null?void 0:ee.data)==null?void 0:fe.map(l=>l.split(" ")[0]),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(l,n){var D,$,Ze;const u=(Ze=($=(D=st.value)==null?void 0:D.x[0])==null?void 0:$.data)==null?void 0:Ze.length,b=40,y=Xe.getWidth(),le=Math.floor(y/b),J=Math.floor((u-1)/(le-1));return(u-1-l)%J===0}},show:!1},{type:"category",data:(B=(G=(he=st.value)==null?void 0:he.x[0])==null?void 0:G.data)==null?void 0:B.map(l=>l.split(" ")[0]),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(l,n){var D,$,Ze;const u=(Ze=($=(D=st.value)==null?void 0:D.x[0])==null?void 0:$.data)==null?void 0:Ze.length,b=40,y=Xe.getWidth(),le=Math.floor(y/b),J=Math.floor((u-1)/(le-1));return(u-1-l)%J===0}},position:"bottom"}],yAxis:{type:"value",axisLabel:{formatter:l=>(l*100).toFixed(1)+"%"}},series:(_=(m=st.value)==null?void 0:m.y)==null?void 0:_.map((l,n)=>({name:l.name,type:"line",data:l.data,symbol:"circle",symbolSize:6,showSymbol:!1,lineStyle:{width:(l.name.includes("多空组合"),1)},itemStyle:{color:c[n]}}))};Xe.setOption(S)}};He(()=>s.factorId,async()=>{s.into&&(console.log("加载mc3"),await vt(),await z(),await Ee(),await pe(),await H(),await de(),await C(),await Ge(),await r(),await W(),await ye(),await Te(),await tt(),await gt(),await pt(),await je(),rt())}),He(()=>s.taskId,async()=>{s.into&&(console.log("加载mc2"),await je(),rt(),await vt(),await z(),await je(),await Ee(),await pe(),await H(),await de(),await C(),await Ge(),await r(),await W(),await ye(),await Te(),await tt(),await gt(),await pt(),await je(),setTimeout(()=>{rt()},1200))}),He(()=>s.into,async()=>{s.into&&(console.log("加载mc1"),await vt(),await z(),await Ee(),await pe(),await H(),await de(),await C(),await Ge(),await r(),await W(),await ye(),await Te(),await tt(),await gt(),await pt(),await je(),rt())}),He(()=>s.into,async()=>{await je(),rt()});const rt=()=>{ae==null||ae.resize(),me==null||me.resize(),E==null||E.resize(),ve==null||ve.resize(),De==null||De.resize(),a==null||a.resize(),re==null||re.resize(),ne==null||ne.resize(),be==null||be.resize(),Ke==null||Ke.resize(),Xe==null||Xe.resize()},Ot=()=>{ae==null||ae.dispose(),me==null||me.dispose(),E==null||E.dispose(),ve==null||ve.dispose(),De==null||De.dispose(),a==null||a.dispose(),re==null||re.dispose(),ne==null||ne.dispose(),be==null||be.dispose(),Ke==null||Ke.dispose(),Xe==null||Xe.dispose()},Et=h([]),vt=async()=>{var S;const{json:t,httpController:Ae}=await ga((s==null?void 0:s.taskId)||F());Et.value=(S=t==null?void 0:t.data)==null?void 0:S.factor_data_analysis},St=h(!0);ut(async()=>{try{s.into&&(await vt(),await z(),await Ee(),await pe(),await H(),await de(),await C(),await Ge(),await r(),await W(),await ye(),await Te(),await tt(),await gt(),await pt(),St.value=!1,await je(),rt())}catch{p.value,St.value=!0}window.addEventListener("resize",rt)}),kt(()=>{window.removeEventListener("resize",rt),Ot()});const Rt=t=>t&&t.split(" ")[0];return(t,Ae)=>(w(),x("div",Ja,[e("div",$a,[e("div",Ha,[e("div",Ka,P(Le.value),1),e("div",Xa,[e("div",{ref_key:"echartsRefEarningsBigChart",ref:wt,style:{width:"100%",height:"400px"}},[Re(Ye,{visible:!0})],512)])]),e("div",ja,[e("div",eo,[e("div",to,[e("div",ao,[e("div",{class:"value",style:Oe({color:Z(te.value.return_ratio)})},P(te.value.return_ratio),5),Ae[0]||(Ae[0]=e("div",{class:"label"},"因子收益",-1))]),e("div",oo,[e("div",{class:"value",style:Oe({color:Z(te.value.sharpe_ratio)})},P(te.value.sharpe_ratio),5),Ae[1]||(Ae[1]=e("div",{class:"label"},"夏普比率",-1))]),e("div",so,[e("div",{class:"value",style:Oe({color:Z(te.value.annualized_ratio)})},P(te.value.annualized_ratio),5),Ae[2]||(Ae[2]=e("div",{class:"label"},"年化收益",-1))]),e("div",ro,[e("div",io,P(te.value.maximum_drawdown),1),Ae[3]||(Ae[3]=e("div",{class:"label"},"最大回撤",-1))]),(w(!0),x(Me,null,$e(Et.value,(S,I)=>(w(),x("div",{key:I,class:"data-item"},[(w(!0),x(Me,null,$e(Object.values(S),(Q,V)=>(w(),x(Me,{key:V},[V==1?(w(),x("div",no,P(Q),1)):We("",!0),V==0?(w(),x("div",lo,P(Q),1)):We("",!0)],64))),128))]))),128))])]),e("div",co,[e("div",uo,P(Fe.value),1),e("div",fo,[e("div",ho,[Re(Ft,{tableData:L.value,headers:N.value,isFixed:!0,"custom-header":ce.value},null,8,["tableData","headers","custom-header"])])])]),e("div",go,[e("div",po,P(k.value),1),e("div",vo,[e("div",Ao,[Re(Ft,{tableData:ge.value,headers:o.value,isFixed:!0,"column-width":110},null,8,["tableData","headers"])])])])]),e("div",mo,[e("div",yo,[e("div",wo,P(se.value),1),e("div",_o,[e("div",{ref_key:"echartsRefSimDietrich",ref:v,style:{width:"100%",height:"400px"}},[Re(Ye,{visible:!0})],512)])]),e("div",xo,[e("div",bo,P(A.value),1),e("div",ko,[e("div",{ref_key:"echartsRefICDistribution",ref:ke,style:{width:"100%",height:"400px"}},[Re(Ye,{visible:!0})],512)])]),e("div",Co,[e("div",Lo,P(Ne.value),1),e("div",Eo,[e("div",{ref_key:"echartsRefICSequence",ref:we,style:{width:"100%",height:"400px"}},[Re(Ye,{visible:!0})],512)])]),e("div",So,[e("div",Ro,P(it.value),1),e("div",No,[e("div",{ref_key:"echartsRefICCcorrelation",ref:Qe,style:{width:"100%",height:"400px"}},[Re(Ye,{visible:!0})],512)])]),e("div",Do,[e("div",Io,P(d.value),1),e("div",Fo,[e("div",{ref_key:"echartsRefRankICDecay",ref:i,style:{width:"100%",height:"400px"}},[Re(Ye,{visible:!0})],512)])]),e("div",Bo,[e("div",To,P(_e.value),1),e("div",Uo,[e("div",{ref_key:"echartsRefRankICDistribution",ref:ue,style:{width:"100%",height:"400px"}},[Re(Ye,{visible:!0})],512)])]),e("div",Wo,[e("div",Mo,P(M.value),1),e("div",Oo,[e("div",{ref_key:"echartsRefRankICSequence",ref:xe,style:{width:"100%",height:"400px"}},[Re(Ye,{visible:!0})],512)])]),e("div",Go,[e("div",zo,P(Ue.value),1),e("div",Vo,[e("div",{ref_key:"echartsRefRankICCcorrelation",ref:Ve,style:{width:"100%",height:"400px"}},[Re(Ye,{visible:!0})],512)])]),e("div",qo,[e("div",Zo,P(Ct.value),1),e("div",Qo,[e("div",{ref_key:"echartsRefCurrentDeepAnalysis",ref:oe,style:{width:"100%",height:"400px"}},[Re(Ye,{visible:!0})],512)])]),e("div",Po,[e("div",Yo,P(Lt.value),1),e("div",Jo,[e("div",{ref_key:"echartsRefExcessReturnBigChart",ref:_t,style:{width:"100%",height:"400px"}},[Re(Ye,{visible:!0})],512)])])])])]))}}),Ho=at($o,[["__scopeId","data-v-62815ad4"]]),Ko={class:"logs-header"},Xo={class:"controls"},jo={class:"filter-group"},es=["value"],ts={class:"filter-group"},as={width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},os={key:0,d:"M3 4h13M3 8h9M3 12h5M19 20V8M15 16l4 4 4-4"},ss={key:1,d:"M3 4h13M3 8h9M3 12h5M19 20V8M15 12l4-4 4 4"},rs={class:"filter-group"},is={class:"logs-content-area"},ns={class:"logs-table"},ls={class:"log-message"},cs={key:0,class:"metadata-container"},ds=["onClick"],us={class:"toggle-text"},fs={class:"log-metadata"},hs={class:"logs-footer"},gs={class:"pagination-info"},ps={key:0},vs={class:"pagination-controls"},As=["disabled"],ms={width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",style:{transform:"rotate(270deg)"}},ys=["disabled"],ws={width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",style:{transform:"rotate(90deg)"}},_s=dt({__name:"Logs",props:{isCollapsed:{type:Boolean,default:!0},logCount:{type:Number,default:0}},setup(q){Ut();const F=bt(),Y=q,T=h([]),U=h(!1),s=h("ALL"),p=h("asc"),Z=h(50),te=h(1),z=h([void 0]),ae=h(void 0),oe=h(!1),Le=h(null),R=h(Y.isCollapsed);He(()=>Y.isCollapsed,A=>{A||(R.value=A)});const H=h(new Set),k=h(50);let o=!1;const ge=A=>{var C;o=!0,document.body.style.cursor="ns-resize",document.body.style.userSelect="none";const g=(C=A.target.closest(".logs-container"))==null?void 0:C.getBoundingClientRect();g&&(g.height,A.clientY,document.addEventListener("mousemove",Ee),document.addEventListener("mouseup",Fe))},Ee=A=>{if(!o)return;const g=window.innerHeight,C=A.clientY,we=(g-C)/g*100;k.value=Math.min(Math.max(we,20),80)},Fe=()=>{o=!1,document.body.style.cursor="",document.body.style.userSelect="",document.removeEventListener("mousemove",Ee),document.removeEventListener("mouseup",Fe)},N=async(A,g=!1)=>{if(!F.workflow_run_id||!F.workflow_id)return T.value=[],g&&(te.value=1,z.value=[void 0],ae.value=void 0,oe.value=!1,Le.value=null),!1;U.value=!0;try{const C=localStorage.getItem("token"),ve={workflow_run_id:F.workflow_run_id,workflow_id:F.workflow_id,limit:Number(Z.value)};A&&(ve.last_sequence=A),s.value!=="ALL"&&(ve.log_level=s.value);const we=await Wt.get("http://localhost:8000/api/workflow/run/log",{params:ve,headers:{uid:"0","quantflow-auth":"2","Content-Type":"application/json",Authorization:`${C}`}});if(we.status===200&&we.data&&we.data.data){const Ne=we.data.data;return T.value=Ne.logs||[],ae.value=Ne.next_sequence,oe.value=Ne.has_more??!1,Le.value=Ne.total_count,g&&(te.value=1,z.value=[void 0]),!0}else return console.error("Failed to fetch logs:",we),T.value=[],g&&(te.value=1,z.value=[void 0],ae.value=void 0),oe.value=!1,!1}catch(C){return console.error("Error fetching logs:",C),T.value=[],g&&(te.value=1,z.value=[void 0],ae.value=void 0),oe.value=!1,!1}finally{U.value=!1}},L=ht(()=>{let A=[...T.value];return s.value!=="ALL"&&(A=A.filter(g=>g.level===s.value)),A.sort((g,C)=>{const ve=new Date(g.timestamp).getTime(),we=new Date(C.timestamp).getTime();return p.value==="asc"?ve-we:we-ve}),A}),ce=ht(()=>["ALL","INFO","ERROR","WARNING","DEBUG"]),pe=async()=>{if(oe.value&&ae.value!=null&&!U.value){const A=ae.value;await N(A,!1)&&(te.value++,z.value.length<te.value?z.value.push(A):z.value[te.value-1]=A)}},me=async()=>{if(te.value>1&&!U.value){const A=te.value-1,g=z.value[A-1];await N(g,!1)&&(te.value=A)}},v=()=>{p.value=p.value==="asc"?"desc":"asc",je(()=>{E()})},se=A=>{if(!A)return"N/A";try{return new Date(A).toLocaleString()}catch{return A}},O=()=>{N(void 0,!0)},de=()=>{R.value=!R.value,R.value||(k.value=50)},E=()=>{const A=document.querySelector(".logs-content-area");A&&(p.value==="asc"?A.scrollTop=A.scrollHeight:A.scrollTop=0)};He(()=>T.value,()=>{je(()=>{E()})},{deep:!0}),He(()=>Y.logCount,async(A,g)=>{A!==g&&F.workflow_run_id&&await N(void 0,!0)},{immediate:!1}),ut(async()=>{await N(void 0,!0)});const ke=A=>{H.value.has(A)?H.value.delete(A):H.value.add(A)};return(A,g)=>(w(),x("div",{class:et(["logs-container",{collapsed:R.value,dragging:Je(o)}]),style:Oe({height:k.value+"%"})},[e("div",{class:"drag-handle",onMousedown:ge},null,32),e("div",{class:"toggle-button",onClick:de},[(w(),x("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",style:Oe({transform:R.value?"rotate(0deg)":"rotate(180deg)"})},g[2]||(g[2]=[e("path",{d:"M7 14l5-5 5 5",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)]),4))]),e("div",Ko,[g[7]||(g[7]=Pt('<div class="title" data-v-7a5cf825><svg width="24" height="24" viewBox="0 0 24 24" fill="none" data-v-7a5cf825><circle cx="12" cy="12" r="9" stroke="currentColor" stroke-width="2" data-v-7a5cf825></circle><path d="M8 9H16" stroke="currentColor" stroke-width="2" stroke-linecap="round" data-v-7a5cf825></path><path d="M8 12H14" stroke="currentColor" stroke-width="2" stroke-linecap="round" data-v-7a5cf825></path><path d="M8 15H12" stroke="currentColor" stroke-width="2" stroke-linecap="round" data-v-7a5cf825></path></svg><span data-v-7a5cf825>日志内容</span></div>',1)),e("div",Xo,[e("div",jo,[g[3]||(g[3]=e("label",{for:"level-filter"},"级别:",-1)),ct(e("select",{id:"level-filter","onUpdate:modelValue":g[0]||(g[0]=C=>s.value=C),onChange:O},[(w(!0),x(Me,null,$e(ce.value,C=>(w(),x("option",{key:C,value:C},P(C),9,es))),128))],544),[[Nt,s.value]])]),e("div",ts,[g[4]||(g[4]=e("label",{for:"sort-order"},"时间:",-1)),e("button",{onClick:v,class:"sort-button"},[(w(),x("svg",as,[p.value==="desc"?(w(),x("path",os)):(w(),x("path",ss))]))])]),e("div",rs,[g[6]||(g[6]=e("label",{for:"limit-per-page"},"每页:",-1)),ct(e("select",{id:"limit-per-page","onUpdate:modelValue":g[1]||(g[1]=C=>Z.value=C),onChange:O},g[5]||(g[5]=[e("option",{value:"50"},"50",-1),e("option",{value:"100"},"100",-1)]),544),[[Nt,Z.value]])])])]),e("div",is,[e("table",ns,[g[9]||(g[9]=e("thead",null,[e("tr",null,[e("th",{class:"timestamp-col"},"时间"),e("th",{class:"level-col"},"级别"),e("th",{class:"message-col"},"消息")])],-1)),e("tbody",null,[(w(!0),x(Me,null,$e(L.value,C=>(w(),x("tr",{key:C._id,class:et(["log-entry",`log-level-${C.level.toLowerCase()}`])},[e("td",null,P(se(C.timestamp)),1),e("td",null,[e("span",{class:et(["log-level-badge",`log-level-badge-${C.level.toLowerCase()}`])},P(C.level),3)]),e("td",ls,[mt(P(C.message)+" ",1),C.metadata&&Object.keys(C.metadata).length>0?(w(),x("div",cs,[e("button",{class:"metadata-toggle",onClick:ve=>ke(C._id)},[e("span",us,P(H.value.has(C._id)?"收起详情":"查看详情"),1),(w(),x("svg",{width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",class:et({rotate:H.value.has(C._id)})},g[8]||(g[8]=[e("path",{d:"M6 9l6 6 6-6",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)]),2))],8,ds),ct(e("div",fs,[e("pre",null,P(JSON.stringify(C.metadata,null,2)),1)],512),[[yt,H.value.has(C._id)]])])):We("",!0)])],2))),128))])])]),e("div",hs,[e("div",gs,[e("span",null,"第 "+P(te.value)+" 页",1),Le.value!==null?(w(),x("span",ps," / 总 "+P(Le.value)+" 条记录",1)):We("",!0),e("span",null," (已加载 "+P(T.value.length)+" 条)",1)]),e("div",vs,[e("button",{onClick:me,disabled:te.value===1||U.value,class:"pagination-button pagination-button-prev"},[(w(),x("svg",ms,g[10]||(g[10]=[e("path",{d:"M7 14l5-5 5 5",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)]))),g[11]||(g[11]=mt(" 上一页 "))],8,As),e("button",{onClick:pe,disabled:!oe.value||U.value,class:"pagination-button pagination-button-next"},[g[13]||(g[13]=mt(" 下一页 ")),(w(),x("svg",ws,g[12]||(g[12]=[e("path",{d:"M7 14l5-5 5 5",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)])))],8,ys)])])],6))}}),xs=at(_s,[["__scopeId","data-v-7a5cf825"]]),bs={class:"alert"},ks={class:"box"},Cs={__name:"alert",props:{show:{type:Boolean,default:!1}},emits:["close"],setup(q,{emit:F}){const Y=q,T=F,U=()=>{T("close")};return(s,p)=>ct((w(),x("div",bs,[e("div",ks,[e("img",{onClick:U,class:"close-btn",src:Mt}),Yt(s.$slots,"default",{},void 0,!0)])],512)),[[yt,Y.show]])}},xt=at(Cs,[["__scopeId","data-v-34167be4"]]),Ls={key:0,class:"alert-container"},Es={class:"alert-header"},Ss={class:"alert-content"},Rs={key:1,class:"alert-container"},Ns={class:"alert-header"},Ds={class:"alert-content"},Is={class:"content-container"},Fs={class:"toolbar-container"},Bs={class:"run-container"},Ts={key:0,style:{"font-size":"12px",color:"#fff",position:"absolute",top:"-26px",transform:"scale(0.8)"}},Us={key:0,class:"drag-overlay"},Ws={class:"monaco_ed"},Ms={class:"alert"},Os={class:"box"},Gs=dt({__name:"Editor",setup(q){const F=h(null);let Y=null;const T=h(null),U=Ut(),s=Jt(),p=bt(),Z=()=>{p.codeStatus=!1,p.code=""};He(()=>p.codeStatus,()=>{console.log("workflowStore.codeStatus",p.codeStatus),console.log("workflowStore.code",p.code),Y&&setTimeout(()=>{console.log("有更改"),Y.setValue(p.code)},100)}),He(()=>s.isBoxShow,async()=>{console.log("---2333-----"),s.isBoxShow&&(await je(),console.log("strategyAnalysisRef.value",T.value),T.value&&(console.log("---23334-----"),T.value.handleApply({workflow_id:s.id,title:p.title,feature_tag:"backtest",locator:"backtest_id",last_run_id:p.workflow_run_id},!1)))});const te=()=>{if(p.node.node){p.node.node.properties[p.node.title]=Y.getValue(),p.code=Y.getValue();const r=p.node.node.widgets.find(a=>a.name===p.node.title);r.value=Y.getValue(),console.log("workflowStore.node.widget",r),p.codeStatus=!1}};function z(r,a=!1){r||(r=window.location.href);const i={},d=r.indexOf("?");if(d!==-1){const f=r.indexOf("#",d),W=f!==-1?r.substring(d+1,f):r.substring(d+1);ae(W,i)}if(a){const f=r.indexOf("#");if(f!==-1&&f<r.length-1){const W=r.substring(f+1),re=W.indexOf("?");if(re!==-1){const ue=W.substring(re+1);ae(ue,i)}}}return i}function ae(r,a){if(!r)return;const i=r.split("&");for(const d of i){const[f,W]=d.split("=",2);f&&(a[decodeURIComponent(f)]=W!==void 0?decodeURIComponent(W):"")}}const oe=Bt(),Le=h(!1),R=h(null);let H=h(null),k,o;const ge=h(!1),Ee=h(0),Fe=h(!1);let N=0,L=0,ce=1;const pe=()=>{L+=.03*ce,L>=1?(L=1,ce=-1):L<=0&&(L=0,ce=1),o&&o.draw(!0,!0),requestAnimationFrame(pe)};pe();const me=()=>{const r=R.value,a=Math.max(window.devicePixelRatio,1),{width:i,height:d}=r.getBoundingClientRect();r.width=Math.round(i*a),r.height=Math.round(d*a),r.getContext("2d").scale(a,a),o.scale_offset=[1,1],o.ds.scale=1,o.dirty_canvas=!0,o.dirty_bgcanvas=!0,o==null||o.draw(!0,!0)},v=r=>{var a;if(ge.value){r.preventDefault();return}(a=r.dataTransfer)!=null&&a.types.includes("Files")&&(r.preventDefault(),N++,Fe.value=!0)},se=r=>{var i,d;if(ge.value){r.preventDefault();return}(((i=r.dataTransfer)==null?void 0:i.types.includes("node-type"))||(d=r.dataTransfer)!=null&&d.types.includes("Files"))&&(r.preventDefault(),r.dataTransfer.dropEffect="copy")},O=r=>{var a;if(ge.value){r.preventDefault();return}(a=r.dataTransfer)!=null&&a.types.includes("Files")&&(r.preventDefault(),N--,N<=0&&(Fe.value=!1,N=0))},de=async r=>{var i,d,f,W,re,ue,_e;if(p.owner==="*"){oe.warning("当前为模版,不支持导入覆盖，请先点击保存按钮创建个人工作流后，再进行导入!");return}if(ge.value){if(r.preventDefault(),(i=r.dataTransfer)!=null&&i.types.includes("Files")){const ie=r.dataTransfer.files;ie!=null&&ie.length&&ie[0].name.toLowerCase().endsWith(".json")&&oe.warning("工作流运行时无法导入 JSON 文件")}return}if((d=r.dataTransfer)==null?void 0:d.types.includes("node-type")){const ie=(f=r.dataTransfer)==null?void 0:f.getData("node-type");if(console.log("nodeType:",ie),ie&&k){const ye=(W=R.value)==null?void 0:W.getBoundingClientRect();if(ye){const ne=r.clientX-ye.left,xe=r.clientY-ye.top,M=o.ds.scale||1,Ce=o.ds.offset||[0,0],Te=[(ne-Ce[0])/M,(xe-Ce[1])/M],be=Ie.createNode(ie);if(be){be.pos=Te;let Ve=0;for(const Ue of k.nodes)Ue.order&&Ue.order>Ve&&(Ve=Ue.order);be.order=Ve+1e3,k.add(be),k.setDirtyCanvas(!0),o.draw(!0,!0)}}}return}if((re=r.dataTransfer)!=null&&re.types.includes("Files")){r.preventDefault(),Fe.value=!1,N=0;const ie=r.dataTransfer.files;if(!(ie!=null&&ie.length))return;const ye=ie[0];if(!ye.name.toLowerCase().endsWith(".json")){xt("请拖入 JSON 文件");return}try{const ne=await ye.text(),xe=JSON.parse(ne).litegraph;if(!k||!o){console.error("Graph or GraphCanvas not initialized");return}k.clear();try{if(k.configure(xe),k.nodes.forEach(M=>{M._fullTitle?M.title=M.truncateTitle(M._fullTitle):(M._fullTitle=M.title||"",M.title=M.truncateTitle(M.title||""))}),k.nodes.length>0){const M=k.nodes[0],Ce=R.value.getBoundingClientRect(),Te=Ce.width,be=Ce.height;o.setZoom(1);const Ve=M.pos[0],Ue=M.pos[1],qe=((ue=M.size)==null?void 0:ue[0])||0,tt=((_e=M.size)==null?void 0:_e[1])||0;o.ds.offset=[Te/2-(Ve+qe/2),be/2-(Ue+tt/2)],o.setDirty(!0,!0)}else o.setZoom(1),o.ds.offset=[0,0];o.draw(!0,!0),console.log("Graph imported successfully")}catch(M){console.error("Error configuring graph:",M),xt("导入失败：图形数据格式不正确")}}catch(ne){console.error("Import failed:",ne),xt("导入失败，请检查文件格式是否正确")}}},E=()=>{const r=getComputedStyle(document.documentElement),a=d=>r.getPropertyValue(d).trim();Ie.NODE_DEFAULT_BGCOLOR=a("--node-bg"),Ie.NODE_TEXT_COLOR=a("--node-text"),Ie.BACKGROUND_IMAGE_COLOR=a("--node-bg"),Ie.GRID_COLOR=a("--grid-color");const i=document.documentElement.classList.contains("dark");Ie.LGraphCanvas.DEFAULT_BACKGROUND_IMAGE,Ie.LGraphCanvas.link_type_colors={tezheng:"#339966",mubiao:"#CC3366",hangqing:"#3366CC",out1:"#CC6600",out2:"#0ff",target1:"#0ff",target2:"#f56c6c"}},ke=h(!1);He(ke,r=>{r&&o.visible_nodes.forEach(a=>{a.inputs.forEach((i,d)=>{})})}),He(ge,r=>{!k||!o||(r?(o.allow_dragnodes=!1,o.allow_reconnect_links=!1,o.allow_linking=!1,o.allow_select_nodes=!1,k.nodes.forEach(a=>{a.inputs&&a.inputs.forEach(i=>{i.locked=!0}),a.outputs&&a.outputs.forEach(i=>{i.locked=!0}),a.widgets&&a.widgets.forEach(i=>{i.disabled=!0,i.element&&(i.element.style.opacity="0.5",i.element.style.cursor="crosshair",i.element.disabled=!0),console.log("widget:",i)})})):(o.allow_dragnodes=!0,o.allow_dragcanvas=!0,o.allow_reconnect_links=!0,o.allow_linking=!0,o.allow_select_nodes=!0,k.nodes.forEach(a=>{a.inputs&&a.inputs.forEach(i=>{i.locked=!1}),a.outputs&&a.outputs.forEach(i=>{i.locked=!1}),a.widgets&&a.widgets.forEach(i=>{i.disabled=!1,i.element&&(i.element.style.opacity="1",i.element.style.cursor="auto",i.element.disabled=!1)})})),o.draw(!0,!0))});const A=async r=>{try{const i={uid:"0","quantflow-auth":"2","Content-Type":"application/json",Authorization:`${localStorage.getItem("token")}`},d=await fetch(`http://localhost:8000/api/workflow/query?workflow_id=${r}`,{headers:i});if(d.status===404)return oe.error("工作流不存在!"),null;if(d.ok){const f=await d.json();return p.title=f.name,p.owner=f.owner,console.log("获取workflow数据成功:",f),f.last_run_id&&(p.workflow_run_id=f.last_run_id,Qe(null,"auto")),f}else throw new Error(`请求失败: ${d.status} ${d.statusText}`)}catch(a){return console.error("获取workflow数据失败:",a),null}},g=r=>{try{if(!r){console.error("获取workflow数据为空:",r);return}const a=r.litegraph;if(!k||!o){console.error("Graph or GraphCanvas not initialized");return}k.clear();try{if(k.configure(a),k.nodes.forEach(i=>{i._fullTitle?i.title=i.truncateTitle(i._fullTitle):(i._fullTitle=i.title||"",i.title=i.truncateTitle(i.title||""))}),k.nodes.length>0){let i=1/0,d=-1/0,f=1/0,W=-1/0;k.nodes.forEach(Ue=>{var qe,tt;i=Math.min(i,Ue.pos[0]),d=Math.max(d,Ue.pos[0]+(((qe=Ue.size)==null?void 0:qe[0])||0)),f=Math.min(f,Ue.pos[1]),W=Math.max(W,Ue.pos[1]+(((tt=Ue.size)==null?void 0:tt[1])||0))});const re=d-i,ue=W-f,_e=R.value.getBoundingClientRect(),ie=_e.width,ye=_e.height,ne=100,xe=(ie-ne*2)/re,M=(ye-ne*2)/ue,Ce=Math.min(xe,M,1);o.setZoom(Ce);const Te=(i+d)/2,be=(f+W)/2,Ve=window.devicePixelRatio||1;o.ds.offset=[ie/2/Ve-Te*Ce+350,ye/2/Ve-be*Ce+100],o.setDirty(!0,!0)}else o.setZoom(1),o.ds.offset=[0,0];o.draw(!0,!0),console.log("Graph imported successfully")}catch(i){console.error("Error configuring graph:",i),console.log("api工作流数据导入失败：图形数据格式不正确",i)}}catch(a){console.error("Import failed:",a),console.log("api工作流数据导入失败，请检查文件格式是否正确",a)}};ut(async()=>{Y=$t.create(F.value,{value:"",theme:"vs-dark",language:"python",automaticLayout:!0,contextmenu:!0,scrollbar:{alwaysConsumeMouseWheel:!0,useShadows:!1,verticalScrollbarSize:5,horizontalScrollbarSize:5},padding:{top:10,bottom:0,left:0,right:0},renderWhitespace:"none",scrollBeyondLastLine:!1,minimap:{enabled:!1},quickSuggestionsDelay:150,suggest:{snippetsPreventQuickSuggestions:!1,showIcons:!0,maxVisibleSuggestions:8,selectionMode:"always",insertMode:"insert",minWordLength:2,filterGraceful:!0},wordBasedSuggestions:!0,parameterHints:{enabled:!0,cycle:!0},maxFileSize:5}),setTimeout(()=>{Y.setValue(p.code)},100);const r=z(window.location.href);if(console.log("params:",r),console.log("params.workflow_id:",r.workflow_id),r.workflow_id===""||!r.workflow_id?(p.workflow_id="",p.owner="",p.workflow_run_id="",p.title=""):p.workflow_id=r.workflow_id,console.log("onMounted"),!R.value)return;E(),Ie.LGraphCanvas.DEFAULT_BACKGROUND_IMAGE="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAIAAAD/gAIDAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAQBJREFUeNrs1rEKwjAUhlETUkj3vP9rdmr1Ysammk2w5wdxuLgcMHyptfawuZX4pJSWZTnfnu/lnIe/jNNxHHGNn//HNbbv+4dr6V+11uF527arU7+u63qfa/bnmh8sWLBgwYJlqRf8MEptXPBXJXa37BSl3ixYsGDBMliwFLyCV/DeLIMFCxYsWLBMwSt4Be/NggXLYMGCBUvBK3iNruC9WbBgwYJlsGApeAWv4L1ZBgsWLFiwYJmCV/AK3psFC5bBggULloJX8BpdwXuzYMGCBctgwVLwCl7Be7MMFixYsGDBsu8FH1FaSmExVfAxBa/gvVmwYMGCZbBg/W4vAQYA5tRF9QYlv/QAAAAASUVORK5CYII=",Ie.pointerListenerAdd(R.value,"move",f=>{ke.value=o.pointer.dragStarted}),k=new Ie.LGraph,o=new Ie.LGraphCanvas(R.value,k),H.value=k,o.high_quality=!0,o.render_connections_border=!0,o.render_curved_connections=!0,o.links_render_mode=Ie.SPLINE_LINK,o.render_canvas_border=!1,o.render_connection_arrows=!1,o.render_curved_connections=!1,o.links_render_mode=Ie.SPLINE_LINK,o.render_shadows=!0,o.zoom_modify_alpha=!0;const a={canvas:o,graph:k,canvasContainer:R.value.parentElement};Ht(a);const i=()=>{o&&E()},d=new MutationObserver(()=>{i()});if(d.observe(document.documentElement,{attributes:!0,attributeFilter:["class"]}),o.setZoom(Math.max(window.devicePixelRatio,1)),o.ds.offset=[0,0],o.allow_dragcanvas=!0,o.allow_dragnodes=!0,o.allow_interaction=!0,o.allow_searchbox=!1,o.drag_mode=!1,o.allow_reconnect_links=!0,o.allow_zoom=!0,o.onShowNodeCreationDialog=()=>!1,o.onDrawForeground=f=>{for(const W of o.visible_nodes){const re=W._pos,ue=W.size,_e=Ie.NODE_TITLE_HEIGHT,ie=W.flags.collapsed?_e:ue[1]+_e,ye=6,ne=15;if(f.strokeStyle="rgba(39,103,238,0)",f.lineWidth=0,W.status==="success")f.strokeStyle="#33FF00",f.lineWidth=2;else if(W.status==="failed")f.strokeStyle="#FF0033",f.lineWidth=7;else if(W.status==="running"){const Ve=0+L*.6;f.strokeStyle=`rgba(51, 102, 255,${Ve})`,f.lineWidth=4}f.beginPath();const xe=re[0]-ye,M=re[1]-_e-ye;let Ce=ue[0];W.flags.collapsed&&(f.save(),f.restore(),Ce=Math.max(W.width));const Te=Ce+ye*2,be=ie+ye*2;f.moveTo(xe+ne,M),f.lineTo(xe+Te-ne,M),f.quadraticCurveTo(xe+Te,M,xe+Te,M+ne),f.lineTo(xe+Te,M+be-ne),f.quadraticCurveTo(xe+Te,M+be,xe+Te-ne,M+be),f.lineTo(xe+ne,M+be),f.quadraticCurveTo(xe,M+be,xe,M+be-ne),f.lineTo(xe,M+ne),f.quadraticCurveTo(xe,M,xe+ne,M),f.stroke()}},me(),window.addEventListener("resize",me),k.runStep(),i(),k.start(),Le.value=!0,k.change(),o.draw(!0,!0),Ie.NODE_BOX_OUTLINE_COLOR="rgba(39,103,238,0.65)",Ie.WIDGET_BGCOLOR="rgba(16,18,19,0.8)",Ie.WIDGET_SECONDARY_TEXT_COLOR="#858585",Ie.NODE_WIDGET_HEIGHT=25,await new Promise(f=>setTimeout(f,500)),r.workflow_id){const f=await A(r.workflow_id);g(f)}kt(()=>{d.disconnect(),window.removeEventListener("resize",me),k&&(k.stop(),Le.value=!1)})});const C=()=>{const r=k.serialize(),a={format_version:"2.0",name:p.title?p.title:`workflow-${new Date().toISOString().replace(/[:]/g,"-").slice(0,19)}`,description:"",litegraph:r,nodes:[],links:[]};p.title=a.name;const i=z(window.location.href);return i.workflow_id&&(a.id=i.workflow_id),console.log("graphData.nodes:",r.nodes),r.nodes&&Array.isArray(r.nodes)&&(a.nodes=r.nodes.map(d=>{const f=Object.keys(d.properties),W={};return f.forEach(re=>{var ie;const ue=(ie=d.inputs.find(ye=>ye.name===re))==null?void 0:ie.fieldName,_e=d.properties[re];W[ue]=_e}),r.links.find(re=>d.id===re[1]),{uuid:d.flags.uuid,title:d.title||"",name:d.type||"",type:d.type||"",litegraph_id:d.id||0,positionX:d.pos?d.pos[0]:0,positionY:d.pos?d.pos[1]:0,width:d.size?d.size[0]:0,height:d.size?d.size[1]:0,static_input_data:W||{},output_db_id:null}})),r.links&&Array.isArray(r.links)&&(a.links=r.links.map(d=>{var xe,M;const[f,W,re,ue,_e,ie]=d,ye=r.nodes.find(Ce=>Ce.id===W),ne=r.nodes.find(Ce=>Ce.id===ue);return console.log("sourceNode,targetNode:",ye,ne),{uuid:Tt(),litegraph_id:W,status:1,previous_node_uuid:ye.flags.uuid,next_node_uuid:ne.flags.uuid,output_field_name:(xe=ne.inputs[_e])==null?void 0:xe.fieldName,input_field_name:(M=ye.outputs[re])==null?void 0:M.fieldName}})),a},ve=async()=>{if(C().nodes.length===0){oe.warning("当前工作区域为空，不可保存空白工作流！");return}const r=JSON.stringify(C());try{const a=localStorage.getItem("token"),i=await fetch("http://localhost:8000/api/workflow/save",{method:"POST",headers:{uid:"0","quantflow-auth":"2","Content-Type":"application/json",Authorization:`${a}`},body:r});if(i.ok){const d=await i.json();console.log("保存的workflow_id:",d);const f=d.data&&d.data.workflow_id;p.workflow_id=f;const W=new URL(window.location.href);if(W.searchParams.set("workflow_id",f),window.history.replaceState({},"",W),console.log("保存的的workflow_id:",f),p.owner==="*"&&oe.success("模版工作流克隆成功！正在运行中...",{timeout:2e3}),p.owner="",p.workflow_id){const _e=new URL(window.location.href);_e.searchParams.set("workflow_id",p.workflow_id),window.history.replaceState({},"",_e)}const re={uid:"0","quantflow-auth":"2","Content-Type":"application/json",Authorization:`${a}`},ue=await fetch(`http://localhost:8000/api/workflow/query?workflow_id=${d.data.workflow_id}`,{headers:re});if(ue.status===404)return console.log("工作流不存在!"),null;if(ue.ok){const _e=await ue.json();console.log("data:",_e),p.title=_e.name}return f}else throw new Error(`请求失败: ${i.status} ${i.statusText}`)}catch(a){return console.error("保存图表时出错:",a),oe.error("运行失败，请重试！"),null}},we=h(0),Ne=async r=>{try{we.value=-1;const a=localStorage.getItem("token"),i=await fetch("http://localhost:8000/api/workflow/run",{method:"POST",headers:{uid:"0","quantflow-auth":"2","Content-Type":"application/json",Authorization:`${a}`},body:JSON.stringify({workflow_id:r})});if(console.log("运行模式:","LOCAL"),i.ok){const d=await i.json();console.log("运行结果:",d);const f=d.data&&d.data.workflow_run_id;return p.workflow_run_id=f,f}else throw new Error(`请求失败: ${i.status} ${i.statusText}`)}catch(a){return console.error("运行图表时出错:",a),oe.error("运行失败，请重试！"),null}},Be=async r=>{console.log("nodeStatus:",r),k.nodes.forEach(a=>{a.status=null}),r.success_node_ids&&r.success_node_ids.forEach(a=>{const i=k.nodes.find(d=>d.flags.uuid===a);i&&(i.status="success",o.draw(!0,!0))}),r.running_node_ids&&r.running_node_ids.forEach(a=>{const i=k.nodes.find(d=>d.flags.uuid===a);i&&(i.status="running",o.draw(!0,!0))}),r.failed_node_ids&&r.failed_node_ids.forEach(a=>{const i=k.nodes.find(d=>d.flags.uuid===a);i&&(i.status="failed",o.draw(!0,!0))})},Ge=h(!0),De=async(r,a,i,d,f)=>{try{we.value++;const W=localStorage.getItem("token"),re=await Wt.get("http://localhost:8000/api/workflow/run",{params:{workflow_run_id:r,last_log_id:a},headers:{uid:"0","quantflow-auth":"2","Content-Type":"application/json",Authorization:`${W}`}});if(re.status===200){const ue=re.data;console.log("轮询结果:",ue),Be(ue.data);const _e=ue.data&&ue.data.progress;Ee.value=_e,_e<100&&ge.value?p.pollTimer=setTimeout(De.bind(this,r,a,i,d,f),d):(Ge.value=!0,f==="click"?oe.success("运行完成！"):f==="auto"&&oe.success("获取运行结果成功！"),console.log("轮询结束:",ue.data),p.pollData={...ue.data},ge.value=!1,clearTimeout(p.pollTimer));const ie=ue.data&&ue.data.status;if(ie>=2&&(ge.value=!1,Ee.value=_e,clearTimeout(p.pollTimer),ie==3)){const ye=ue.data.last_error_message+`
`+ue.data.last_error_stacktrace;f!=="auto"&&(Ge.value=!1),setTimeout(()=>{Ge.value=!0},300)}ie>2&&f==="auto"&&k.nodes.forEach(ye=>{ye.status=null})}else throw new Error(`请求失败: ${re.status} ${re.statusText}`)}catch(W){console.error("轮询工作流状态时出错:",W),ge.value=!1,clearTimeout(p.pollTimer)}},Qe=async(r,a)=>{if(r&&ze(r),r){if(p.workflow_id=await ve(),console.log("workflow_id:",p.workflow_id),p.workflow_id){const i=await Ne(p.workflow_id);p.workflow_run_id=i,console.log("workflow_run_id11:",p.workflow_run_id),De(i,0,p.user_id,5e3,"click"),ge.value=!0,Ee.value=0,p.pollData=null}}else De(p.workflow_run_id,0,p.user_id,5e3,"auto"),ge.value=!0,Ee.value=0,p.pollData=null},it=async r=>{r&&ze(r);try{ge.value=!1,Ee.value=0,clearTimeout(p.pollTimer),await new Promise(d=>setTimeout(d,500)),k.nodes.forEach(d=>{d.status=null}),o.allow_dragnodes=!0,o.allow_dragcanvas=!0,o.allow_reconnect_links=!0,o.allow_linking=!0,o.allow_select_nodes=!0,k.nodes.forEach(d=>{d.inputs&&d.inputs.forEach(f=>{f.locked=!1}),d.outputs&&d.outputs.forEach(f=>{f.locked=!1}),d.widgets&&d.widgets.forEach(f=>{f.disabled=!1})}),k.setDirtyCanvas(!0),o.draw(!0,!0);const a=localStorage.getItem("token"),i=await fetch("http://localhost:8000/api/workflow/run/terminate",{method:"POST",headers:{uid:"0","quantflow-auth":"2","Content-Type":"application/json",Authorization:`${a}`},body:JSON.stringify({workflow_run_id:p.workflow_run_id})});if(i.ok)console.log("停止运行成功"),oe.success("停止运行成功");else throw new Error(`请求失败: ${i.status} ${i.statusText}`)}catch(a){console.error("停止运行时出错:",a),oe.error("停止运行失败")}},ze=r=>{const a=r.currentTarget;a.classList.remove("animating-ripple"),a.offsetWidth,a.classList.add("animating-ripple"),setTimeout(()=>{a.classList.remove("animating-ripple")},600)};return(r,a)=>(w(),x(Me,null,[Je(U).isBoxShow?(w(),x("div",Ls,[e("div",Es,[e("img",{class:"close-btn",src:Dt,alt:"close",style:{width:"16px",height:"16px",cursor:"pointer"},onClick:a[0]||(a[0]=(...i)=>Je(U).closeBox&&Je(U).closeBox(...i))})]),e("div",Ss,[Re(Ho,{into:!0,factorId:"",taskId:Je(U).id},null,8,["taskId"])])])):We("",!0),Je(s).isBoxShow?(w(),x("div",Rs,[e("div",Ns,[e("img",{class:"close-btn",src:Dt,alt:"close",style:{width:"16px",height:"16px",cursor:"pointer"},onClick:a[1]||(a[1]=(...i)=>Je(s).closeBox&&Je(s).closeBox(...i))})]),e("div",Ds,[Re(pa,{ref_key:"strategyAnalysisRef",ref:T},null,512)])])):We("",!0),e("div",{class:"graph-editor litegraph .litegraph-editor",onDragenter:ft(v,["prevent"]),onDragover:ft(se,["prevent"]),onDragleave:ft(O,["prevent"]),onDrop:ft(de,["prevent"])},[e("div",Is,[e("canvas",{ref_key:"canvas",ref:R},null,512),e("div",Fs,[Re(Ga,{"is-running":ge.value,progress:Ee.value,graph:Je(k),"is-graph-ready":Le.value,canvas:Je(o)},null,8,["is-running","progress","graph","is-graph-ready","canvas"])]),e("div",Bs,[ge.value?(w(),x("div",Ts,P(Ee.value.toFixed(2))+"%",1)):We("",!0),ge.value?(w(),x("div",{key:1,class:"progress-bar",style:Oe({width:Ee.value+"%"})},null,4)):We("",!0),ge.value?(w(),x("button",{key:3,class:"run-button stop-run-button",onClick:a[3]||(a[3]=i=>it(i))},a[5]||(a[5]=[e("svg",{class:"loading-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[e("path",{d:"M6 19h4V5H6v14zm8-14v14h4V5h-4z",fill:"currentColor"})],-1)]))):(w(),x("button",{key:2,class:"run-button",onClick:a[2]||(a[2]=i=>Qe(i))},a[4]||(a[4]=[e("svg",{class:"play-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[e("path",{d:"M8 5.14v14.72a1 1 0 001.5.86l11-7.36a1 1 0 000-1.72l-11-7.36a1 1 0 00-1.5.86z",fill:"currentColor"})],-1)])))]),Re(xs,{logCount:we.value,isCollapsed:Ge.value},null,8,["logCount","isCollapsed"]),Re(Kt,{name:"fade"},{default:Xt(()=>[Fe.value?(w(),x("div",Us,a[6]||(a[6]=[e("div",{class:"drag-message"},[e("svg",{class:"upload-icon",width:"48",height:"48",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[e("path",{d:"M12 3L20 10H16.5V19H7.5V10H4L12 3Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})]),e("p",null,"释放以导入 JSON 文件")],-1)]))):We("",!0)]),_:1})])],32),ct(e("div",Ws,[e("div",Ms,[e("div",Os,[e("img",{onClick:Z,class:"close-btn",src:Mt}),a[7]||(a[7]=e("div",{class:"editorMonaco-head",style:{height:"40px","line-height":"40px","text-indent":"20px",color:"#fff","font-size":"14px"}},"编辑代码",-1)),e("div",{class:"editorMonaco",style:{height:"calc(100% - 100px)"},ref_key:"editorDom",ref:F},null,512),e("div",{class:"editorMonaco-foot",style:{height:"40px","padding-top":"15px"}},[e("div",{class:"content-btn active",onClick:te,style:{display:"inline-flex",float:"right","margin-right":"20px"}},"确定")])])])],512),[[yt,Je(p).codeStatus]])],64))}}),zs=at(Gs,[["__scopeId","data-v-c3fd878c"]]),Vs={class:"quantflow-container"},qs={class:"content-container"},Zs={class:"aside-container"},Qs=dt({__name:"Quantflow",setup(q){const F=h(!0),Y=T=>{F.value=T};return(T,U)=>(w(),x("div",Vs,[Re(Gt),U[0]||(U[0]=mt()),e("div",qs,[e("div",Zs,[Re(Ta,{onCategoriesLoadingState:Y})]),Re(zs)])]))}}),Ks=at(Qs,[["__scopeId","data-v-36d9fec6"]]);export{Ks as default};
