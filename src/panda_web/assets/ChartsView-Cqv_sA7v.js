var Es=Object.defineProperty;var Is=(t,r,e)=>r in t?Es(t,r,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[r]=e;var Yr=(t,r,e)=>Is(t,typeof r!="symbol"?r+"":r,e);import{d as Ne,r as B,f as Ge,C as Ar,c as j,o as H,b as w,y as di,N as pr,t as rt,i as le,A as Kn,_ as Te,a1 as hi,a2 as As,a3 as vi,v as Ze,n as Ce,j as pe,F as ge,k as Ae,x as Un,h as wt,l as Qa,H as Re,a as ne,a4 as Ds,g as Zr,w as dr,a5 as Ps,B as Qn,e as qn,z as Fs,m as $i,L as Rs,I as ar,J as ze,a6 as Bs,G as Jn,a7 as Ya}from"./main-BMJQvnQN.js";import{u as ta,q as cr,r as Wi,N as to,l as $e,t as Ns,v as Os,o as Vs,G as zs,a as $s,b as Ws,c as Ys,d as Zs,e as Hs,f as js,g as Xs,h as Gs,i as Ks,j as Us,k as Qs,m as qs,n as Js,T as tl,w as el,L as eo,s as rl,_ as al}from"./index-CiWTbwln.js";import{r as il}from"./index-C81Kkeck.js";/**
     * @license
     * KLineChart v10.0.1
     * Copyright (c) 2019 lihu.
     * Licensed under Apache License 2.0 https://www.apache.org/licenses/LICENSE-2.0
     */function ye(t,r){if(!(!He(t)&&!He(r))){for(var e in r)if(Object.prototype.hasOwnProperty.call(r,e)){var a=t[e],i=r[e];He(i)&&He(a)?ye(a,i):R(r[e])&&(t[e]=Er(r[e]))}}}function Er(t){if(!He(t))return t;var r=null;Ue(t)?r=[]:r={};for(var e in t)if(Object.prototype.hasOwnProperty.call(t,e)){var a=t[e];He(a)?r[e]=Er(a):r[e]=a}return r}function Ue(t){return Object.prototype.toString.call(t)==="[object Array]"}function Ie(t){return typeof t=="function"}function He(t){return typeof t=="object"&&R(t)}function vt(t){return typeof t=="number"&&Number.isFinite(t)}function R(t){return t!=null}function Qr(t){return typeof t=="boolean"}function Kt(t){return typeof t=="string"}var nl=/\\(\\)?/g,ol=RegExp(`[^.[\\]]+|\\[(?:([^"'][^[]*)|(["'])((?:(?!\\2)[^\\\\]|\\\\.)*?)\\2)\\]|(?=(?:\\.|\\[\\])(?:\\.|\\[\\]|$))`,"g");function be(t,r,e){if(R(t)){var a=[];r.replace(ol,function(s){for(var l=[],c=1;c<arguments.length;c++)l[c-1]=arguments[c];var u=s;return R(l[1])?u=l[2].replace(nl,"$1"):R(l[0])&&(u=l[0].trim()),a.push(u),""});for(var i=t,n=0,o=a.length;R(i)&&n<o;)i=i==null?void 0:i[a[n++]];return R(i)?i:e??"--"}return e??"--"}function sl(t,r){var e={};return t.formatToParts(new Date(r)).forEach(function(a){var i=a.type,n=a.value;switch(i){case"year":{e.YYYY=n;break}case"month":{e.MM=n;break}case"day":{e.DD=n;break}case"hour":{e.HH=n==="24"?"00":n;break}case"minute":{e.mm=n;break}case"second":{e.ss=n;break}}}),e}function ro(t,r,e){var a=sl(t,r);return e.replace(/YYYY|MM|DD|HH|mm|ss/g,function(i){return a[i]})}function Me(t,r){var e=+t;return vt(e)?e.toFixed(r??2):"".concat(t)}function ao(t){var r=+t;if(vt(r)){if(r>1e9)return"".concat(+(r/1e9).toFixed(3),"B");if(r>1e6)return"".concat(+(r/1e6).toFixed(3),"M");if(r>1e3)return"".concat(+(r/1e3).toFixed(3),"K")}return"".concat(t)}function io(t,r){var e="".concat(t);if(r.length===0)return e;if(e.includes(".")){var a=e.split(".");return"".concat(a[0].replace(/(\d)(?=(\d{3})+$)/g,function(i){return"".concat(i).concat(r)}),".").concat(a[1])}return e.replace(/(\d)(?=(\d{3})+$)/g,function(i){return"".concat(i).concat(r)})}function no(t,r){var e="".concat(t),a=new RegExp("\\.0{"+r+",}[1-9][0-9]*$");if(a.test(e)){var i=e.split("."),n=i.length-1,o=i[n],s=/0*/.exec(o);if(R(s)){var l=s[0].length;return i[n]=o.replace(/0*/,"0{".concat(l,"}")),i.join(".")}}return e}function Yi(t,r){return t.replace(/\{(\w+)\}/g,function(e,a){var i=r[a];return R(i)?i:"{".concat(a,"}")})}var Hr=null;function vr(t){var r,e;return(e=(r=t.ownerDocument.defaultView)===null||r===void 0?void 0:r.devicePixelRatio)!==null&&e!==void 0?e:1}function br(t,r,e){return"".concat(r??"normal"," ").concat(t??12,"px ").concat(e??"Helvetica Neue")}function je(t,r,e,a){if(!R(Hr)){var i=document.createElement("canvas"),n=vr(i);Hr=i.getContext("2d"),Hr.scale(n,n)}return Hr.font=br(r,e,a),Math.round(Hr.measureText(t).width)}var qa=function(t,r){return qa=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,a){e.__proto__=a}||function(e,a){for(var i in a)Object.prototype.hasOwnProperty.call(a,i)&&(e[i]=a[i])},qa(t,r)};function St(t,r){if(typeof r!="function"&&r!==null)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");qa(t,r);function e(){this.constructor=t}t.prototype=r===null?Object.create(r):(e.prototype=r.prototype,new e)}var J=function(){return J=Object.assign||function(r){for(var e,a=1,i=arguments.length;a<i;a++){e=arguments[a];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(r[n]=e[n])}return r},J.apply(this,arguments)};function ga(t,r){var e={};for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&r.indexOf(a)<0&&(e[a]=t[a]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,a=Object.getOwnPropertySymbols(t);i<a.length;i++)r.indexOf(a[i])<0&&Object.prototype.propertyIsEnumerable.call(t,a[i])&&(e[a[i]]=t[a[i]]);return e}function oo(t,r,e,a){function i(n){return n instanceof e?n:new e(function(o){o(n)})}return new(e||(e=Promise))(function(n,o){function s(u){try{c(a.next(u))}catch(d){o(d)}}function l(u){try{c(a.throw(u))}catch(d){o(d)}}function c(u){u.done?n(u.value):i(u.value).then(s,l)}c((a=a.apply(t,[])).next())})}function so(t,r){var e={label:0,sent:function(){if(n[0]&1)throw n[1];return n[1]},trys:[],ops:[]},a,i,n,o=Object.create((typeof Iterator=="function"?Iterator:Object).prototype);return o.next=s(0),o.throw=s(1),o.return=s(2),typeof Symbol=="function"&&(o[Symbol.iterator]=function(){return this}),o;function s(c){return function(u){return l([c,u])}}function l(c){if(a)throw new TypeError("Generator is already executing.");for(;o&&(o=0,c[0]&&(e=0)),e;)try{if(a=1,i&&(n=c[0]&2?i.return:c[0]?i.throw||((n=i.return)&&n.call(i),0):i.next)&&!(n=n.call(i,c[1])).done)return n;switch(i=0,n&&(c=[c[0]&2,n.value]),c[0]){case 0:case 1:n=c;break;case 4:return e.label++,{value:c[1],done:!1};case 5:e.label++,i=c[1],c=[0];continue;case 7:c=e.ops.pop(),e.trys.pop();continue;default:if(n=e.trys,!(n=n.length>0&&n[n.length-1])&&(c[0]===6||c[0]===2)){e=0;continue}if(c[0]===3&&(!n||c[1]>n[0]&&c[1]<n[3])){e.label=c[1];break}if(c[0]===6&&e.label<n[1]){e.label=n[1],n=c;break}if(n&&e.label<n[2]){e.label=n[2],e.ops.push(c);break}n[2]&&e.ops.pop(),e.trys.pop();continue}c=r.call(t,e)}catch(u){c=[6,u],i=0}finally{a=n=0}if(c[0]&5)throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}}function Xe(t){var r=typeof Symbol=="function"&&Symbol.iterator,e=r&&t[r],a=0;if(e)return e.call(t);if(t&&typeof t.length=="number")return{next:function(){return t&&a>=t.length&&(t=void 0),{value:t&&t[a++],done:!t}}};throw new TypeError(r?"Object is not iterable.":"Symbol.iterator is not defined.")}function nr(t,r){var e=typeof Symbol=="function"&&t[Symbol.iterator];if(!e)return t;var a=e.call(t),i,n=[],o;try{for(;(r===void 0||r-- >0)&&!(i=a.next()).done;)n.push(i.value)}catch(s){o={error:s}}finally{try{i&&!i.done&&(e=a.return)&&e.call(a)}finally{if(o)throw o.error}}return n}function hr(t,r,e){if(arguments.length===2)for(var a=0,i=r.length,n;a<i;a++)(n||!(a in r))&&(n||(n=Array.prototype.slice.call(r,0,a)),n[a]=r[a]);return t.concat(n||Array.prototype.slice.call(r))}function ma(t){var r={width:0,height:0,left:0,right:0,top:0,bottom:0};return R(t)&&ye(r,t),r}var Tr=-1;function ya(t){return Ie(window.requestAnimationFrame)?window.requestAnimationFrame(t):window.setTimeout(t,20)}function Zi(t){Ie(window.cancelAnimationFrame)?window.cancelAnimationFrame(t):window.clearTimeout(t)}function Hi(t){if(Ie(window.requestIdleCallback))return window.requestIdleCallback(t);var r=performance.now();return window.setTimeout(function(){t({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(performance.now()-r))}})},1)}function ll(t){Ie(window.cancelIdleCallback)?window.cancelIdleCallback(t):window.clearTimeout(t)}var Ja=function(){function t(r){this._options={duration:500,iterationCount:1},this._currentIterationCount=0,this._running=!1,this._time=0,ye(this._options,r)}return t.prototype._loop=function(){var r=this;this._running=!0;var e=function(){var a;if(r._running){var i=new Date().getTime()-r._time;i<r._options.duration?((a=r._doFrameCallback)===null||a===void 0||a.call(r,i),ya(e)):(r.stop(),r._currentIterationCount++,r._currentIterationCount<r._options.iterationCount&&r.start())}};ya(e)},t.prototype.doFrame=function(r){return this._doFrameCallback=r,this},t.prototype.setDuration=function(r){return this._options.duration=r,this},t.prototype.setIterationCount=function(r){return this._options.iterationCount=r,this},t.prototype.start=function(){this._running||(this._time=new Date().getTime(),this._loop())},t.prototype.stop=function(){var r;this._running&&((r=this._doFrameCallback)===null||r===void 0||r.call(this,this._options.duration)),this._running=!1},t}(),Za=1,ji=new Date().getTime();function ua(t){var r=new Date().getTime();return r===ji?++Za:Za=1,ji=r,"".concat(t??"").concat(r,"_").concat(Za)}function sr(t,r){var e,a=document.createElement(t),i=r??{};for(var n in i)a.style[n]=(e=i[n])!==null&&e!==void 0?e:"";return a}function ti(t,r,e){var a=0,i=0;for(i=t.length-1;a!==i;){var n=Math.floor((i+a)/2),o=i-a,s=t[n][r];if(e===t[a][r])return a;if(e===t[i][r])return i;if(e===s)return n;if(e>s?a=n:i=n,o<=2)break}return a}function cl(t){var r=Math.floor(rr(t)),e=Cr(r),a=t/e,i=0;return a<1.5?i=1:a<2.5?i=2:a<3.5?i=3:a<4.5?i=4:a<5.5?i=5:a<6.5?i=6:i=8,t=i*e,+t.toFixed(Math.abs(r))}function Xi(t,r){r=Math.max(0,r??0);var e=Math.pow(10,r);return Math.round(t*e)/e}function ul(t){var r=t.toString(),e=r.indexOf("e");if(e>0){var a=+r.slice(e+1);return a<0?-a:0}var i=r.indexOf(".");return i<0?0:r.length-1-i}function lo(t,r,e){for(var a,i,n=[Number.MIN_SAFE_INTEGER,Number.MAX_SAFE_INTEGER],o=t.length,s=0;s<o;){var l=t[s];n[0]=Math.max((a=l[r])!==null&&a!==void 0?a:Number.MIN_SAFE_INTEGER,n[0]),n[1]=Math.min((i=l[e])!==null&&i!==void 0?i:Number.MAX_SAFE_INTEGER,n[1]),++s}return n}function rr(t){return t===0?0:Math.log10(t)}function Cr(t){return Math.pow(10,t)}function Gi(){return{from:0,to:0,realFrom:0,realTo:0}}function Ki(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return t.join("_")}var dl=function(){function t(r){this._requestIdleCallbackId=Tr,this._tasks=r??[],this._operateTasks()}return t.prototype._operateTasks=function(r){var e=this;this._requestIdleCallbackId!==Tr&&(ll(this._requestIdleCallbackId),this._requestIdleCallbackId=Tr),r==null||r(),this._requestIdleCallbackId=Hi(function(a){e._runTasks(a)})},t.prototype._runTasks=function(r){for(var e=this;r.timeRemaining()>0&&this._tasks.length>0;){var a=this._tasks.shift();a==null||a.handler()}this._tasks.length>0&&(this._requestIdleCallbackId=Hi(function(i){e._runTasks(i)}))},t.prototype.addTask=function(r){var e=this;return this._operateTasks(function(){var a=e._tasks.findIndex(function(i){return i.id===r.id});a>-1?e._tasks[a]=r:e._tasks.push(r)}),this},t.prototype.removeTask=function(r){var e=this;return R(r)&&this._operateTasks(function(){if(R(r)){var a=e._tasks.findIndex(function(i){return i.id===r});a>-1&&e._tasks.splice(a,1)}else e._tasks=[]}),this},t}(),hl=function(){function t(){this._callbacks=[]}return t.prototype.subscribe=function(r){var e=this._callbacks.indexOf(r);e<0&&this._callbacks.push(r)},t.prototype.unsubscribe=function(r){if(Ie(r)){var e=this._callbacks.indexOf(r);e>-1&&this._callbacks.splice(e,1)}else this._callbacks=[]},t.prototype.execute=function(r){this._callbacks.forEach(function(e){e(r)})},t.prototype.isEmpty=function(){return this._callbacks.length===0},t}();function Ir(t){return t==="transparent"||t==="none"||/^[rR][gG][Bb][Aa]\(([\s]*(2[0-4][0-9]|25[0-5]|[01]?[0-9][0-9]?)[\s]*,){3}[\s]*0[\s]*\)$/.test(t)||/^[hH][Ss][Ll][Aa]\(([\s]*(360｜3[0-5][0-9]|[012]?[0-9][0-9]?)[\s]*,)([\s]*((100|[0-9][0-9]?)%|0)[\s]*,){2}([\s]*0[\s]*)\)$/.test(t)}function fr(t,r){var e=t.replace(/^#/,""),a=parseInt(e,16),i=a>>16&255,n=a>>8&255,o=a&255;return"rgba(".concat(i,", ").concat(n,", ").concat(o,", ").concat(r??1,")")}var ft={RED:"#F92855",GREEN:"#2DC08E",WHITE:"#FFFFFF",GREY:"#76808F",BLUE:"#1677FF"};function vl(){return{show:!0,horizontal:{show:!0,size:1,color:"#EDEDED",style:"dashed",dashedValue:[2,2]},vertical:{show:!0,size:1,color:"#EDEDED",style:"dashed",dashedValue:[2,2]}}}function fl(){var t={show:!0,color:ft.GREY,textOffset:5,textSize:10,textFamily:"Helvetica Neue",textWeight:"normal"};return{type:"candle_solid",bar:{compareRule:"current_open",upColor:ft.RED,downColor:ft.GREEN,noChangeColor:ft.GREY,upBorderColor:ft.RED,downBorderColor:ft.GREEN,noChangeBorderColor:ft.GREY,upWickColor:ft.RED,downWickColor:ft.GREEN,noChangeWickColor:ft.GREY},area:{lineSize:2,lineColor:ft.BLUE,smooth:!1,value:"close",backgroundColor:[{offset:0,color:fr(ft.BLUE,.01)},{offset:1,color:fr(ft.BLUE,.2)}],point:{show:!0,color:ft.BLUE,radius:4,rippleColor:fr(ft.BLUE,.3),rippleRadius:8,animation:!0,animationDuration:1e3}},priceMark:{show:!0,high:J({},t),low:J({},t),last:{show:!0,compareRule:"current_open",upColor:ft.RED,downColor:ft.GREEN,noChangeColor:ft.GREY,line:{show:!0,style:"dashed",dashedValue:[4,4],size:1},text:{show:!0,style:"fill",size:12,paddingLeft:4,paddingTop:4,paddingRight:4,paddingBottom:4,borderColor:"transparent",borderStyle:"solid",borderSize:0,borderDashedValue:[2,2],color:ft.WHITE,family:"Helvetica Neue",weight:"normal",borderRadius:2},extendTexts:[]}},tooltip:{offsetLeft:4,offsetTop:6,offsetRight:4,offsetBottom:6,showRule:"always",showType:"standard",rect:{position:"fixed",paddingLeft:4,paddingRight:4,paddingTop:4,paddingBottom:4,offsetLeft:4,offsetTop:4,offsetRight:4,offsetBottom:4,borderRadius:4,borderSize:1,borderColor:"#F2F3F5",color:"#FEFEFE"},title:{show:!0,size:14,family:"Helvetica Neue",weight:"normal",color:ft.GREY,marginLeft:8,marginTop:4,marginRight:8,marginBottom:4,template:"{ticker} · {period}"},legend:{size:12,family:"Helvetica Neue",weight:"normal",color:ft.GREY,marginLeft:8,marginTop:4,marginRight:8,marginBottom:4,defaultValue:"n/a",custom:[{title:"time",value:"{time}"},{title:"open",value:"{open}"},{title:"high",value:"{high}"},{title:"low",value:"{low}"},{title:"close",value:"{close}"},{title:"volume",value:"{volume}"}]},features:[]}}}function pl(){var t=fr(ft.RED,.7),r=fr(ft.GREEN,.7);return{ohlc:{compareRule:"current_open",upColor:t,downColor:r,noChangeColor:ft.GREY},bars:[{style:"fill",borderStyle:"solid",borderSize:1,borderDashedValue:[2,2],upColor:t,downColor:r,noChangeColor:ft.GREY}],lines:["#FF9600","#935EBD",ft.BLUE,"#E11D74","#01C5C4"].map(function(e){return{style:"solid",smooth:!1,size:1,dashedValue:[2,2],color:e}}),circles:[{style:"fill",borderStyle:"solid",borderSize:1,borderDashedValue:[2,2],upColor:t,downColor:r,noChangeColor:ft.GREY}],lastValueMark:{show:!1,text:{show:!1,style:"fill",color:ft.WHITE,size:12,family:"Helvetica Neue",weight:"normal",borderStyle:"solid",borderColor:"transparent",borderSize:0,borderDashedValue:[2,2],paddingLeft:4,paddingTop:4,paddingRight:4,paddingBottom:4,borderRadius:2}},tooltip:{offsetLeft:4,offsetTop:6,offsetRight:4,offsetBottom:6,showRule:"always",showType:"standard",title:{show:!0,showName:!0,showParams:!0,size:12,family:"Helvetica Neue",weight:"normal",color:ft.GREY,marginLeft:8,marginTop:4,marginRight:8,marginBottom:4},legend:{size:12,family:"Helvetica Neue",weight:"normal",color:ft.GREY,marginLeft:8,marginTop:4,marginRight:8,marginBottom:4,defaultValue:"n/a"},features:[]}}}function Ui(){return{show:!0,size:"auto",axisLine:{show:!0,color:"#DDDDDD",size:1},tickText:{show:!0,color:ft.GREY,size:12,family:"Helvetica Neue",weight:"normal",marginStart:4,marginEnd:6},tickLine:{show:!0,size:1,length:3,color:"#DDDDDD"}}}function gl(){return{show:!0,horizontal:{show:!0,line:{show:!0,style:"dashed",dashedValue:[4,2],size:1,color:ft.GREY},text:{show:!0,style:"fill",color:ft.WHITE,size:12,family:"Helvetica Neue",weight:"normal",borderStyle:"solid",borderDashedValue:[2,2],borderSize:1,borderColor:ft.GREY,borderRadius:2,paddingLeft:4,paddingRight:4,paddingTop:4,paddingBottom:4,backgroundColor:ft.GREY},features:[]},vertical:{show:!0,line:{show:!0,style:"dashed",dashedValue:[4,2],size:1,color:ft.GREY},text:{show:!0,style:"fill",color:ft.WHITE,size:12,family:"Helvetica Neue",weight:"normal",borderStyle:"solid",borderDashedValue:[2,2],borderSize:1,borderColor:ft.GREY,borderRadius:2,paddingLeft:4,paddingRight:4,paddingTop:4,paddingBottom:4,backgroundColor:ft.GREY}}}}function ml(){var t=fr(ft.BLUE,.35),r=fr(ft.BLUE,.25);function e(){return{style:"fill",color:ft.WHITE,size:12,family:"Helvetica Neue",weight:"normal",borderStyle:"solid",borderDashedValue:[2,2],borderSize:1,borderRadius:2,borderColor:ft.BLUE,paddingLeft:4,paddingRight:4,paddingTop:4,paddingBottom:4,backgroundColor:ft.BLUE}}return{point:{color:ft.BLUE,borderColor:t,borderSize:1,radius:5,activeColor:ft.BLUE,activeBorderColor:t,activeBorderSize:3,activeRadius:5},line:{style:"solid",smooth:!1,color:ft.BLUE,size:1,dashedValue:[2,2]},rect:{style:"fill",color:r,borderColor:ft.BLUE,borderSize:1,borderRadius:0,borderStyle:"solid",borderDashedValue:[2,2]},polygon:{style:"fill",color:ft.BLUE,borderColor:ft.BLUE,borderSize:1,borderStyle:"solid",borderDashedValue:[2,2]},circle:{style:"fill",color:r,borderColor:ft.BLUE,borderSize:1,borderStyle:"solid",borderDashedValue:[2,2]},arc:{style:"solid",color:ft.BLUE,size:1,dashedValue:[2,2]},text:e()}}function yl(){return{size:1,color:"#DDDDDD",fill:!0,activeBackgroundColor:fr(ft.BLUE,.08)}}function _l(){return{grid:vl(),candle:fl(),indicator:pl(),xAxis:Ui(),yAxis:Ui(),separator:yl(),crosshair:gl(),overlay:ml()}}function fi(t,r,e,a){var i=t.result,n=t.figures,o=t.styles,s=be(o,"circles",e.circles),l=s.length,c=be(o,"bars",e.bars),u=c.length,d=be(o,"lines",e.lines),h=d.length,v=0,f=0,p=0,g,m=0;n.forEach(function(_){var x;switch(_.type){case"circle":{m=v;var S=s[v%l];g=J(J({},S),{color:S.noChangeColor}),v++;break}case"bar":{m=f;var y=c[f%u];g=J(J({},y),{color:y.noChangeColor}),f++;break}case"line":{m=p,g=d[p%h],p++;break}}if(R(_.type)){var k=(x=_.styles)===null||x===void 0?void 0:x.call(_,{data:{prev:i[r-1],current:i[r],next:i[r+1]},indicator:t,defaultStyles:e});a(_,J(J({},g),k),m)}})}var co=function(){function t(r){this.precision=4,this.calcParams=[],this.shouldOhlc=!1,this.shouldFormatBigNumber=!1,this.visible=!0,this.zLevel=0,this.series="normal",this.figures=[],this.minValue=null,this.maxValue=null,this.styles=null,this.shouldUpdate=function(e,a){var i=JSON.stringify(e.calcParams)!==JSON.stringify(a.calcParams)||e.figures!==a.figures||e.calc!==a.calc,n=i||e.shortName!==a.shortName||e.series!==a.series||e.minValue!==a.minValue||e.maxValue!==a.maxValue||e.precision!==a.precision||e.shouldOhlc!==a.shouldOhlc||e.shouldFormatBigNumber!==a.shouldFormatBigNumber||e.visible!==a.visible||e.zLevel!==a.zLevel||e.extendData!==a.extendData||e.regenerateFigures!==a.regenerateFigures||e.createTooltipDataSource!==a.createTooltipDataSource||e.draw!==a.draw;return{calc:i,draw:n}},this.calc=function(){return[]},this.regenerateFigures=null,this.createTooltipDataSource=null,this.draw=null,this.onDataStateChange=null,this.result=[],this._lockSeriesPrecision=!1,this.override(r),this._lockSeriesPrecision=!1}return t.prototype.override=function(r){var e,a,i=this,n=i.result,o=ga(i,["result"]);this._prevIndicator=J(J({},Er(o)),{result:n});var s=r.id,l=r.name,c=r.shortName,u=r.precision,d=r.styles,h=r.figures,v=r.calcParams,f=ga(r,["id","name","shortName","precision","styles","figures","calcParams"]);!Kt(this.id)&&Kt(s)&&(this.id=s),Kt(this.name)||(this.name=l??""),this.shortName=(e=c??this.shortName)!==null&&e!==void 0?e:this.name,vt(u)&&(this.precision=u,this._lockSeriesPrecision=!0),R(d)&&((a=this.styles)!==null&&a!==void 0||(this.styles={}),ye(this.styles,d)),ye(this,f),R(v)&&(this.calcParams=v,Ie(this.regenerateFigures)&&(this.figures=this.regenerateFigures(this.calcParams))),this.figures=h??this.figures},t.prototype.setSeriesPrecision=function(r){this._lockSeriesPrecision||(this.precision=r)},t.prototype.shouldUpdateImp=function(){var r=this._prevIndicator.zLevel!==this.zLevel,e=this.shouldUpdate(this._prevIndicator,this);return Qr(e)?{calc:e,draw:e,sort:r}:J(J({},e),{sort:r})},t.prototype.calcImp=function(r){return oo(this,void 0,void 0,function(){var e;return so(this,function(a){switch(a.label){case 0:return a.trys.push([0,2,,3]),[4,this.calc(r,this)];case 1:return e=a.sent(),this.result=e,[2,!0];case 2:return a.sent(),[2,!1];case 3:return[2]}})})},t.extend=function(r){var e=function(a){St(i,a);function i(){return a.call(this,r)||this}return i}(t);return e},t}(),Cl={name:"AVP",shortName:"AVP",series:"price",precision:2,figures:[{key:"avp",title:"AVP: ",type:"line"}],calc:function(t){var r=0,e=0;return t.map(function(a){var i,n,o={},s=(i=a.turnover)!==null&&i!==void 0?i:0,l=(n=a.volume)!==null&&n!==void 0?n:0;return r+=s,e+=l,e!==0&&(o.avp=r/e),o})}},bl={name:"AO",shortName:"AO",calcParams:[5,34],figures:[{key:"ao",title:"AO: ",type:"bar",baseValue:0,styles:function(t){var r,e,a=t.data,i=t.indicator,n=t.defaultStyles,o=a.prev,s=a.current,l=(r=o==null?void 0:o.ao)!==null&&r!==void 0?r:Number.MIN_SAFE_INTEGER,c=(e=s==null?void 0:s.ao)!==null&&e!==void 0?e:Number.MIN_SAFE_INTEGER,u="";c>l?u=be(i.styles,"bars[0].upColor",n.bars[0].upColor):u=be(i.styles,"bars[0].downColor",n.bars[0].downColor);var d=c>l?"stroke":"fill";return{color:u,style:d,borderColor:u}}}],calc:function(t,r){var e=r.calcParams,a=Math.max(e[0],e[1]),i=0,n=0,o=0,s=0;return t.map(function(l,c){var u={},d=(l.low+l.high)/2;if(i+=d,n+=d,c>=e[0]-1){o=i/e[0];var h=t[c-(e[0]-1)];i-=(h.low+h.high)/2}if(c>=e[1]-1){s=n/e[1];var h=t[c-(e[1]-1)];n-=(h.low+h.high)/2}return c>=a-1&&(u.ao=o-s),u})}},xl={name:"BIAS",shortName:"BIAS",calcParams:[6,12,24],figures:[{key:"bias1",title:"BIAS6: ",type:"line"},{key:"bias2",title:"BIAS12: ",type:"line"},{key:"bias3",title:"BIAS24: ",type:"line"}],regenerateFigures:function(t){return t.map(function(r,e){return{key:"bias".concat(e+1),title:"BIAS".concat(r,": "),type:"line"}})},calc:function(t,r){var e=r.calcParams,a=r.figures,i=[];return t.map(function(n,o){var s={},l=n.close;return e.forEach(function(c,u){var d;if(i[u]=((d=i[u])!==null&&d!==void 0?d:0)+l,o>=c-1){var h=i[u]/e[u];s[a[u].key]=(l-h)/h*100,i[u]-=t[o-(c-1)].close}}),s})}};function wl(t,r){var e=t.length,a=0;return t.forEach(function(i){var n=i.close-r;a+=n*n}),a=Math.abs(a),Math.sqrt(a/e)}var Ll={name:"BOLL",shortName:"BOLL",series:"price",calcParams:[20,2],precision:2,shouldOhlc:!0,figures:[{key:"up",title:"UP: ",type:"line"},{key:"mid",title:"MID: ",type:"line"},{key:"dn",title:"DN: ",type:"line"}],calc:function(t,r){var e=r.calcParams,a=e[0]-1,i=0;return t.map(function(n,o){var s=n.close,l={};if(i+=s,o>=a){l.mid=i/e[0];var c=wl(t.slice(o-a,o+1),l.mid);l.up=l.mid+e[1]*c,l.dn=l.mid-e[1]*c,i-=t[o-a].close}return l})}},Sl={name:"BRAR",shortName:"BRAR",calcParams:[26],figures:[{key:"br",title:"BR: ",type:"line"},{key:"ar",title:"AR: ",type:"line"}],calc:function(t,r){var e=r.calcParams,a=0,i=0,n=0,o=0;return t.map(function(s,l){var c,u,d={},h=s.high,v=s.low,f=s.open,p=((c=t[l-1])!==null&&c!==void 0?c:s).close;if(n+=h-f,o+=f-v,a+=h-p,i+=p-v,l>=e[0]-1){o!==0?d.ar=n/o*100:d.ar=0,i!==0?d.br=a/i*100:d.br=0;var g=t[l-(e[0]-1)],m=g.high,_=g.low,x=g.open,S=((u=t[l-e[0]])!==null&&u!==void 0?u:t[l-(e[0]-1)]).close;a-=m-S,i-=S-_,n-=m-x,o-=x-_}return d})}},kl={name:"BBI",shortName:"BBI",series:"price",precision:2,calcParams:[3,6,12,24],shouldOhlc:!0,figures:[{key:"bbi",title:"BBI: ",type:"line"}],calc:function(t,r){var e=r.calcParams,a=Math.max.apply(Math,hr([],nr(e),!1)),i=[],n=[];return t.map(function(o,s){var l={},c=o.close;if(e.forEach(function(d,h){var v;i[h]=((v=i[h])!==null&&v!==void 0?v:0)+c,s>=d-1&&(n[h]=i[h]/d,i[h]-=t[s-(d-1)].close)}),s>=a-1){var u=0;n.forEach(function(d){u+=d}),l.bbi=u/4}return l})}},Ml={name:"CCI",shortName:"CCI",calcParams:[20],figures:[{key:"cci",title:"CCI: ",type:"line"}],calc:function(t,r){var e=r.calcParams,a=e[0]-1,i=0,n=[];return t.map(function(o,s){var l={},c=(o.high+o.low+o.close)/3;if(i+=c,n.push(c),s>=a){var u=i/e[0],d=n.slice(s-a,s+1),h=0;d.forEach(function(p){h+=Math.abs(p-u)});var v=h/e[0];l.cci=v!==0?(c-u)/v/.015:0;var f=(t[s-a].high+t[s-a].low+t[s-a].close)/3;i-=f}return l})}},Tl={name:"CR",shortName:"CR",calcParams:[26,10,20,40,60],figures:[{key:"cr",title:"CR: ",type:"line"},{key:"ma1",title:"MA1: ",type:"line"},{key:"ma2",title:"MA2: ",type:"line"},{key:"ma3",title:"MA3: ",type:"line"},{key:"ma4",title:"MA4: ",type:"line"}],calc:function(t,r){var e=r.calcParams,a=Math.ceil(e[1]/2.5+1),i=Math.ceil(e[2]/2.5+1),n=Math.ceil(e[3]/2.5+1),o=Math.ceil(e[4]/2.5+1),s=0,l=[],c=0,u=[],d=0,h=[],v=0,f=[],p=[];return t.forEach(function(g,m){var _,x,S,y,k,C={},D=(_=t[m-1])!==null&&_!==void 0?_:g,E=(D.high+D.close+D.low+D.open)/4,I=Math.max(0,g.high-E),T=Math.max(0,E-g.low);m>=e[0]-1&&(T!==0?C.cr=I/T*100:C.cr=0,s+=C.cr,c+=C.cr,d+=C.cr,v+=C.cr,m>=e[0]+e[1]-2&&(l.push(s/e[1]),m>=e[0]+e[1]+a-3&&(C.ma1=l[l.length-1-a]),s-=(x=p[m-(e[1]-1)].cr)!==null&&x!==void 0?x:0),m>=e[0]+e[2]-2&&(u.push(c/e[2]),m>=e[0]+e[2]+i-3&&(C.ma2=u[u.length-1-i]),c-=(S=p[m-(e[2]-1)].cr)!==null&&S!==void 0?S:0),m>=e[0]+e[3]-2&&(h.push(d/e[3]),m>=e[0]+e[3]+n-3&&(C.ma3=h[h.length-1-n]),d-=(y=p[m-(e[3]-1)].cr)!==null&&y!==void 0?y:0),m>=e[0]+e[4]-2&&(f.push(v/e[4]),m>=e[0]+e[4]+o-3&&(C.ma4=f[f.length-1-o]),v-=(k=p[m-(e[4]-1)].cr)!==null&&k!==void 0?k:0)),p.push(C)}),p}},El={name:"DMA",shortName:"DMA",calcParams:[10,50,10],figures:[{key:"dma",title:"DMA: ",type:"line"},{key:"ama",title:"AMA: ",type:"line"}],calc:function(t,r){var e=r.calcParams,a=Math.max(e[0],e[1]),i=0,n=0,o=0,s=[];return t.forEach(function(l,c){var u,d={},h=l.close;i+=h,n+=h;var v=0,f=0;if(c>=e[0]-1&&(v=i/e[0],i-=t[c-(e[0]-1)].close),c>=e[1]-1&&(f=n/e[1],n-=t[c-(e[1]-1)].close),c>=a-1){var p=v-f;d.dma=p,o+=p,c>=a+e[2]-2&&(d.ama=o/e[2],o-=(u=s[c-(e[2]-1)].dma)!==null&&u!==void 0?u:0)}s.push(d)}),s}},Il={name:"DMI",shortName:"DMI",calcParams:[14,6],figures:[{key:"pdi",title:"PDI: ",type:"line"},{key:"mdi",title:"MDI: ",type:"line"},{key:"adx",title:"ADX: ",type:"line"},{key:"adxr",title:"ADXR: ",type:"line"}],calc:function(t,r){var e=r.calcParams,a=0,i=0,n=0,o=0,s=0,l=0,c=0,u=0,d=[];return t.forEach(function(h,v){var f,p,g={},m=(f=t[v-1])!==null&&f!==void 0?f:h,_=m.close,x=h.high,S=h.low,y=x-S,k=Math.abs(x-_),C=Math.abs(_-S),D=x-m.high,E=m.low-S,I=Math.max(Math.max(y,k),C),T=D>0&&D>E?D:0,L=E>0&&E>D?E:0;if(a+=I,i+=T,n+=L,v>=e[0]-1){v>e[0]-1?(o=o-o/e[0]+I,s=s-s/e[0]+T,l=l-l/e[0]+L):(o=a,s=i,l=n);var b=0,M=0;o!==0&&(b=s*100/o,M=l*100/o),g.pdi=b,g.mdi=M;var A=0;M+b!==0&&(A=Math.abs(M-b)/(M+b)*100),c+=A,v>=e[0]*2-2&&(v>e[0]*2-2?u=(u*(e[0]-1)+A)/e[0]:u=c/e[0],g.adx=u,v>=e[0]*2+e[1]-3&&(g.adxr=(((p=d[v-(e[1]-1)].adx)!==null&&p!==void 0?p:0)+u)/2))}d.push(g)}),d}},Al={name:"EMV",shortName:"EMV",calcParams:[14,9],figures:[{key:"emv",title:"EMV: ",type:"line"},{key:"maEmv",title:"MAEMV: ",type:"line"}],calc:function(t,r){var e=r.calcParams,a=0,i=[];return t.map(function(n,o){var s,l={};if(o>0){var c=t[o-1],u=n.high,d=n.low,h=(s=n.volume)!==null&&s!==void 0?s:0,v=(u+d)/2-(c.high+c.low)/2;if(h===0||u-d===0)l.emv=0;else{var f=h/1e8/(u-d);l.emv=v/f}a+=l.emv,i.push(l.emv),o>=e[0]&&(l.maEmv=a/e[0],a-=i[o-e[0]])}return l})}},Dl={name:"EMA",shortName:"EMA",series:"price",calcParams:[6,12,20],precision:2,shouldOhlc:!0,figures:[{key:"ema1",title:"EMA6: ",type:"line"},{key:"ema2",title:"EMA12: ",type:"line"},{key:"ema3",title:"EMA20: ",type:"line"}],regenerateFigures:function(t){return t.map(function(r,e){return{key:"ema".concat(e+1),title:"EMA".concat(r,": "),type:"line"}})},calc:function(t,r){var e=r.calcParams,a=r.figures,i=0,n=[];return t.map(function(o,s){var l={},c=o.close;return i+=c,e.forEach(function(u,d){s>=u-1&&(s>u-1?n[d]=(2*c+(u-1)*n[d])/(u+1):n[d]=i/u,l[a[d].key]=n[d])}),l})}},Pl={name:"MTM",shortName:"MTM",calcParams:[12,6],figures:[{key:"mtm",title:"MTM: ",type:"line"},{key:"maMtm",title:"MAMTM: ",type:"line"}],calc:function(t,r){var e=r.calcParams,a=0,i=[];return t.forEach(function(n,o){var s,l={};if(o>=e[0]){var c=n.close,u=t[o-e[0]].close;l.mtm=c-u,a+=l.mtm,o>=e[0]+e[1]-1&&(l.maMtm=a/e[1],a-=(s=i[o-(e[1]-1)].mtm)!==null&&s!==void 0?s:0)}i.push(l)}),i}},Fl={name:"MA",shortName:"MA",series:"price",calcParams:[5,10,30,60],precision:2,shouldOhlc:!0,figures:[{key:"ma1",title:"MA5: ",type:"line"},{key:"ma2",title:"MA10: ",type:"line"},{key:"ma3",title:"MA30: ",type:"line"},{key:"ma4",title:"MA60: ",type:"line"}],regenerateFigures:function(t){return t.map(function(r,e){return{key:"ma".concat(e+1),title:"MA".concat(r,": "),type:"line"}})},calc:function(t,r){var e=r.calcParams,a=r.figures,i=[];return t.map(function(n,o){var s={},l=n.close;return e.forEach(function(c,u){var d;i[u]=((d=i[u])!==null&&d!==void 0?d:0)+l,o>=c-1&&(s[a[u].key]=i[u]/c,i[u]-=t[o-(c-1)].close)}),s})}},Rl={name:"MACD",shortName:"MACD",calcParams:[12,26,9],figures:[{key:"dif",title:"DIF: ",type:"line"},{key:"dea",title:"DEA: ",type:"line"},{key:"macd",title:"MACD: ",type:"bar",baseValue:0,styles:function(t){var r,e,a=t.data,i=t.indicator,n=t.defaultStyles,o=a.prev,s=a.current,l=(r=o==null?void 0:o.macd)!==null&&r!==void 0?r:Number.MIN_SAFE_INTEGER,c=(e=s==null?void 0:s.macd)!==null&&e!==void 0?e:Number.MIN_SAFE_INTEGER,u="";c>0?u=be(i.styles,"bars[0].upColor",n.bars[0].upColor):c<0?u=be(i.styles,"bars[0].downColor",n.bars[0].downColor):u=be(i.styles,"bars[0].noChangeColor",n.bars[0].noChangeColor);var d=l<c?"stroke":"fill";return{style:d,color:u,borderColor:u}}}],calc:function(t,r){var e=r.calcParams,a=0,i=0,n=0,o=0,s=0,l=0,c=Math.max(e[0],e[1]);return t.map(function(u,d){var h={},v=u.close;return a+=v,d>=e[0]-1&&(d>e[0]-1?i=(2*v+(e[0]-1)*i)/(e[0]+1):i=a/e[0]),d>=e[1]-1&&(d>e[1]-1?n=(2*v+(e[1]-1)*n)/(e[1]+1):n=a/e[1]),d>=c-1&&(o=i-n,h.dif=o,s+=o,d>=c+e[2]-2&&(d>c+e[2]-2?l=(o*2+l*(e[2]-1))/(e[2]+1):l=s/e[2],h.macd=(o-l)*2,h.dea=l)),h})}},Bl={name:"OBV",shortName:"OBV",calcParams:[30],figures:[{key:"obv",title:"OBV: ",type:"line"},{key:"maObv",title:"MAOBV: ",type:"line"}],calc:function(t,r){var e=r.calcParams,a=0,i=0,n=[];return t.forEach(function(o,s){var l,c,u,d,h=(l=t[s-1])!==null&&l!==void 0?l:o;o.close<h.close?i-=(c=o.volume)!==null&&c!==void 0?c:0:o.close>h.close&&(i+=(u=o.volume)!==null&&u!==void 0?u:0);var v={obv:i};a+=i,s>=e[0]-1&&(v.maObv=a/e[0],a-=(d=n[s-(e[0]-1)].obv)!==null&&d!==void 0?d:0),n.push(v)}),n}},Nl={name:"PVT",shortName:"PVT",figures:[{key:"pvt",title:"PVT: ",type:"line"}],calc:function(t){var r=0;return t.map(function(e,a){var i,n,o={},s=e.close,l=(i=e.volume)!==null&&i!==void 0?i:1,c=((n=t[a-1])!==null&&n!==void 0?n:e).close,u=0,d=c*l;return d!==0&&(u=(s-c)/d),r+=u,o.pvt=r,o})}},Ol={name:"PSY",shortName:"PSY",calcParams:[12,6],figures:[{key:"psy",title:"PSY: ",type:"line"},{key:"maPsy",title:"MAPSY: ",type:"line"}],calc:function(t,r){var e=r.calcParams,a=0,i=0,n=[],o=[];return t.forEach(function(s,l){var c,u,d={},h=((c=t[l-1])!==null&&c!==void 0?c:s).close,v=s.close-h>0?1:0;n.push(v),a+=v,l>=e[0]-1&&(d.psy=a/e[0]*100,i+=d.psy,l>=e[0]+e[1]-2&&(d.maPsy=i/e[1],i-=(u=o[l-(e[1]-1)].psy)!==null&&u!==void 0?u:0),a-=n[l-(e[0]-1)]),o.push(d)}),o}},Vl={name:"ROC",shortName:"ROC",calcParams:[12,6],figures:[{key:"roc",title:"ROC: ",type:"line"},{key:"maRoc",title:"MAROC: ",type:"line"}],calc:function(t,r){var e=r.calcParams,a=[],i=0;return t.forEach(function(n,o){var s,l,c={};if(o>=e[0]-1){var u=n.close,d=((s=t[o-e[0]])!==null&&s!==void 0?s:t[o-(e[0]-1)]).close;d!==0?c.roc=(u-d)/d*100:c.roc=0,i+=c.roc,o>=e[0]-1+e[1]-1&&(c.maRoc=i/e[1],i-=(l=a[o-(e[1]-1)].roc)!==null&&l!==void 0?l:0)}a.push(c)}),a}},zl={name:"RSI",shortName:"RSI",calcParams:[6,12,24],figures:[{key:"rsi1",title:"RSI1: ",type:"line"},{key:"rsi2",title:"RSI2: ",type:"line"},{key:"rsi3",title:"RSI3: ",type:"line"}],regenerateFigures:function(t){return t.map(function(r,e){var a=e+1;return{key:"rsi".concat(a),title:"RSI".concat(a,": "),type:"line"}})},calc:function(t,r){var e=r.calcParams,a=r.figures,i=[],n=[];return t.map(function(o,s){var l,c={},u=((l=t[s-1])!==null&&l!==void 0?l:o).close,d=o.close-u;return e.forEach(function(h,v){var f,p,g;if(d>0?i[v]=((f=i[v])!==null&&f!==void 0?f:0)+d:n[v]=((p=n[v])!==null&&p!==void 0?p:0)+Math.abs(d),s>=h-1){n[v]!==0?c[a[v].key]=100-100/(1+i[v]/n[v]):c[a[v].key]=0;var m=t[s-(h-1)],_=(g=t[s-h])!==null&&g!==void 0?g:m,x=m.close-_.close;x>0?i[v]-=x:n[v]-=Math.abs(x)}}),c})}},$l={name:"SMA",shortName:"SMA",series:"price",calcParams:[12,2],precision:2,figures:[{key:"sma",title:"SMA: ",type:"line"}],shouldOhlc:!0,calc:function(t,r){var e=r.calcParams,a=0,i=0;return t.map(function(n,o){var s={},l=n.close;return a+=l,o>=e[0]-1&&(o>e[0]-1?i=(l*e[1]+i*(e[0]-e[1]+1))/(e[0]+1):i=a/e[0],s.sma=i),s})}},Wl={name:"KDJ",shortName:"KDJ",calcParams:[9,3,3],figures:[{key:"k",title:"K: ",type:"line"},{key:"d",title:"D: ",type:"line"},{key:"j",title:"J: ",type:"line"}],calc:function(t,r){var e=r.calcParams,a=[];return t.forEach(function(i,n){var o,s,l,c,u={},d=i.close;if(n>=e[0]-1){var h=lo(t.slice(n-(e[0]-1),n+1),"high","low"),v=h[0],f=h[1],p=v-f,g=(d-f)/(p===0?1:p)*100;u.k=((e[1]-1)*((s=(o=a[n-1])===null||o===void 0?void 0:o.k)!==null&&s!==void 0?s:50)+g)/e[1],u.d=((e[2]-1)*((c=(l=a[n-1])===null||l===void 0?void 0:l.d)!==null&&c!==void 0?c:50)+u.k)/e[2],u.j=3*u.k-2*u.d}a.push(u)}),a}},Yl={name:"SAR",shortName:"SAR",series:"price",calcParams:[2,2,20],precision:2,shouldOhlc:!0,figures:[{key:"sar",title:"SAR: ",type:"circle",styles:function(t){var r,e,a,i=t.data,n=t.indicator,o=t.defaultStyles,s=i.current,l=(r=s==null?void 0:s.sar)!==null&&r!==void 0?r:Number.MIN_SAFE_INTEGER,c=(((e=s==null?void 0:s.high)!==null&&e!==void 0?e:0)+((a=s==null?void 0:s.low)!==null&&a!==void 0?a:0))/2,u=l<c?be(n.styles,"circles[0].upColor",o.circles[0].upColor):be(n.styles,"circles[0].downColor",o.circles[0].downColor);return{color:u}}}],calc:function(t,r){var e=r.calcParams,a=e[0]/100,i=e[1]/100,n=e[2]/100,o=a,s=-100,l=!1,c=0;return t.map(function(u,d){var h=c,v=u.high,f=u.low;if(l){(s===-100||s<v)&&(s=v,o=Math.min(o+i,n)),c=h+o*(s-h);var p=Math.min(t[Math.max(1,d)-1].low,f);c>u.low?(c=s,o=a,s=-100,l=!l):c>p&&(c=p)}else{(s===-100||s>f)&&(s=f,o=Math.min(o+i,n)),c=h+o*(s-h);var g=Math.max(t[Math.max(1,d)-1].high,v);c<u.high?(c=s,o=0,s=-100,l=!l):c<g&&(c=g)}return{high:v,low:f,sar:c}})}},Zl={name:"TRIX",shortName:"TRIX",calcParams:[12,9],figures:[{key:"trix",title:"TRIX: ",type:"line"},{key:"maTrix",title:"MATRIX: ",type:"line"}],calc:function(t,r){var e=r.calcParams,a=0,i=0,n=0,o=0,s=0,l=0,c=0,u=[];return t.forEach(function(d,h){var v,f={},p=d.close;if(a+=p,h>=e[0]-1&&(h>e[0]-1?i=(2*p+(e[0]-1)*i)/(e[0]+1):i=a/e[0],s+=i,h>=e[0]*2-2&&(h>e[0]*2-2?n=(2*i+(e[0]-1)*n)/(e[0]+1):n=s/e[0],l+=n,h>=e[0]*3-3))){var g=0,m=0;h>e[0]*3-3?(g=(2*n+(e[0]-1)*o)/(e[0]+1),m=(g-o)/o*100):g=l/e[0],o=g,f.trix=m,c+=m,h>=e[0]*3+e[1]-4&&(f.maTrix=c/e[1],c-=(v=u[h-(e[1]-1)].trix)!==null&&v!==void 0?v:0)}u.push(f)}),u}};function Qi(){return{key:"volume",title:"VOLUME: ",type:"bar",baseValue:0,styles:function(t){var r=t.data,e=t.indicator,a=t.defaultStyles,i=r.current,n=be(e.styles,"bars[0].noChangeColor",a.bars[0].noChangeColor);return R(i)&&(i.close>i.open?n=be(e.styles,"bars[0].upColor",a.bars[0].upColor):i.close<i.open&&(n=be(e.styles,"bars[0].downColor",a.bars[0].downColor))),{color:n}}}}var Hl={name:"VOL",shortName:"VOL",series:"volume",calcParams:[5,10,20],shouldFormatBigNumber:!0,precision:0,minValue:0,figures:[{key:"ma1",title:"MA5: ",type:"line"},{key:"ma2",title:"MA10: ",type:"line"},{key:"ma3",title:"MA20: ",type:"line"},Qi()],regenerateFigures:function(t){var r=t.map(function(e,a){return{key:"ma".concat(a+1),title:"MA".concat(e,": "),type:"line"}});return r.push(Qi()),r},calc:function(t,r){var e=r.calcParams,a=r.figures,i=[];return t.map(function(n,o){var s,l=(s=n.volume)!==null&&s!==void 0?s:0,c={volume:l,open:n.open,close:n.close};return e.forEach(function(u,d){var h,v;i[d]=((h=i[d])!==null&&h!==void 0?h:0)+l,o>=u-1&&(c[a[d].key]=i[d]/u,i[d]-=(v=t[o-(u-1)].volume)!==null&&v!==void 0?v:0)}),c})}},jl={name:"VR",shortName:"VR",calcParams:[26,6],figures:[{key:"vr",title:"VR: ",type:"line"},{key:"maVr",title:"MAVR: ",type:"line"}],calc:function(t,r){var e=r.calcParams,a=0,i=0,n=0,o=0,s=[];return t.forEach(function(l,c){var u,d,h,v,f,p={},g=l.close,m=((u=t[c-1])!==null&&u!==void 0?u:l).close,_=(d=l.volume)!==null&&d!==void 0?d:0;if(g>m?a+=_:g<m?i+=_:n+=_,c>=e[0]-1){var x=n/2;i+x===0?p.vr=0:p.vr=(a+x)/(i+x)*100,o+=p.vr,c>=e[0]+e[1]-2&&(p.maVr=o/e[1],o-=(h=s[c-(e[1]-1)].vr)!==null&&h!==void 0?h:0);var S=t[c-(e[0]-1)],y=(v=t[c-e[0]])!==null&&v!==void 0?v:S,k=S.close,C=(f=S.volume)!==null&&f!==void 0?f:0;k>y.close?a-=C:k<y.close?i-=C:n-=C}s.push(p)}),s}},Xl={name:"WR",shortName:"WR",calcParams:[6,10,14],figures:[{key:"wr1",title:"WR1: ",type:"line"},{key:"wr2",title:"WR2: ",type:"line"},{key:"wr3",title:"WR3: ",type:"line"}],regenerateFigures:function(t){return t.map(function(r,e){return{key:"wr".concat(e+1),title:"WR".concat(e+1,": "),type:"line"}})},calc:function(t,r){var e=r.calcParams,a=r.figures;return t.map(function(i,n){var o={},s=i.close;return e.forEach(function(l,c){var u=l-1;if(n>=u){var d=lo(t.slice(n-u,n+1),"high","low"),h=d[0],v=d[1],f=h-v;o[a[c].key]=f===0?0:(s-h)/f*100}}),o})}},pi={},Gl=[Cl,bl,xl,Ll,Sl,kl,Ml,Tl,El,Il,Al,Dl,Pl,Fl,Rl,Bl,Nl,Ol,Vl,zl,$l,Wl,Yl,Zl,Hl,jl,Xl];Gl.forEach(function(t){pi[t.name]=co.extend(t)});function Kl(t){pi[t.name]=co.extend(t)}function uo(t){var r;return(r=pi[t])!==null&&r!==void 0?r:null}function Ye(t,r){var e,a=(e=r==null?void 0:r.ignoreEvent)!==null&&e!==void 0?e:!1;return Qr(a)?!a:!a.includes(t)}var qi=1,jr=-1,Ul="overlay_",Sr="overlay_figure_",ho=function(){function t(r){this.groupId="",this.totalStep=1,this.currentStep=qi,this.lock=!1,this.visible=!0,this.zLevel=0,this.needDefaultPointFigure=!1,this.needDefaultXAxisFigure=!1,this.needDefaultYAxisFigure=!1,this.mode="normal",this.modeSensitivity=8,this.points=[],this.styles=null,this.createPointFigures=null,this.createXAxisFigures=null,this.createYAxisFigures=null,this.performEventPressedMove=null,this.performEventMoveForDrawing=null,this.onDrawStart=null,this.onDrawing=null,this.onDrawEnd=null,this.onClick=null,this.onDoubleClick=null,this.onRightClick=null,this.onPressedMoveStart=null,this.onPressedMoving=null,this.onPressedMoveEnd=null,this.onMouseMove=null,this.onMouseEnter=null,this.onMouseLeave=null,this.onRemoved=null,this.onSelected=null,this.onDeselected=null,this._prevZLevel=0,this._prevPressedPoint=null,this._prevPressedPoints=[],this.override(r)}return t.prototype.override=function(r){var e,a;this._prevOverlay=Er(this);var i=r.id,n=r.name;r.currentStep;var o=r.points,s=r.styles,l=ga(r,["id","name","currentStep","points","styles"]);if(ye(this,l),Kt(this.name)||(this.name=n??""),!Kt(this.id)&&Kt(i)&&(this.id=i),R(s)&&((e=this.styles)!==null&&e!==void 0||(this.styles={}),ye(this.styles,s)),Ue(o)&&o.length>0){var c=0;if(this.points=hr([],nr(o),!1),o.length>=this.totalStep-1?(this.currentStep=jr,c=this.totalStep-1):(this.currentStep=o.length+1,c=o.length),Ie(this.performEventMoveForDrawing))for(var u=0;u<c;u++)this.performEventMoveForDrawing({currentStep:u+2,mode:this.mode,points:this.points,performPointIndex:u,performPoint:this.points[u]});this.currentStep===jr&&((a=this.performEventPressedMove)===null||a===void 0||a.call(this,{currentStep:this.currentStep,mode:this.mode,points:this.points,performPointIndex:this.points.length-1,performPoint:this.points[this.points.length-1]}))}},t.prototype.getPrevZLevel=function(){return this._prevZLevel},t.prototype.setPrevZLevel=function(r){this._prevZLevel=r},t.prototype.shouldUpdate=function(){var r=this._prevOverlay.zLevel!==this.zLevel,e=r||JSON.stringify(this._prevOverlay.points)!==JSON.stringify(this.points)||this._prevOverlay.visible!==this.visible||this._prevOverlay.extendData!==this.extendData||this._prevOverlay.styles!==this.styles;return{sort:r,draw:e}},t.prototype.nextStep=function(){this.currentStep===this.totalStep-1?this.currentStep=jr:this.currentStep++},t.prototype.forceComplete=function(){this.currentStep=jr},t.prototype.isDrawing=function(){return this.currentStep!==jr},t.prototype.isStart=function(){return this.currentStep===qi},t.prototype.eventMoveForDrawing=function(r){var e,a=this.currentStep-1,i={};vt(r.timestamp)&&(i.timestamp=r.timestamp),vt(r.dataIndex)&&(i.dataIndex=r.dataIndex),vt(r.value)&&(i.value=r.value),this.points[a]=i,(e=this.performEventMoveForDrawing)===null||e===void 0||e.call(this,{currentStep:this.currentStep,mode:this.mode,points:this.points,performPointIndex:a,performPoint:i})},t.prototype.eventPressedPointMove=function(r,e){var a;this.points[e].timestamp=r.timestamp,vt(r.value)&&(this.points[e].value=r.value),(a=this.performEventPressedMove)===null||a===void 0||a.call(this,{currentStep:this.currentStep,points:this.points,mode:this.mode,performPointIndex:e,performPoint:this.points[e]})},t.prototype.startPressedMove=function(r){this._prevPressedPoint=J({},r),this._prevPressedPoints=Er(this.points)},t.prototype.eventPressedOtherMove=function(r,e){if(this._prevPressedPoint!==null){var a=null;vt(r.dataIndex)&&vt(this._prevPressedPoint.dataIndex)&&(a=r.dataIndex-this._prevPressedPoint.dataIndex);var i=null;vt(r.value)&&vt(this._prevPressedPoint.value)&&(i=r.value-this._prevPressedPoint.value),this.points=this._prevPressedPoints.map(function(n){var o;vt(n.timestamp)&&(n.dataIndex=e.timestampToDataIndex(n.timestamp));var s=J({},n);return vt(a)&&vt(n.dataIndex)&&(s.dataIndex=n.dataIndex+a,s.timestamp=(o=e.dataIndexToTimestamp(s.dataIndex))!==null&&o!==void 0?o:void 0),vt(i)&&vt(n.value)&&(s.value=n.value+i),s})}},t.extend=function(r){var e=function(a){St(i,a);function i(){return a.call(this,r)||this}return i}(t);return e},t}(),Ql={name:"fibonacciLine",totalStep:3,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:function(t){var r,e,a,i=t.chart,n=t.coordinates,o=t.bounding,s=t.overlay,l=t.yAxis,c=s.points;if(n.length>0){var u=0;if(!((r=l==null?void 0:l.isInCandle())!==null&&r!==void 0)||r)u=(a=(e=i.getSymbol())===null||e===void 0?void 0:e.pricePrecision)!==null&&a!==void 0?a:2;else{var d=i.getIndicators({paneId:s.paneId});d.forEach(function(x){u=Math.max(u,x.precision)})}var h=[],v=[],f=0,p=o.width;if(n.length>1&&vt(c[0].value)&&vt(c[1].value)){var g=[1,.786,.618,.5,.382,.236,0],m=n[0].y-n[1].y,_=c[0].value-c[1].value;g.forEach(function(x){var S,y=n[1].y+m*x,k=i.getDecimalFold().format(i.getThousandsSeparator().format((((S=c[1].value)!==null&&S!==void 0?S:0)+_*x).toFixed(u)));h.push({coordinates:[{x:f,y},{x:p,y}]}),v.push({x:f,y,text:"".concat(k," (").concat((x*100).toFixed(1),"%)"),baseline:"bottom"})})}return[{type:"line",attrs:h},{type:"text",isCheckEvent:!1,attrs:v}]}return[]}},ql={name:"horizontalRayLine",totalStep:3,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:function(t){var r=t.coordinates,e=t.bounding,a={x:0,y:r[0].y};return R(r[1])&&r[0].x<r[1].x&&(a.x=e.width),[{type:"line",attrs:{coordinates:[r[0],a]}}]},performEventPressedMove:function(t){var r=t.points,e=t.performPoint;r[0].value=e.value,r[1].value=e.value},performEventMoveForDrawing:function(t){var r=t.currentStep,e=t.points,a=t.performPoint;r===2&&(e[0].value=a.value)}},Jl={name:"horizontalSegment",totalStep:3,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:function(t){var r=t.coordinates,e=[];return r.length===2&&e.push({coordinates:r}),[{type:"line",attrs:e}]},performEventPressedMove:function(t){var r=t.points,e=t.performPoint;r[0].value=e.value,r[1].value=e.value},performEventMoveForDrawing:function(t){var r=t.currentStep,e=t.points,a=t.performPoint;r===2&&(e[0].value=a.value)}},t1={name:"horizontalStraightLine",totalStep:2,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:function(t){var r=t.coordinates,e=t.bounding;return[{type:"line",attrs:{coordinates:[{x:0,y:r[0].y},{x:e.width,y:r[0].y}]}}]}},gi=function(){function t(){this._children=[],this._callbacks=new Map}return t.prototype.registerEvent=function(r,e){return this._callbacks.set(r,e),this},t.prototype.onEvent=function(r,e){var a=this._callbacks.get(r);return R(a)&&this.checkEventOn(e)?a(e):!1},t.prototype.dispatchEventToChildren=function(r,e){var a=this._children.length-1;if(a>-1){for(var i=a;i>-1;i--)if(this._children[i].dispatchEvent(r,e))return!0}return!1},t.prototype.dispatchEvent=function(r,e){return this.dispatchEventToChildren(r,e)?!0:this.onEvent(r,e)},t.prototype.addChild=function(r){return this._children.push(r),this},t.prototype.clear=function(){this._children=[]},t}(),Le=2,vo=function(t){St(r,t);function r(e){var a=t.call(this)||this;return a.attrs=e.attrs,a.styles=e.styles,a}return r.prototype.checkEventOn=function(e){return this.checkEventOnImp(e,this.attrs,this.styles)},r.prototype.setAttrs=function(e){return this.attrs=e,this},r.prototype.setStyles=function(e){return this.styles=e,this},r.prototype.draw=function(e){this.drawImp(e,this.attrs,this.styles)},r.extend=function(e){var a=function(i){St(n,i);function n(){return i!==null&&i.apply(this,arguments)||this}return n.prototype.checkEventOnImp=function(o,s,l){return e.checkEventOn(o,s,l)},n.prototype.drawImp=function(o,s,l){e.draw(o,s,l)},n}(r);return a},r}(gi);function fo(t,r){var e,a,i=[];i=i.concat(r);try{for(var n=Xe(i),o=n.next();!o.done;o=n.next()){var s=o.value,l=s.coordinates;if(l.length>1)for(var c=1;c<l.length;c++){var u=l[c-1],d=l[c];if(u.x===d.x){if(Math.abs(u.y-t.y)+Math.abs(d.y-t.y)-Math.abs(u.y-d.y)<Le+Le&&Math.abs(t.x-u.x)<Le)return!0}else{var h=Da(u,d),v=mi(h,t),f=Math.abs(v-t.y);if(Math.abs(u.x-t.x)+Math.abs(d.x-t.x)-Math.abs(u.x-d.x)<Le+Le&&f*f/(h[0]*h[0]+1)<Le*Le)return!0}}}}catch(p){e={error:p}}finally{try{o&&!o.done&&(a=n.return)&&a.call(n)}finally{if(e)throw e.error}}return!1}function mi(t,r){return t!==null?r.x*t[0]+t[1]:r.y}function qr(t,r,e){var a=Da(t,r);return mi(a,e)}function Da(t,r){var e=t.x-r.x;if(e!==0){var a=(t.y-r.y)/e,i=t.y-a*t.x;return[a,i]}return null}function po(t,r,e){var a=r.length,i=vt(e)?e>0&&e<1?e:0:e?.5:0;if(i>0&&a>2){for(var n=r[0].x,o=r[0].y,s=1;s<a-1;s++){var l=r[s-1],c=r[s],u=r[s+1],d=c.x-l.x,h=c.y-l.y,v=u.x-c.x,f=u.y-c.y,p=u.x-l.x,g=u.y-l.y,m=Math.sqrt(d*d+h*h),_=Math.sqrt(v*v+f*f),x=_/(_+m),S=c.x+p*i*x,y=c.y+g*i*x;S=Math.min(S,Math.max(u.x,c.x)),y=Math.min(y,Math.max(u.y,c.y)),S=Math.max(S,Math.min(u.x,c.x)),y=Math.max(y,Math.min(u.y,c.y)),p=S-c.x,g=y-c.y;var k=c.x-p*m/_,C=c.y-g*m/_;k=Math.min(k,Math.max(l.x,c.x)),C=Math.min(C,Math.max(l.y,c.y)),k=Math.max(k,Math.min(l.x,c.x)),C=Math.max(C,Math.min(l.y,c.y)),p=c.x-k,g=c.y-C,S=c.x+p*_/m,y=c.y+g*_/m,t.bezierCurveTo(n,o,k,C,c.x,c.y),n=S,o=y}var D=r[a-1];t.bezierCurveTo(n,o,D.x,D.y,D.x,D.y)}else for(var s=1;s<a;s++)t.lineTo(r[s].x,r[s].y)}function e1(t,r,e){var a=[];a=a.concat(r);var i=e.style,n=i===void 0?"solid":i,o=e.smooth,s=o===void 0?!1:o,l=e.size,c=l===void 0?1:l,u=e.color,d=u===void 0?"currentColor":u,h=e.dashedValue,v=h===void 0?[2,2]:h;t.lineWidth=c,t.strokeStyle=d,n==="dashed"?t.setLineDash(v):t.setLineDash([]);var f=c%2===1?.5:0;a.forEach(function(p){var g=p.coordinates;g.length>1&&(g.length===2&&(g[0].x===g[1].x||g[0].y===g[1].y)?(t.beginPath(),g[0].x===g[1].x?(t.moveTo(g[0].x+f,g[0].y),t.lineTo(g[1].x+f,g[1].y)):(t.moveTo(g[0].x,g[0].y+f),t.lineTo(g[1].x,g[1].y+f)),t.stroke(),t.closePath()):(t.save(),c%2===1&&t.translate(.5,.5),t.beginPath(),t.moveTo(g[0].x,g[0].y),po(t,g,s),t.stroke(),t.closePath(),t.restore()))})}var r1={name:"line",checkEventOn:fo,draw:function(t,r,e){e1(t,r,e)}};function go(t,r,e){var a=e??0,i=[];if(t.length>1)if(t[0].x===t[1].x){var n=0,o=r.height;if(i.push({coordinates:[{x:t[0].x,y:n},{x:t[0].x,y:o}]}),t.length>2){i.push({coordinates:[{x:t[2].x,y:n},{x:t[2].x,y:o}]});for(var s=t[0].x-t[2].x,l=0;l<a;l++){var c=s*(l+1);i.push({coordinates:[{x:t[0].x+c,y:n},{x:t[0].x+c,y:o}]})}}}else{var u=0,d=r.width,h=Da(t[0],t[1]),v=h[0],f=h[1];if(i.push({coordinates:[{x:u,y:u*v+f},{x:d,y:d*v+f}]}),t.length>2){var p=t[2].y-v*t[2].x;i.push({coordinates:[{x:u,y:u*v+p},{x:d,y:d*v+p}]});for(var s=f-p,l=0;l<a;l++){var g=f+s*(l+1);i.push({coordinates:[{x:u,y:u*v+g},{x:d,y:d*v+g}]})}}}return i}var a1={name:"parallelStraightLine",totalStep:4,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:function(t){var r=t.coordinates,e=t.bounding;return[{type:"line",attrs:go(r,e)}]}},i1={name:"priceChannelLine",totalStep:4,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:function(t){var r=t.coordinates,e=t.bounding;return[{type:"line",attrs:go(r,e,1)}]}},n1={name:"priceLine",totalStep:2,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:function(t){var r,e,a,i=t.chart,n=t.coordinates,o=t.bounding,s=t.overlay,l=t.yAxis,c=0;if(!((r=l==null?void 0:l.isInCandle())!==null&&r!==void 0)||r)c=(a=(e=i.getSymbol())===null||e===void 0?void 0:e.pricePrecision)!==null&&a!==void 0?a:2;else{var u=i.getIndicators({paneId:s.paneId});u.forEach(function(v){c=Math.max(c,v.precision)})}var d=s.points[0].value,h=d===void 0?0:d;return[{type:"line",attrs:{coordinates:[n[0],{x:o.width,y:n[0].y}]}},{type:"text",ignoreEvent:!0,attrs:{x:n[0].x,y:n[0].y,text:i.getDecimalFold().format(i.getThousandsSeparator().format(h.toFixed(c))),baseline:"bottom"}}]}};function o1(t,r){if(t.length>1){var e={x:0,y:0};return t[0].x===t[1].x&&t[0].y!==t[1].y?t[0].y<t[1].y?e={x:t[0].x,y:r.height}:e={x:t[0].x,y:0}:t[0].x>t[1].x?e={x:0,y:qr(t[0],t[1],{x:0,y:t[0].y})}:e={x:r.width,y:qr(t[0],t[1],{x:r.width,y:t[0].y})},{coordinates:[t[0],e]}}return[]}var s1={name:"rayLine",totalStep:3,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:function(t){var r=t.coordinates,e=t.bounding;return[{type:"line",attrs:o1(r,e)}]}},l1={name:"segment",totalStep:3,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:function(t){var r=t.coordinates;return r.length===2?[{type:"line",attrs:{coordinates:r}}]:[]}},c1={name:"straightLine",totalStep:3,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:function(t){var r=t.coordinates,e=t.bounding;return r.length===2?r[0].x===r[1].x?[{type:"line",attrs:{coordinates:[{x:r[0].x,y:0},{x:r[0].x,y:e.height}]}}]:[{type:"line",attrs:{coordinates:[{x:0,y:qr(r[0],r[1],{x:0,y:r[0].y})},{x:e.width,y:qr(r[0],r[1],{x:e.width,y:r[0].y})}]}}]:[]}},u1={name:"verticalRayLine",totalStep:3,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:function(t){var r=t.coordinates,e=t.bounding;if(r.length===2){var a={x:r[0].x,y:0};return r[0].y<r[1].y&&(a.y=e.height),[{type:"line",attrs:{coordinates:[r[0],a]}}]}return[]},performEventPressedMove:function(t){var r=t.points,e=t.performPoint;r[0].timestamp=e.timestamp,r[0].dataIndex=e.dataIndex,r[1].timestamp=e.timestamp,r[1].dataIndex=e.dataIndex},performEventMoveForDrawing:function(t){var r=t.currentStep,e=t.points,a=t.performPoint;r===2&&(e[0].timestamp=a.timestamp,e[0].dataIndex=a.dataIndex)}},d1={name:"verticalSegment",totalStep:3,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:function(t){var r=t.coordinates;return r.length===2?[{type:"line",attrs:{coordinates:r}}]:[]},performEventPressedMove:function(t){var r=t.points,e=t.performPoint;r[0].timestamp=e.timestamp,r[0].dataIndex=e.dataIndex,r[1].timestamp=e.timestamp,r[1].dataIndex=e.dataIndex},performEventMoveForDrawing:function(t){var r=t.currentStep,e=t.points,a=t.performPoint;r===2&&(e[0].timestamp=a.timestamp,e[0].dataIndex=a.dataIndex)}},h1={name:"verticalStraightLine",totalStep:2,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:function(t){var r=t.coordinates,e=t.bounding;return[{type:"line",attrs:{coordinates:[{x:r[0].x,y:0},{x:r[0].x,y:e.height}]}}]}},v1={name:"simpleAnnotation",totalStep:2,styles:{line:{style:"dashed"}},createPointFigures:function(t){var r,e=t.overlay,a=t.coordinates,i="";R(e.extendData)&&(Ie(e.extendData)?i=e.extendData(e):i=(r=e.extendData)!==null&&r!==void 0?r:"");var n=a[0].x,o=a[0].y-6,s=o-50,l=s-5;return[{type:"line",attrs:{coordinates:[{x:n,y:o},{x:n,y:s}]},ignoreEvent:!0},{type:"polygon",attrs:{coordinates:[{x:n,y:s},{x:n-4,y:l},{x:n+4,y:l}]},ignoreEvent:!0},{type:"text",attrs:{x:n,y:l,text:i,align:"center",baseline:"bottom"},ignoreEvent:!0}]}},f1={name:"simpleTag",totalStep:2,styles:{line:{style:"dashed"}},createPointFigures:function(t){var r=t.bounding,e=t.coordinates;return{type:"line",attrs:{coordinates:[{x:0,y:e[0].y},{x:r.width,y:e[0].y}]},ignoreEvent:!0}},createYAxisFigures:function(t){var r,e,a,i,n=t.chart,o=t.overlay,s=t.coordinates,l=t.bounding,c=t.yAxis,u=(r=c==null?void 0:c.isFromZero())!==null&&r!==void 0?r:!1,d="left",h=0;u?(d="left",h=0):(d="right",h=l.width);var v="";return R(o.extendData)&&(Ie(o.extendData)?v=o.extendData(o):v=(e=o.extendData)!==null&&e!==void 0?e:""),!R(v)&&vt(o.points[0].value)&&(v=Me(o.points[0].value,(i=(a=n.getSymbol())===null||a===void 0?void 0:a.pricePrecision)!==null&&i!==void 0?i:2)),{type:"text",attrs:{x:h,y:s[0].y,text:v,align:d,baseline:"middle"}}}},yi={},p1=[Ql,ql,Jl,t1,a1,i1,n1,s1,l1,c1,u1,d1,h1,v1,f1];p1.forEach(function(t){yi[t.name]=ho.extend(t)});function _a(t){yi[t.name]=ho.extend(t)}function g1(t){var r;return(r=yi[t])!==null&&r!==void 0?r:null}var m1={grid:{horizontal:{color:"#EDEDED"},vertical:{color:"#EDEDED"}},candle:{priceMark:{high:{color:"#76808F"},low:{color:"#76808F"}},tooltip:{rect:{color:"#FEFEFE",borderColor:"#F2F3F5"},title:{color:"#76808F"},legend:{color:"#76808F"}}},indicator:{tooltip:{title:{color:"#76808F"},legend:{color:"#76808F"}}},xAxis:{axisLine:{color:"#DDDDDD"},tickText:{color:"#76808F"},tickLine:{color:"#DDDDDD"}},yAxis:{axisLine:{color:"#DDDDDD"},tickText:{color:"#76808F"},tickLine:{color:"#DDDDDD"}},separator:{color:"#DDDDDD"},crosshair:{horizontal:{line:{color:"#76808F"},text:{borderColor:"#686D76",backgroundColor:"#686D76"}},vertical:{line:{color:"#76808F"},text:{borderColor:"#686D76",backgroundColor:"#686D76"}}}},y1={grid:{horizontal:{color:"#292929"},vertical:{color:"#292929"}},candle:{priceMark:{high:{color:"#929AA5"},low:{color:"#929AA5"}},tooltip:{rect:{color:"rgba(10, 10, 10, .6)",borderColor:"rgba(10, 10, 10, .6)"},title:{color:"#929AA5"},legend:{color:"#929AA5"}}},indicator:{tooltip:{title:{color:"#929AA5"},legend:{color:"#929AA5"}}},xAxis:{axisLine:{color:"#333333"},tickText:{color:"#929AA5"},tickLine:{color:"#333333"}},yAxis:{axisLine:{color:"#333333"},tickText:{color:"#929AA5"},tickLine:{color:"#333333"}},separator:{color:"#333333"},crosshair:{horizontal:{line:{color:"#929AA5"},text:{borderColor:"#373a40",backgroundColor:"#373a40"}},vertical:{line:{color:"#929AA5"},text:{borderColor:"#373a40",backgroundColor:"#373a40"}}}},_1={light:m1,dark:y1};function C1(t){var r;return(r=_1[t])!==null&&r!==void 0?r:null}var da=30,mo=100,xt={CANDLE:"candle_pane",INDICATOR:"indicator_pane_",X_AXIS:"x_axis_pane"},Ji={MIN:1,MAX:125},b1=10,x1=80,ei=10,w1=function(){function t(r,e){var a=this;this._styles=_l(),this._formatter={formatDate:function(d){var h=d.dateTimeFormat,v=d.timestamp,f=d.template;return ro(h,v,f)},formatBigNumber:ao,formatExtendText:function(d){return""}},this._innerFormatter={formatDate:function(d,h,v){return a._formatter.formatDate({dateTimeFormat:a._dateTimeFormat,timestamp:d,template:h,type:v})},formatBigNumber:function(d){return a._formatter.formatBigNumber(d)},formatExtendText:function(d){return a._formatter.formatExtendText(d)}},this._locale="en-US",this._thousandsSeparator={sign:",",format:function(d){return io(d,a._thousandsSeparator.sign)}},this._decimalFold={threshold:3,format:function(d){return no(d,a._decimalFold.threshold)}},this._symbol=null,this._period=null,this._dataList=[],this._orderBookDataList=[],this._dataLoader=null,this._loading=!1,this._dataLoadMore={forward:!1,backward:!1},this._zoomEnabled=!0,this._scrollEnabled=!0,this._candleVisible=!0,this._totalBarSpace=0,this._barSpace=b1,this._offsetRightDistance=x1,this._startLastBarRightSideDiffBarCount=0,this._scrollLimitRole="bar_count",this._minVisibleBarCount={left:2,right:2},this._maxOffsetDistance={left:50,right:50},this._visibleRange=Gi(),this._visibleRangeDataList=[],this._visibleRangeHighLowPrice=[{x:0,price:Number.MIN_SAFE_INTEGER},{x:0,price:Number.MAX_SAFE_INTEGER}],this._crosshair={},this._actions=new Map,this._indicators=new Map,this._taskScheduler=new dl,this._overlays=new Map,this._progressOverlayInfo=null,this._lastPriceMarkExtendTextUpdateTimers=[],this._pressedOverlayInfo={paneId:"",overlay:null,figureType:"none",figureIndex:-1,figure:null},this._hoverOverlayInfo={paneId:"",overlay:null,figureType:"none",figureIndex:-1,figure:null},this._clickOverlayInfo={paneId:"",overlay:null,figureType:"none",figureIndex:-1,figure:null},this._chart=r,this._calcOptimalBarSpace(),this._lastBarRightSideDiffBarCount=this._offsetRightDistance/this._barSpace;var i=e??{},n=i.styles,o=i.locale,s=i.timezone,l=i.formatter,c=i.thousandsSeparator,u=i.decimalFold;R(n)&&this.setStyles(n),Kt(o)&&this.setLocale(o),this.setTimezone(s??""),R(l)&&this.setFormatter(l),R(c)&&this.setThousandsSeparator(c),R(u)&&this.setDecimalFold(u)}return t.prototype.setStyles=function(r){var e=this,a,i,n,o,s,l,c=null;if(Kt(r)?c=C1(r):c=r,ye(this._styles,c),Ue((n=(i=(a=c==null?void 0:c.candle)===null||a===void 0?void 0:a.tooltip)===null||i===void 0?void 0:i.legend)===null||n===void 0?void 0:n.custom)&&(this._styles.candle.tooltip.legend.custom=c.candle.tooltip.legend.custom),R((l=(s=(o=c==null?void 0:c.candle)===null||o===void 0?void 0:o.priceMark)===null||s===void 0?void 0:s.last)===null||l===void 0?void 0:l.extendTexts)){this._clearLastPriceMarkExtendTextUpdateTimer();var u=[];this._styles.candle.priceMark.last.extendTexts.forEach(function(d){var h=d.updateInterval;if(d.show&&h>0&&!u.includes(h)){u.push(h);var v=setInterval(function(){e._chart.updatePane(0,xt.CANDLE)},h);e._lastPriceMarkExtendTextUpdateTimers.push(v)}})}},t.prototype.getStyles=function(){return this._styles},t.prototype.setFormatter=function(r){ye(this._formatter,r)},t.prototype.getFormatter=function(){return this._formatter},t.prototype.getInnerFormatter=function(){return this._innerFormatter},t.prototype.setLocale=function(r){this._locale=r},t.prototype.getLocale=function(){return this._locale},t.prototype.setTimezone=function(r){if(!R(this._dateTimeFormat)||this.getTimezone()!==r){var e={hour12:!1,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"};r.length>0&&(e.timeZone=r);var a=null;try{a=new Intl.DateTimeFormat("en",e)}catch{}a!==null&&(this._dateTimeFormat=a)}},t.prototype.getTimezone=function(){return this._dateTimeFormat.resolvedOptions().timeZone},t.prototype.getDateTimeFormat=function(){return this._dateTimeFormat},t.prototype.setThousandsSeparator=function(r){ye(this._thousandsSeparator,r)},t.prototype.getThousandsSeparator=function(){return this._thousandsSeparator},t.prototype.setDecimalFold=function(r){ye(this._decimalFold,r)},t.prototype.getDecimalFold=function(){return this._decimalFold},t.prototype.setSymbol=function(r){this._processDataUnsubscribe(),this._symbol=r,this._synchronizeIndicatorSeriesPrecision(),this.resetData()},t.prototype.getSymbol=function(){return this._symbol},t.prototype.setPeriod=function(r){this._processDataUnsubscribe(),this._period=r,this.resetData()},t.prototype.getPeriod=function(){return this._period},t.prototype.getDataList=function(){return this._dataList},t.prototype.getVisibleRangeDataList=function(){return this._visibleRangeDataList},t.prototype.getVisibleRangeHighLowPrice=function(){return this._visibleRangeHighLowPrice},t.prototype._addData=function(r,e,a){var i=this,n,o,s=!1,l=!1,c=0;if(Ue(r)){var u={backward:!1,forward:!1};switch(Qr(a)?(u.backward=a,u.forward=a):(u.backward=(n=a==null?void 0:a.backward)!==null&&n!==void 0?n:!1,u.forward=(o=a==null?void 0:a.forward)!==null&&o!==void 0?o:!1),c=r.length,e){case"init":{this._clearData(),this._dataList=r,this._dataLoadMore.backward=u.backward,this._dataLoadMore.forward=u.forward,this.setOffsetRightDistance(this._offsetRightDistance),l=!0;break}case"backward":{this._dataList=this._dataList.concat(r),this._dataLoadMore.backward=u.backward,l=c>0;break}case"forward":{this._dataList=r.concat(this._dataList),this._dataLoadMore.forward=u.forward,l=c>0;break}}s=!0}else{var d=this._dataList.length,h=r.timestamp,v=be(this._dataList[d-1],"timestamp",0);if(h>v){this._dataList.push(r);var f=this.getLastBarRightSideDiffBarCount();f<0&&this.setLastBarRightSideDiffBarCount(--f),c=1,s=!0,l=!0}else h===v&&(this._dataList[d-1]=r,s=!0,l=!0)}if(s&&l){this._adjustVisibleRange(),this.setCrosshair(this._crosshair,{notInvalidate:!0});var p=this.getIndicatorsByFilter({});p.forEach(function(g){i._addIndicatorCalcTask(g,e)}),this._chart.layout({measureWidth:!0,update:!0,buildYAxisTick:!0,cacheYAxisWidth:e!=="init"})}},t.prototype.setDataLoader=function(r){this._dataLoader=r,this.resetData()},t.prototype._calcOptimalBarSpace=function(){this._gapBarSpace=Math.max(2,Math.min(this._barSpace*.8,this._barSpace-2))},t.prototype._adjustVisibleRange=function(){var r,e,a=this._dataList.length,i=this._totalBarSpace/this._barSpace,n=0,o=0;this._scrollLimitRole==="distance"?(n=(this._totalBarSpace-this._maxOffsetDistance.right)/this._barSpace,o=(this._totalBarSpace-this._maxOffsetDistance.left)/this._barSpace):(n=this._minVisibleBarCount.left,o=this._minVisibleBarCount.right),n=Math.max(0,n),o=Math.max(0,o);var s=i-Math.min(n,a);this._lastBarRightSideDiffBarCount>s&&(this._lastBarRightSideDiffBarCount=s);var l=-a+Math.min(o,a);this._lastBarRightSideDiffBarCount<l&&(this._lastBarRightSideDiffBarCount=l);var c=Math.round(this._lastBarRightSideDiffBarCount+a+.5),u=c,d=c;d>a&&(d=a);var h=Math.round(d-i)-1,v=h;v<0&&(v=0);var f=this._lastBarRightSideDiffBarCount>0?Math.round(a+this._lastBarRightSideDiffBarCount-i)-1:v;this._visibleRange={from:v,to:d,realFrom:f,realTo:u},this.executeAction("onVisibleRangeChange",this._visibleRange),this._visibleRangeDataList=[],this._visibleRangeHighLowPrice=[{x:0,price:Number.MIN_SAFE_INTEGER},{x:0,price:Number.MAX_SAFE_INTEGER}];for(var p=f;p<u;p++){var g=this._dataList[p],m=this.dataIndexToCoordinate(p);this._visibleRangeDataList.push({dataIndex:p,x:m,data:{prev:(r=this._dataList[p-1])!==null&&r!==void 0?r:g,current:g,next:(e=this._dataList[p+1])!==null&&e!==void 0?e:g}}),R(g)&&(this._visibleRangeHighLowPrice[0].price<g.high&&(this._visibleRangeHighLowPrice[0].price=g.high,this._visibleRangeHighLowPrice[0].x=m),this._visibleRangeHighLowPrice[1].price>g.low&&(this._visibleRangeHighLowPrice[1].price=g.low,this._visibleRangeHighLowPrice[1].x=m))}!this._loading&&R(this._dataLoader)&&R(this._symbol)&&R(this._period)&&(v===0?this._dataLoadMore.forward&&this._processDataLoad("forward"):d===a&&this._dataLoadMore.backward&&this._processDataLoad("backward"))},t.prototype._processDataLoad=function(r){var e=this,a,i,n,o;if(!this._loading&&R(this._dataLoader)&&R(this._symbol)&&R(this._period)){this._loading=!0;var s={type:r,symbol:this._symbol,period:this._period,timestamp:null,callback:function(l,c){var u,d;e._loading=!1,e._addData(l,r,c),r==="init"&&((d=(u=e._dataLoader)===null||u===void 0?void 0:u.subscribeBar)===null||d===void 0||d.call(u,{symbol:e._symbol,period:e._period,callback:function(h){e._addData(h,"update")}}))}};switch(r){case"backward":{s.timestamp=(i=(a=this._dataList[this._dataList.length-1])===null||a===void 0?void 0:a.timestamp)!==null&&i!==void 0?i:null;break}case"forward":{s.timestamp=(o=(n=this._dataList[0])===null||n===void 0?void 0:n.timestamp)!==null&&o!==void 0?o:null;break}}this._dataLoader.getBars(s)}},t.prototype._processDataUnsubscribe=function(){var r,e;R(this._dataLoader)&&R(this._symbol)&&R(this._period)&&((e=(r=this._dataLoader).unsubscribeBar)===null||e===void 0||e.call(r,{symbol:this._symbol,period:this._period}))},t.prototype.resetData=function(){this._loading=!1,this._processDataLoad("init")},t.prototype.getBarSpace=function(){return{bar:this._barSpace,halfBar:this._barSpace/2,gapBar:this._gapBarSpace,halfGapBar:Math.floor(this._gapBarSpace/2)}},t.prototype.setBarSpace=function(r,e){r<Ji.MIN||r>Ji.MAX||this._barSpace===r||(this._barSpace=r,this._calcOptimalBarSpace(),e==null||e(),this._adjustVisibleRange(),this.setCrosshair(this._crosshair,{notInvalidate:!0}),this._chart.layout({measureWidth:!0,update:!0,buildYAxisTick:!0,cacheYAxisWidth:!0}))},t.prototype.setTotalBarSpace=function(r){this._totalBarSpace!==r&&(this._totalBarSpace=r,this._adjustVisibleRange(),this.setCrosshair(this._crosshair,{notInvalidate:!0}))},t.prototype.setOffsetRightDistance=function(r,e){return this._offsetRightDistance=this._scrollLimitRole==="distance"?Math.min(this._maxOffsetDistance.right,r):r,this._lastBarRightSideDiffBarCount=this._offsetRightDistance/this._barSpace,(e??!1)&&(this._adjustVisibleRange(),this.setCrosshair(this._crosshair,{notInvalidate:!0}),this._chart.layout({measureWidth:!0,update:!0,buildYAxisTick:!0,cacheYAxisWidth:!0})),this},t.prototype.getInitialOffsetRightDistance=function(){return this._offsetRightDistance},t.prototype.getOffsetRightDistance=function(){return Math.max(0,this._lastBarRightSideDiffBarCount*this._barSpace)},t.prototype.getLastBarRightSideDiffBarCount=function(){return this._lastBarRightSideDiffBarCount},t.prototype.setLastBarRightSideDiffBarCount=function(r){this._lastBarRightSideDiffBarCount=r},t.prototype.setMaxOffsetLeftDistance=function(r){this._scrollLimitRole="distance",this._maxOffsetDistance.left=r},t.prototype.setMaxOffsetRightDistance=function(r){this._scrollLimitRole="distance",this._maxOffsetDistance.right=r},t.prototype.setLeftMinVisibleBarCount=function(r){this._scrollLimitRole="bar_count",this._minVisibleBarCount.left=r},t.prototype.setRightMinVisibleBarCount=function(r){this._scrollLimitRole="bar_count",this._minVisibleBarCount.right=r},t.prototype.getVisibleRange=function(){return this._visibleRange},t.prototype.startScroll=function(){this._startLastBarRightSideDiffBarCount=this._lastBarRightSideDiffBarCount},t.prototype.scroll=function(r){if(this._scrollEnabled){var e=r/this._barSpace,a=this._lastBarRightSideDiffBarCount*this._barSpace;this._lastBarRightSideDiffBarCount=this._startLastBarRightSideDiffBarCount-e,this._adjustVisibleRange(),this.setCrosshair(this._crosshair,{notInvalidate:!0}),this._chart.layout({measureWidth:!0,update:!0,buildYAxisTick:!0,cacheYAxisWidth:!0});var i=Math.round(a-this._lastBarRightSideDiffBarCount*this._barSpace);i!==0&&this.executeAction("onScroll",{distance:i})}},t.prototype.getDataByDataIndex=function(r){var e;return(e=this._dataList[r])!==null&&e!==void 0?e:null},t.prototype.coordinateToFloatIndex=function(r){var e=this._dataList.length,a=(this._totalBarSpace-r)/this._barSpace,i=e+this._lastBarRightSideDiffBarCount-a;return Math.round(i*1e6)/1e6},t.prototype.dataIndexToTimestamp=function(r){var e=this._dataList.length;if(e===0)return null;var a=this.getDataByDataIndex(r);if(R(a))return a.timestamp;if(R(this._period)){var i=e-1,n=null,o=0;if(r>i?(n=this._dataList[i].timestamp,o=r-i):r<0&&(n=this._dataList[0].timestamp,o=r),vt(n)){var s=this._period,l=s.type,c=s.span;switch(l){case"second":return n+c*1e3*o;case"minute":return n+c*60*1e3*o;case"hour":return n+c*60*60*1e3*o;case"day":return n+c*24*60*60*1e3*o;case"week":return n+c*7*24*60*60*1e3*o;case"month":{var u=new Date(n),d=u.getDate(),h=u.getMonth()+c*o;u.setMonth(h);var v=new Date(u.getFullYear(),u.getMonth()+1,0).getDate();return u.setDate(Math.min(d,v)),u.getTime()}case"year":{var u=new Date(n);return u.setFullYear(u.getFullYear()+c*o),u.getTime()}}}}return null},t.prototype.timestampToDataIndex=function(r){var e=this._dataList.length;if(e===0)return 0;if(R(this._period)){var a=null,i=0,n=e-1,o=this._dataList[n].timestamp;r>o&&(a=o,i=n);var s=this._dataList[0].timestamp;if(r<s&&(a=s,i=0),vt(a)){var l=this._period,c=l.type,u=l.span;switch(c){case"second":return i+Math.floor((r-a)/(u*1e3));case"minute":return i+Math.floor((r-a)/(u*60*1e3));case"hour":return i+Math.floor((r-a)/(u*60*60*1e3));case"day":return i+Math.floor((r-a)/(u*24*60*60*1e3));case"week":return i+Math.floor((r-a)/(u*7*24*60*60*1e3));case"month":{var d=new Date(a),h=new Date(r),v=d.getFullYear(),f=h.getFullYear(),p=d.getMonth(),g=h.getMonth();return i+Math.floor((f-v)*12+(g-p)/u)}case"year":{var v=new Date(a).getFullYear(),f=new Date(r).getFullYear();return i+Math.floor((f-v)/u)}}}}return ti(this._dataList,"timestamp",r)},t.prototype.dataIndexToCoordinate=function(r){var e=this._dataList.length,a=e+this._lastBarRightSideDiffBarCount-r;return Math.floor(this._totalBarSpace-(a-.5)*this._barSpace+.5)},t.prototype.coordinateToDataIndex=function(r){return Math.ceil(this.coordinateToFloatIndex(r))-1},t.prototype.zoom=function(r,e){var a=this,i;if(this._zoomEnabled){var n=e??null;vt(n==null?void 0:n.x)||(n={x:(i=this._crosshair.x)!==null&&i!==void 0?i:this._totalBarSpace/2});var o=n.x,s=this.coordinateToFloatIndex(o),l=this._barSpace,c=this._barSpace+r*(this._barSpace/ei);this.setBarSpace(c,function(){a._lastBarRightSideDiffBarCount+=s-a.coordinateToFloatIndex(o)});var u=this._barSpace/l;u!==1&&this.executeAction("onZoom",{scale:u})}},t.prototype.setZoomEnabled=function(r){this._zoomEnabled=r},t.prototype.isZoomEnabled=function(){return this._zoomEnabled},t.prototype.setScrollEnabled=function(r){this._scrollEnabled=r},t.prototype.isScrollEnabled=function(){return this._scrollEnabled},t.prototype.setCandleVisible=function(r){this._candleVisible=r},t.prototype.isCandleVisible=function(){return this._candleVisible},t.prototype.setCrosshair=function(r,e){var a,i=e??{},n=i.notInvalidate,o=i.notExecuteAction,s=i.forceInvalidate,l=r??{},c=0,u=0;vt(l.x)?(c=this.coordinateToDataIndex(l.x),c<0?u=0:c>this._dataList.length-1?u=this._dataList.length-1:u=c):(c=this._dataList.length-1,u=c);var d=this._dataList[u],h=this.dataIndexToCoordinate(c),v={x:this._crosshair.x,y:this._crosshair.y,paneId:this._crosshair.paneId};this._crosshair=J(J({},l),{realX:h,kLineData:d,realDataIndex:c,dataIndex:u,timestamp:(a=this.dataIndexToTimestamp(c))!==null&&a!==void 0?a:void 0}),(v.x!==l.x||v.y!==l.y||v.paneId!==l.paneId||(s??!1))&&(R(d)&&!(o??!1)&&this._chart.crosshairChange(this._crosshair),(n??!1)||this._chart.updatePane(1))},t.prototype.getCrosshair=function(){return this._crosshair},t.prototype.executeAction=function(r,e){var a;(a=this._actions.get(r))===null||a===void 0||a.execute(e)},t.prototype.subscribeAction=function(r,e){var a;this._actions.has(r)||this._actions.set(r,new hl),(a=this._actions.get(r))===null||a===void 0||a.subscribe(e)},t.prototype.unsubscribeAction=function(r,e){var a=this._actions.get(r);R(a)&&(a.unsubscribe(e),a.isEmpty()&&this._actions.delete(r))},t.prototype.hasAction=function(r){var e=this._actions.get(r);return R(e)&&!e.isEmpty()},t.prototype._sortIndicators=function(r){var e;Kt(r)?(e=this._indicators.get(r))===null||e===void 0||e.sort(function(a,i){return a.zLevel-i.zLevel}):this._indicators.forEach(function(a){a.sort(function(i,n){return i.zLevel-n.zLevel})})},t.prototype._addIndicatorCalcTask=function(r,e){var a=this;this._taskScheduler.addTask({id:Ki(r.id),handler:function(){var i;(i=r.onDataStateChange)===null||i===void 0||i.call(r,{state:"loading",type:e,indicator:r}),r.calcImp(a._dataList).then(function(n){var o;n&&(a._chart.layout({measureWidth:!0,update:!0,buildYAxisTick:!0,cacheYAxisWidth:e!=="init"}),(o=r.onDataStateChange)===null||o===void 0||o.call(r,{state:"ready",type:e,indicator:r}))}).catch(function(){var n;(n=r.onDataStateChange)===null||n===void 0||n.call(r,{state:"error",type:e,indicator:r})})}})},t.prototype.addIndicator=function(r,e,a){var i=r.name,n=this.getIndicatorsByFilter(r);if(n.length>0)return!1;var o=this.getIndicatorsByPaneId(e),s=uo(i),l=new s;return this._synchronizeIndicatorSeriesPrecision(l),l.paneId=e,l.override(r),a||(this.removeIndicator({paneId:e}),o=[]),o.push(l),this._indicators.set(e,o),this._sortIndicators(e),this._addIndicatorCalcTask(l,"init"),!0},t.prototype.getIndicatorsByPaneId=function(r){var e;return(e=this._indicators.get(r))!==null&&e!==void 0?e:[]},t.prototype.getIndicatorsByFilter=function(r){var e=r.paneId,a=r.name,i=r.id,n=function(s){return R(i)?s.id===i:!R(a)||s.name===a},o=[];return R(e)?o=o.concat(this.getIndicatorsByPaneId(e).filter(n)):this._indicators.forEach(function(s){o=o.concat(s.filter(n))}),o},t.prototype.removeIndicator=function(r){var e=this,a=!1,i=this.getIndicatorsByFilter(r);return i.forEach(function(n){var o=e.getIndicatorsByPaneId(n.paneId),s=o.findIndex(function(l){return l.id===n.id});s>-1&&(e._taskScheduler.removeTask(Ki(n.id)),o.splice(s,1),a=!0),o.length===0&&e._indicators.delete(n.paneId)}),a},t.prototype.hasIndicators=function(r){return this._indicators.has(r)},t.prototype._synchronizeIndicatorSeriesPrecision=function(r){if(R(this._symbol)){var e=this._symbol,a=e.pricePrecision,i=a===void 0?2:a,n=e.volumePrecision,o=n===void 0?0:n,s=function(l){switch(l.series){case"price":{l.setSeriesPrecision(i);break}case"volume":{l.setSeriesPrecision(o);break}}};R(r)?s(r):this._indicators.forEach(function(l){l.forEach(function(c){s(c)})})}},t.prototype.overrideIndicator=function(r){var e=this,a=!1,i=!1,n=this.getIndicatorsByFilter(r);return n.forEach(function(o){o.override(r);var s=o.shouldUpdateImp(),l=s.calc,c=s.draw,u=s.sort;u&&(i=!0),l?e._addIndicatorCalcTask(o,"update"):c&&(a=!0)}),i&&this._sortIndicators(),a?(this._chart.layout({update:!0}),!0):!1},t.prototype.getOverlaysByFilter=function(r){var e,a=r.id,i=r.groupId,n=r.paneId,o=r.name,s=function(u){return R(a)?u.id===a:R(i)?u.groupId===i&&(!R(o)||u.name===o):!R(o)||u.name===o},l=[];R(n)?l=l.concat(this.getOverlaysByPaneId(n).filter(s)):this._overlays.forEach(function(u){l=l.concat(u.filter(s))});var c=(e=this._progressOverlayInfo)===null||e===void 0?void 0:e.overlay;return R(c)&&s(c)&&l.push(c),l},t.prototype.getOverlaysByPaneId=function(r){var e;if(!Kt(r)){var a=[];return this._overlays.forEach(function(i){a=a.concat(i)}),a}return(e=this._overlays.get(r))!==null&&e!==void 0?e:[]},t.prototype._sortOverlays=function(r){var e;Kt(r)?(e=this._overlays.get(r))===null||e===void 0||e.sort(function(a,i){return a.zLevel-i.zLevel}):this._overlays.forEach(function(a){a.sort(function(i,n){return i.zLevel-n.zLevel})})},t.prototype.addOverlays=function(r,e){var a=this,i=[],n=r.map(function(o,s){var l,c,u,d,h,v,f,p;if(R(o.id)){var g=null;try{for(var m=Xe(a._overlays),_=m.next();!_.done;_=m.next()){var x=_.value,S=x[1],y=S.find(function(I){return I.id===o.id});if(R(y)){g=y;break}}}catch(I){l={error:I}}finally{try{_&&!_.done&&(c=m.return)&&c.call(m)}finally{if(l)throw l.error}}if(R(g))return o.id}var k=g1(o.name);if(R(k)){var C=(u=o.id)!==null&&u!==void 0?u:ua(Ul),y=new k,D=(d=o.paneId)!==null&&d!==void 0?d:xt.CANDLE;o.id=C,(h=o.groupId)!==null&&h!==void 0||(o.groupId=C);var E=a.getOverlaysByPaneId(D).length;return(v=o.zLevel)!==null&&v!==void 0||(o.zLevel=E),y.override(o),i.includes(D)||i.push(D),y.isDrawing()?a._progressOverlayInfo={paneId:D,overlay:y,appointPaneFlag:e[s]}:(a._overlays.has(D)||a._overlays.set(D,[]),(f=a._overlays.get(D))===null||f===void 0||f.push(y)),y.isStart()&&((p=y.onDrawStart)===null||p===void 0||p.call(y,{overlay:y,chart:a._chart})),C}return null});return i.length>0&&(this._sortOverlays(),i.forEach(function(o){a._chart.updatePane(1,o)}),this._chart.updatePane(1,xt.X_AXIS)),n},t.prototype.getProgressOverlayInfo=function(){return this._progressOverlayInfo},t.prototype.progressOverlayComplete=function(){var r;if(this._progressOverlayInfo!==null){var e=this._progressOverlayInfo,a=e.overlay,i=e.paneId;a.isDrawing()||(this._overlays.has(i)||this._overlays.set(i,[]),(r=this._overlays.get(i))===null||r===void 0||r.push(a),this._sortOverlays(i),this._progressOverlayInfo=null)}},t.prototype.updateProgressOverlayInfo=function(r,e){this._progressOverlayInfo!==null&&(Qr(e)&&e&&(this._progressOverlayInfo.appointPaneFlag=e),this._progressOverlayInfo.paneId=r,this._progressOverlayInfo.overlay.override({paneId:r}))},t.prototype.overrideOverlay=function(r){var e=this,a=!1,i=[],n=this.getOverlaysByFilter(r);return n.forEach(function(o){o.override(r);var s=o.shouldUpdate(),l=s.sort,c=s.draw;l&&(a=!0),(l||c)&&(i.includes(o.paneId)||i.push(o.paneId))}),a&&this._sortOverlays(),i.length>0?(i.forEach(function(o){e._chart.updatePane(1,o)}),this._chart.updatePane(1,xt.X_AXIS),!0):!1},t.prototype.removeOverlay=function(r){var e=this,a=[],i=this.getOverlaysByFilter(r);return i.forEach(function(n){var o,s=n.paneId,l=e.getOverlaysByPaneId(n.paneId);if((o=n.onRemoved)===null||o===void 0||o.call(n,{overlay:n,chart:e._chart}),a.includes(s)||a.push(s),n.isDrawing())e._progressOverlayInfo=null;else{var c=l.findIndex(function(u){return u.id===n.id});c>-1&&l.splice(c,1)}l.length===0&&e._overlays.delete(s)}),a.length>0?(a.forEach(function(n){e._chart.updatePane(1,n)}),this._chart.updatePane(1,xt.X_AXIS),!0):!1},t.prototype.setPressedOverlayInfo=function(r){this._pressedOverlayInfo=r},t.prototype.getPressedOverlayInfo=function(){return this._pressedOverlayInfo},t.prototype.setHoverOverlayInfo=function(r,e,a){var i=this._hoverOverlayInfo,n=i.overlay,o=i.figureType,s=i.figureIndex,l=i.figure,c=r.overlay;if(((n==null?void 0:n.id)!==(c==null?void 0:c.id)||o!==r.figureType||s!==r.figureIndex)&&(this._hoverOverlayInfo=r,(n==null?void 0:n.id)!==(c==null?void 0:c.id))){var u=!1,d=!1;n!==null&&(n.override({zLevel:n.getPrevZLevel()}),d=!0,a(n,l)&&(u=!0)),c!==null&&(c.setPrevZLevel(c.zLevel),c.override({zLevel:Number.MAX_SAFE_INTEGER}),d=!0,e(c,r.figure)&&(u=!0)),d&&this._sortOverlays(),u||this._chart.updatePane(1)}},t.prototype.getHoverOverlayInfo=function(){return this._hoverOverlayInfo},t.prototype.setClickOverlayInfo=function(r,e,a){var i=this._clickOverlayInfo,n=i.paneId,o=i.overlay,s=i.figureType,l=i.figure,c=i.figureIndex,u=r.overlay;((o==null?void 0:o.id)!==(u==null?void 0:u.id)||s!==r.figureType||c!==r.figureIndex)&&(this._clickOverlayInfo=r,(o==null?void 0:o.id)!==(u==null?void 0:u.id)&&(R(o)&&a(o,l),R(u)&&e(u,r.figure),this._chart.updatePane(1,r.paneId),n!==r.paneId&&this._chart.updatePane(1,n),this._chart.updatePane(1,xt.X_AXIS)))},t.prototype.getClickOverlayInfo=function(){return this._clickOverlayInfo},t.prototype.isOverlayEmpty=function(){return this._overlays.size===0&&this._progressOverlayInfo===null},t.prototype.isOverlayDrawing=function(){var r,e;return(e=(r=this._progressOverlayInfo)===null||r===void 0?void 0:r.overlay.isDrawing())!==null&&e!==void 0?e:!1},t.prototype._clearLastPriceMarkExtendTextUpdateTimer=function(){this._lastPriceMarkExtendTextUpdateTimers.forEach(function(r){clearInterval(r)}),this._lastPriceMarkExtendTextUpdateTimers=[]},t.prototype._clearData=function(){this._dataLoadMore.backward=!1,this._dataLoadMore.forward=!1,this._loading=!1,this._dataList=[],this._visibleRangeDataList=[],this._visibleRangeHighLowPrice=[{x:0,price:Number.MIN_SAFE_INTEGER},{x:0,price:Number.MAX_SAFE_INTEGER}],this._visibleRange=Gi(),this._crosshair={}},t.prototype.getChart=function(){return this._chart},t.prototype.destroy=function(){this._clearData(),this._clearLastPriceMarkExtendTextUpdateTimer(),this._taskScheduler.removeTask(),this._overlays.clear(),this._indicators.clear(),this._actions.clear()},t.prototype.setOrderFlowData=function(r){this._orderBookDataList=r;var e=this._chart.getDrawPaneById(xt.CANDLE);if(R(e)){var a=e.getMainWidget();R(a)&&"setOrderFlowData"in a&&typeof a.setOrderFlowData=="function"&&(a.setOrderFlowData(r),this._chart.updatePane(3,xt.CANDLE))}},t.prototype.getOrderFlowData=function(){return this._orderBookDataList},t.prototype.applyNewData=function(r,e){this._addData(r,"init",e)},t.prototype.updateData=function(r){this._addData(r,"update")},t}(),Nt={MAIN:"main",X_AXIS:"xAxis",Y_AXIS:"yAxis",SEPARATOR:"separator"},Kr=7;function L1(){return oo(this,void 0,void 0,function(){return so(this,function(t){switch(t.label){case 0:return[4,new Promise(function(r){var e=new ResizeObserver(function(a){r(a.every(function(i){return"devicePixelContentBoxSize"in i})),e.disconnect()});e.observe(document.body,{box:"device-pixel-content-box"})}).catch(function(){return!1})];case 1:return[2,t.sent()]}})})}var tn=function(){function t(r,e){var a=this;this._supportedDevicePixelContentBox=!1,this._width=0,this._height=0,this._pixelWidth=0,this._pixelHeight=0,this._nextPixelWidth=0,this._nextPixelHeight=0,this._requestAnimationId=Tr,this._mediaQueryListener=function(){var i=vr(a._element);a._nextPixelWidth=Math.round(a._element.clientWidth*i),a._nextPixelHeight=Math.round(a._element.clientHeight*i),a._resetPixelRatio()},this._listener=e,this._element=sr("canvas",r),this._ctx=this._element.getContext("2d"),L1().then(function(i){a._supportedDevicePixelContentBox=i,i?(a._resizeObserver=new ResizeObserver(function(n){var o=n.find(function(l){return l.target===a._element}),s=o==null?void 0:o.devicePixelContentBoxSize[0];R(s)&&(a._nextPixelWidth=s.inlineSize,a._nextPixelHeight=s.blockSize,(a._pixelWidth!==a._nextPixelWidth||a._pixelHeight!==a._nextPixelHeight)&&a._resetPixelRatio())}),a._resizeObserver.observe(a._element,{box:"device-pixel-content-box"})):(a._mediaQueryList=window.matchMedia("(resolution: ".concat(vr(a._element),"dppx)")),a._mediaQueryList.addListener(a._mediaQueryListener))}).catch(function(i){return!1})}return t.prototype._resetPixelRatio=function(){var r=this;this._executeListener(function(){var e=r._element.clientWidth,a=r._element.clientHeight;r._width=e,r._height=a,r._pixelWidth=r._nextPixelWidth,r._pixelHeight=r._nextPixelHeight,r._element.width=r._nextPixelWidth,r._element.height=r._nextPixelHeight;var i=r._nextPixelWidth/e,n=r._nextPixelHeight/a;r._ctx.scale(i,n)})},t.prototype._executeListener=function(r){var e=this;this._requestAnimationId===Tr&&(this._requestAnimationId=ya(function(){e._ctx.clearRect(0,0,e._width,e._height),r==null||r(),e._listener(),e._requestAnimationId=Tr}))},t.prototype.update=function(r,e){if(this._width!==r||this._height!==e){if(this._element.style.width="".concat(r,"px"),this._element.style.height="".concat(e,"px"),!this._supportedDevicePixelContentBox){var a=vr(this._element);this._nextPixelWidth=Math.round(r*a),this._nextPixelHeight=Math.round(e*a),this._resetPixelRatio()}}else this._executeListener()},t.prototype.getElement=function(){return this._element},t.prototype.getContext=function(){return this._ctx},t.prototype.destroy=function(){R(this._resizeObserver)&&this._resizeObserver.unobserve(this._element),R(this._mediaQueryList)&&this._mediaQueryList.removeListener(this._mediaQueryListener)},t}(),yo=function(t){St(r,t);function r(e,a){var i=t.call(this)||this;return i._bounding=ma(),i._cursor="crosshair",i._forceCursor=null,i._pane=a,i._rootContainer=e,i._container=i.createContainer(),e.appendChild(i._container),i}return r.prototype.setBounding=function(e){return ye(this._bounding,e),this},r.prototype.getContainer=function(){return this._container},r.prototype.getBounding=function(){return this._bounding},r.prototype.getPane=function(){return this._pane},r.prototype.checkEventOn=function(e){return!0},r.prototype.setCursor=function(e){Kt(this._forceCursor)||e!==this._cursor&&(this._cursor=e,this._container.style.cursor=this._cursor)},r.prototype.setForceCursor=function(e){var a;e!==this._forceCursor&&(this._forceCursor=e,this._container.style.cursor=(a=this._forceCursor)!==null&&a!==void 0?a:this._cursor)},r.prototype.getForceCursor=function(){return this._forceCursor},r.prototype.update=function(e){this.updateImp(this._container,this._bounding,e??3)},r.prototype.destroy=function(){this._rootContainer.removeChild(this._container)},r}(gi),_i=function(t){St(r,t);function r(e,a){var i=t.call(this,e,a)||this;i._mainCanvas=new tn({position:"absolute",top:"0",left:"0",zIndex:"2",boxSizing:"border-box"},function(){i.updateMain(i._mainCanvas.getContext())}),i._overlayCanvas=new tn({position:"absolute",top:"0",left:"0",zIndex:"2",boxSizing:"border-box"},function(){i.updateOverlay(i._overlayCanvas.getContext())});var n=i.getContainer();return n.appendChild(i._mainCanvas.getElement()),n.appendChild(i._overlayCanvas.getElement()),i}return r.prototype.createContainer=function(){return sr("div",{margin:"0",padding:"0",position:"absolute",top:"0",overflow:"hidden",boxSizing:"border-box",zIndex:"1"})},r.prototype.updateImp=function(e,a,i){var n=a.width,o=a.height,s=a.left;e.style.left="".concat(s,"px");var l=i,c=e.clientWidth,u=e.clientHeight;switch((n!==c||o!==u)&&(e.style.width="".concat(n,"px"),e.style.height="".concat(o,"px"),l=3),l){case 0:{this._mainCanvas.update(n,o);break}case 1:{this._overlayCanvas.update(n,o);break}case 3:case 4:{this._mainCanvas.update(n,o),this._overlayCanvas.update(n,o);break}}},r.prototype.destroy=function(){this._mainCanvas.destroy(),this._overlayCanvas.destroy()},r.prototype.getImage=function(e){var a=this.getBounding(),i=a.width,n=a.height,o=sr("canvas",{width:"".concat(i,"px"),height:"".concat(n,"px"),boxSizing:"border-box"}),s=o.getContext("2d"),l=vr(o);return o.width=i*l,o.height=n*l,s.scale(l,l),s.drawImage(this._mainCanvas.getElement(),0,0,i,n),e&&s.drawImage(this._overlayCanvas.getElement(),0,0,i,n),o},r}(yo);function _o(t,r){var e,a,i=[];i=i.concat(r);try{for(var n=Xe(i),o=n.next();!o.done;o=n.next()){var s=o.value,l=s.x,c=s.y,u=s.r,d=t.x-l,h=t.y-c;if(!(d*d+h*h>u*u))return!0}}catch(v){e={error:v}}finally{try{o&&!o.done&&(a=n.return)&&a.call(n)}finally{if(e)throw e.error}}return!1}function S1(t,r,e){var a=[];a=a.concat(r);var i=e.style,n=i===void 0?"fill":i,o=e.color,s=o===void 0?"currentColor":o,l=e.borderSize,c=l===void 0?1:l,u=e.borderColor,d=u===void 0?"currentColor":u,h=e.borderStyle,v=h===void 0?"solid":h,f=e.borderDashedValue,p=f===void 0?[2,2]:f,g=(n==="fill"||e.style==="stroke_fill")&&(!Kt(s)||!Ir(s));g&&(t.fillStyle=s,a.forEach(function(m){var _=m.x,x=m.y,S=m.r;t.beginPath(),t.arc(_,x,S,0,Math.PI*2),t.closePath(),t.fill()})),(n==="stroke"||e.style==="stroke_fill")&&c>0&&!Ir(d)&&(t.strokeStyle=d,t.lineWidth=c,v==="dashed"?t.setLineDash(p):t.setLineDash([]),a.forEach(function(m){var _=m.x,x=m.y,S=m.r;(!g||S>c)&&(t.beginPath(),t.arc(_,x,S,0,Math.PI*2),t.closePath(),t.stroke())}))}var k1={name:"circle",checkEventOn:_o,draw:function(t,r,e){S1(t,r,e)}};function Co(t,r){var e,a,i=[];i=i.concat(r);try{for(var n=Xe(i),o=n.next();!o.done;o=n.next()){for(var s=o.value,l=!1,c=s.coordinates,u=0,d=c.length-1;u<c.length;d=u++)c[u].y>t.y!=c[d].y>t.y&&t.x<(c[d].x-c[u].x)*(t.y-c[u].y)/(c[d].y-c[u].y)+c[u].x&&(l=!l);if(l)return!0}}catch(h){e={error:h}}finally{try{o&&!o.done&&(a=n.return)&&a.call(n)}finally{if(e)throw e.error}}return!1}function M1(t,r,e){var a=[];a=a.concat(r);var i=e.style,n=i===void 0?"fill":i,o=e.color,s=o===void 0?"currentColor":o,l=e.borderSize,c=l===void 0?1:l,u=e.borderColor,d=u===void 0?"currentColor":u,h=e.borderStyle,v=h===void 0?"solid":h,f=e.borderDashedValue,p=f===void 0?[2,2]:f;(n==="fill"||e.style==="stroke_fill")&&(!Kt(s)||!Ir(s))&&(t.fillStyle=s,a.forEach(function(g){var m=g.coordinates;t.beginPath(),t.moveTo(m[0].x,m[0].y);for(var _=1;_<m.length;_++)t.lineTo(m[_].x,m[_].y);t.closePath(),t.fill()})),(n==="stroke"||e.style==="stroke_fill")&&c>0&&!Ir(d)&&(t.strokeStyle=d,t.lineWidth=c,v==="dashed"?t.setLineDash(p):t.setLineDash([]),a.forEach(function(g){var m=g.coordinates;t.beginPath(),t.moveTo(m[0].x,m[0].y);for(var _=1;_<m.length;_++)t.lineTo(m[_].x,m[_].y);t.closePath(),t.stroke()}))}var T1={name:"polygon",checkEventOn:Co,draw:function(t,r,e){M1(t,r,e)}};function Ci(t,r){var e,a,i=[];i=i.concat(r);try{for(var n=Xe(i),o=n.next();!o.done;o=n.next()){var s=o.value,l=s.x,c=s.width;c<Le*2&&(l-=Le,c=Le*2);var u=s.y,d=s.height;if(d<Le*2&&(u-=Le,d=Le*2),t.x>=l&&t.x<=l+c&&t.y>=u&&t.y<=u+d)return!0}}catch(h){e={error:h}}finally{try{o&&!o.done&&(a=n.return)&&a.call(n)}finally{if(e)throw e.error}}return!1}function bo(t,r,e){var a,i=[];i=i.concat(r);var n=e.style,o=n===void 0?"fill":n,s=e.color,l=s===void 0?"transparent":s,c=e.borderSize,u=c===void 0?1:c,d=e.borderColor,h=d===void 0?"transparent":d,v=e.borderStyle,f=v===void 0?"solid":v,p=e.borderRadius,g=p===void 0?0:p,m=e.borderDashedValue,_=m===void 0?[2,2]:m,x=(a=t.roundRect)!==null&&a!==void 0?a:t.rect,S=(o==="fill"||e.style==="stroke_fill")&&(!Kt(l)||!Ir(l));if(S&&(t.fillStyle=l,i.forEach(function(C){var D=C.x,E=C.y,I=C.width,T=C.height;t.beginPath(),x.call(t,D,E,I,T,g),t.closePath(),t.fill()})),(o==="stroke"||e.style==="stroke_fill")&&u>0&&!Ir(h)){t.strokeStyle=h,t.fillStyle=h,t.lineWidth=u,f==="dashed"?t.setLineDash(_):t.setLineDash([]);var y=u%2===1?.5:0,k=Math.round(y*2);i.forEach(function(C){var D=C.x,E=C.y,I=C.width,T=C.height;I>u*2&&T>u*2?(t.beginPath(),x.call(t,D+y,E+y,I-k,T-k,g),t.closePath(),t.stroke()):S||t.fillRect(D,E,I,T)})}}var E1={name:"rect",checkEventOn:Ci,draw:function(t,r,e){bo(t,r,e)}};function xo(t,r){var e=r.size,a=e===void 0?12:e,i=r.paddingLeft,n=i===void 0?0:i,o=r.paddingTop,s=o===void 0?0:o,l=r.paddingRight,c=l===void 0?0:l,u=r.paddingBottom,d=u===void 0?0:u,h=r.weight,v=h===void 0?"normal":h,f=r.family,p=t.x,g=t.y,m=t.text,_=t.align,x=_===void 0?"left":_,S=t.baseline,y=S===void 0?"top":S,k=t.width,C=t.height,D=k??n+je(m,a,v,f)+c,E=C??s+a+d,I=0;switch(x){case"left":case"start":{I=p;break}case"right":case"end":{I=p-D;break}default:{I=p-D/2;break}}var T=0;switch(y){case"top":case"hanging":{T=g;break}case"bottom":case"ideographic":case"alphabetic":{T=g-E;break}default:{T=g-E/2;break}}return{x:I,y:T,width:D,height:E}}function wo(t,r,e){var a,i,n=[];n=n.concat(r);try{for(var o=Xe(n),s=o.next();!s.done;s=o.next()){var l=s.value,c=xo(l,e),u=c.x,d=c.y,h=c.width,v=c.height;if(t.x>=u&&t.x<=u+h&&t.y>=d&&t.y<=d+v)return!0}}catch(f){a={error:f}}finally{try{s&&!s.done&&(i=o.return)&&i.call(o)}finally{if(a)throw a.error}}return!1}function I1(t,r,e){var a=[];a=a.concat(r);var i=e.color,n=i===void 0?"currentColor":i,o=e.size,s=o===void 0?12:o,l=e.family,c=e.weight,u=e.paddingLeft,d=u===void 0?0:u,h=e.paddingTop,v=h===void 0?0:h,f=e.paddingRight,p=f===void 0?0:f,g=a.map(function(m){return xo(m,e)});bo(t,g,J(J({},e),{color:e.backgroundColor})),t.textAlign="left",t.textBaseline="top",t.font=br(s,c,l),t.fillStyle=n,a.forEach(function(m,_){var x=g[_];t.fillText(m.text,x.x+d,x.y+v,x.width-d-p)})}var A1={name:"text",checkEventOn:wo,draw:function(t,r,e){I1(t,r,e)}};function D1(t,r){var e=t.x-r.x,a=t.y-r.y;return Math.sqrt(e*e+a*a)}function Lo(t,r){var e,a,i=[];i=i.concat(r);try{for(var n=Xe(i),o=n.next();!o.done;o=n.next()){var s=o.value;if(Math.abs(D1(t,s)-s.r)<Le){var l=s.r,c=s.startAngle,u=s.endAngle,d=l*Math.cos(c)+s.x,h=l*Math.sin(c)+s.y,v=l*Math.cos(u)+s.x,f=l*Math.sin(u)+s.y;if(t.x<=Math.max(d,v)+Le&&t.x>=Math.min(d,v)-Le&&t.y<=Math.max(h,f)+Le&&t.y>=Math.min(h,f)-Le)return!0}}}catch(p){e={error:p}}finally{try{o&&!o.done&&(a=n.return)&&a.call(n)}finally{if(e)throw e.error}}return!1}function P1(t,r,e){var a=[];a=a.concat(r);var i=e.style,n=i===void 0?"solid":i,o=e.size,s=o===void 0?1:o,l=e.color,c=l===void 0?"currentColor":l,u=e.dashedValue,d=u===void 0?[2,2]:u;t.lineWidth=s,t.strokeStyle=c,n==="dashed"?t.setLineDash(d):t.setLineDash([]),a.forEach(function(h){var v=h.x,f=h.y,p=h.r,g=h.startAngle,m=h.endAngle;t.beginPath(),t.arc(v,f,p,g,m),t.stroke(),t.closePath()})}var F1={name:"arc",checkEventOn:Lo,draw:function(t,r,e){P1(t,r,e)}};function en(t,r,e,a,i,n,o){var s=nr(a,7),l=s[0],c=s[1],u=s[2],d=s[3],h=s[4],v=s[5],f=s[6],p=o?r+v:v+i,g=o?e+f:f+n,m=R1(r,e,l,c,u,d,h,p,g);m.forEach(function(_){t.bezierCurveTo(_[0],_[1],_[2],_[3],_[4],_[5])})}function R1(t,r,e,a,i,n,o,s,l){for(var c=B1(t,r,e,a,i,n,o,s,l),u=c.cx,d=c.cy,h=c.startAngle,v=c.deltaAngle,f=[],p=Math.ceil(Math.abs(v)/(Math.PI/2)),g=0;g<p;g++){var m=h+g*v/p,_=h+(g+1)*v/p,x=N1(u,d,e,a,i,m,_);f.push(x)}return f}function B1(t,r,e,a,i,n,o,s,l){var c=i*Math.PI/180,u=(t-s)/2,d=(r-l)/2,h=Math.cos(c)*u+Math.sin(c)*d,v=-Math.sin(c)*u+Math.cos(c)*d,f=Math.pow(h,2)/Math.pow(e,2)+Math.pow(v,2)/Math.pow(a,2);f>1&&(e*=Math.sqrt(f),a*=Math.sqrt(f));var p=n===o?-1:1,g=Math.pow(e,2)*Math.pow(a,2)-Math.pow(e,2)*Math.pow(v,2)-Math.pow(a,2)*Math.pow(h,2),m=Math.pow(e,2)*Math.pow(v,2)+Math.pow(a,2)*Math.pow(h,2),_=p*Math.sqrt(Math.abs(g/m))*(e*v/a),x=p*Math.sqrt(Math.abs(g/m))*(-a*h/e),S=Math.cos(c)*_-Math.sin(c)*x+(t+s)/2,y=Math.sin(c)*_+Math.cos(c)*x+(r+l)/2,k=Math.atan2((v-x)/a,(h-_)/e),C=Math.atan2((-v-x)/a,(-h-_)/e)-k;return C<0&&o===1?C+=2*Math.PI:C>0&&o===0&&(C-=2*Math.PI),{cx:S,cy:y,startAngle:k,deltaAngle:C}}function N1(t,r,e,a,i,n,o){var s=Math.sin(o-n)*(Math.sqrt(4+3*Math.pow(Math.tan((o-n)/2),2))-1)/3,l=Math.cos(i),c=Math.sin(i),u=t+e*Math.cos(n)*l-a*Math.sin(n)*c,d=r+e*Math.cos(n)*c+a*Math.sin(n)*l,h=t+e*Math.cos(o)*l-a*Math.sin(o)*c,v=r+e*Math.cos(o)*c+a*Math.sin(o)*l,f=u+s*(-e*Math.sin(n)*l-a*Math.cos(n)*c),p=d+s*(-e*Math.sin(n)*c+a*Math.cos(n)*l),g=h-s*(-e*Math.sin(o)*l-a*Math.cos(o)*c),m=v-s*(-e*Math.sin(o)*c+a*Math.cos(o)*l);return[f,p,g,m,h,v]}function O1(t,r,e){var a=[];a=a.concat(r);var i=e.lineWidth,n=i===void 0?1:i,o=e.color,s=o===void 0?"currentColor":o;t.lineWidth=n,t.strokeStyle=s,t.setLineDash([]),a.forEach(function(l){var c=l.x,u=l.y,d=l.path,h=d.match(/[MLHVCSQTAZ][^MLHVCSQTAZ]*/gi);if(R(h)){var v=c,f=u;t.beginPath(),h.forEach(function(p){var g=0,m=0,_=0,x=0,S=p[0],y=p.slice(1).trim().split(/[\s,]+/).map(Number);switch(S){case"M":g=y[0]+v,m=y[1]+f,t.moveTo(g,m),_=g,x=m;break;case"m":g+=y[0],m+=y[1],t.moveTo(g,m),_=g,x=m;break;case"L":g=y[0]+v,m=y[1]+f,t.lineTo(g,m);break;case"l":g+=y[0],m+=y[1],t.lineTo(g,m);break;case"H":g=y[0]+v,t.lineTo(g,m);break;case"h":g+=y[0],t.lineTo(g,m);break;case"V":m=y[0]+f,t.lineTo(g,m);break;case"v":m+=y[0],t.lineTo(g,m);break;case"C":t.bezierCurveTo(y[0]+v,y[1]+f,y[2]+v,y[3]+f,y[4]+v,y[5]+f),g=y[4]+v,m=y[5]+f;break;case"c":t.bezierCurveTo(g+y[0],m+y[1],g+y[2],m+y[3],g+y[4],m+y[5]),g+=y[4],m+=y[5];break;case"S":t.bezierCurveTo(g,m,y[0]+v,y[1]+f,y[2]+v,y[3]+f),g=y[2]+v,m=y[3]+f;break;case"s":t.bezierCurveTo(g,m,g+y[0],m+y[1],g+y[2],m+y[3]),g+=y[2],m+=y[3];break;case"Q":t.quadraticCurveTo(y[0]+v,y[1]+f,y[2]+v,y[3]+f),g=y[2]+v,m=y[3]+f;break;case"q":t.quadraticCurveTo(g+y[0],m+y[1],g+y[2],m+y[3]),g+=y[2],m+=y[3];break;case"T":t.quadraticCurveTo(g,m,y[0]+v,y[1]+f),g=y[0]+v,m=y[1]+f;break;case"t":t.quadraticCurveTo(g,m,g+y[0],m+y[1]),g+=y[0],m+=y[1];break;case"A":en(t,g,m,y,v,f,!1),g=y[5]+v,m=y[6]+f;break;case"a":en(t,g,m,y,v,f,!0),g+=y[5],m+=y[6];break;case"Z":case"z":t.closePath(),g=_,m=x;break}}),e.style==="fill"?t.fill():t.stroke()}})}var V1={name:"path",checkEventOn:Ci,draw:function(t,r,e){O1(t,r,e)}},Pa={},z1=[k1,r1,T1,E1,A1,F1,V1];z1.forEach(function(t){Pa[t.name]=vo.extend(t)});function $1(t){Pa[t.name]=vo.extend(t)}function W1(t){var r;return(r=Pa[t])!==null&&r!==void 0?r:null}function Y1(t){var r;return(r=Pa[t])!==null&&r!==void 0?r:null}var We=function(t){St(r,t);function r(e){var a=t.call(this)||this;return a._widget=e,a}return r.prototype.getWidget=function(){return this._widget},r.prototype.createFigure=function(e,a){var i=W1(e.name);if(i!==null){var n=new i(e);if(R(a)){for(var o in a)a.hasOwnProperty(o)&&n.registerEvent(o,a[o]);this.addChild(n)}return n}return null},r.prototype.draw=function(e){for(var a=[],i=1;i<arguments.length;i++)a[i-1]=arguments[i];this.clear(),this.drawImp(e,a)},r.prototype.checkEventOn=function(e){return!0},r}(gi),Z1=function(t){St(r,t);function r(){return t!==null&&t.apply(this,arguments)||this}return r.prototype.drawImp=function(e){var a,i,n=this.getWidget(),o=this.getWidget().getPane(),s=o.getChart(),l=n.getBounding(),c=s.getStyles().grid,u=c.show;if(u){e.save(),e.globalCompositeOperation="destination-over";var d=c.horizontal,h=d.show;if(h){var v=o.getAxisComponent(),f=v.getTicks().map(function(_){return{coordinates:[{x:0,y:_.coord},{x:l.width,y:_.coord}]}});(a=this.createFigure({name:"line",attrs:f,styles:d}))===null||a===void 0||a.draw(e)}var p=c.vertical,g=p.show;if(g){var m=s.getXAxisPane().getAxisComponent(),f=m.getTicks().map(function(x){return{coordinates:[{x:x.coord,y:0},{x:x.coord,y:l.height}]}});(i=this.createFigure({name:"line",attrs:f,styles:p}))===null||i===void 0||i.draw(e)}e.restore()}},r}(We),bi=function(t){St(r,t);function r(){return t!==null&&t.apply(this,arguments)||this}return r.prototype.eachChildren=function(e){for(var a=this.getWidget().getPane(),i=a.getChart().getChartStore(),n=i.getVisibleRangeDataList(),o=i.getBarSpace(),s=n.length,l=0;l<s;)e(n[l],o,l),++l},r}(We),So=function(t){St(r,t);function r(){var e=t.apply(this,hr([],nr(arguments),!1))||this;return e._boundCandleBarClickEvent=function(a){return function(){return e.getWidget().getPane().getChart().getChartStore().executeAction("onCandleBarClick",a),!1}},e}return r.prototype.drawImp=function(e){var a=this,i=this.getWidget().getPane(),n=i.getId()===xt.CANDLE,o=i.getChart().getChartStore();if(o.isCandleVisible()){var s=this.getCandleBarOptions();if(s!==null){var l=s.type,c=s.styles,u=0,d=0;if(s.type==="ohlc"){var h=o.getBarSpace().gapBar;u=Math.min(Math.max(Math.round(h*.2),1),8),u>2&&u%2===1&&u--,d=Math.floor(u/2)}var v=i.getAxisComponent();this.eachChildren(function(f,p){var g,m=f.x,_=f.data,x=_.current,S=_.prev;if(R(x)){var y=x.open,k=x.high,C=x.low,D=x.close,E=c.compareRule==="current_open"?y:(g=S==null?void 0:S.close)!==null&&g!==void 0?g:D,I=[];D>E?(I[0]=c.upColor,I[1]=c.upBorderColor,I[2]=c.upWickColor):D<E?(I[0]=c.downColor,I[1]=c.downBorderColor,I[2]=c.downWickColor):(I[0]=c.noChangeColor,I[1]=c.noChangeBorderColor,I[2]=c.noChangeWickColor);var T=v.convertToPixel(y),L=v.convertToPixel(D),b=[T,L,v.convertToPixel(k),v.convertToPixel(C)];b.sort(function(P,N){return P-N});var M=p.gapBar%2===0?1:0,A=[];switch(l){case"candle_solid":{A=a._createSolidBar(m,b,p,I,M);break}case"candle_stroke":{A=a._createStrokeBar(m,b,p,I,M);break}case"candle_up_stroke":{D>y?A=a._createStrokeBar(m,b,p,I,M):A=a._createSolidBar(m,b,p,I,M);break}case"candle_down_stroke":{y>D?A=a._createStrokeBar(m,b,p,I,M):A=a._createSolidBar(m,b,p,I,M);break}case"ohlc":{A=[{name:"rect",attrs:[{x:m-d,y:b[0],width:u,height:b[3]-b[0]},{x:m-p.halfGapBar,y:T+u>b[3]?b[3]-u:T,width:p.halfGapBar-d,height:u},{x:m+d,y:L+u>b[3]?b[3]-u:L,width:p.halfGapBar-d,height:u}],styles:{color:I[0]}}];break}}A.forEach(function(P){var N,W=null;n&&(W={mouseClickEvent:a._boundCandleBarClickEvent(f)}),(N=a.createFigure(P,W??void 0))===null||N===void 0||N.draw(e)})}})}}},r.prototype.getCandleBarOptions=function(){var e=this.getWidget().getPane().getChart().getStyles().candle;return{type:e.type,styles:e.bar}},r.prototype._createSolidBar=function(e,a,i,n,o){return[{name:"rect",attrs:{x:e,y:a[0],width:1,height:a[3]-a[0]},styles:{color:n[2]}},{name:"rect",attrs:{x:e-i.halfGapBar,y:a[1],width:i.gapBar+o,height:Math.max(1,a[2]-a[1])},styles:{style:"stroke_fill",color:n[0],borderColor:n[1]}}]},r.prototype._createStrokeBar=function(e,a,i,n,o){return[{name:"rect",attrs:[{x:e,y:a[0],width:1,height:a[1]-a[0]},{x:e,y:a[2],width:1,height:a[3]-a[2]}],styles:{color:n[2]}},{name:"rect",attrs:{x:e-i.halfGapBar,y:a[1],width:i.gapBar+o,height:Math.max(1,a[2]-a[1])},styles:{style:"stroke",borderColor:n[1]}}]},r}(bi),H1=function(t){St(r,t);function r(){return t!==null&&t.apply(this,arguments)||this}return r.prototype.getCandleBarOptions=function(){var e,a,i=this.getWidget().getPane(),n=i.getAxisComponent();if(!n.isInCandle()){var o=i.getChart().getChartStore(),s=o.getIndicatorsByPaneId(i.getId());try{for(var l=Xe(s),c=l.next();!c.done;c=l.next()){var u=c.value;if(u.shouldOhlc&&u.visible){var d=u.styles,h=o.getStyles().indicator,v=be(d,"ohlc.compareRule",h.ohlc.compareRule),f=be(d,"ohlc.upColor",h.ohlc.upColor),p=be(d,"ohlc.downColor",h.ohlc.downColor),g=be(d,"ohlc.noChangeColor",h.ohlc.noChangeColor);return{type:"ohlc",styles:{compareRule:v,upColor:f,downColor:p,noChangeColor:g,upBorderColor:f,downBorderColor:p,noChangeBorderColor:g,upWickColor:f,downWickColor:p,noChangeWickColor:g}}}}}catch(m){e={error:m}}finally{try{c&&!c.done&&(a=l.return)&&a.call(l)}finally{if(e)throw e.error}}}return null},r.prototype.drawImp=function(e){var a=this;t.prototype.drawImp.call(this,e);var i=this.getWidget(),n=i.getPane(),o=n.getChart(),s=i.getBounding(),l=o.getXAxisPane().getAxisComponent(),c=n.getAxisComponent(),u=o.getChartStore(),d=u.getIndicatorsByPaneId(n.getId()),h=u.getStyles().indicator;e.save(),d.forEach(function(v){if(v.visible){v.zLevel<0?e.globalCompositeOperation="destination-over":e.globalCompositeOperation="source-over";var f=!1;if(v.draw!==null&&(e.save(),f=v.draw({ctx:e,chart:o,indicator:v,bounding:s,xAxis:l,yAxis:c}),e.restore()),!f){var p=v.result,g=[];a.eachChildren(function(m,_){var x,S,y,k=_.halfGapBar,C=m.dataIndex,D=m.x,E=l.convertToPixel(C-1),I=l.convertToPixel(C+1),T=(x=p[C-1])!==null&&x!==void 0?x:null,L=(S=p[C])!==null&&S!==void 0?S:null,b=(y=p[C+1])!==null&&y!==void 0?y:null,M={x:E},A={x:D},P={x:I};v.figures.forEach(function(N){var W=N.key,X=T==null?void 0:T[W];vt(X)&&(M[W]=c.convertToPixel(X));var $=L==null?void 0:L[W];vt($)&&(A[W]=c.convertToPixel($));var ct=b==null?void 0:b[W];vt(ct)&&(P[W]=c.convertToPixel(ct))}),fi(v,C,h,function(N,W,X){var $,ct,at;if(R(L==null?void 0:L[N.key])){var gt=A[N.key],Tt=($=N.attrs)===null||$===void 0?void 0:$.call(N,{data:{prev:T,current:L,next:b},coordinate:{prev:M,current:A,next:P},bounding:s,barSpace:_,xAxis:l,yAxis:c});if(!R(Tt))switch(N.type){case"circle":{Tt={x:D,y:gt,r:Math.max(1,k)};break}case"rect":case"bar":{var Wt=(ct=N.baseValue)!==null&&ct!==void 0?ct:c.getRange().from,V=c.convertToPixel(Wt),G=Math.abs(V-gt);Wt!==(L==null?void 0:L[N.key])&&(G=Math.max(1,G));var O=0;gt>V?O=V:O=gt,Tt={x:D-k,y:O,width:Math.max(1,k*2),height:G};break}case"line":{R(g[X])||(g[X]=[]),vt(A[N.key])&&vt(P[N.key])&&g[X].push({coordinates:[{x:A.x,y:A[N.key]},{x:P.x,y:P[N.key]}],styles:W});break}}var q=N.type;R(Tt)&&q!=="line"&&((at=a.createFigure({name:q==="bar"?"rect":q,attrs:Tt,styles:W}))===null||at===void 0||at.draw(e))}})}),g.forEach(function(m){var _,x,S,y;if(m.length>1){for(var k=[{coordinates:[m[0].coordinates[0],m[0].coordinates[1]],styles:m[0].styles}],C=1;C<m.length;C++){var D=k[k.length-1],E=m[C],I=D.coordinates[D.coordinates.length-1];I.x===E.coordinates[0].x&&I.y===E.coordinates[0].y&&D.styles.style===E.styles.style&&D.styles.color===E.styles.color&&D.styles.size===E.styles.size&&D.styles.smooth===E.styles.smooth&&((_=D.styles.dashedValue)===null||_===void 0?void 0:_[0])===((x=E.styles.dashedValue)===null||x===void 0?void 0:x[0])&&((S=D.styles.dashedValue)===null||S===void 0?void 0:S[1])===((y=E.styles.dashedValue)===null||y===void 0?void 0:y[1])?D.coordinates.push(E.coordinates[1]):k.push({coordinates:[E.coordinates[0],E.coordinates[1]],styles:E.styles})}k.forEach(function(T){var L,b=T.coordinates,M=T.styles;(L=a.createFigure({name:"line",attrs:{coordinates:b},styles:M}))===null||L===void 0||L.draw(e)})}})}}}),e.restore()},r}(So),j1=function(t){St(r,t);function r(){return t!==null&&t.apply(this,arguments)||this}return r.prototype.drawImp=function(e){var a=this.getWidget(),i=a.getPane(),n=a.getBounding(),o=a.getPane().getChart().getChartStore(),s=o.getCrosshair(),l=o.getStyles().crosshair;if(Kt(s.paneId)&&l.show){if(s.paneId===i.getId()){var c=s.y;this._drawLine(e,[{x:0,y:c},{x:n.width,y:c}],l.horizontal)}var u=s.realX;this._drawLine(e,[{x:u,y:0},{x:u,y:n.height}],l.vertical)}},r.prototype._drawLine=function(e,a,i){var n;if(i.show){var o=i.line;o.show&&((n=this.createFigure({name:"line",attrs:{coordinates:a},styles:o}))===null||n===void 0||n.draw(e))}},r}(We),ko=function(t){St(r,t);function r(e){var a=t.call(this,e)||this;return a._activeFeatureInfo=null,a._featureClickEvent=function(i,n){return function(){var o=a.getWidget().getPane();return o.getChart().getChartStore().executeAction(i,n),!0}},a._featureMouseMoveEvent=function(i){return function(){return a._activeFeatureInfo=i,!0}},a.registerEvent("mouseMoveEvent",function(i){return a._activeFeatureInfo=null,!1}),a}return r.prototype.drawImp=function(e){var a=this.getWidget(),i=a.getPane(),n=i.getChart().getChartStore(),o=n.getCrosshair();if(R(o.kLineData)){var s=a.getBounding(),l=n.getStyles().indicator.tooltip,c=l.offsetLeft,u=l.offsetTop,d=l.offsetRight;this.drawIndicatorTooltip(e,c,u,s.width-d)}},r.prototype.drawIndicatorTooltip=function(e,a,i,n){var o=this,s=this.getWidget().getPane(),l=s.getChart().getChartStore(),c=l.getStyles().indicator,u=c.tooltip;if(this.isDrawTooltip(l.getCrosshair(),u)){var d=l.getIndicatorsByPaneId(s.getId()),h=u.title,v=u.legend;d.forEach(function(f){var p=0,g={x:a,y:i},m=o.getIndicatorTooltipData(f),_=m.name,x=m.calcParamsText,S=m.legends,y=m.features,k=_.length>0,C=S.length>0;if(k||C){var D=o.classifyTooltipFeatures(y);if(p=o.drawStandardTooltipFeatures(e,D[0],g,f,a,p,n),k){var E=_;x.length>0&&(E="".concat(E).concat(x));var I=h.color;p=o.drawStandardTooltipLegends(e,[{title:{text:"",color:I},value:{text:E,color:I}}],g,a,p,n,h)}p=o.drawStandardTooltipFeatures(e,D[1],g,f,a,p,n),C&&(p=o.drawStandardTooltipLegends(e,S,g,a,p,n,v)),p=o.drawStandardTooltipFeatures(e,D[2],g,f,a,p,n),i=g.y+p}})}return i},r.prototype.drawStandardTooltipFeatures=function(e,a,i,n,o,s,l){var c=this;if(a.length>0){var u=0,d=0;a.forEach(function(f){var p=f.marginLeft,g=p===void 0?0:p,m=f.marginTop,_=m===void 0?0:m,x=f.marginRight,S=x===void 0?0:x,y=f.marginBottom,k=y===void 0?0:y,C=f.paddingLeft,D=C===void 0?0:C,E=f.paddingTop,I=E===void 0?0:E,T=f.paddingRight,L=T===void 0?0:T,b=f.paddingBottom,M=b===void 0?0:b,A=f.size,P=A===void 0?0:A,N=f.type,W=f.content,X=0;if(N==="icon_font"){var $=W;e.font=br(P,"normal",$.family),X=e.measureText($.code).width}else X=P;u+=g+D+X+L+S,d=Math.max(d,_+I+P+M+k)}),i.x+u>l?(i.x=o,i.y+=s,s=d):s=Math.max(s,d);var h=this.getWidget().getPane(),v=h.getId();a.forEach(function(f){var p,g,m,_,x,S=f.marginLeft,y=S===void 0?0:S,k=f.marginTop,C=k===void 0?0:k,D=f.marginRight,E=D===void 0?0:D,I=f.paddingLeft,T=I===void 0?0:I,L=f.paddingTop,b=L===void 0?0:L,M=f.paddingRight,A=M===void 0?0:M,P=f.paddingBottom,N=P===void 0?0:P,W=f.backgroundColor,X=f.activeBackgroundColor,$=f.borderRadius,ct=f.size,at=ct===void 0?0:ct,gt=f.color,Tt=f.activeColor,Wt=f.type,V=f.content,G=gt,O=W;((p=c._activeFeatureInfo)===null||p===void 0?void 0:p.paneId)===v&&((g=c._activeFeatureInfo.indicator)===null||g===void 0?void 0:g.id)===(n==null?void 0:n.id)&&c._activeFeatureInfo.feature.id===f.id&&(G=Tt??gt,O=X??W);var q="onCandleTooltipFeatureClick",dt={paneId:v,feature:f};R(n)&&(q="onIndicatorTooltipFeatureClick",dt.indicator=n);var tt={mouseClickEvent:c._featureClickEvent(q,dt),mouseMoveEvent:c._featureMouseMoveEvent(dt)},mt=0;if(Wt==="icon_font"){var Lt=V;(m=c.createFigure({name:"text",attrs:{text:Lt.code,x:i.x+y,y:i.y+C},styles:{paddingLeft:T,paddingTop:b,paddingRight:A,paddingBottom:N,borderRadius:$,size:at,family:Lt.family,color:G,backgroundColor:O}},tt))===null||m===void 0||m.draw(e),mt=e.measureText(Lt.code).width}else{(_=c.createFigure({name:"rect",attrs:{x:i.x+y,y:i.y+C,width:at,height:at},styles:{paddingLeft:T,paddingTop:b,paddingRight:A,paddingBottom:N,color:O}},tt))===null||_===void 0||_.draw(e);var Ht=V;(x=c.createFigure({name:"path",attrs:{path:Ht.path,x:i.x+y+T,y:i.y+C+b,width:at,height:at},styles:{style:Ht.style,lineWidth:Ht.lineWidth,color:G}}))===null||x===void 0||x.draw(e),mt=at}i.x+=y+T+mt+A+E})}return s},r.prototype.drawStandardTooltipLegends=function(e,a,i,n,o,s,l){var c=this;if(a.length>0){var u=l.marginLeft,d=l.marginTop,h=l.marginRight,v=l.marginBottom,f=l.size,p=l.family,g=l.weight;e.font=br(f,g,p),a.forEach(function(m){var _,x,S=m.title,y=m.value,k=e.measureText(S.text).width,C=e.measureText(y.text).width,D=k+C,E=d+f+v;i.x+u+D+h>s?(i.x=n,i.y+=o,o=E):o=Math.max(o,E),S.text.length>0&&((_=c.createFigure({name:"text",attrs:{x:i.x+u,y:i.y+d,text:S.text},styles:{color:S.color,size:f,family:p,weight:g}}))===null||_===void 0||_.draw(e)),(x=c.createFigure({name:"text",attrs:{x:i.x+u+k,y:i.y+d,text:y.text},styles:{color:y.color,size:f,family:p,weight:g}}))===null||x===void 0||x.draw(e),i.x+=u+D+h})}return o},r.prototype.isDrawTooltip=function(e,a){var i=a.showRule;return i==="always"||i==="follow_cross"&&Kt(e.paneId)},r.prototype.getIndicatorTooltipData=function(e){var a,i,n=this.getWidget().getPane().getChart().getChartStore(),o=n.getStyles().indicator,s=o.tooltip,l=s.title,c="",u="";if(l.show&&(l.showName&&(c=e.shortName),l.showParams)){var d=e.calcParams;d.length>0&&(u="(".concat(d.join(","),")"))}var h={name:c,calcParamsText:u,legends:[],features:s.features},v=n.getCrosshair().dataIndex,f=e.result,p=n.getInnerFormatter(),g=n.getDecimalFold(),m=n.getThousandsSeparator(),_=[];if(e.visible){var x=(i=(a=f[v])!==null&&a!==void 0?a:f[v-1])!==null&&i!==void 0?i:{},S=s.legend.defaultValue;fi(e,v,o,function(A,P){if(Kt(A.title)){var N=P.color,W=x[A.key];vt(W)&&(W=Me(W,e.precision),e.shouldFormatBigNumber&&(W=p.formatBigNumber(W)),W=g.format(m.format(W))),_.push({title:{text:A.title,color:N},value:{text:W??S,color:N}})}}),h.legends=_}if(Ie(e.createTooltipDataSource)){var y=this.getWidget(),k=y.getPane(),C=k.getChart(),D=e.createTooltipDataSource({chart:C,indicator:e,crosshair:n.getCrosshair(),bounding:y.getBounding(),xAxis:k.getChart().getXAxisPane().getAxisComponent(),yAxis:k.getAxisComponent()}),E=D.name,I=D.calcParamsText,T=D.legends,L=D.features;if(l.show&&(Kt(E)&&l.showName&&(h.name=E),Kt(I)&&l.showParams&&(h.calcParamsText=I)),R(L)&&(h.features=L),R(T)&&e.visible){var b=[],M=o.tooltip.legend.color;T.forEach(function(A){var P={text:"",color:M};He(A.title)?P=A.title:P.text=A.title;var N={text:"",color:M};He(A.value)?N=A.value:N.text=A.value,vt(Number(N.text))&&(N.text=g.format(m.format(N.text))),b.push({title:P,value:N})}),h.legends=b}}return h},r.prototype.classifyTooltipFeatures=function(e){var a=[],i=[],n=[];return e.forEach(function(o){switch(o.position){case"left":{a.push(o);break}case"middle":{i.push(o);break}case"right":{n.push(o);break}}}),[a,i,n]},r}(We),Mo=function(t){St(r,t);function r(e){var a=t.call(this,e)||this;return a._initEvent(),a}return r.prototype._initEvent=function(){var e=this,a=this.getWidget(),i=a.getPane(),n=i.getId(),o=i.getChart(),s=o.getChartStore();this.registerEvent("mouseMoveEvent",function(l){var c,u=s.getProgressOverlayInfo();if(u!==null){var d=u.overlay,h=u.paneId;d.isStart()&&(s.updateProgressOverlayInfo(n),h=n);var v=d.points.length-1;return d.isDrawing()&&h===n&&(d.eventMoveForDrawing(e._coordinateToPoint(d,l)),(c=d.onDrawing)===null||c===void 0||c.call(d,J({chart:o,overlay:d},l))),e._figureMouseMoveEvent(d,"point",v,{key:"".concat(Sr,"point_").concat(v),type:"circle",attrs:{}})(l)}return s.setHoverOverlayInfo({paneId:n,overlay:null,figureType:"none",figureIndex:-1,figure:null},function(f,p){return e._processOverlayMouseEnterEvent(f,p,l)},function(f,p){return e._processOverlayMouseLeaveEvent(f,p,l)}),a.setForceCursor(null),!1}).registerEvent("mouseClickEvent",function(l){var c,u,d=s.getProgressOverlayInfo();if(d!==null){var h=d.overlay,v=d.paneId;h.isStart()&&(s.updateProgressOverlayInfo(n,!0),v=n);var f=h.points.length-1;return h.isDrawing()&&v===n&&(h.eventMoveForDrawing(e._coordinateToPoint(h,l)),(c=h.onDrawing)===null||c===void 0||c.call(h,J({chart:o,overlay:h},l)),h.nextStep(),h.isDrawing()||(s.progressOverlayComplete(),(u=h.onDrawEnd)===null||u===void 0||u.call(h,J({chart:o,overlay:h},l)))),e._figureMouseClickEvent(h,"point",f,{key:"".concat(Sr,"point_").concat(f),type:"circle",attrs:{}})(l)}return s.setClickOverlayInfo({paneId:n,overlay:null,figureType:"none",figureIndex:-1,figure:null},function(p,g){return e._processOverlaySelectedEvent(p,g,l)},function(p,g){return e._processOverlayDeselectedEvent(p,g,l)}),!1}).registerEvent("mouseDoubleClickEvent",function(l){var c,u=s.getProgressOverlayInfo();if(u!==null){var d=u.overlay,h=u.paneId;d.isDrawing()&&h===n&&(d.forceComplete(),d.isDrawing()||(s.progressOverlayComplete(),(c=d.onDrawEnd)===null||c===void 0||c.call(d,J({chart:o,overlay:d},l))));var v=d.points.length-1;return e._figureMouseClickEvent(d,"point",v,{key:"".concat(Sr,"point_").concat(v),type:"circle",attrs:{}})(l)}return!1}).registerEvent("mouseRightClickEvent",function(l){var c=s.getProgressOverlayInfo();if(c!==null){var u=c.overlay;if(u.isDrawing()){var d=u.points.length-1;return e._figureMouseRightClickEvent(u,"point",d,{key:"".concat(Sr,"point_").concat(d),type:"circle",attrs:{}})(l)}}return!1}).registerEvent("mouseUpEvent",function(l){var c,u=s.getPressedOverlayInfo(),d=u.overlay,h=u.figure;return d!==null&&Ye("onPressedMoveEnd",h)&&((c=d.onPressedMoveEnd)===null||c===void 0||c.call(d,J({chart:o,overlay:d,figure:h??void 0},l))),s.setPressedOverlayInfo({paneId:n,overlay:null,figureType:"none",figureIndex:-1,figure:null}),!1}).registerEvent("pressedMouseMoveEvent",function(l){var c,u=s.getPressedOverlayInfo(),d=u.overlay,h=u.figureType,v=u.figureIndex,f=u.figure;if(d!==null&&Ye("onPressedMoving",f)){if(!d.lock){var p=e._coordinateToPoint(d,l);h==="point"?d.eventPressedPointMove(p,v):d.eventPressedOtherMove(p,e.getWidget().getPane().getChart().getChartStore());var g=!1;(c=d.onPressedMoving)===null||c===void 0||c.call(d,J(J({chart:o,overlay:d,figure:f??void 0},l),{preventDefault:function(){g=!0}})),g?e.getWidget().setForceCursor(null):e.getWidget().setForceCursor("pointer")}return!0}return e.getWidget().setForceCursor(null),!1})},r.prototype._createFigureEvents=function(e,a,i,n){return e.isDrawing()?null:{mouseMoveEvent:this._figureMouseMoveEvent(e,a,i,n),mouseDownEvent:this._figureMouseDownEvent(e,a,i,n),mouseClickEvent:this._figureMouseClickEvent(e,a,i,n),mouseRightClickEvent:this._figureMouseRightClickEvent(e,a,i,n),mouseDoubleClickEvent:this._figureMouseDoubleClickEvent(e,a,i,n)}},r.prototype._processOverlayMouseEnterEvent=function(e,a,i){return Ie(e.onMouseEnter)&&Ye("onMouseEnter",a)?(e.onMouseEnter(J({chart:this.getWidget().getPane().getChart(),overlay:e,figure:a??void 0},i)),!0):!1},r.prototype._processOverlayMouseLeaveEvent=function(e,a,i){return Ie(e.onMouseLeave)&&Ye("onMouseLeave",a)?(e.onMouseLeave(J({chart:this.getWidget().getPane().getChart(),overlay:e,figure:a??void 0},i)),!0):!1},r.prototype._processOverlaySelectedEvent=function(e,a,i){var n;return Ye("onSelected",a)?((n=e.onSelected)===null||n===void 0||n.call(e,J({chart:this.getWidget().getPane().getChart(),overlay:e,figure:a??void 0},i)),!0):!1},r.prototype._processOverlayDeselectedEvent=function(e,a,i){var n;return Ye("onDeselected",a)?((n=e.onDeselected)===null||n===void 0||n.call(e,J({chart:this.getWidget().getPane().getChart(),overlay:e,figure:a??void 0},i)),!0):!1},r.prototype._figureMouseMoveEvent=function(e,a,i,n){var o=this;return function(s){var l,c=o.getWidget().getPane(),u=!e.isDrawing()&&Ye("onMouseMove",n);if(u){var d=!1;(l=e.onMouseMove)===null||l===void 0||l.call(e,J(J({chart:c.getChart(),overlay:e,figure:n},s),{preventDefault:function(){d=!0}})),d?o.getWidget().setForceCursor(null):o.getWidget().setForceCursor("pointer")}return c.getChart().getChartStore().setHoverOverlayInfo({paneId:c.getId(),overlay:e,figureType:a,figure:n,figureIndex:i},function(h,v){return o._processOverlayMouseEnterEvent(h,v,s)},function(h,v){return o._processOverlayMouseLeaveEvent(h,v,s)}),u}},r.prototype._figureMouseDownEvent=function(e,a,i,n){var o=this;return function(s){var l,c=o.getWidget().getPane(),u=c.getId();return e.startPressedMove(o._coordinateToPoint(e,s)),Ye("onPressedMoveStart",n)?((l=e.onPressedMoveStart)===null||l===void 0||l.call(e,J({chart:c.getChart(),overlay:e,figure:n},s)),c.getChart().getChartStore().setPressedOverlayInfo({paneId:u,overlay:e,figureType:a,figureIndex:i,figure:n}),!e.isDrawing()):!1}},r.prototype._figureMouseClickEvent=function(e,a,i,n){var o=this;return function(s){var l,c=o.getWidget().getPane(),u=c.getId(),d=!e.isDrawing()&&Ye("onClick",n);return d&&((l=e.onClick)===null||l===void 0||l.call(e,J({chart:o.getWidget().getPane().getChart(),overlay:e,figure:n},s))),c.getChart().getChartStore().setClickOverlayInfo({paneId:u,overlay:e,figureType:a,figureIndex:i,figure:n},function(h,v){return o._processOverlaySelectedEvent(h,v,s)},function(h,v){return o._processOverlayDeselectedEvent(h,v,s)}),d}},r.prototype._figureMouseDoubleClickEvent=function(e,a,i,n){var o=this;return function(s){var l;return Ye("onDoubleClick",n)?((l=e.onDoubleClick)===null||l===void 0||l.call(e,J(J({},s),{chart:o.getWidget().getPane().getChart(),figure:n,overlay:e})),!e.isDrawing()):!1}},r.prototype._figureMouseRightClickEvent=function(e,a,i,n){var o=this;return function(s){var l;if(Ye("onRightClick",n)){var c=!1;return(l=e.onRightClick)===null||l===void 0||l.call(e,J(J({chart:o.getWidget().getPane().getChart(),overlay:e,figure:n},s),{preventDefault:function(){c=!0}})),c||o.getWidget().getPane().getChart().getChartStore().removeOverlay(e),!e.isDrawing()}return!1}},r.prototype._coordinateToPoint=function(e,a){var i,n={},o=this.getWidget().getPane(),s=o.getChart(),l=o.getId(),c=s.getChartStore();if(this.coordinateToPointTimestampDataIndexFlag()){var u=s.getXAxisPane().getAxisComponent(),d=u.convertFromPixel(a.x),h=(i=c.dataIndexToTimestamp(d))!==null&&i!==void 0?i:void 0;n.timestamp=h,n.dataIndex=d}if(this.coordinateToPointValueFlag()){var v=o.getAxisComponent(),f=v.convertFromPixel(a.y);if(e.mode!=="normal"&&l===xt.CANDLE&&vt(n.dataIndex)){var p=c.getDataByDataIndex(n.dataIndex);if(p!==null){var g=e.modeSensitivity;if(f>p.high)if(e.mode==="weak_magnet"){var m=v.convertToPixel(p.high),_=v.convertFromPixel(m-g);f<_&&(f=p.high)}else f=p.high;else if(f<p.low)if(e.mode==="weak_magnet"){var x=v.convertToPixel(p.low),_=v.convertFromPixel(x-g);f>_&&(f=p.low)}else f=p.low;else{var S=Math.max(p.open,p.close),y=Math.min(p.open,p.close);f>S?f-S<p.high-f?f=S:f=p.high:f<y?f-p.low<y-f?f=p.low:f=y:S-f<f-y?f=S:f=y}}}n.value=f}return n},r.prototype.coordinateToPointValueFlag=function(){return!0},r.prototype.coordinateToPointTimestampDataIndexFlag=function(){return!0},r.prototype.dispatchEvent=function(e,a){return this.getWidget().getPane().getChart().getChartStore().isOverlayDrawing()?this.onEvent(e,a):t.prototype.dispatchEvent.call(this,e,a)},r.prototype.drawImp=function(e){var a=this,i=this.getCompleteOverlays();i.forEach(function(o){o.visible&&a._drawOverlay(e,o)});var n=this.getProgressOverlay();R(n)&&n.visible&&this._drawOverlay(e,n)},r.prototype._drawOverlay=function(e,a){var i=a.points,n=this.getWidget().getPane(),o=n.getChart(),s=o.getChartStore(),l=n.getAxisComponent(),c=o.getXAxisPane().getAxisComponent(),u=i.map(function(h){var v,f=null;vt(h.timestamp)&&(f=s.timestampToDataIndex(h.timestamp));var p={x:0,y:0};return vt(f)&&(p.x=c.convertToPixel(f)),vt(h.value)&&(p.y=(v=l==null?void 0:l.convertToPixel(h.value))!==null&&v!==void 0?v:0),p});if(u.length>0){var d=[].concat(this.getFigures(a,u));this.drawFigures(e,a,d)}this.drawDefaultFigures(e,a,u)},r.prototype.drawFigures=function(e,a,i){var n=this,o=this.getWidget().getPane().getChart().getStyles().overlay;i.forEach(function(s,l){var c=s.type,u=s.styles,d=s.attrs,h=[].concat(d);h.forEach(function(v){var f,p,g=n._createFigureEvents(a,"other",l,s),m=J(J(J({},o[c]),(f=a.styles)===null||f===void 0?void 0:f[c]),u);(p=n.createFigure({name:c,attrs:v,styles:m},g??void 0))===null||p===void 0||p.draw(e)})})},r.prototype.getCompleteOverlays=function(){var e=this.getWidget().getPane();return e.getChart().getChartStore().getOverlaysByPaneId(e.getId())},r.prototype.getProgressOverlay=function(){var e=this.getWidget().getPane(),a=e.getChart().getChartStore().getProgressOverlayInfo();return R(a)&&a.paneId===e.getId()?a.overlay:null},r.prototype.getFigures=function(e,a){var i,n,o=this.getWidget(),s=o.getPane(),l=s.getChart(),c=s.getAxisComponent(),u=l.getXAxisPane().getAxisComponent(),d=o.getBounding();return(n=(i=e.createPointFigures)===null||i===void 0?void 0:i.call(e,{chart:l,overlay:e,coordinates:a,bounding:d,xAxis:u,yAxis:c}))!==null&&n!==void 0?n:[]},r.prototype.drawDefaultFigures=function(e,a,i){var n=this,o,s;if(a.needDefaultPointFigure){var l=this.getWidget().getPane().getChart().getChartStore(),c=l.getHoverOverlayInfo(),u=l.getClickOverlayInfo();if(((o=c.overlay)===null||o===void 0?void 0:o.id)===a.id&&c.figureType!=="none"||((s=u.overlay)===null||s===void 0?void 0:s.id)===a.id&&u.figureType!=="none"){var d=l.getStyles().overlay,h=a.styles,v=J(J({},d.point),h==null?void 0:h.point);i.forEach(function(f,p){var g,m,_,x,S,y=f.x,k=f.y,C=v.radius,D=v.color,E=v.borderColor,I=v.borderSize;((g=c.overlay)===null||g===void 0?void 0:g.id)===a.id&&c.figureType==="point"&&((m=c.figure)===null||m===void 0?void 0:m.key)==="".concat(Sr,"point_").concat(p)&&(C=v.activeRadius,D=v.activeColor,E=v.activeBorderColor,I=v.activeBorderSize),(x=n.createFigure({name:"circle",attrs:{x:y,y:k,r:C+I},styles:{color:E}},(_=n._createFigureEvents(a,"point",p,{key:"".concat(Sr,"point_").concat(p),type:"circle",attrs:{x:y,y:k,r:C+I},styles:{color:E}}))!==null&&_!==void 0?_:void 0))===null||x===void 0||x.draw(e),(S=n.createFigure({name:"circle",attrs:{x:y,y:k,r:C},styles:{color:D}}))===null||S===void 0||S.draw(e)})}}},r}(We),To=function(t){St(r,t);function r(e,a){var i=t.call(this,e,a)||this;return i._gridView=new Z1(i),i._indicatorView=new H1(i),i._crosshairLineView=new j1(i),i._tooltipView=i.createTooltipView(),i._overlayView=new Mo(i),i.addChild(i._tooltipView),i.addChild(i._overlayView),i}return r.prototype.getName=function(){return Nt.MAIN},r.prototype.updateMain=function(e){this.getPane().getOptions().state!=="minimize"&&(this.updateMainContent(e),this._indicatorView.draw(e),this._gridView.draw(e))},r.prototype.createTooltipView=function(){return new ko(this)},r.prototype.updateMainContent=function(e){},r.prototype.updateOverlayContent=function(e){},r.prototype.updateOverlay=function(e){this.getPane().getOptions().state!=="minimize"&&(this._overlayView.draw(e),this._crosshairLineView.draw(e),this.updateOverlayContent(e)),this._tooltipView.draw(e)},r}(_i),X1=function(t){St(r,t);function r(){var e=t.apply(this,hr([],nr(arguments),!1))||this;return e._ripplePoint=e.createFigure({name:"circle",attrs:{x:0,y:0,r:0},styles:{style:"fill"}}),e._animationFrameTime=0,e._animation=new Ja({iterationCount:1/0}).doFrame(function(a){e._animationFrameTime=a;var i=e.getWidget().getPane();i.getChart().updatePane(0,i.getId())}),e}return r.prototype.drawImp=function(e){var a,i,n,o=this.getWidget(),s=o.getPane(),l=s.getChart(),c=l.getDataList(),u=c.length-1,d=o.getBounding(),h=s.getAxisComponent(),v=l.getStyles().candle.area,f=[],p=Number.MAX_SAFE_INTEGER,g=Number.MIN_SAFE_INTEGER,m=null;if(this.eachChildren(function(C){var D=C.x,E=C.data.current,I=E==null?void 0:E[v.value];if(vt(I)){var T=h.convertToPixel(I);g===Number.MIN_SAFE_INTEGER&&(g=D),f.push({x:D,y:T}),p=Math.min(p,T),C.dataIndex===u&&(m={x:D,y:T})}}),f.length>0){(a=this.createFigure({name:"line",attrs:{coordinates:f},styles:{color:v.lineColor,size:v.lineSize,smooth:v.smooth}}))===null||a===void 0||a.draw(e);var _=v.backgroundColor,x="";if(Ue(_)){var S=e.createLinearGradient(0,d.height,0,p);try{_.forEach(function(C){var D=C.offset,E=C.color;S.addColorStop(D,E)})}catch{}x=S}else x=_;e.fillStyle=x,e.beginPath(),e.moveTo(g,d.height),e.lineTo(f[0].x,f[0].y),po(e,f,v.smooth),e.lineTo(f[f.length-1].x,d.height),e.closePath(),e.fill()}var y=v.point;if(y.show&&R(m)){(i=this.createFigure({name:"circle",attrs:{x:m.x,y:m.y,r:y.radius},styles:{style:"fill",color:y.color}}))===null||i===void 0||i.draw(e);var k=y.rippleRadius;y.animation&&(k=y.radius+this._animationFrameTime/y.animationDuration*(y.rippleRadius-y.radius),this._animation.setDuration(y.animationDuration).start()),(n=this._ripplePoint)===null||n===void 0||n.setAttrs({x:m.x,y:m.y,r:k}).setStyles({style:"fill",color:y.rippleColor}).draw(e)}else this.stopAnimation()},r.prototype.stopAnimation=function(){this._animation.stop()},r}(bi),G1=function(t){St(r,t);function r(){return t!==null&&t.apply(this,arguments)||this}return r.prototype.drawImp=function(e){var a,i,n=this.getWidget(),o=n.getPane(),s=o.getChart().getChartStore(),l=s.getStyles().candle.priceMark,c=l.high,u=l.low;if(l.show&&(c.show||u.show)){var d=s.getVisibleRangeHighLowPrice(),h=(i=(a=s.getSymbol())===null||a===void 0?void 0:a.pricePrecision)!==null&&i!==void 0?i:2,v=o.getAxisComponent(),f=d[0],p=f.price,g=f.x,m=d[1],_=m.price,x=m.x,S=v.convertToPixel(p),y=v.convertToPixel(_),k=s.getDecimalFold(),C=s.getThousandsSeparator();c.show&&p!==Number.MIN_SAFE_INTEGER&&this._drawMark(e,k.format(C.format(Me(p,h))),{x:g,y:S},S<y?[-2,-5]:[2,5],c),u.show&&_!==Number.MAX_SAFE_INTEGER&&this._drawMark(e,k.format(C.format(Me(_,h))),{x,y},S<y?[2,5]:[-2,-5],u)}},r.prototype._drawMark=function(e,a,i,n,o){var s,l,c,u=i.x,d=i.y+n[0];(s=this.createFigure({name:"line",attrs:{coordinates:[{x:u-2,y:d+n[0]},{x:u,y:d},{x:u+2,y:d+n[0]}]},styles:{color:o.color}}))===null||s===void 0||s.draw(e);var h=0,v=0,f="left",p=this.getWidget().getBounding().width;u>p/2?(h=u-5,v=h-o.textOffset,f="right"):(h=u+5,f="left",v=h+o.textOffset);var g=d+n[1];(l=this.createFigure({name:"line",attrs:{coordinates:[{x:u,y:d},{x:u,y:g},{x:h,y:g}]},styles:{color:o.color}}))===null||l===void 0||l.draw(e),(c=this.createFigure({name:"text",attrs:{x:v,y:g,text:a,align:f,baseline:"middle"},styles:{color:o.color,size:o.textSize,family:o.textFamily,weight:o.textWeight}}))===null||c===void 0||c.draw(e)},r}(We),K1=function(t){St(r,t);function r(){return t!==null&&t.apply(this,arguments)||this}return r.prototype.drawImp=function(e){var a,i,n,o=this.getWidget(),s=o.getPane(),l=o.getBounding(),c=s.getChart().getChartStore(),u=c.getStyles().candle.priceMark,d=u.last,h=d.line;if(u.show&&d.show&&h.show){var v=s.getAxisComponent(),f=c.getDataList(),p=f[f.length-1];if(R(p)){var g=p.close,m=p.open,_=d.compareRule==="current_open"?m:(i=(a=f[f.length-2])===null||a===void 0?void 0:a.close)!==null&&i!==void 0?i:g,x=v.convertToNicePixel(g),S="";g>_?S=d.upColor:g<_?S=d.downColor:S=d.noChangeColor,(n=this.createFigure({name:"line",attrs:{coordinates:[{x:0,y:x},{x:l.width,y:x}]},styles:{style:h.style,color:S,size:h.size,dashedValue:h.dashedValue}}))===null||n===void 0||n.draw(e)}}},r}(We),U1=function(t){St(r,t);function r(){var e=t.apply(this,hr([],nr(arguments),!1))||this;return e._orderFlowDataList=[],e._expandedBars=new Set,e._momentumLines=[],e._boundOrderFlowClickEvent=function(a,i){return function(){var n=e.getWidget().getPane(),o=n.getChart().getChartStore();return o.executeAction("onCandleBarClick",J(J({},a),{orderFlowPriceLevel:i})),!1}},e._boundExpandClickEvent=function(a){return function(){return e._expandedBars.has(a)?e._expandedBars.delete(a):e._expandedBars.add(a),!1}},e}return r.prototype.setOrderFlowData=function(e){this._orderFlowDataList=e,this._updateMomentumLines()},r.prototype.getOrderFlowData=function(){return this._orderFlowDataList},r.prototype._calculateKlineDelta=function(e){return e.priceLevels.reduce(function(a,i){return a+(i.buyVolume-i.sellVolume)},0)},r.prototype._checkPriceDirection=function(e,a,i){var n=e.close,o=a.close,s=i.close;return o>n&&s>o?"up":o<n&&s<o?"down":"mixed"},r.prototype._updateMomentumLines=function(){var e=this;if(this._orderFlowDataList.length<3){this._momentumLines=[];return}for(var a=function(o){var s=i._orderFlowDataList[o],l=i._orderFlowDataList[o-1],c=i._orderFlowDataList[o-2],u=i._calculateKlineDelta(c),d=i._calculateKlineDelta(l),h=i._calculateKlineDelta(s),v=i._checkPriceDirection(c,l,s),f=!1,p=!1;if(u>0&&d>0&&h>0&&v==="up"?(f=!0,p=!0):u<0&&d<0&&h<0&&v==="down"&&(f=!0,p=!1),f){var g=i._momentumLines.find(function(_){return _.startTimestamp===s.timestamp});if(g===void 0){var m=s.open;i._momentumLines.push({startIndex:o,startTimestamp:s.timestamp,startPrice:s.close,linePrice:m,isPositive:p,isActive:!0,totalDelta:u+d+h})}}},i=this,n=2;n<this._orderFlowDataList.length;n++)a(n);this._momentumLines.forEach(function(o){if(o.isActive){var s=e._orderFlowDataList.findIndex(function(u){return u.timestamp===o.startTimestamp});if(s!==-1)for(var l=s+1;l<e._orderFlowDataList.length;l++){var c=e._orderFlowDataList[l];if(c.close<o.startPrice){o.isActive=!1,o.endIndex=l,o.endTimestamp=c.timestamp;break}}}})},r.prototype.drawImp=function(e){var a=this,i=this.getWidget().getPane(),n=this.getOrderFlowOptions();if(!(n===null||!n.enabled)&&this._orderFlowDataList.length!==0){var o=n.styles,s=i.getAxisComponent(),l=i.getId()===xt.CANDLE;e.save(),this._drawMomentumLines(e,s),this.eachChildren(function(c,u){var d,h=c.x,v=c.data.current;if(R(v)){var f=a._getOrderFlowByTimestamp(v.timestamp);if(f!==null){var p=s.convertToPixel(v.high),g=s.convertToPixel(v.low),m=Math.max(1,u.bar),_=m<=30?m*.8:m*.9,x=_/2,S=a._expandedBars.has(v.timestamp),y=f.priceLevels.filter(function(O){return O.totalVolume>0}),k=50,C=S?y:y.slice(0,k);if(C.length!==0){var D=Math.max(C.length,3),E=(g-p)/D*.95,I=Math.max(15,Math.min(60,E)),T=m>5&&I>10&&m>88;try{var L=new CustomEvent("orderFlowDebug",{detail:{barWidth:m.toFixed(1),levelHeight:I.toFixed(1),barSpace:u.bar.toFixed(1),shouldShowText:T}});window.dispatchEvent(L)}catch{}var b=8,M=Math.max(.6,Math.min(1.4,m/20)),A=Math.round(b*M),P=Math.max(8,Math.round(A*.8)),N=I*C.length,W=p,X=W+N,$=y.length>k,ct=$?15:0,at=N+ct;if(a._drawOrderFlowBorder(e,h,W,_,at),C.forEach(function(O,q){var dt=W+(q+.5)*I,tt=a._createTwoColumnOrderFlow(h,dt,O,x,I,o,_,C,y,S,T,A);tt.forEach(function(mt){var Lt,Ht=null;l&&(Ht={mouseClickEvent:a._boundOrderFlowClickEvent(c,O)}),(Lt=a.createFigure(mt,Ht??void 0))===null||Lt===void 0||Lt.draw(e)})}),$&&T){var gt=X+8,Tt=S?"收起":"+".concat(y.length-k,"更多"),Wt={x:h,y:gt,text:Tt,align:"center",baseline:"top"},V={name:"text",attrs:Wt,styles:{color:"#888888",size:P,weight:"normal",family:"Arial"}},G=null;l&&(G={mouseClickEvent:a._boundExpandClickEvent(v.timestamp)}),(d=a.createFigure(V,G??void 0))===null||d===void 0||d.draw(e)}_>0&&a._drawSimpleOHLC(e,h,_,v,s)}}}}),e.restore()}},r.prototype.getOrderFlowOptions=function(){return{enabled:!0,styles:{buyVolume:{color:"rgba(0, 150, 0, 0.8)",textColor:"#ffffff"},sellVolume:{color:"rgba(220, 20, 60, 0.8)",textColor:"#ffffff"},dominantVolume:{buyColor:"rgba(0, 200, 0, 0.9)",sellColor:"rgba(255, 20, 20, 0.9)",textColor:"#ffffff"},text:{size:12,weight:"normal",family:"Arial"},background:{color:"rgba(128, 128, 128, 0.1)",opacity:.2},showVolumeNumbers:!0,showImbalance:!0,imbalanceThreshold:70}}},r.prototype._getOrderFlowByTimestamp=function(e){var a;return(a=this._orderFlowDataList.find(function(i){return i.timestamp===e}))!==null&&a!==void 0?a:null},r.prototype._formatVolume=function(e){return e>=1e6?"".concat((e/1e6).toFixed(1),"M"):e>=1e3?"".concat((e/1e3).toFixed(1),"K"):e.toString()},r.prototype._drawOrderFlowBorder=function(e,a,i,n,o){e.save(),e.setLineDash([]),e.strokeStyle="rgba(200, 200, 200, 0.8)",e.lineWidth=1;var s=a-n/2;e.strokeRect(s,i,n,o),e.strokeStyle="rgba(160, 160, 160, 0.7)";var l=s+n/2;e.beginPath(),e.moveTo(l,i),e.lineTo(l,i+o),e.stroke(),e.restore()},r.prototype._createTwoColumnOrderFlow=function(e,a,i,n,o,s,l,c,u,d,h,v){var f=[],p=d?u:c,g=Math.max.apply(Math,hr([],nr(p.map(function(at){return at.totalVolume})),!1)),m=Math.max.apply(Math,hr([],nr(p.map(function(at){return Math.abs(at.buyVolume-at.sellVolume)})),!1));if(g===0||i.totalVolume===0)return f;var _=i.buyVolume-i.sellVolume,x=m>0?Math.abs(_)/m:0,S=i.totalVolume/g,y=e-l/2,k=y,C=_>0;if(Math.abs(_)>0&&x>0){var D=this._getDeltaGradientColor(x,C),E=Math.max(1,n*.95*x),I={x:k+1,y:a-o/2+1,width:E,height:o-2};f.push({name:"rect",attrs:I,styles:{style:"fill",color:D}})}if(h){var T="rgba(220, 220, 220, 0.9)",L=_===0?"0":_>0?"+".concat(this._formatVolume(Math.abs(_))):"-".concat(this._formatVolume(Math.abs(_))),b={x:k+n/2,y:a,text:L,align:"center",baseline:"middle"};f.push({name:"text",attrs:b,styles:{color:T,size:v,weight:s.text.weight,family:s.text.family}})}var M=y+n,A=S>0?Math.max(2,n*.95*S):0;if(A>0){var P=this._getVolumeGradientColor(S),N={x:M+1,y:a-o/2+1,width:A,height:o-2};f.push({name:"rect",attrs:N,styles:{style:"fill",color:P}})}if(h){var W=this._formatVolume(i.buyVolume),X=this._formatVolume(i.sellVolume),$="".concat(W," ").concat(X),ct={x:M+n/2,y:a,text:$,align:"center",baseline:"middle"};f.push({name:"text",attrs:ct,styles:{color:"rgba(200, 200, 200, 0.85)",size:v,weight:s.text.weight,family:s.text.family}})}return f},r.prototype._getDeltaGradientColor=function(e,a){var i=Math.sqrt(e),n=Math.min(.8,Math.max(.4,i*.6+.2));return a?"rgba(220, 100, 100, ".concat(n,")"):"rgba(100, 180, 100, ".concat(n,")")},r.prototype._getVolumeGradientColor=function(e){var a=Math.sqrt(e),i=Math.min(.9,Math.max(.4,a*.6+.3));return"rgba(100, 150, 200, ".concat(i,")")},r.prototype._drawSimpleOHLC=function(e,a,i,n,o){var s=o.convertToPixel(n.open),l=o.convertToPixel(n.close),c=o.convertToPixel(n.high),u=o.convertToPixel(n.low),d=n.close>=n.open,h=d?"#15803d":"#b91c1c",v=a-i/2-5;e.save(),e.setLineDash([]),e.strokeStyle=h,e.lineWidth=2,e.beginPath(),e.moveTo(v,c),e.lineTo(v,u),e.stroke(),Math.abs(s-l)>=1&&(e.lineWidth=8,e.beginPath(),e.moveTo(v,s),e.lineTo(v,l),e.stroke()),e.restore()},r.prototype.getOrderFlowDrawingBounds=function(){return this._orderFlowDataList.length===0?null:{topY:0,bottomY:1e3}},r.prototype._drawMomentumLines=function(e,a){var i=this;if(this._momentumLines.length!==0){e.save();var n=new Map,o=Number.MAX_SAFE_INTEGER,s=Number.MIN_SAFE_INTEGER;if(this.eachChildren(function(c,u){var d=c.x,h=c.data.current;h!=null&&(n.set(h.timestamp,d),o=Math.min(o,d),s=Math.max(s,d))}),o===Number.MAX_SAFE_INTEGER){e.restore();return}var l=Math.max.apply(Math,hr([],nr(this._momentumLines.map(function(c){return Math.abs(c.totalDelta)})),!1));this._momentumLines.forEach(function(c){var u=n.get(c.startTimestamp);if(u!==void 0){var d=i._orderFlowDataList.find(function(D){return D.timestamp===c.startTimestamp});if(d!==void 0){var h=a.convertToPixel(d.open),v=a.convertToPixel(d.close),f=Math.abs(v-h),p=Math.min(h,v),g=u+200;if(c.isActive)g=s+50;else if(c.endTimestamp!==void 0){var m=n.get(c.endTimestamp);g=m??u+200}if(!(g<o||u>s+50)){var _=Math.max(u,o-10),x=Math.min(g,s+50),S=x-_,y=l>0?Math.abs(c.totalDelta)/l:.5,k=Math.max(.1,Math.min(.4,y*.3+.1)),C=Math.max(.3,Math.min(.7,y*.4+.3));c.isPositive?(e.fillStyle="rgba(255, 99, 99, ".concat(k,")"),e.strokeStyle="rgba(255, 99, 99, ".concat(C,")")):(e.fillStyle="rgba(76, 175, 80, ".concat(k,")"),e.strokeStyle="rgba(76, 175, 80, ".concat(C,")")),e.fillRect(_,p,S,f),e.lineWidth=1,e.setLineDash([]),e.strokeRect(_,p,S,f),e.fillStyle=c.isPositive?"rgba(255, 99, 99, ".concat(C,")"):"rgba(76, 175, 80, ".concat(C,")"),e.fillRect(u-1,p-2,2,f+4)}}}}),e.restore()}},r}(bi),Q1={second:"HH:mm:ss",minute:"HH:mm",hour:"MM-DD HH:mm",day:"YYYY-MM-DD",week:"YYYY-MM-DD",month:"YYYY-MM",year:"YYYY"},Eo={second:"HH:mm:ss",minute:"YYYY-MM-DD HH:mm",hour:"YYYY-MM-DD HH:mm",day:"YYYY-MM-DD",week:"YYYY-MM-DD",month:"YYYY-MM",year:"YYYY"},q1={time:"时间：",open:"开：",high:"高：",low:"低：",close:"收：",volume:"成交量：",turnover:"成交额：",change:"涨幅：",second:"秒",minute:"",hour:"小时",day:"天",week:"周",month:"月",year:"年"},J1={time:"Time: ",open:"Open: ",high:"High: ",low:"Low: ",close:"Close: ",volume:"Volume: ",turnover:"Turnover: ",change:"Change: ",second:"S",minute:"",hour:"H",day:"D",week:"W",month:"M",year:"Y"},t0={"zh-CN":q1,"en-US":J1};function rn(t,r){var e;return(e=t0[r][t])!==null&&e!==void 0?e:t}var e0=function(t){St(r,t);function r(){return t!==null&&t.apply(this,arguments)||this}return r.prototype.drawImp=function(e){var a=this.getWidget(),i=a.getPane().getChart().getChartStore(),n=i.getCrosshair();if(R(n.kLineData)){var o=a.getBounding(),s=i.getStyles(),l=s.candle,c=s.indicator;if(l.tooltip.showType==="rect"&&c.tooltip.showType==="rect"){var u=this.isDrawTooltip(n,l.tooltip),d=this.isDrawTooltip(n,c.tooltip);this._drawRectTooltip(e,u,d,l.tooltip.offsetTop)}else if(l.tooltip.showType==="standard"&&c.tooltip.showType==="standard"){var h=l.tooltip,v=h.offsetLeft,f=h.offsetTop,p=h.offsetRight,g=o.width-p,m=this._drawCandleStandardTooltip(e,v,f,g);this.drawIndicatorTooltip(e,v,m,g)}else if(l.tooltip.showType==="rect"&&c.tooltip.showType==="standard"){var _=l.tooltip,v=_.offsetLeft,f=_.offsetTop,p=_.offsetRight,g=o.width-p,x=this.drawIndicatorTooltip(e,v,f,g),u=this.isDrawTooltip(n,l.tooltip);this._drawRectTooltip(e,u,!1,x)}else{var S=l.tooltip,v=S.offsetLeft,f=S.offsetTop,p=S.offsetRight,g=o.width-p,y=this._drawCandleStandardTooltip(e,v,f,g),d=this.isDrawTooltip(n,c.tooltip);this._drawRectTooltip(e,!1,d,y)}}},r.prototype._drawCandleStandardTooltip=function(e,a,i,n){var o,s=this.getWidget().getPane().getChart().getChartStore(),l=s.getStyles().candle,c=l.tooltip,u=c.legend,d=0,h={x:a,y:i},v=s.getCrosshair();if(this.isDrawTooltip(v,c)){var f=c.title;if(f.show){var p=(o=s.getPeriod())!==null&&o!==void 0?o:{},g=p.type,m=g===void 0?"":g,_=p.span,x=_===void 0?"":_,S=Yi(f.template,J(J({},s.getSymbol()),{period:"".concat(x).concat(rn(m,s.getLocale()))})),y=f.color,k=this.drawStandardTooltipLegends(e,[{title:{text:"",color:y},value:{text:S,color:y}}],{x:a,y:i},a,0,n,f);h.y=h.y+k}var C=this._getCandleTooltipLegends(),D=this.classifyTooltipFeatures(c.features);d=this.drawStandardTooltipFeatures(e,D[0],h,null,a,d,n),d=this.drawStandardTooltipFeatures(e,D[1],h,null,a,d,n),C.length>0&&(d=this.drawStandardTooltipLegends(e,C,h,a,d,n,u)),d=this.drawStandardTooltipFeatures(e,D[2],h,null,a,d,n)}return h.y+d},r.prototype._drawRectTooltip=function(e,a,i,n){var o=this,s,l,c=this.getWidget(),u=c.getPane(),d=u.getChart().getChartStore(),h=d.getStyles(),v=h.candle,f=h.indicator,p=v.tooltip,g=f.tooltip;if(a||i){var m=this._getCandleTooltipLegends(),_=p.offsetLeft,x=p.offsetTop,S=p.offsetRight,y=p.offsetBottom,k=p.legend,C=k.marginLeft,D=k.marginRight,E=k.marginTop,I=k.marginBottom,T=k.size,L=k.weight,b=k.family,M=p.rect,A=M.position,P=M.paddingLeft,N=M.paddingRight,W=M.paddingTop,X=M.paddingBottom,$=M.offsetLeft,ct=M.offsetRight,at=M.offsetTop,gt=M.offsetBottom,Tt=M.borderSize,Wt=M.borderRadius,V=M.borderColor,G=M.color,O=0,q=0,dt=0;a&&(e.font=br(T,L,b),m.forEach(function(he){var ae=he.title,oe=he.value,fe="".concat(ae.text).concat(oe.text),Se=e.measureText(fe).width+C+D;O=Math.max(O,Se)}),dt+=(I+E+T)*m.length);var tt=g.legend,mt=tt.marginLeft,Lt=tt.marginRight,Ht=tt.marginTop,jt=tt.marginBottom,It=tt.size,Xt=tt.weight,ee=tt.family,me=[];if(i){var de=d.getIndicatorsByPaneId(u.getId());e.font=br(It,Xt,ee),de.forEach(function(he){var ae=o.getIndicatorTooltipData(he).legends;me.push(ae),ae.forEach(function(oe){var fe=oe.title,Se=oe.value,er="".concat(fe.text).concat(Se.text),Lr=e.measureText(er).width+mt+Lt;O=Math.max(O,Lr),dt+=Ht+jt+It})})}if(q+=O,q!==0&&dt!==0){var xe=d.getCrosshair(),ue=c.getBounding(),et=u.getYAxisWidget().getBounding();q+=Tt*2+P+N,dt+=Tt*2+W+X;var ut=ue.width/2,Dt=A==="pointer"&&xe.paneId===xt.CANDLE,Ot=((s=xe.realX)!==null&&s!==void 0?s:0)>ut,yt=0;if(Dt){var ce=xe.realX;Ot?yt=ce-ct-q:yt=ce+$}else{var we=this.getWidget().getPane().getAxisComponent();Ot?(yt=$+_,we.inside&&we.position==="left"&&(yt+=et.width)):(yt=ue.width-ct-q-S,we.inside&&we.position==="right"&&(yt-=et.width))}var Zt=n+at;if(Dt){var Qt=xe.y;Zt=Qt-dt/2,Zt+dt>ue.height-gt-y&&(Zt=ue.height-gt-dt-y),Zt<n+at&&(Zt=n+at+x)}(l=this.createFigure({name:"rect",attrs:{x:yt,y:Zt,width:q,height:dt},styles:{style:"stroke_fill",color:G,borderColor:V,borderSize:Tt,borderRadius:Wt}}))===null||l===void 0||l.draw(e);var qt=yt+Tt+P+C,Jt=Zt+Tt+W;if(a&&m.forEach(function(he){var ae,oe;Jt+=E;var fe=he.title;(ae=o.createFigure({name:"text",attrs:{x:qt,y:Jt,text:fe.text},styles:{color:fe.color,size:T,family:b,weight:L}}))===null||ae===void 0||ae.draw(e);var Se=he.value;(oe=o.createFigure({name:"text",attrs:{x:yt+q-Tt-D-N,y:Jt,text:Se.text,align:"right"},styles:{color:Se.color,size:T,family:b,weight:L}}))===null||oe===void 0||oe.draw(e),Jt+=T+I}),i){var De=yt+Tt+P+mt;me.forEach(function(he){he.forEach(function(ae){var oe,fe;Jt+=Ht;var Se=ae.title,er=ae.value;(oe=o.createFigure({name:"text",attrs:{x:De,y:Jt,text:Se.text},styles:{color:Se.color,size:It,family:ee,weight:Xt}}))===null||oe===void 0||oe.draw(e),(fe=o.createFigure({name:"text",attrs:{x:yt+q-Tt-Lt-N,y:Jt,text:er.text,align:"right"},styles:{color:er.color,size:It,family:ee,weight:Xt}}))===null||fe===void 0||fe.draw(e),Jt+=It+jt})})}}}},r.prototype._getCandleTooltipLegends=function(){var e,a,i,n,o,s,l,c,u=this.getWidget().getPane().getChart().getChartStore(),d=u.getStyles().candle,h=u.getDataList(),v=u.getInnerFormatter(),f=u.getDecimalFold(),p=u.getThousandsSeparator(),g=u.getLocale(),m=(e=u.getSymbol())!==null&&e!==void 0?e:{},_=m.pricePrecision,x=_===void 0?2:_,S=m.volumePrecision,y=S===void 0?0:S,k=u.getPeriod(),C=(a=u.getCrosshair().dataIndex)!==null&&a!==void 0?a:0,D=d.tooltip,E=D.legend,I=E.color,T=E.defaultValue,L=E.custom,b=(i=h[C-1])!==null&&i!==void 0?i:null,M=h[C],A=(n=b==null?void 0:b.close)!==null&&n!==void 0?n:M.close,P=M.close-A,N=J(J({},M),{time:v.formatDate(M.timestamp,Eo[(o=k==null?void 0:k.type)!==null&&o!==void 0?o:"day"],"tooltip"),open:f.format(p.format(Me(M.open,x))),high:f.format(p.format(Me(M.high,x))),low:f.format(p.format(Me(M.low,x))),close:f.format(p.format(Me(M.close,x))),volume:f.format(p.format(v.formatBigNumber(Me((s=M.volume)!==null&&s!==void 0?s:T,y)))),turnover:f.format(p.format(Me((l=M.turnover)!==null&&l!==void 0?l:T,x))),change:A===0?T:"".concat(p.format(Me(P/A*100)),"%")}),W=Ie(L)?L({prev:b,current:M,next:(c=h[C+1])!==null&&c!==void 0?c:null},d):L;return W.map(function(X){var $=X.title,ct=X.value,at={text:"",color:I};He($)?at=J({},$):at.text=$,at.text=rn(at.text,g);var gt={text:T,color:I};return He(ct)?gt=J({},ct):gt.text=ct,R(/{change}/.exec(gt.text))&&(gt.color=P===0?d.priceMark.last.noChangeColor:P>0?d.priceMark.last.upColor:d.priceMark.last.downColor),gt.text=Yi(gt.text,N),{title:at,value:gt}})},r}(ko),r0=function(t){St(r,t);function r(e){var a=t.call(this,e)||this;return a._activeFeatureInfo=null,a._featureClickEvent=function(i){return function(){var n=a.getWidget().getPane();return n.getChart().getChartStore().executeAction("onCrosshairFeatureClick",i),!0}},a._featureMouseMoveEvent=function(i){return function(){return a._activeFeatureInfo=i,a.getWidget().setForceCursor("pointer"),!0}},a.registerEvent("mouseMoveEvent",function(i){return a._activeFeatureInfo=null,a.getWidget().setForceCursor(null),!1}),a}return r.prototype.drawImp=function(e){var a=this,i,n,o=this.getWidget(),s=o.getPane(),l=o.getPane().getChart().getChartStore(),c=l.getCrosshair(),u=this.getWidget(),d=u.getPane().getAxisComponent();if(Kt(c.paneId)&&c.paneId===s.getId()&&d.isInCandle()){var h=l.getStyles().crosshair,v=h.horizontal.features;if(h.show&&h.horizontal.show&&v.length>0){var f=d.position==="right",p=u.getBounding(),g=0,m=h.horizontal.text;if(d.inside&&m.show){var _=d.convertFromPixel(c.y),x=d.getRange(),S=d.displayValueToText(d.realValueToDisplayValue(d.valueToRealValue(_,{range:x}),{range:x}),(n=(i=l.getSymbol())===null||i===void 0?void 0:i.pricePrecision)!==null&&n!==void 0?n:2);S=l.getDecimalFold().format(l.getThousandsSeparator().format(S)),g=m.paddingLeft+je(S,m.size,m.weight,m.family)+m.paddingRight}var y=g;f&&(y=p.width-g);var k=c.y;v.forEach(function(C){var D,E,I,T,L=C.marginLeft,b=L===void 0?0:L,M=C.marginTop,A=M===void 0?0:M,P=C.marginRight,N=P===void 0?0:P,W=C.paddingLeft,X=W===void 0?0:W,$=C.paddingTop,ct=$===void 0?0:$,at=C.paddingRight,gt=at===void 0?0:at,Tt=C.paddingBottom,Wt=Tt===void 0?0:Tt,V=C.color,G=C.activeColor,O=C.backgroundColor,q=C.activeBackgroundColor,dt=C.borderRadius,tt=C.size,mt=tt===void 0?0:tt,Lt=C.type,Ht=C.content,jt=mt;if(Lt==="icon_font"){var It=Ht;jt=X+je(It.code,mt,"normal",It.family)+gt}f?y-=jt+N:y+=b;var Xt=V,ee=O;((D=a._activeFeatureInfo)===null||D===void 0?void 0:D.feature.id)===C.id&&(Xt=G??V,ee=q??O);var me={mouseClickEvent:a._featureClickEvent({crosshair:c,feature:C}),mouseMoveEvent:a._featureMouseMoveEvent({crosshair:c,feature:C})};if(Lt==="icon_font"){var It=Ht;(E=a.createFigure({name:"text",attrs:{text:It.code,x:y,y:k+A,baseline:"middle"},styles:{paddingLeft:X,paddingTop:ct,paddingRight:gt,paddingBottom:Wt,borderRadius:dt,size:mt,family:It.family,color:Xt,backgroundColor:ee}},me))===null||E===void 0||E.draw(e)}else{(I=a.createFigure({name:"rect",attrs:{x:y,y:k+A-mt/2,width:mt,height:mt},styles:{paddingLeft:X,paddingTop:ct,paddingRight:gt,paddingBottom:Wt,color:ee}},me))===null||I===void 0||I.draw(e);var de=Ht;(T=a.createFigure({name:"path",attrs:{path:de.path,x:y,y:k+A+ct-mt/2,width:mt,height:mt},styles:{style:de.style,lineWidth:de.lineWidth,color:Xt}}))===null||T===void 0||T.draw(e)}f?y-=b:y+=jt+N})}}},r}(We),a0=function(t){St(r,t);function r(e,a){var i=t.call(this,e,a)||this;return i._candleBarView=new So(i),i._candleAreaView=new X1(i),i._candleHighLowPriceView=new G1(i),i._candleLastPriceLineView=new K1(i),i._crosshairFeatureView=new r0(i),i._orderFlowView=new U1(i),i.addChild(i._candleBarView),i.addChild(i._crosshairFeatureView),i.addChild(i._orderFlowView),i}return r.prototype.updateMainContent=function(e){var a=this.getPane().getChart().getStyles().candle;a.type!=="area"?(this._candleBarView.draw(e),this._candleHighLowPriceView.draw(e),this._candleAreaView.stopAnimation()):this._candleAreaView.draw(e),this._candleLastPriceLineView.draw(e),this._orderFlowView.draw(e)},r.prototype.updateOverlayContent=function(e){this._crosshairFeatureView.draw(e)},r.prototype.createTooltipView=function(){return new e0(this)},r.prototype.setOrderFlowData=function(e){this._orderFlowView.setOrderFlowData(e)},r.prototype.getOrderFlowData=function(){return this._orderFlowView.getOrderFlowData()},r.prototype.getOrderFlowView=function(){return this._orderFlowView},r.prototype.getOrderFlowDrawingBounds=function(){return this._orderFlowView.getOrderFlowDrawingBounds()},r}(To),Io=function(t){St(r,t);function r(){return t!==null&&t.apply(this,arguments)||this}return r.prototype.drawImp=function(e,a){var i=this,n,o,s=this.getWidget(),l=s.getPane(),c=s.getBounding(),u=l.getAxisComponent(),d=this.getAxisStyles(l.getChart().getStyles());if(d.show&&(d.axisLine.show&&((n=this.createFigure({name:"line",attrs:this.createAxisLine(c,d),styles:d.axisLine}))===null||n===void 0||n.draw(e)),!a[0])){var h=u.getTicks();if(d.tickLine.show){var v=this.createTickLines(h,c,d);v.forEach(function(p){var g;(g=i.createFigure({name:"line",attrs:p,styles:d.tickLine}))===null||g===void 0||g.draw(e)})}if(d.tickText.show){var f=this.createTickTexts(h,c,d);(o=this.createFigure({name:"text",attrs:f,styles:d.tickText}))===null||o===void 0||o.draw(e)}}},r}(We),i0=function(t){St(r,t);function r(){return t!==null&&t.apply(this,arguments)||this}return r.prototype.getAxisStyles=function(e){return e.yAxis},r.prototype.createAxisLine=function(e,a){var i=this.getWidget().getPane().getAxisComponent(),n=a.axisLine.size,o=0;return i.isFromZero()?o=0:o=e.width-n,{coordinates:[{x:o,y:0},{x:o,y:e.height}]}},r.prototype.createTickLines=function(e,a,i){var n=this.getWidget().getPane().getAxisComponent(),o=i.axisLine,s=i.tickLine,l=0,c=0;return n.isFromZero()?(l=0,o.show&&(l+=o.size),c=l+s.length):(l=a.width,o.show&&(l-=o.size),c=l-s.length),e.map(function(u){return{coordinates:[{x:l,y:u.coord},{x:c,y:u.coord}]}})},r.prototype.createTickTexts=function(e,a,i){var n=this.getWidget().getPane().getAxisComponent(),o=i.axisLine,s=i.tickLine,l=i.tickText,c=0;n.isFromZero()?(c=l.marginStart,o.show&&(c+=o.size),s.show&&(c+=s.length)):(c=a.width-l.marginEnd,o.show&&(c-=o.size),s.show&&(c-=s.length));var u=this.getWidget().getPane().getAxisComponent().isFromZero()?"left":"right";return e.map(function(d){return{x:c,y:d.coord,text:d.text,align:u,baseline:"middle"}})},r}(Io),n0=function(t){St(r,t);function r(){return t!==null&&t.apply(this,arguments)||this}return r.prototype.drawImp=function(e){var a=this,i,n,o,s,l=this.getWidget(),c=l.getPane(),u=l.getBounding(),d=c.getChart().getChartStore(),h=d.getStyles().candle.priceMark,v=h.last,f=v.text;if(h.show&&v.show&&f.show){var p=(n=(i=d.getSymbol())===null||i===void 0?void 0:i.pricePrecision)!==null&&n!==void 0?n:2,g=c.getAxisComponent(),m=d.getDataList(),_=m[m.length-1];if(R(_)){var x=_.close,S=_.open,y=v.compareRule==="current_open"?S:(s=(o=m[m.length-2])===null||o===void 0?void 0:o.close)!==null&&s!==void 0?s:x,k=g.convertToNicePixel(x),C="";x>y?C=v.upColor:x<y?C=v.downColor:C=v.noChangeColor;var D=0,E="left";g.isFromZero()?(D=0,E="left"):(D=u.width,E="right");var I=[],T=g.getRange(),L=g.displayValueToText(g.realValueToDisplayValue(g.valueToRealValue(x,{range:T}),{range:T}),p);L=d.getDecimalFold().format(d.getThousandsSeparator().format(L));var b=f.paddingLeft,M=f.paddingRight,A=f.paddingTop,P=f.paddingBottom,N=f.size,W=f.family,X=f.weight,$=b+je(L,N,X,W)+M,ct=A+N+P;I.push({name:"text",attrs:{x:D,y:k,width:$,height:ct,text:L,align:E,baseline:"middle"},styles:J(J({},f),{backgroundColor:C})});var at=d.getInnerFormatter().formatExtendText,gt=N/2,Tt=k-gt-A,Wt=k+gt+P;v.extendTexts.forEach(function(V,G){var O=at({type:"last_price",data:_,index:G});if(O.length>0&&V.show){var q=V.size/2,dt=0;V.position==="above_price"?(Tt-=V.paddingBottom+q,dt=Tt,Tt-=q+V.paddingTop):(Wt+=V.paddingTop+q,dt=Wt,Wt+=q+V.paddingBottom),$=Math.max($,V.paddingLeft+je(O,V.size,V.weight,V.family)+V.paddingRight),I.push({name:"text",attrs:{x:D,y:dt,width:$,height:V.paddingTop+V.size+V.paddingBottom,text:O,align:E,baseline:"middle"},styles:J(J({},V),{backgroundColor:C})})}}),I.forEach(function(V){var G;V.attrs.width=$,(G=a.createFigure(V))===null||G===void 0||G.draw(e)})}}},r}(We),o0=function(t){St(r,t);function r(){return t!==null&&t.apply(this,arguments)||this}return r.prototype.drawImp=function(e){var a=this,i=this.getWidget(),n=i.getPane(),o=i.getBounding(),s=n.getChart().getChartStore(),l=s.getStyles().indicator,c=l.lastValueMark,u=c.text;if(c.show){var d=n.getAxisComponent(),h=d.getRange(),v=s.getDataList(),f=v.length-1,p=s.getIndicatorsByPaneId(n.getId()),g=s.getInnerFormatter(),m=s.getDecimalFold(),_=s.getThousandsSeparator();p.forEach(function(x){var S,y,k=x.result,C=(y=(S=k[f])!==null&&S!==void 0?S:k[f-1])!==null&&y!==void 0?y:{};if(R(C)&&x.visible){var D=x.precision;fi(x,f,l,function(E,I){var T,L=C[E.key];if(vt(L)){var b=d.convertToNicePixel(L),M=d.displayValueToText(d.realValueToDisplayValue(d.valueToRealValue(L,{range:h}),{range:h}),D);x.shouldFormatBigNumber&&(M=g.formatBigNumber(M)),M=m.format(_.format(M));var A=0,P="left";d.isFromZero()?(A=0,P="left"):(A=o.width,P="right"),(T=a.createFigure({name:"text",attrs:{x:A,y:b,text:M,align:P,baseline:"middle"},styles:J(J({},u),{backgroundColor:I.color})}))===null||T===void 0||T.draw(e)}})}})}},r}(We),Ao=function(t){St(r,t);function r(){return t!==null&&t.apply(this,arguments)||this}return r.prototype.coordinateToPointTimestampDataIndexFlag=function(){return!1},r.prototype.drawDefaultFigures=function(e,a,i){this.drawFigures(e,a,this.getDefaultFigures(a,i))},r.prototype.getDefaultFigures=function(e,a){var i,n=this.getWidget(),o=n.getPane(),s=o.getChart().getChartStore(),l=s.getClickOverlayInfo(),c=[];if(e.needDefaultYAxisFigure&&e.id===((i=l.overlay)===null||i===void 0?void 0:i.id)&&l.paneId===o.getId()){var u=o.getAxisComponent(),d=n.getBounding(),h=Number.MAX_SAFE_INTEGER,v=Number.MIN_SAFE_INTEGER,f=u.isFromZero(),p="left",g=0;f?(p="left",g=0):(p="right",g=d.width);var m=s.getDecimalFold(),_=s.getThousandsSeparator();a.forEach(function(x,S){var y,k,C=e.points[S];if(vt(C.value)){h=Math.min(h,x.y),v=Math.max(v,x.y);var D=m.format(_.format(Me(C.value,(k=(y=s.getSymbol())===null||y===void 0?void 0:y.pricePrecision)!==null&&k!==void 0?k:2)));c.push({type:"text",attrs:{x:g,y:x.y,text:D,align:p,baseline:"middle"},ignoreEvent:!0})}}),a.length>1&&c.unshift({type:"rect",attrs:{x:0,y:h,width:d.width,height:v-h},ignoreEvent:!0})}return c},r.prototype.getFigures=function(e,a){var i,n,o=this.getWidget(),s=o.getPane(),l=s.getChart(),c=s.getAxisComponent(),u=l.getXAxisPane().getAxisComponent(),d=o.getBounding();return(n=(i=e.createYAxisFigures)===null||i===void 0?void 0:i.call(e,{chart:l,overlay:e,coordinates:a,bounding:d,xAxis:u,yAxis:c}))!==null&&n!==void 0?n:[]},r}(Mo),Do=function(t){St(r,t);function r(){return t!==null&&t.apply(this,arguments)||this}return r.prototype.drawImp=function(e){var a,i=this.getWidget(),n=i.getPane(),o=i.getPane().getChart().getChartStore(),s=o.getCrosshair();if(Kt(s.paneId)&&this.compare(s,n.getId())){var l=o.getStyles().crosshair;if(l.show){var c=this.getDirectionStyles(l),u=c.text;if(c.show&&u.show){var d=i.getBounding(),h=n.getAxisComponent(),v=this.getText(s,o,h);e.font=br(u.size,u.weight,u.family),(a=this.createFigure({name:"text",attrs:this.getTextAttrs(v,e.measureText(v).width,s,d,h,u),styles:u}))===null||a===void 0||a.draw(e)}}}},r.prototype.compare=function(e,a){return e.paneId===a},r.prototype.getDirectionStyles=function(e){return e.horizontal},r.prototype.getText=function(e,a,i){var n,o,s=i,l=i.convertFromPixel(e.y),c=0,u=!1;if(s.isInCandle())c=(o=(n=a.getSymbol())===null||n===void 0?void 0:n.pricePrecision)!==null&&o!==void 0?o:2;else{var d=a.getIndicatorsByPaneId(e.paneId);d.forEach(function(f){c=Math.max(f.precision,c),u||(u=f.shouldFormatBigNumber)})}var h=s.getRange(),v=s.displayValueToText(s.realValueToDisplayValue(s.valueToRealValue(l,{range:h}),{range:h}),c);return u&&(v=a.getInnerFormatter().formatBigNumber(v)),a.getDecimalFold().format(a.getThousandsSeparator().format(v))},r.prototype.getTextAttrs=function(e,a,i,n,o,s){var l=o,c=0,u="left";return l.isFromZero()?(c=0,u="left"):(c=n.width,u="right"),{x:c,y:i.y,text:e,align:u,baseline:"middle"}},r}(We),s0=function(t){St(r,t);function r(e,a){var i=t.call(this,e,a)||this;return i._yAxisView=new i0(i),i._candleLastPriceLabelView=new n0(i),i._indicatorLastValueView=new o0(i),i._overlayYAxisView=new Ao(i),i._crosshairHorizontalLabelView=new Do(i),i.setCursor("ns-resize"),i.addChild(i._overlayYAxisView),i}return r.prototype.getName=function(){return Nt.Y_AXIS},r.prototype.updateMain=function(e){var a=this.getPane().getOptions().state==="minimize";this._yAxisView.draw(e,a),a||(this.getPane().getAxisComponent().isInCandle()&&this._candleLastPriceLabelView.draw(e),this._indicatorLastValueView.draw(e))},r.prototype.updateOverlay=function(e){this.getPane().getOptions().state!=="minimize"&&(this._overlayYAxisView.draw(e),this._crosshairHorizontalLabelView.draw(e))},r}(_i);function an(){return{from:0,to:0,range:0,realFrom:0,realTo:0,realRange:0,displayFrom:0,displayTo:0,displayRange:0}}var Po=function(){function t(r){this.scrollZoomEnabled=!0,this._range=an(),this._prevRange=an(),this._ticks=[],this._autoCalcTickFlag=!0,this._parent=r}return t.prototype.getParent=function(){return this._parent},t.prototype.buildTicks=function(r){return this._autoCalcTickFlag&&(this._range=this.createRangeImp()),this._prevRange.from!==this._range.from||this._prevRange.to!==this._range.to||r?(this._prevRange=this._range,this._ticks=this.createTicksImp(),!0):!1},t.prototype.getTicks=function(){return this._ticks},t.prototype.setRange=function(r){this._autoCalcTickFlag=!1,this._range=r},t.prototype.getRange=function(){return this._range},t.prototype.setAutoCalcTickFlag=function(r){this._autoCalcTickFlag=r},t.prototype.getAutoCalcTickFlag=function(){return this._autoCalcTickFlag},t}(),ra=8,ha=function(t){St(r,t);function r(e,a){var i=t.call(this,e)||this;return i.reverse=!1,i.inside=!1,i.position="right",i.gap={top:.2,bottom:.1},i.createRange=function(n){return n.defaultRange},i.minSpan=function(n){return Cr(-n)},i.valueToRealValue=function(n){return n},i.realValueToDisplayValue=function(n){return n},i.displayValueToRealValue=function(n){return n},i.realValueToValue=function(n){return n},i.displayValueToText=function(n,o){return Me(n,o)},i.override(a),i}return r.prototype.override=function(e){var a=e.name,i=e.gap,n=ga(e,["name","gap"]);Kt(this.name)||(this.name=a),ye(this.gap,i),ye(this,n)},r.prototype.createRangeImp=function(){var e,a,i=this.getParent(),n=i.getChart(),o=n.getChartStore(),s=i.getId(),l=Number.MAX_SAFE_INTEGER,c=Number.MIN_SAFE_INTEGER,u=!1,d=Number.MAX_SAFE_INTEGER,h=Number.MIN_SAFE_INTEGER,v=Number.MAX_SAFE_INTEGER,f=o.getIndicatorsByPaneId(s);f.forEach(function(It){u||(u=It.shouldOhlc),v=Math.min(v,It.precision),vt(It.minValue)&&(d=Math.min(d,It.minValue)),vt(It.maxValue)&&(h=Math.max(h,It.maxValue))});var p=4,g=this.isInCandle();if(g){var m=(a=(e=o.getSymbol())===null||e===void 0?void 0:e.pricePrecision)!==null&&a!==void 0?a:2;v!==Number.MAX_SAFE_INTEGER?p=Math.min(v,m):p=m}else v!==Number.MAX_SAFE_INTEGER&&(p=v);var _=o.getVisibleRangeDataList(),x=n.getStyles().candle,S=x.type==="area",y=x.area.value,k=g&&!S||!g&&u,C=g&&!o.isCandleVisible();if(C){var D=this.getParent();if(D.getId()===xt.CANDLE){var E=D.getMainWidget();if(R(E)&&typeof E.getOrderFlowData=="function"){var I=E.getOrderFlowData(),T=Number.MAX_SAFE_INTEGER,L=Number.MIN_SAFE_INTEGER,b=!1;if(_.forEach(function(It){var Xt,ee,me=It.data.current;if(R(me)){var de=I.find(function(Jt){return Jt.timestamp===me.timestamp});if(R(de)){b=!0;var xe=me.high,ue=me.low,et=xe-ue,ut=(ee=(Xt=de.priceLevels)===null||Xt===void 0?void 0:Xt.filter(function(Jt){return Jt.totalVolume>0}))!==null&&ee!==void 0?ee:[],Dt=50,Ot=ut.slice(0,Dt);if(Array.isArray(Ot)&&Ot.length>0){var yt=Math.max(Ot.length,3),ce=Math.max(1.5,yt/10),we=et*ce,Zt=(we-et)/2,Qt=xe+Zt,qt=ue-Zt;T=Math.min(T,qt),L=Math.max(L,Qt)}}}}),b&&T<Number.MAX_SAFE_INTEGER&&L>Number.MIN_SAFE_INTEGER){var M=L-T,A=M*.2;l=Math.min(l,T-A),c=Math.max(c,L+A)}}}}_.forEach(function(It){var Xt=It.data.current;if(R(Xt)&&(k&&(l=Math.min(l,Xt.low),c=Math.max(c,Xt.high)),g&&S)){var ee=Xt[y];vt(ee)&&(l=Math.min(l,ee),c=Math.max(c,ee))}}),_.forEach(function(It){var Xt=It.dataIndex;f.forEach(function(ee){var me,de=ee.result,xe=ee.figures,ue=(me=de[Xt])!==null&&me!==void 0?me:{};xe.forEach(function(et){var ut=ue[et.key];vt(ut)&&(l=Math.min(l,ut),c=Math.max(c,ut))})})}),l!==Number.MAX_SAFE_INTEGER&&c!==Number.MIN_SAFE_INTEGER?(l=Math.min(d,l),c=Math.max(h,c)):(l=0,c=10);var P=c-l,N={from:l,to:c,range:P,realFrom:l,realTo:c,realRange:P,displayFrom:l,displayTo:c,displayRange:P},W=this.createRange({chart:n,paneId:s,defaultRange:N}),X=W.realFrom,$=W.realTo,ct=W.realRange,at=this.minSpan(p);if(X===$||ct<at){var gt=d===X,Tt=h===$,Wt=ra/2;X=gt?X:Tt?X-ra*at:X-Wt*at,$=Tt?$:gt?$+ra*at:$+Wt*at}var V=this.getBounding().height,G=this.gap,O=G.top,q=G.bottom,dt=O;dt>=1&&(dt=dt/V);var tt=q;tt>=1&&(tt=tt/V),ct=$-X,X=X-ct*tt,$=$+ct*dt;var mt=this.realValueToValue(X,{range:W}),Lt=this.realValueToValue($,{range:W}),Ht=this.realValueToDisplayValue(X,{range:W}),jt=this.realValueToDisplayValue($,{range:W});return{from:mt,to:Lt,range:Lt-mt,realFrom:X,realTo:$,realRange:$-X,displayFrom:Ht,displayTo:jt,displayRange:jt-Ht}},r.prototype.isInCandle=function(){return this.getParent().getId()===xt.CANDLE},r.prototype.isFromZero=function(){return this.position==="left"&&this.inside||this.position==="right"&&!this.inside},r.prototype.createTicksImp=function(){var e=this,a,i,n,o,s=this.getRange(),l=s.displayFrom,c=s.displayTo,u=s.displayRange,d=[];if(u>=0){var h=cl(u/ra),v=ul(h),f=Xi(Math.ceil(l/h)*h,v),p=Xi(Math.floor(c/h)*h,v),g=0,m=f;if(h!==0)for(;m<=p;){var _=m.toFixed(v);d[g]={text:_,coord:0,value:_},++g,m+=h}}var x=this.getParent(),S=(i=(a=x.getYAxisWidget())===null||a===void 0?void 0:a.getBounding().height)!==null&&i!==void 0?i:0,y=x.getChart().getChartStore(),k=[],C=y.getIndicatorsByPaneId(x.getId()),D=y.getStyles(),E=0,I=!1;this.isInCandle()?E=(o=(n=y.getSymbol())===null||n===void 0?void 0:n.pricePrecision)!==null&&o!==void 0?o:2:C.forEach(function(P){E=Math.max(E,P.precision),I||(I=P.shouldFormatBigNumber)});var T=y.getInnerFormatter(),L=y.getThousandsSeparator(),b=y.getDecimalFold(),M=D.xAxis.tickText.size,A=NaN;return d.forEach(function(P){var N=P.value,W=e.displayValueToText(+N,E),X=e.convertToPixel(e.realValueToValue(e.displayValueToRealValue(+N,{range:s}),{range:s}));I&&(W=T.formatBigNumber(N)),W=b.format(L.format(W));var $=vt(A);X>M&&X<S-M&&($&&Math.abs(A-X)>M*2||!$)&&(k.push({text:W,coord:X,value:N}),A=X)}),Ie(this.createTicks)?this.createTicks({range:this.getRange(),bounding:this.getBounding(),defaultTicks:k}):k},r.prototype.getAutoSize=function(){var e,a,i=this.getParent(),n=i.getChart(),o=n.getChartStore(),s=o.getStyles(),l=s.yAxis,c=l.size;if(c!=="auto")return c;var u=0;if(l.show&&(l.axisLine.show&&(u+=l.axisLine.size),l.tickLine.show&&(u+=l.tickLine.length),l.tickText.show)){var d=0;this.getTicks().forEach(function(X){d=Math.max(d,je(X.text,l.tickText.size,l.tickText.weight,l.tickText.family))}),u+=l.tickText.marginStart+l.tickText.marginEnd+d}var h=s.candle.priceMark,v=h.show&&h.last.show&&h.last.text.show,f=0,p=s.crosshair,g=p.show&&p.horizontal.show&&p.horizontal.text.show,m=0;if(v||g){var _=(a=(e=o.getSymbol())===null||e===void 0?void 0:e.pricePrecision)!==null&&a!==void 0?a:2,x=this.getRange().displayTo;if(v){var S=o.getDataList(),y=S[S.length-1];if(R(y)){var k=h.last.text,C=k.paddingLeft,D=k.paddingRight,E=k.size,I=k.family,T=k.weight;f=C+je(Me(y.close,_),E,T,I)+D;var L=o.getInnerFormatter().formatExtendText;h.last.extendTexts.forEach(function(X,$){var ct=L({type:"last_price",data:y,index:$});ct.length>0&&X.show&&(f=Math.max(f,X.paddingLeft+je(ct,X.size,X.weight,X.family)+X.paddingRight))})}}if(g){var b=o.getIndicatorsByPaneId(i.getId()),M=0,A=!1;b.forEach(function(X){M=Math.max(X.precision,M),A||(A=X.shouldFormatBigNumber)});var P=2;if(this.isInCandle()){var N=s.indicator.lastValueMark;N.show&&N.text.show?P=Math.max(M,_):P=_}else P=M;var W=Me(x,P);A&&(W=o.getInnerFormatter().formatBigNumber(W)),W=o.getDecimalFold().format(W),m+=p.horizontal.text.paddingLeft+p.horizontal.text.paddingRight+p.horizontal.text.borderSize*2+je(W,p.horizontal.text.size,p.horizontal.text.weight,p.horizontal.text.family)}}return Math.max(u,f,m)},r.prototype.getBounding=function(){return this.getParent().getYAxisWidget().getBounding()},r.prototype.convertFromPixel=function(e){var a=this.getBounding().height,i=this.getRange(),n=i.realFrom,o=i.realRange,s=this.reverse?e/a:1-e/a,l=s*o+n;return this.realValueToValue(l,{range:i})},r.prototype.convertToPixel=function(e){var a,i,n=this.getRange(),o=this.valueToRealValue(e,{range:n}),s=(i=(a=this.getParent().getYAxisWidget())===null||a===void 0?void 0:a.getBounding().height)!==null&&i!==void 0?i:0,l=n.realFrom,c=n.realRange,u=(o-l)/c;return this.reverse?Math.round(u*s):Math.round((1-u)*s)},r.prototype.convertToNicePixel=function(e){var a,i,n=(i=(a=this.getParent().getYAxisWidget())===null||a===void 0?void 0:a.getBounding().height)!==null&&i!==void 0?i:0,o=this.convertToPixel(e);return Math.round(Math.max(n*.05,Math.min(o,n*.98)))},r.extend=function(e){var a=function(i){St(n,i);function n(o){return i.call(this,o,e)||this}return n}(r);return a},r}(Po),l0={name:"normal"},c0={name:"percentage",minSpan:function(){return Math.pow(10,-2)},displayValueToText:function(t){return"".concat(Me(t,2),"%")},valueToRealValue:function(t,r){var e=r.range;return(t-e.from)/e.range*e.realRange+e.realFrom},realValueToValue:function(t,r){var e=r.range;return(t-e.realFrom)/e.realRange*e.range+e.from},createRange:function(t){var r=t.chart,e=t.defaultRange,a=r.getDataList(),i=r.getVisibleRange(),n=a[i.from];if(R(n)){var o=e.from,s=e.to,l=e.range,c=(e.from-n.close)/n.close*100,u=(e.to-n.close)/n.close*100,d=u-c;return{from:o,to:s,range:l,realFrom:c,realTo:u,realRange:d,displayFrom:c,displayTo:u,displayRange:d}}return e}},u0={name:"logarithm",minSpan:function(t){return .05*Cr(-t)},valueToRealValue:function(t){return t<0?-rr(Math.abs(t)):rr(t)},realValueToDisplayValue:function(t){return t<0?-Cr(Math.abs(t)):Cr(t)},displayValueToRealValue:function(t){return t<0?-rr(Math.abs(t)):rr(t)},realValueToValue:function(t){return t<0?-Cr(Math.abs(t)):Cr(t)},createRange:function(t){var r=t.defaultRange,e=r.from,a=r.to,i=r.range,n=e<0?-rr(Math.abs(e)):rr(e),o=a<0?-rr(Math.abs(a)):rr(a);return{from:e,to:a,range:i,realFrom:n,realTo:o,realRange:o-n,displayFrom:e,displayTo:a,displayRange:i}}},nn={normal:ha.extend(l0),percentage:ha.extend(c0),logarithm:ha.extend(u0)};function d0(t){var r;return(r=nn[t])!==null&&r!==void 0?r:nn.normal}var Fo=function(){function t(r,e){this._bounding=ma(),this._originalBounding=ma(),this._visible=!0,this._chart=r,this._id=e,this._container=sr("div",{width:"100%",margin:"0",padding:"0",position:"relative",overflow:"hidden",boxSizing:"border-box"})}return t.prototype.getContainer=function(){return this._container},t.prototype.setVisible=function(r){this._visible!==r&&(this._container.style.display=r?"block":"none",this._visible=r)},t.prototype.getVisible=function(){return this._visible},t.prototype.getId=function(){return this._id},t.prototype.getChart=function(){return this._chart},t.prototype.getBounding=function(){return this._bounding},t.prototype.setOriginalBounding=function(r){ye(this._originalBounding,r)},t.prototype.getOriginalBounding=function(){return this._originalBounding},t.prototype.update=function(r){this._bounding.height!==this._container.clientHeight&&(this._container.style.height="".concat(this._bounding.height,"px")),this.updateImp(r??3,this._container,this._bounding)},t}(),Ro=function(t){St(r,t);function r(e,a,i){var n=t.call(this,e,a)||this;n._yAxisWidget=null,n._options={id:"",minHeight:da,dragEnabled:!0,order:0,height:mo,state:"normal",axis:{name:"normal",scrollZoomEnabled:!0}};var o=n.getContainer();return n._mainWidget=n.createMainWidget(o),n._yAxisWidget=n.createYAxisWidget(o),n.setOptions(i),n}return r.prototype.setOptions=function(e){var a,i,n,o,s,l=this.getId();if(l===xt.CANDLE||l===xt.X_AXIS){var c=(a=e.axis)===null||a===void 0?void 0:a.name;(!R(this._axis)||R(c)&&this._options.axis.name!==c)&&(this._axis=this.createAxisComponent(c??"normal"))}else R(this._axis)||(this._axis=this.createAxisComponent("normal"));this._axis instanceof ha&&this._axis.setAutoCalcTickFlag(!0),ye(this._options,e),this._axis.override(J(J({},this._options.axis),{name:(n=(i=e.axis)===null||i===void 0?void 0:i.name)!==null&&n!==void 0?n:"normal"}));var u=null,d="default";return this.getId()===xt.X_AXIS?(u=this.getMainWidget().getContainer(),d="ew-resize"):(u=this.getYAxisWidget().getContainer(),d="ns-resize"),!((s=(o=e.axis)===null||o===void 0?void 0:o.scrollZoomEnabled)!==null&&s!==void 0)||s?u.style.cursor=d:u.style.cursor="default",this},r.prototype.getOptions=function(){return this._options},r.prototype.getAxisComponent=function(){return this._axis},r.prototype.setBounding=function(e,a,i,n){var o,s,l,c;ye(this.getBounding(),e);var u={};R(e.height)&&(u.height=e.height),R(e.top)&&(u.top=e.top),this._mainWidget.setBounding(u);var d=R(a);if(d&&this._mainWidget.setBounding(a),R(this._yAxisWidget)){this._yAxisWidget.setBounding(u);var h=this._axis;h.position==="left"?R(i)&&this._yAxisWidget.setBounding(J(J({},i),{left:0})):R(n)&&(this._yAxisWidget.setBounding(n),d&&this._yAxisWidget.setBounding({left:((o=a.left)!==null&&o!==void 0?o:0)+((s=a.width)!==null&&s!==void 0?s:0)+((l=a.right)!==null&&l!==void 0?l:0)-((c=n.width)!==null&&c!==void 0?c:0)}))}return this},r.prototype.getMainWidget=function(){return this._mainWidget},r.prototype.getYAxisWidget=function(){return this._yAxisWidget},r.prototype.updateImp=function(e){var a;this._mainWidget.update(e),(a=this._yAxisWidget)===null||a===void 0||a.update(e)},r.prototype.destroy=function(){var e;this._mainWidget.destroy(),(e=this._yAxisWidget)===null||e===void 0||e.destroy()},r.prototype.getImage=function(e){var a=this.getBounding(),i=a.width,n=a.height,o=sr("canvas",{width:"".concat(i,"px"),height:"".concat(n,"px"),boxSizing:"border-box"}),s=o.getContext("2d"),l=vr(o);o.width=i*l,o.height=n*l,s.scale(l,l);var c=this._mainWidget.getBounding();if(s.drawImage(this._mainWidget.getImage(e),c.left,0,c.width,c.height),this._yAxisWidget!==null){var u=this._yAxisWidget.getBounding();s.drawImage(this._yAxisWidget.getImage(e),u.left,0,u.width,u.height)}return o},r.prototype.createYAxisWidget=function(e){return null},r}(Fo),Bo=function(t){St(r,t);function r(){return t!==null&&t.apply(this,arguments)||this}return r.prototype.createAxisComponent=function(e){var a=d0(e??"default");return new a(this)},r.prototype.createMainWidget=function(e){return new To(e,this)},r.prototype.createYAxisWidget=function(e){return new s0(e,this)},r}(Ro),h0=function(t){St(r,t);function r(){return t!==null&&t.apply(this,arguments)||this}return r.prototype.createMainWidget=function(e){return new a0(e,this)},r}(Bo),v0=function(t){St(r,t);function r(){return t!==null&&t.apply(this,arguments)||this}return r.prototype.getAxisStyles=function(e){return e.xAxis},r.prototype.createAxisLine=function(e){return{coordinates:[{x:0,y:0},{x:e.width,y:0}]}},r.prototype.createTickLines=function(e,a,i){var n=i.tickLine,o=i.axisLine.size;return e.map(function(s){return{coordinates:[{x:s.coord,y:0},{x:s.coord,y:o+n.length}]}})},r.prototype.createTickTexts=function(e,a,i){var n=i.tickText,o=i.axisLine.size,s=i.tickLine.length;return e.map(function(l){return{x:l.coord,y:o+s+n.marginStart,text:l.text,align:"center",baseline:"top"}})},r}(Io),f0=function(t){St(r,t);function r(){return t!==null&&t.apply(this,arguments)||this}return r.prototype.coordinateToPointTimestampDataIndexFlag=function(){return!0},r.prototype.coordinateToPointValueFlag=function(){return!1},r.prototype.getCompleteOverlays=function(){return this.getWidget().getPane().getChart().getChartStore().getOverlaysByPaneId()},r.prototype.getProgressOverlay=function(){var e,a;return(a=(e=this.getWidget().getPane().getChart().getChartStore().getProgressOverlayInfo())===null||e===void 0?void 0:e.overlay)!==null&&a!==void 0?a:null},r.prototype.getDefaultFigures=function(e,a){var i,n=[],o=this.getWidget(),s=o.getPane(),l=s.getChart().getChartStore(),c=l.getClickOverlayInfo();if(e.needDefaultXAxisFigure&&e.id===((i=c.overlay)===null||i===void 0?void 0:i.id)){var u=Number.MAX_SAFE_INTEGER,d=Number.MIN_SAFE_INTEGER;a.forEach(function(h,v){u=Math.min(u,h.x),d=Math.max(d,h.x);var f=e.points[v];if(vt(f.timestamp)){var p=l.getInnerFormatter().formatDate(f.timestamp,"YYYY-MM-DD HH:mm","crosshair");n.push({type:"text",attrs:{x:h.x,y:0,text:p,align:"center"},ignoreEvent:!0})}}),a.length>1&&n.unshift({type:"rect",attrs:{x:u,y:0,width:d-u,height:o.getBounding().height},ignoreEvent:!0})}return n},r.prototype.getFigures=function(e,a){var i,n,o=this.getWidget(),s=o.getPane(),l=s.getChart(),c=s.getAxisComponent(),u=l.getXAxisPane().getAxisComponent(),d=o.getBounding();return(n=(i=e.createXAxisFigures)===null||i===void 0?void 0:i.call(e,{chart:l,overlay:e,coordinates:a,bounding:d,xAxis:u,yAxis:c}))!==null&&n!==void 0?n:[]},r}(Ao),p0=function(t){St(r,t);function r(){return t!==null&&t.apply(this,arguments)||this}return r.prototype.compare=function(e){return R(e.timestamp)},r.prototype.getDirectionStyles=function(e){return e.vertical},r.prototype.getText=function(e,a){var i,n,o=e.timestamp;return a.getInnerFormatter().formatDate(o,Eo[(n=(i=a.getPeriod())===null||i===void 0?void 0:i.type)!==null&&n!==void 0?n:"day"],"crosshair")},r.prototype.getTextAttrs=function(e,a,i,n,o,s){var l=i.realX,c=0,u="center";return l-a/2-s.paddingLeft<0?(c=0,u="left"):l+a/2+s.paddingRight>n.width?(c=n.width,u="right"):c=l,{x:c,y:0,text:e,align:u,baseline:"top"}},r}(Do),g0=function(t){St(r,t);function r(e,a){var i=t.call(this,e,a)||this;return i._xAxisView=new v0(i),i._overlayXAxisView=new f0(i),i._crosshairVerticalLabelView=new p0(i),i.setCursor("ew-resize"),i.addChild(i._overlayXAxisView),i}return r.prototype.getName=function(){return Nt.X_AXIS},r.prototype.updateMain=function(e){this._xAxisView.draw(e)},r.prototype.updateOverlay=function(e){this._overlayXAxisView.draw(e),this._crosshairVerticalLabelView.draw(e)},r}(_i),m0=function(t){St(r,t);function r(e,a){var i=t.call(this,e)||this;return i.override(a),i}return r.prototype.override=function(e){var a=e.name,i=e.scrollZoomEnabled,n=e.createTicks;Kt(this.name)||(this.name=a),this.scrollZoomEnabled=i??this.scrollZoomEnabled,this.createTicks=n??this.createTicks},r.prototype.createRangeImp=function(){var e=this.getParent().getChart().getChartStore(),a=e.getVisibleRange(),i=a.realFrom,n=a.realTo,o=i,s=n,l=n-i+1,c={from:o,to:s,range:l,realFrom:o,realTo:s,realRange:l,displayFrom:o,displayTo:s,displayRange:l};return c},r.prototype.createTicksImp=function(){var e,a=this.getRange(),i=a.realFrom,n=a.realTo,o=a.from,s=this.getParent().getChart().getChartStore(),l=s.getInnerFormatter().formatDate,c=s.getPeriod(),u=[],d=s.getBarSpace().bar,h=s.getStyles().xAxis.tickText,v=Math.max(je("YYYY-MM-DD HH:mm:ss",h.size,h.weight,h.family),this.getBounding().width/8),f=Math.ceil(v/d);f%2!==0&&(f+=1);for(var p=Math.floor(i/f)*f,g=p;g<n;g+=f)if(g>=o){var m=s.dataIndexToTimestamp(g);vt(m)&&u.push({coord:this.convertToPixel(g),value:m,text:l(m,Q1[(e=c==null?void 0:c.type)!==null&&e!==void 0?e:"day"],"xAxis")})}return Ie(this.createTicks)?this.createTicks({range:this.getRange(),bounding:this.getBounding(),defaultTicks:u}):u},r.prototype.getAutoSize=function(){var e=this.getParent().getChart().getStyles(),a=e.xAxis,i=a.size;if(i!=="auto")return i;var n=e.crosshair,o=0;a.show&&(a.axisLine.show&&(o+=a.axisLine.size),a.tickLine.show&&(o+=a.tickLine.length),a.tickText.show&&(o+=a.tickText.marginStart+a.tickText.marginEnd+a.tickText.size));var s=0;return n.show&&n.vertical.show&&n.vertical.text.show&&(s+=n.vertical.text.paddingTop+n.vertical.text.paddingBottom+n.vertical.text.borderSize*2+n.vertical.text.size),Math.max(o,s)},r.prototype.getBounding=function(){return this.getParent().getMainWidget().getBounding()},r.prototype.convertTimestampFromPixel=function(e){var a=this.getParent().getChart().getChartStore(),i=a.coordinateToDataIndex(e);return a.dataIndexToTimestamp(i)},r.prototype.convertTimestampToPixel=function(e){var a=this.getParent().getChart().getChartStore(),i=a.timestampToDataIndex(e);return a.dataIndexToCoordinate(i)},r.prototype.convertFromPixel=function(e){return this.getParent().getChart().getChartStore().coordinateToDataIndex(e)},r.prototype.convertToPixel=function(e){return this.getParent().getChart().getChartStore().dataIndexToCoordinate(e)},r.extend=function(e){var a=function(i){St(n,i);function n(o){return i.call(this,o,e)||this}return n}(r);return a},r}(Po),y0={name:"normal"},on={normal:m0.extend(y0)};function _0(t){var r;return(r=on[t])!==null&&r!==void 0?r:on.normal}var C0=function(t){St(r,t);function r(){return t!==null&&t.apply(this,arguments)||this}return r.prototype.createAxisComponent=function(e){var a=_0(e);return new a(this)},r.prototype.createMainWidget=function(e){return new g0(e,this)},r}(Ro);function b0(t,r){var e=0;return function(){var a=Date.now();a-e>r&&(t.apply(this,arguments),e=a)}}var x0=function(t){St(r,t);function r(e,a){var i=t.call(this,e,a)||this;return i._dragFlag=!1,i._dragStartY=0,i._topPaneHeight=0,i._bottomPaneHeight=0,i._topPane=null,i._bottomPane=null,i._pressedMouseMoveEvent=b0(i._pressedTouchMouseMoveEvent,20),i.registerEvent("touchStartEvent",i._mouseDownEvent.bind(i)).registerEvent("touchMoveEvent",i._pressedMouseMoveEvent.bind(i)).registerEvent("touchEndEvent",i._mouseUpEvent.bind(i)).registerEvent("mouseDownEvent",i._mouseDownEvent.bind(i)).registerEvent("mouseUpEvent",i._mouseUpEvent.bind(i)).registerEvent("pressedMouseMoveEvent",i._pressedMouseMoveEvent.bind(i)).registerEvent("mouseEnterEvent",i._mouseEnterEvent.bind(i)).registerEvent("mouseLeaveEvent",i._mouseLeaveEvent.bind(i)),i}return r.prototype.getName=function(){return Nt.SEPARATOR},r.prototype._mouseDownEvent=function(e){var a=this;this._dragFlag=!0,this._dragStartY=e.pageY;var i=this.getPane(),n=i.getChart();this._topPane=i.getTopPane(),this._bottomPane=i.getBottomPane();var o=n.getDrawPanes();if(this._topPane.getOptions().state==="minimize")for(var s=o.findIndex(function(d){var h;return d.getId()===((h=a._topPane)===null||h===void 0?void 0:h.getId())}),l=s-1;l>-1;l--){var c=o[l];if(c.getOptions().state!=="minimize"){this._topPane=c;break}}if(this._bottomPane.getOptions().state==="minimize")for(var s=o.findIndex(function(h){var v;return h.getId()===((v=a._bottomPane)===null||v===void 0?void 0:v.getId())}),l=s+1;l<o.length;l++){var u=o[l];if(u.getOptions().state!=="minimize"){this._bottomPane=u;break}}return this._topPaneHeight=this._topPane.getBounding().height,this._bottomPaneHeight=this._bottomPane.getBounding().height,!0},r.prototype._mouseUpEvent=function(){return this._dragFlag=!1,this._topPane=null,this._bottomPane=null,this._topPaneHeight=0,this._bottomPaneHeight=0,this._mouseLeaveEvent()},r.prototype._pressedTouchMouseMoveEvent=function(e){var a=e.pageY-this._dragStartY,i=a<0;if(R(this._topPane)&&R(this._bottomPane)){var n=this._bottomPane.getOptions();if(this._topPane.getOptions().state!=="minimize"&&n.state!=="minimize"&&n.dragEnabled){var o=null,s=null,l=0,c=0;i?(o=this._topPane,s=this._bottomPane,l=this._topPaneHeight,c=this._bottomPaneHeight):(o=this._bottomPane,s=this._topPane,l=this._bottomPaneHeight,c=this._topPaneHeight);var u=o.getOptions().minHeight;if(l>u){var d=Math.max(l-Math.abs(a),u),h=l-d;o.setBounding({height:d}),s.setBounding({height:c+h});var v=this.getPane(),f=v.getChart();f.getChartStore().executeAction("onPaneDrag",{paneId:v.getId()}),f.layout({measureHeight:!0,measureWidth:!0,update:!0,buildYAxisTick:!0,forceBuildYAxisTick:!0})}}}return!0},r.prototype._mouseEnterEvent=function(){var e=this.getPane(),a=e.getBottomPane();if(a.getOptions().dragEnabled){var i=e.getChart(),n=i.getStyles().separator;return this.getContainer().style.background=n.activeBackgroundColor,!0}return!1},r.prototype._mouseLeaveEvent=function(){return this._dragFlag?!1:(this.getContainer().style.background="transparent",!0)},r.prototype.createContainer=function(){return sr("div",{width:"100%",height:"".concat(Kr,"px"),margin:"0",padding:"0",position:"absolute",top:"-3px",zIndex:"20",boxSizing:"border-box",cursor:"ns-resize"})},r.prototype.updateImp=function(e,a,i){if(i===4||i===2){var n=this.getPane().getChart().getStyles().separator;e.style.top="".concat(-Math.floor((Kr-n.size)/2),"px"),e.style.height="".concat(Kr,"px")}},r}(yo),w0=function(t){St(r,t);function r(e,a,i,n){var o=t.call(this,e,a)||this;return o.getContainer().style.overflow="",o._topPane=i,o._bottomPane=n,o._separatorWidget=new x0(o.getContainer(),o),o}return r.prototype.setBounding=function(e){return ye(this.getBounding(),e),this},r.prototype.getTopPane=function(){return this._topPane},r.prototype.setTopPane=function(e){return this._topPane=e,this},r.prototype.getBottomPane=function(){return this._bottomPane},r.prototype.setBottomPane=function(e){return this._bottomPane=e,this},r.prototype.getWidget=function(){return this._separatorWidget},r.prototype.getImage=function(e){var a=this.getBounding(),i=a.width,n=a.height,o=this.getChart().getStyles().separator,s=sr("canvas",{width:"".concat(i,"px"),height:"".concat(n,"px"),boxSizing:"border-box"}),l=s.getContext("2d"),c=vr(s);return s.width=i*c,s.height=n*c,l.scale(c,c),l.fillStyle=o.color,l.fillRect(0,0,i,n),s},r.prototype.updateImp=function(e,a,i){if(e===4||e===2){var n=this.getChart().getStyles().separator;a.style.backgroundColor=n.color,a.style.height="".concat(i.height,"px"),a.style.marginLeft="".concat(i.left,"px"),a.style.width="".concat(i.width,"px"),this._separatorWidget.update(e)}},r}(Fo);function sn(){return typeof window>"u"?!1:window.navigator.userAgent.toLowerCase().includes("firefox")}function Ha(){return typeof window>"u"?!1:/iPhone|iPad|iPod|iOS/.test(window.navigator.userAgent)}var aa={ResetClick:500,LongTap:500,PreventFiresTouchEvents:500},kr={CancelClick:5,CancelTap:5,DoubleClick:5,DoubleTap:30},Xr={Left:0,Middle:1,Right:2},L0=10,S0=function(){function t(r,e,a){var i=this;this._clickCount=0,this._clickTimeoutId=null,this._clickCoordinate={x:Number.NEGATIVE_INFINITY,y:Number.POSITIVE_INFINITY},this._tapCount=0,this._tapTimeoutId=null,this._tapCoordinate={x:Number.NEGATIVE_INFINITY,y:Number.POSITIVE_INFINITY},this._longTapTimeoutId=null,this._longTapActive=!1,this._mouseMoveStartCoordinate=null,this._touchMoveStartCoordinate=null,this._touchMoveExceededManhattanDistance=!1,this._cancelClick=!1,this._cancelTap=!1,this._unsubscribeOutsideMouseEvents=null,this._unsubscribeOutsideTouchEvents=null,this._unsubscribeMobileSafariEvents=null,this._unsubscribeMousemove=null,this._unsubscribeMouseWheel=null,this._unsubscribeContextMenu=null,this._unsubscribeRootMouseEvents=null,this._unsubscribeRootTouchEvents=null,this._startPinchMiddleCoordinate=null,this._startPinchDistance=0,this._pinchPrevented=!1,this._preventTouchDragProcess=!1,this._mousePressed=!1,this._lastTouchEventTimeStamp=0,this._activeTouchId=null,this._acceptMouseLeave=!Ha(),this._onFirefoxOutsideMouseUp=function(n){i._mouseUpHandler(n)},this._onMobileSafariDoubleClick=function(n){if(i._firesTouchEvents(n)){if(++i._tapCount,i._tapTimeoutId!==null&&i._tapCount>1){var o=i._mouseTouchMoveWithDownInfo(i._getCoordinate(n),i._tapCoordinate).manhattanDistance;o<kr.DoubleTap&&!i._cancelTap&&i._processEvent(i._makeCompatEvent(n),i._handler.doubleTapEvent),i._resetTapTimeout()}}else if(++i._clickCount,i._clickTimeoutId!==null&&i._clickCount>1){var o=i._mouseTouchMoveWithDownInfo(i._getCoordinate(n),i._clickCoordinate).manhattanDistance;o<kr.DoubleClick&&!i._cancelClick&&i._processEvent(i._makeCompatEvent(n),i._handler.mouseDoubleClickEvent),i._resetClickTimeout()}},this._target=r,this._handler=e,this._options=a,this._init()}return t.prototype.destroy=function(){this._unsubscribeOutsideMouseEvents!==null&&(this._unsubscribeOutsideMouseEvents(),this._unsubscribeOutsideMouseEvents=null),this._unsubscribeOutsideTouchEvents!==null&&(this._unsubscribeOutsideTouchEvents(),this._unsubscribeOutsideTouchEvents=null),this._unsubscribeMousemove!==null&&(this._unsubscribeMousemove(),this._unsubscribeMousemove=null),this._unsubscribeMouseWheel!==null&&(this._unsubscribeMouseWheel(),this._unsubscribeMouseWheel=null),this._unsubscribeContextMenu!==null&&(this._unsubscribeContextMenu(),this._unsubscribeContextMenu=null),this._unsubscribeRootMouseEvents!==null&&(this._unsubscribeRootMouseEvents(),this._unsubscribeRootMouseEvents=null),this._unsubscribeRootTouchEvents!==null&&(this._unsubscribeRootTouchEvents(),this._unsubscribeRootTouchEvents=null),this._unsubscribeMobileSafariEvents!==null&&(this._unsubscribeMobileSafariEvents(),this._unsubscribeMobileSafariEvents=null),this._clearLongTapTimeout(),this._resetClickTimeout()},t.prototype._mouseEnterHandler=function(r){var e=this,a,i,n;(a=this._unsubscribeMousemove)===null||a===void 0||a.call(this),(i=this._unsubscribeMouseWheel)===null||i===void 0||i.call(this),(n=this._unsubscribeContextMenu)===null||n===void 0||n.call(this);var o=this._mouseMoveHandler.bind(this);this._unsubscribeMousemove=function(){e._target.removeEventListener("mousemove",o)},this._target.addEventListener("mousemove",o);var s=this._mouseWheelHandler.bind(this);this._unsubscribeMouseWheel=function(){e._target.removeEventListener("wheel",s)},this._target.addEventListener("wheel",s,{passive:!1});var l=this._contextMenuHandler.bind(this);this._unsubscribeContextMenu=function(){e._target.removeEventListener("contextmenu",l)},this._target.addEventListener("contextmenu",l,{passive:!1}),!this._firesTouchEvents(r)&&(this._processEvent(this._makeCompatEvent(r),this._handler.mouseEnterEvent),this._acceptMouseLeave=!0)},t.prototype._resetClickTimeout=function(){this._clickTimeoutId!==null&&clearTimeout(this._clickTimeoutId),this._clickCount=0,this._clickTimeoutId=null,this._clickCoordinate={x:Number.NEGATIVE_INFINITY,y:Number.POSITIVE_INFINITY}},t.prototype._resetTapTimeout=function(){this._tapTimeoutId!==null&&clearTimeout(this._tapTimeoutId),this._tapCount=0,this._tapTimeoutId=null,this._tapCoordinate={x:Number.NEGATIVE_INFINITY,y:Number.POSITIVE_INFINITY}},t.prototype._mouseMoveHandler=function(r){this._mousePressed||this._touchMoveStartCoordinate!==null||this._firesTouchEvents(r)||(this._processEvent(this._makeCompatEvent(r),this._handler.mouseMoveEvent),this._acceptMouseLeave=!0)},t.prototype._mouseWheelHandler=function(r){if(Math.abs(r.deltaX)>Math.abs(r.deltaY)){if(!R(this._handler.mouseWheelHortEvent)||(this._preventDefault(r),Math.abs(r.deltaX)===0))return;this._handler.mouseWheelHortEvent(this._makeCompatEvent(r),-r.deltaX)}else{if(!R(this._handler.mouseWheelVertEvent))return;var e=-(r.deltaY/100);if(e===0)return;switch(this._preventDefault(r),r.deltaMode){case r.DOM_DELTA_PAGE:{e*=120;break}case r.DOM_DELTA_LINE:{e*=32;break}}if(e!==0){var a=Math.sign(e)*Math.min(1,Math.abs(e));this._handler.mouseWheelVertEvent(this._makeCompatEvent(r),a)}}},t.prototype._contextMenuHandler=function(r){this._preventDefault(r)},t.prototype._touchMoveHandler=function(r){var e=this._touchWithId(r.changedTouches,this._activeTouchId);if(e!==null&&(this._lastTouchEventTimeStamp=this._eventTimeStamp(r),this._startPinchMiddleCoordinate===null&&!this._preventTouchDragProcess)){this._pinchPrevented=!0;var a=this._mouseTouchMoveWithDownInfo(this._getCoordinate(e),this._touchMoveStartCoordinate),i=a.xOffset,n=a.yOffset,o=a.manhattanDistance;if(!(!this._touchMoveExceededManhattanDistance&&o<kr.CancelTap)){if(!this._touchMoveExceededManhattanDistance){var s=i*.5,l=n>=s&&!this._options.treatVertDragAsPageScroll(),c=s>n&&!this._options.treatHorzDragAsPageScroll();!l&&!c&&(this._preventTouchDragProcess=!0),this._touchMoveExceededManhattanDistance=!0,this._cancelTap=!0,this._clearLongTapTimeout(),this._resetTapTimeout()}this._preventTouchDragProcess||this._processEvent(this._makeCompatEvent(r,e),this._handler.touchMoveEvent)}}},t.prototype._mouseMoveWithDownHandler=function(r){if(r.button===Xr.Left){var e=this._mouseTouchMoveWithDownInfo(this._getCoordinate(r),this._mouseMoveStartCoordinate),a=e.manhattanDistance;a>=kr.CancelClick&&(this._cancelClick=!0,this._resetClickTimeout()),this._cancelClick&&this._processEvent(this._makeCompatEvent(r),this._handler.pressedMouseMoveEvent)}},t.prototype._mouseTouchMoveWithDownInfo=function(r,e){var a=Math.abs(e.x-r.x),i=Math.abs(e.y-r.y),n=a+i;return{xOffset:a,yOffset:i,manhattanDistance:n}},t.prototype._touchEndHandler=function(r){var e=this._touchWithId(r.changedTouches,this._activeTouchId);if(e===null&&r.touches.length===0&&(e=r.changedTouches[0]),e!==null){this._activeTouchId=null,this._lastTouchEventTimeStamp=this._eventTimeStamp(r),this._clearLongTapTimeout(),this._touchMoveStartCoordinate=null,this._unsubscribeRootTouchEvents!==null&&(this._unsubscribeRootTouchEvents(),this._unsubscribeRootTouchEvents=null);var a=this._makeCompatEvent(r,e);if(this._processEvent(a,this._handler.touchEndEvent),++this._tapCount,this._tapTimeoutId!==null&&this._tapCount>1){var i=this._mouseTouchMoveWithDownInfo(this._getCoordinate(e),this._tapCoordinate).manhattanDistance;i<kr.DoubleTap&&!this._cancelTap&&this._processEvent(a,this._handler.doubleTapEvent),this._resetTapTimeout()}else this._cancelTap||(this._processEvent(a,this._handler.tapEvent),R(this._handler.tapEvent)&&this._preventDefault(r));this._tapCount===0&&this._preventDefault(r),r.touches.length===0&&this._longTapActive&&(this._longTapActive=!1,this._preventDefault(r))}},t.prototype._mouseUpHandler=function(r){if(r.button===Xr.Left){var e=this._makeCompatEvent(r);if(this._mouseMoveStartCoordinate=null,this._mousePressed=!1,this._unsubscribeRootMouseEvents!==null&&(this._unsubscribeRootMouseEvents(),this._unsubscribeRootMouseEvents=null),sn()){var a=this._target.ownerDocument.documentElement;a.removeEventListener("mouseleave",this._onFirefoxOutsideMouseUp)}if(!this._firesTouchEvents(r))if(this._processEvent(e,this._handler.mouseUpEvent),++this._clickCount,this._clickTimeoutId!==null&&this._clickCount>1){var i=this._mouseTouchMoveWithDownInfo(this._getCoordinate(r),this._clickCoordinate).manhattanDistance;i<kr.DoubleClick&&!this._cancelClick&&this._processEvent(e,this._handler.mouseDoubleClickEvent),this._resetClickTimeout()}else this._cancelClick||this._processEvent(e,this._handler.mouseClickEvent)}},t.prototype._clearLongTapTimeout=function(){this._longTapTimeoutId!==null&&(clearTimeout(this._longTapTimeoutId),this._longTapTimeoutId=null)},t.prototype._touchStartHandler=function(r){if(this._activeTouchId===null){var e=r.changedTouches[0];this._activeTouchId=e.identifier,this._lastTouchEventTimeStamp=this._eventTimeStamp(r);var a=this._target.ownerDocument.documentElement;this._cancelTap=!1,this._touchMoveExceededManhattanDistance=!1,this._preventTouchDragProcess=!1,this._touchMoveStartCoordinate=this._getCoordinate(e),this._unsubscribeRootTouchEvents!==null&&(this._unsubscribeRootTouchEvents(),this._unsubscribeRootTouchEvents=null);{var i=this._touchMoveHandler.bind(this),n=this._touchEndHandler.bind(this);this._unsubscribeRootTouchEvents=function(){a.removeEventListener("touchmove",i),a.removeEventListener("touchend",n)},a.addEventListener("touchmove",i,{passive:!1}),a.addEventListener("touchend",n,{passive:!1}),this._clearLongTapTimeout(),this._longTapTimeoutId=setTimeout(this._longTapHandler.bind(this,r),aa.LongTap)}this._processEvent(this._makeCompatEvent(r,e),this._handler.touchStartEvent),this._tapTimeoutId===null&&(this._tapCount=0,this._tapTimeoutId=setTimeout(this._resetTapTimeout.bind(this),aa.ResetClick),this._tapCoordinate=this._getCoordinate(e))}},t.prototype._mouseDownHandler=function(r){if(r.button===Xr.Right){this._preventDefault(r),this._processEvent(this._makeCompatEvent(r),this._handler.mouseRightClickEvent);return}if(r.button===Xr.Left){var e=this._target.ownerDocument.documentElement;sn()&&e.addEventListener("mouseleave",this._onFirefoxOutsideMouseUp),this._cancelClick=!1,this._mouseMoveStartCoordinate=this._getCoordinate(r),this._unsubscribeRootMouseEvents!==null&&(this._unsubscribeRootMouseEvents(),this._unsubscribeRootMouseEvents=null);{var a=this._mouseMoveWithDownHandler.bind(this),i=this._mouseUpHandler.bind(this);this._unsubscribeRootMouseEvents=function(){e.removeEventListener("mousemove",a),e.removeEventListener("mouseup",i)},e.addEventListener("mousemove",a),e.addEventListener("mouseup",i)}this._mousePressed=!0,!this._firesTouchEvents(r)&&(this._processEvent(this._makeCompatEvent(r),this._handler.mouseDownEvent),this._clickTimeoutId===null&&(this._clickCount=0,this._clickTimeoutId=setTimeout(this._resetClickTimeout.bind(this),aa.ResetClick),this._clickCoordinate=this._getCoordinate(r)))}},t.prototype._init=function(){var r=this;this._target.addEventListener("mouseenter",this._mouseEnterHandler.bind(this)),this._target.addEventListener("touchcancel",this._clearLongTapTimeout.bind(this));{var e=this._target.ownerDocument,a=function(i){r._handler.mouseDownOutsideEvent!=null&&(i.composed&&r._target.contains(i.composedPath()[0])||i.target!==null&&r._target.contains(i.target)||r._handler.mouseDownOutsideEvent({x:0,y:0,pageX:0,pageY:0}))};this._unsubscribeOutsideTouchEvents=function(){e.removeEventListener("touchstart",a)},this._unsubscribeOutsideMouseEvents=function(){e.removeEventListener("mousedown",a)},e.addEventListener("mousedown",a),e.addEventListener("touchstart",a,{passive:!0})}Ha()&&(this._unsubscribeMobileSafariEvents=function(){r._target.removeEventListener("dblclick",r._onMobileSafariDoubleClick)},this._target.addEventListener("dblclick",this._onMobileSafariDoubleClick)),this._target.addEventListener("mouseleave",this._mouseLeaveHandler.bind(this)),this._target.addEventListener("touchstart",this._touchStartHandler.bind(this),{passive:!0}),this._target.addEventListener("mousedown",function(i){if(i.button===Xr.Middle)return i.preventDefault(),!1}),this._target.addEventListener("mousedown",this._mouseDownHandler.bind(this)),this._initPinch(),this._target.addEventListener("touchmove",function(){},{passive:!1})},t.prototype._initPinch=function(){var r=this;!R(this._handler.pinchStartEvent)&&!R(this._handler.pinchEvent)&&!R(this._handler.pinchEndEvent)||(this._target.addEventListener("touchstart",function(e){r._checkPinchState(e.touches)},{passive:!0}),this._target.addEventListener("touchmove",function(e){if(!(e.touches.length!==2||r._startPinchMiddleCoordinate===null)&&R(r._handler.pinchEvent)){var a=r._getTouchDistance(e.touches[0],e.touches[1]),i=a/r._startPinchDistance;r._handler.pinchEvent(J(J({},r._startPinchMiddleCoordinate),{pageX:0,pageY:0}),i),r._preventDefault(e)}},{passive:!1}),this._target.addEventListener("touchend",function(e){r._checkPinchState(e.touches)}))},t.prototype._checkPinchState=function(r){r.length===1&&(this._pinchPrevented=!1),r.length!==2||this._pinchPrevented||this._longTapActive?this._stopPinch():this._startPinch(r)},t.prototype._startPinch=function(r){var e=this._target.getBoundingClientRect();this._startPinchMiddleCoordinate={x:(r[0].clientX-e.left+(r[1].clientX-e.left))/2,y:(r[0].clientY-e.top+(r[1].clientY-e.top))/2},this._startPinchDistance=this._getTouchDistance(r[0],r[1]),R(this._handler.pinchStartEvent)&&this._handler.pinchStartEvent({x:0,y:0,pageX:0,pageY:0}),this._clearLongTapTimeout()},t.prototype._stopPinch=function(){this._startPinchMiddleCoordinate!==null&&(this._startPinchMiddleCoordinate=null,R(this._handler.pinchEndEvent)&&this._handler.pinchEndEvent({x:0,y:0,pageX:0,pageY:0}))},t.prototype._mouseLeaveHandler=function(r){var e,a,i;(e=this._unsubscribeMousemove)===null||e===void 0||e.call(this),(a=this._unsubscribeMouseWheel)===null||a===void 0||a.call(this),(i=this._unsubscribeContextMenu)===null||i===void 0||i.call(this),!this._firesTouchEvents(r)&&this._acceptMouseLeave&&(this._processEvent(this._makeCompatEvent(r),this._handler.mouseLeaveEvent),this._acceptMouseLeave=!Ha())},t.prototype._longTapHandler=function(r){var e=this._touchWithId(r.touches,this._activeTouchId);e!==null&&(this._processEvent(this._makeCompatEvent(r,e),this._handler.longTapEvent),this._cancelTap=!0,this._longTapActive=!0)},t.prototype._firesTouchEvents=function(r){var e;return R((e=r.sourceCapabilities)===null||e===void 0?void 0:e.firesTouchEvents)?r.sourceCapabilities.firesTouchEvents:this._eventTimeStamp(r)<this._lastTouchEventTimeStamp+aa.PreventFiresTouchEvents},t.prototype._processEvent=function(r,e){e==null||e.call(this._handler,r)},t.prototype._makeCompatEvent=function(r,e){var a=this,i=e??r,n=this._target.getBoundingClientRect();return{x:i.clientX-n.left,y:i.clientY-n.top,pageX:i.pageX,pageY:i.pageY,isTouch:!r.type.startsWith("mouse")&&r.type!=="contextmenu"&&r.type!=="click"&&r.type!=="wheel",preventDefault:function(){r.type!=="touchstart"&&a._preventDefault(r)}}},t.prototype._getTouchDistance=function(r,e){var a=r.clientX-e.clientX,i=r.clientY-e.clientY;return Math.sqrt(a*a+i*i)},t.prototype._preventDefault=function(r){r.cancelable&&r.preventDefault()},t.prototype._getCoordinate=function(r){return{x:r.pageX,y:r.pageY}},t.prototype._eventTimeStamp=function(r){var e;return(e=r.timeStamp)!==null&&e!==void 0?e:performance.now()},t.prototype._touchWithId=function(r,e){for(var a=0;a<r.length;++a)if(r[a].identifier===e)return r[a];return null},t}(),k0=function(){function t(r,e){var a=this;this._flingStartTime=new Date().getTime(),this._flingScrollRequestId=null,this._startScrollCoordinate=null,this._touchCoordinate=null,this._touchCancelCrosshair=!1,this._touchZoomed=!1,this._pinchScale=1,this._mouseDownWidget=null,this._prevYAxisRange=null,this._xAxisStartScaleCoordinate=null,this._xAxisStartScaleDistance=0,this._xAxisScale=1,this._yAxisStartScaleDistance=0,this._mouseMoveTriggerWidgetInfo={pane:null,widget:null},this._boundKeyBoardDownEvent=function(i){if(i.shiftKey)switch(i.code){case"Equal":{a._chart.getChartStore().zoom(.5);break}case"Minus":{a._chart.getChartStore().zoom(-.5);break}case"ArrowLeft":{var n=a._chart.getChartStore();n.startScroll(),n.scroll(-3*n.getBarSpace().bar);break}case"ArrowRight":{var n=a._chart.getChartStore();n.startScroll(),n.scroll(3*n.getBarSpace().bar);break}}},this._container=r,this._chart=e,this._event=new S0(r,this,{treatVertDragAsPageScroll:function(){return!1},treatHorzDragAsPageScroll:function(){return!1}}),r.addEventListener("keydown",this._boundKeyBoardDownEvent)}return t.prototype.pinchStartEvent=function(){return this._touchZoomed=!0,this._pinchScale=1,!0},t.prototype.pinchEvent=function(r,e){var a=this._findWidgetByEvent(r),i=a.pane,n=a.widget;if((i==null?void 0:i.getId())!==xt.X_AXIS&&(n==null?void 0:n.getName())===Nt.MAIN){var o=this._makeWidgetEvent(r,n),s=(e-this._pinchScale)*5;return this._pinchScale=e,this._chart.getChartStore().zoom(s,{x:o.x,y:o.y}),!0}return!1},t.prototype.mouseWheelHortEvent=function(r,e){var a=this._chart.getChartStore();return a.startScroll(),a.scroll(e),!0},t.prototype.mouseWheelVertEvent=function(r,e){var a=this._findWidgetByEvent(r).widget,i=this._makeWidgetEvent(r,a),n=a==null?void 0:a.getName();return n===Nt.MAIN?(this._chart.getChartStore().zoom(e,{x:i.x,y:i.y}),!0):!1},t.prototype.mouseDownEvent=function(r){var e=this._findWidgetByEvent(r),a=e.pane,i=e.widget;if(this._mouseDownWidget=i,i!==null){var n=this._makeWidgetEvent(r,i),o=i.getName();switch(o){case Nt.SEPARATOR:return i.dispatchEvent("mouseDownEvent",n);case Nt.MAIN:{var s=a.getAxisComponent();if(!s.getAutoCalcTickFlag()){var l=s.getRange();this._prevYAxisRange=J({},l)}return this._startScrollCoordinate={x:n.x,y:n.y},this._chart.getChartStore().startScroll(),i.dispatchEvent("mouseDownEvent",n)}case Nt.X_AXIS:return this._processXAxisScrollStartEvent(i,n);case Nt.Y_AXIS:return this._processYAxisScaleStartEvent(i,n)}}return!1},t.prototype.mouseMoveEvent=function(r){var e,a,i,n=this._findWidgetByEvent(r),o=n.pane,s=n.widget,l=this._makeWidgetEvent(r,s);if((((e=this._mouseMoveTriggerWidgetInfo.pane)===null||e===void 0?void 0:e.getId())!==(o==null?void 0:o.getId())||((a=this._mouseMoveTriggerWidgetInfo.widget)===null||a===void 0?void 0:a.getName())!==(s==null?void 0:s.getName()))&&(s==null||s.dispatchEvent("mouseEnterEvent",l),(i=this._mouseMoveTriggerWidgetInfo.widget)===null||i===void 0||i.dispatchEvent("mouseLeaveEvent",l),this._mouseMoveTriggerWidgetInfo={pane:o,widget:s}),s!==null){var c=s.getName();switch(c){case Nt.MAIN:{var u=s.dispatchEvent("mouseMoveEvent",l),d={x:l.x,y:l.y,paneId:o==null?void 0:o.getId()};return u?(s.getForceCursor()!=="pointer"&&(d=void 0),s.setCursor("pointer")):s.setCursor("crosshair"),this._chart.getChartStore().setCrosshair(d),u}case Nt.SEPARATOR:case Nt.X_AXIS:case Nt.Y_AXIS:{var u=s.dispatchEvent("mouseMoveEvent",l);return this._chart.getChartStore().setCrosshair(),u}}}return!1},t.prototype.pressedMouseMoveEvent=function(r){var e,a;if(this._mouseDownWidget!==null&&this._mouseDownWidget.getName()===Nt.SEPARATOR)return this._mouseDownWidget.dispatchEvent("pressedMouseMoveEvent",r);var i=this._findWidgetByEvent(r),n=i.pane,o=i.widget;if(o!==null&&((e=this._mouseDownWidget)===null||e===void 0?void 0:e.getPane().getId())===(n==null?void 0:n.getId())&&((a=this._mouseDownWidget)===null||a===void 0?void 0:a.getName())===o.getName()){var s=this._makeWidgetEvent(r,o),l=o.getName();switch(l){case Nt.MAIN:{var c=void 0,u=o.dispatchEvent("pressedMouseMoveEvent",s);return u||this._processMainScrollingEvent(o,s),(!u||o.getForceCursor()==="pointer")&&(c={x:s.x,y:s.y,paneId:n==null?void 0:n.getId()}),this._chart.getChartStore().setCrosshair(c,{forceInvalidate:!0}),u}case Nt.X_AXIS:return this._processXAxisScrollingEvent(o,s);case Nt.Y_AXIS:return this._processYAxisScalingEvent(o,s)}}return!1},t.prototype.mouseUpEvent=function(r){var e=this._findWidgetByEvent(r).widget,a=!1;if(e!==null){var i=this._makeWidgetEvent(r,e),n=e.getName();switch(n){case Nt.MAIN:case Nt.SEPARATOR:case Nt.X_AXIS:case Nt.Y_AXIS:{a=e.dispatchEvent("mouseUpEvent",i);break}}a&&this._chart.updatePane(1)}return this._mouseDownWidget=null,this._startScrollCoordinate=null,this._prevYAxisRange=null,this._xAxisStartScaleCoordinate=null,this._xAxisStartScaleDistance=0,this._xAxisScale=1,this._yAxisStartScaleDistance=0,a},t.prototype.mouseClickEvent=function(r){var e=this._findWidgetByEvent(r).widget;if(e!==null){var a=this._makeWidgetEvent(r,e);return e.dispatchEvent("mouseClickEvent",a)}return!1},t.prototype.mouseRightClickEvent=function(r){var e=this._findWidgetByEvent(r).widget,a=!1;if(e!==null){var i=this._makeWidgetEvent(r,e),n=e.getName();switch(n){case Nt.MAIN:case Nt.X_AXIS:case Nt.Y_AXIS:{a=e.dispatchEvent("mouseRightClickEvent",i);break}}a&&this._chart.updatePane(1)}return!1},t.prototype.mouseDoubleClickEvent=function(r){var e=this._findWidgetByEvent(r),a=e.pane,i=e.widget;if(i!==null){var n=i.getName();switch(n){case Nt.MAIN:{var o=this._makeWidgetEvent(r,i);return i.dispatchEvent("mouseDoubleClickEvent",o)}case Nt.Y_AXIS:{var s=a.getAxisComponent();if(!s.getAutoCalcTickFlag())return s.setAutoCalcTickFlag(!0),this._chart.layout({measureWidth:!0,update:!0,buildYAxisTick:!0}),!0;break}}}return!1},t.prototype.mouseLeaveEvent=function(){return this._chart.getChartStore().setCrosshair(),!0},t.prototype.touchStartEvent=function(r){var e,a=this._findWidgetByEvent(r),i=a.pane,n=a.widget;if(n!==null){var o=this._makeWidgetEvent(r,n);(e=o.preventDefault)===null||e===void 0||e.call(o);var s=n.getName();switch(s){case Nt.MAIN:{var l=this._chart.getChartStore();if(n.dispatchEvent("mouseDownEvent",o))return this._touchCancelCrosshair=!0,this._touchCoordinate=null,l.setCrosshair(void 0,{notInvalidate:!0}),this._chart.updatePane(1),!0;this._flingScrollRequestId!==null&&(Zi(this._flingScrollRequestId),this._flingScrollRequestId=null),this._flingStartTime=new Date().getTime();var c=i.getAxisComponent();if(!c.getAutoCalcTickFlag()){var u=c.getRange();this._prevYAxisRange=J({},u)}if(this._startScrollCoordinate={x:o.x,y:o.y},l.startScroll(),this._touchZoomed=!1,this._touchCoordinate!==null){var d=o.x-this._touchCoordinate.x,h=o.y-this._touchCoordinate.y,v=Math.sqrt(d*d+h*h);v<L0?(this._touchCoordinate={x:o.x,y:o.y},l.setCrosshair({x:o.x,y:o.y,paneId:i==null?void 0:i.getId()})):(this._touchCoordinate=null,this._touchCancelCrosshair=!0,l.setCrosshair())}return!0}case Nt.X_AXIS:return this._processXAxisScrollStartEvent(n,o);case Nt.Y_AXIS:return this._processYAxisScaleStartEvent(n,o)}}return!1},t.prototype.touchMoveEvent=function(r){var e,a=this._findWidgetByEvent(r),i=a.pane,n=a.widget;if(n!==null){var o=this._makeWidgetEvent(r,n);(e=o.preventDefault)===null||e===void 0||e.call(o);var s=n.getName(),l=this._chart.getChartStore();switch(s){case Nt.MAIN:return n.dispatchEvent("pressedMouseMoveEvent",o)?(l.setCrosshair(void 0,{notInvalidate:!0}),this._chart.updatePane(1),!0):(this._touchCoordinate!==null?l.setCrosshair({x:o.x,y:o.y,paneId:i==null?void 0:i.getId()}):this._processMainScrollingEvent(n,o),!0);case Nt.X_AXIS:return this._processXAxisScrollingEvent(n,o);case Nt.Y_AXIS:return this._processYAxisScalingEvent(n,o)}}return!1},t.prototype.touchEndEvent=function(r){var e=this,a=this._findWidgetByEvent(r).widget;if(a!==null){var i=this._makeWidgetEvent(r,a),n=a.getName();switch(n){case Nt.MAIN:{if(a.dispatchEvent("mouseUpEvent",i),this._startScrollCoordinate!==null){var o=new Date().getTime()-this._flingStartTime,s=i.x-this._startScrollCoordinate.x,l=s/(o>0?o:1)*20;if(o<200&&Math.abs(l)>0){var c=this._chart.getChartStore(),u=function(){e._flingScrollRequestId=ya(function(){c.startScroll(),c.scroll(l),l=l*(1-.025),Math.abs(l)<1?e._flingScrollRequestId!==null&&(Zi(e._flingScrollRequestId),e._flingScrollRequestId=null):u()})};u()}}return!0}case Nt.X_AXIS:case Nt.Y_AXIS:{var d=a.dispatchEvent("mouseUpEvent",i);d&&this._chart.updatePane(1)}}this._startScrollCoordinate=null,this._prevYAxisRange=null,this._xAxisStartScaleCoordinate=null,this._xAxisStartScaleDistance=0,this._xAxisScale=1,this._yAxisStartScaleDistance=0}return!1},t.prototype.tapEvent=function(r){var e=this._findWidgetByEvent(r),a=e.pane,i=e.widget,n=!1;if(i!==null){var o=this._makeWidgetEvent(r,i),s=i.dispatchEvent("mouseClickEvent",o);if(i.getName()===Nt.MAIN){var l=this._makeWidgetEvent(r,i),c=this._chart.getChartStore();s?(this._touchCancelCrosshair=!0,this._touchCoordinate=null,c.setCrosshair(void 0,{notInvalidate:!0}),n=!0):(!this._touchCancelCrosshair&&!this._touchZoomed&&(this._touchCoordinate={x:l.x,y:l.y},c.setCrosshair({x:l.x,y:l.y,paneId:a==null?void 0:a.getId()},{notInvalidate:!0}),n=!0),this._touchCancelCrosshair=!1)}(n||s)&&this._chart.updatePane(1)}return n},t.prototype.doubleTapEvent=function(r){return this.mouseDoubleClickEvent(r)},t.prototype.longTapEvent=function(r){var e=this._findWidgetByEvent(r),a=e.pane,i=e.widget;if(i!==null&&i.getName()===Nt.MAIN){var n=this._makeWidgetEvent(r,i);return this._touchCoordinate={x:n.x,y:n.y},this._chart.getChartStore().setCrosshair({x:n.x,y:n.y,paneId:a==null?void 0:a.getId()}),!0}return!1},t.prototype._processMainScrollingEvent=function(r,e){if(this._startScrollCoordinate!==null){var a=r.getPane().getAxisComponent();if(this._prevYAxisRange!==null&&!a.getAutoCalcTickFlag()&&a.scrollZoomEnabled){var i=this._prevYAxisRange,n=i.from,o=i.to,s=i.range,l=0;a.reverse?l=this._startScrollCoordinate.y-e.y:l=e.y-this._startScrollCoordinate.y;var c=r.getBounding(),u=l/c.height,d=s*u,h=n+d,v=o+d,f=a.valueToRealValue(h,{range:this._prevYAxisRange}),p=a.valueToRealValue(v,{range:this._prevYAxisRange}),g=a.realValueToDisplayValue(f,{range:this._prevYAxisRange}),m=a.realValueToDisplayValue(p,{range:this._prevYAxisRange});a.setRange({from:h,to:v,range:v-h,realFrom:f,realTo:p,realRange:p-f,displayFrom:g,displayTo:m,displayRange:m-g})}var _=e.x-this._startScrollCoordinate.x;this._chart.getChartStore().scroll(_)}},t.prototype._processXAxisScrollStartEvent=function(r,e){var a=r.dispatchEvent("mouseDownEvent",e);return a&&this._chart.updatePane(1),this._xAxisStartScaleCoordinate={x:e.x,y:e.y},this._xAxisStartScaleDistance=e.pageX,a},t.prototype._processXAxisScrollingEvent=function(r,e){var a,i=r.dispatchEvent("pressedMouseMoveEvent",e);if(i)this._chart.updatePane(1);else{var n=r.getPane().getAxisComponent();if(n.scrollZoomEnabled&&this._xAxisStartScaleDistance!==0){var o=this._xAxisStartScaleDistance/e.pageX;if(Number.isFinite(o)){var s=(o-this._xAxisScale)*10;this._xAxisScale=o,this._chart.getChartStore().zoom(s,(a=this._xAxisStartScaleCoordinate)!==null&&a!==void 0?a:void 0)}}}return i},t.prototype._processYAxisScaleStartEvent=function(r,e){var a=r.dispatchEvent("mouseDownEvent",e);a&&this._chart.updatePane(1);var i=r.getPane().getAxisComponent().getRange();return this._prevYAxisRange=J({},i),this._yAxisStartScaleDistance=e.pageY,a},t.prototype._processYAxisScalingEvent=function(r,e){var a=r.dispatchEvent("pressedMouseMoveEvent",e);if(a)this._chart.updatePane(1);else{var i=r.getPane().getAxisComponent();if(this._prevYAxisRange!==null&&i.scrollZoomEnabled&&this._yAxisStartScaleDistance!==0){var n=this._prevYAxisRange,o=n.from,s=n.to,l=n.range,c=e.pageY/this._yAxisStartScaleDistance,u=l*c,d=(u-l)/2,h=o-d,v=s+d,f=i.valueToRealValue(h,{range:this._prevYAxisRange}),p=i.valueToRealValue(v,{range:this._prevYAxisRange}),g=i.realValueToDisplayValue(f,{range:this._prevYAxisRange}),m=i.realValueToDisplayValue(p,{range:this._prevYAxisRange});i.setRange({from:h,to:v,range:u,realFrom:f,realTo:p,realRange:p-f,displayFrom:g,displayTo:m,displayRange:m-g}),this._chart.layout({measureWidth:!0,update:!0,buildYAxisTick:!0})}}return a},t.prototype._findWidgetByEvent=function(r){var e,a,i,n,o=r.x,s=r.y,l=this._chart.getSeparatorPanes(),c=this._chart.getStyles().separator.size;try{for(var u=Xe(l),d=u.next();!d.done;d=u.next()){var h=d.value,v=h[1],f=v.getBounding(),p=f.top-Math.round((Kr-c)/2);if(o>=f.left&&o<=f.left+f.width&&s>=p&&s<=p+Kr)return{pane:v,widget:v.getWidget()}}}catch(I){e={error:I}}finally{try{d&&!d.done&&(a=u.return)&&a.call(u)}finally{if(e)throw e.error}}var g=this._chart.getDrawPanes(),m=null;try{for(var _=Xe(g),x=_.next();!x.done;x=_.next()){var S=x.value,f=S.getBounding();if(o>=f.left&&o<=f.left+f.width&&s>=f.top&&s<=f.top+f.height){m=S;break}}}catch(I){i={error:I}}finally{try{x&&!x.done&&(n=_.return)&&n.call(_)}finally{if(i)throw i.error}}var y=null;if(m!==null){if(!R(y)){var k=m.getMainWidget(),C=k.getBounding();o>=C.left&&o<=C.left+C.width&&s>=C.top&&s<=C.top+C.height&&(y=k)}if(!R(y)){var D=m.getYAxisWidget();if(D!==null){var E=D.getBounding();o>=E.left&&o<=E.left+E.width&&s>=E.top&&s<=E.top+E.height&&(y=D)}}}return{pane:m,widget:y}},t.prototype._makeWidgetEvent=function(r,e){var a,i,n,o=(a=e==null?void 0:e.getBounding())!==null&&a!==void 0?a:null;return J(J({},r),{x:r.x-((i=o==null?void 0:o.left)!==null&&i!==void 0?i:0),y:r.y-((n=o==null?void 0:o.top)!==null&&n!==void 0?n:0)})},t.prototype.destroy=function(){this._container.removeEventListener("keydown",this._boundKeyBoardDownEvent),this._event.destroy()},t}(),No=function(){function t(r,e){this._chartBounding=ma(),this._drawPanes=[],this._separatorPanes=new Map,this._layoutOptions={sort:!0,measureHeight:!0,measureWidth:!0,update:!0,buildYAxisTick:!1,cacheYAxisWidth:!1,forceBuildYAxisTick:!1},this._layoutPending=!1,this._cacheYAxisWidth={left:0,right:0},this._initContainer(r),this._chartEvent=new k0(this._chartContainer,this),this._chartStore=new w1(this,e),this._initPanes(e),this._layout()}return t.prototype._initContainer=function(r){this._container=r,this._chartContainer=sr("div",{position:"relative",width:"100%",height:"100%",outline:"none",borderStyle:"none",cursor:"crosshair",boxSizing:"border-box",userSelect:"none",webkitUserSelect:"none",overflow:"hidden",msUserSelect:"none",MozUserSelect:"none",webkitTapHighlightColor:"transparent"}),this._chartContainer.tabIndex=1,r.appendChild(this._chartContainer),this._cacheChartBounding()},t.prototype._cacheChartBounding=function(){this._chartBounding.width=Math.floor(this._chartContainer.clientWidth),this._chartBounding.height=Math.floor(this._chartContainer.clientHeight)},t.prototype._initPanes=function(r){var e=this,a,i=(a=r==null?void 0:r.layout)!==null&&a!==void 0?a:[{type:"candle"}],n=function(s){var l,c;if(!R(e._candlePane)){var u=(l=s.options)!==null&&l!==void 0?l:{};ye(u,{id:xt.CANDLE}),e._candlePane=e._createPane(h0,xt.CANDLE,u);var d=(c=s.content)!==null&&c!==void 0?c:[];d.forEach(function(h){e.createIndicator(h,!0,u)})}},o=function(s){if(!R(e._xAxisPane)){var l=e._createPane(C0,xt.X_AXIS,s??{});e._xAxisPane=l}};i.forEach(function(s){var l,c,u;switch(s.type){case"candle":{n(s);break}case"indicator":{var d=(l=s.content)!==null&&l!==void 0?l:[];if(d.length>0){var h=(u=(c=s.options)===null||c===void 0?void 0:c.id)!==null&&u!==void 0?u:null;R(h)&&(h=ua(xt.INDICATOR));var v=J(J({},s.options),{id:h});d.forEach(function(f){e.createIndicator(f,!0,v)})}break}case"xAxis":{o(s.options);break}}}),n({}),o({order:Number.MAX_SAFE_INTEGER})},t.prototype._createPane=function(r,e,a){var i=new r(this,e,a??{});return this._drawPanes.push(i),i},t.prototype._recalculatePaneHeight=function(r,e,a){if(a===0)return!1;var i=this._drawPanes.filter(function(d){var h=d.getId();return d.getOptions().state==="normal"&&h!==r.getId()&&h!==xt.X_AXIS}),n=i.length;if(n===0)return!1;if(r.getId()!==xt.CANDLE&&R(this._candlePane)&&this._candlePane.getOptions().state==="normal"){var o=this._candlePane.getBounding().height;if(o>0){var s=this._candlePane.getOptions().minHeight,l=o+a;l<s&&(l=s,e-=o+a-l),this._candlePane.setBounding({height:l})}}else{var c=a,u=Math.floor(a/n);i.forEach(function(d,h){var v=d.getBounding().height,f=0;h===n-1?f=v+c:f=v+u,f<d.getOptions().minHeight&&(f=d.getOptions().minHeight),d.setBounding({height:f}),c-=f-v}),Math.abs(c)>0&&(e-=c)}return r.setBounding({height:e}),!0},t.prototype.getDrawPaneById=function(r){if(r===xt.CANDLE)return this._candlePane;if(r===xt.X_AXIS)return this._xAxisPane;var e=this._drawPanes.find(function(a){return a.getId()===r});return e??null},t.prototype.getContainer=function(){return this._container},t.prototype.getChartStore=function(){return this._chartStore},t.prototype.getXAxisPane=function(){return this._xAxisPane},t.prototype.getDrawPanes=function(){return this._drawPanes},t.prototype.getSeparatorPanes=function(){return this._separatorPanes},t.prototype.layout=function(r){var e=this,a,i,n,o,s,l,c;(a=r.sort)!==null&&a!==void 0&&a&&(this._layoutOptions.sort=r.sort),(i=r.measureHeight)!==null&&i!==void 0&&i&&(this._layoutOptions.measureHeight=r.measureHeight),(n=r.measureWidth)!==null&&n!==void 0&&n&&(this._layoutOptions.measureWidth=r.measureWidth),(o=r.update)!==null&&o!==void 0&&o&&(this._layoutOptions.update=r.update),(s=r.buildYAxisTick)!==null&&s!==void 0&&s&&(this._layoutOptions.buildYAxisTick=r.buildYAxisTick),(l=r.cacheYAxisWidth)!==null&&l!==void 0&&l&&(this._layoutOptions.cacheYAxisWidth=r.cacheYAxisWidth),(c=r.buildYAxisTick)!==null&&c!==void 0&&c&&(this._layoutOptions.forceBuildYAxisTick=r.forceBuildYAxisTick),this._layoutPending||(this._layoutPending=!0,Promise.resolve().then(function(u){e._layout(),e._layoutPending=!1}).catch(function(u){}))},t.prototype._layout=function(){var r=this,e=this._layoutOptions,a=e.sort,i=e.measureHeight,n=e.measureWidth,o=e.update,s=e.buildYAxisTick,l=e.cacheYAxisWidth,c=e.forceBuildYAxisTick;if(a){for(;R(this._chartContainer.firstChild);)this._chartContainer.removeChild(this._chartContainer.firstChild);this._separatorPanes.clear(),this._drawPanes.sort(function(P,N){return P.getOptions().order-N.getOptions().order});var u=null;this._drawPanes.forEach(function(P){if(P.getId()!==xt.X_AXIS){if(R(u)){var N=new w0(r,"",u,P);r._chartContainer.appendChild(N.getContainer()),r._separatorPanes.set(P,N)}u=P}r._chartContainer.appendChild(P.getContainer())})}if(i){var d=this._chartBounding.height,h=this.getStyles().separator.size,v=this._xAxisPane.getAxisComponent().getAutoSize(),f=d-v;f<0&&(f=0),this._drawPanes.forEach(function(P){var N=P.getId();if(R(r._separatorPanes.get(P))&&(f-=h),N!==xt.X_AXIS&&N!==xt.CANDLE&&P.getVisible()){var W=P.getBounding().height;W>f?(W=f,f=0):f-=W,P.setBounding({height:W})}}),this._candlePane.setBounding({height:Math.max(f,0)}),this._xAxisPane.setBounding({height:v});var p=0;this._drawPanes.forEach(function(P){var N=r._separatorPanes.get(P);R(N)&&(N.setBounding({height:h,top:p}),p+=h),P.setBounding({top:p}),p+=P.getBounding().height})}var g=n;if((s||c)&&this._drawPanes.forEach(function(P){var N=P.getAxisComponent().buildTicks(c);g||(g=N)}),g){var m=this._chartBounding.width,_=this.getStyles(),x=0,S=!0,y=0,k=!0;this._drawPanes.forEach(function(P){if(P.getId()!==xt.X_AXIS){var N=P.getAxisComponent(),W=N.inside,X=N.getAutoSize();N.position==="left"?(x=Math.max(x,X),W&&(S=!1)):(y=Math.max(y,X),W&&(k=!1))}}),l&&(x=Math.max(this._cacheYAxisWidth.left,x),y=Math.max(this._cacheYAxisWidth.right,y)),this._cacheYAxisWidth.left=x,this._cacheYAxisWidth.right=y;var C=m,D=0,E=0;S&&(C-=x,D=x),k&&(C-=y,E=y),this._chartStore.setTotalBarSpace(C);var I={width:m},T={width:C,left:D,right:E},L={width:x},b={width:y},M=_.separator.fill,A={};M?A=I:A=T,this._drawPanes.forEach(function(P){var N;(N=r._separatorPanes.get(P))===null||N===void 0||N.setBounding(A),P.setBounding(I,T,L,b)})}o&&(this._xAxisPane.getAxisComponent().buildTicks(!0),this.updatePane(4)),this._layoutOptions={sort:!1,measureHeight:!1,measureWidth:!1,update:!1,buildYAxisTick:!1,cacheYAxisWidth:!1,forceBuildYAxisTick:!1}},t.prototype.updatePane=function(r,e){var a=this;if(R(e)){var i=this.getDrawPaneById(e);i==null||i.update(r)}else this._drawPanes.forEach(function(n){var o;n.update(r),(o=a._separatorPanes.get(n))===null||o===void 0||o.update(r)})},t.prototype.crosshairChange=function(r){var e=this;if(this._chartStore.hasAction("onCrosshairChange")){var a={};this._drawPanes.forEach(function(i){var n=i.getId(),o={},s=e._chartStore.getIndicatorsByPaneId(n);s.forEach(function(l){var c,u=l.result;o[l.name]=u[(c=r.dataIndex)!==null&&c!==void 0?c:u.length-1]}),a[n]=o}),Kt(r.paneId)&&this._chartStore.executeAction("onCrosshairChange",{crosshair:r,indicatorData:a})}},t.prototype.getDom=function(r,e){var a,i;if(R(r)){var n=this.getDrawPaneById(r);if(R(n)){var o=e??"root";switch(o){case"root":return n.getContainer();case"main":return n.getMainWidget().getContainer();case"yAxis":return(i=(a=n.getYAxisWidget())===null||a===void 0?void 0:a.getContainer())!==null&&i!==void 0?i:null}}}else return this._chartContainer;return null},t.prototype.getSize=function(r,e){var a,i;if(R(r)){var n=this.getDrawPaneById(r);if(R(n)){var o=e??"root";switch(o){case"root":return n.getBounding();case"main":return n.getMainWidget().getBounding();case"yAxis":return(i=(a=n.getYAxisWidget())===null||a===void 0?void 0:a.getBounding())!==null&&i!==void 0?i:null}}}else return this._chartBounding;return null},t.prototype.setSymbol=function(r){this._chartStore.setSymbol(r)},t.prototype.getSymbol=function(){return this._chartStore.getSymbol()},t.prototype.setPeriod=function(r){this._chartStore.setPeriod(r)},t.prototype.getPeriod=function(){return this._chartStore.getPeriod()},t.prototype.setStyles=function(r){var e=this;this._setOptions(function(){e._chartStore.setStyles(r)})},t.prototype.getStyles=function(){return this._chartStore.getStyles()},t.prototype.setFormatter=function(r){var e=this;this._setOptions(function(){e._chartStore.setFormatter(r)})},t.prototype.getFormatter=function(){return this._chartStore.getFormatter()},t.prototype.setLocale=function(r){var e=this;this._setOptions(function(){e._chartStore.setLocale(r)})},t.prototype.getLocale=function(){return this._chartStore.getLocale()},t.prototype.setTimezone=function(r){var e=this;this._setOptions(function(){e._chartStore.setTimezone(r)})},t.prototype.getTimezone=function(){return this._chartStore.getTimezone()},t.prototype.setThousandsSeparator=function(r){var e=this;this._setOptions(function(){e._chartStore.setThousandsSeparator(r)})},t.prototype.getThousandsSeparator=function(){return this._chartStore.getThousandsSeparator()},t.prototype.setDecimalFold=function(r){var e=this;this._setOptions(function(){e._chartStore.setDecimalFold(r)})},t.prototype.getDecimalFold=function(){return this._chartStore.getDecimalFold()},t.prototype._setOptions=function(r){r(),this.layout({measureHeight:!0,measureWidth:!0,update:!0,buildYAxisTick:!0,forceBuildYAxisTick:!0})},t.prototype.setOffsetRightDistance=function(r){this._chartStore.setOffsetRightDistance(r,!0)},t.prototype.getOffsetRightDistance=function(){return this._chartStore.getOffsetRightDistance()},t.prototype.setMaxOffsetLeftDistance=function(r){r<0||this._chartStore.setMaxOffsetLeftDistance(r)},t.prototype.setMaxOffsetRightDistance=function(r){r<0||this._chartStore.setMaxOffsetRightDistance(r)},t.prototype.setLeftMinVisibleBarCount=function(r){r<0||this._chartStore.setLeftMinVisibleBarCount(Math.ceil(r))},t.prototype.setRightMinVisibleBarCount=function(r){r<0||this._chartStore.setRightMinVisibleBarCount(Math.ceil(r))},t.prototype.setBarSpace=function(r){this._chartStore.setBarSpace(r)},t.prototype.getBarSpace=function(){return this._chartStore.getBarSpace()},t.prototype.getVisibleRange=function(){return this._chartStore.getVisibleRange()},t.prototype.resetData=function(){this._chartStore.resetData()},t.prototype.getDataList=function(){return this._chartStore.getDataList()},t.prototype.applyNewData=function(r,e){this._chartStore.applyNewData(r,e)},t.prototype.updateData=function(r){this._chartStore.updateData(r)},t.prototype.setDataLoader=function(r){this._chartStore.setDataLoader(r)},t.prototype.createIndicator=function(r,e,a){var i,n=Kt(r)?{name:r}:r;if(uo(n.name)===null)return null;var o=a??{};Kt(o.id)||(o.id=ua(xt.INDICATOR)),Kt(n.id)||(n.id=ua(n.name));var s=this._chartStore.addIndicator(n,o.id,e??!1);if(s){var l=!1;return R(this.getDrawPaneById(o.id))||(this._createPane(Bo,o.id,o),(i=o.height)!==null&&i!==void 0||(o.height=mo),l=!0),this.setPaneOptions(o),this.layout({sort:l,measureHeight:!0,measureWidth:!0,update:!0,buildYAxisTick:!0,forceBuildYAxisTick:!0}),n.id}return null},t.prototype.overrideIndicator=function(r){return this._chartStore.overrideIndicator(r)},t.prototype.getIndicators=function(r){return this._chartStore.getIndicatorsByFilter(r??{})},t.prototype.removeIndicator=function(r){var e=this,a=this._chartStore.removeIndicator(r??{});if(a){var i=!1,n=[];this._drawPanes.forEach(function(o){var s=o.getId();s!==xt.CANDLE&&s!==xt.X_AXIS&&n.push(s)}),n.forEach(function(o){if(!e._chartStore.hasIndicators(o)){var s=e._drawPanes.findIndex(function(c){return c.getId()===o}),l=e._drawPanes[s];R(l)&&(i=!0,e._recalculatePaneHeight(l,0,l.getBounding().height),e._drawPanes.splice(s,1),l.destroy())}}),this._drawPanes.length===2&&(this._candlePane.setVisible(!0),this._candlePane.setBounding({height:this._chartBounding.height-this._xAxisPane.getBounding().height})),this.layout({sort:i,measureHeight:i,measureWidth:!0,update:!0,buildYAxisTick:!0,forceBuildYAxisTick:!0})}return a},t.prototype.createOverlay=function(r){var e=this,a=[],i=[],n=function(s){!R(s.paneId)||e.getDrawPaneById(s.paneId)===null?(s.paneId=xt.CANDLE,i.push(!1)):i.push(!0),a.push(s)};Kt(r)?n({name:r}):Ue(r)?r.forEach(function(s){var l=null;Kt(s)?l={name:s}:l=s,n(l)}):n(r);var o=this._chartStore.addOverlays(a,i);return Ue(r)?o:o[0]},t.prototype.getOverlays=function(r){return this._chartStore.getOverlaysByFilter(r??{})},t.prototype.overrideOverlay=function(r){return this._chartStore.overrideOverlay(r)},t.prototype.removeOverlay=function(r){return this._chartStore.removeOverlay(r??{})},t.prototype.setPaneOptions=function(r){var e,a,i=this,n,o=!1,s=!1,l=R(r.id),c=function(p){var g=p.getId();if(l&&r.id===g||!l){if(g!==xt.X_AXIS){if(vt(r.height)&&r.height>0){var m=Math.max((n=r.minHeight)!==null&&n!==void 0?n:p.getOptions().minHeight,0),_=Math.max(m,r.height);s=!0,o=!0,p.setOriginalBounding({height:_}),u._recalculatePaneHeight(p,_,-_)}if(R(r.state)&&p.getOptions().state!==r.state){o=!0,s=!0;var x=r.state;switch(x){case"maximize":{var S=u._drawPanes.find(function(E){var I=E.getId();return E.getOptions().state==="maximize"&&I!==xt.X_AXIS});if(!R(S)){p.getOptions().state==="normal"&&p.setOriginalBounding({height:p.getBounding().height}),p.setOptions({state:x});var y=u._chartBounding.height;p.setBounding({height:y-u._xAxisPane.getBounding().height}),u._drawPanes.forEach(function(E){var I;E.getId()!==xt.X_AXIS&&E.getId()!==g&&(E.setBounding({height:E.getOriginalBounding().height}),E.setVisible(!1),(I=i._separatorPanes.get(E))===null||I===void 0||I.setVisible(!1))})}break}case"minimize":{var _=p.getBounding().height,k=p.getOptions().state,C=_-da;k==="maximize"&&(C=p.getOriginalBounding().height-da),u._recalculatePaneHeight(p,da,C)&&(k==="normal"&&p.setOriginalBounding({height:_}),p.setOptions({state:x})),u._drawPanes.forEach(function(I){var T;I.getId()!==xt.X_AXIS&&(I.setVisible(!0),(T=i._separatorPanes.get(I))===null||T===void 0||T.setVisible(!0))});break}default:{var _=p.getOriginalBounding().height;u._recalculatePaneHeight(p,_,p.getBounding().height-_)&&p.setOptions({state:x}),u._drawPanes.forEach(function(I){var T;I.getId()!==xt.X_AXIS&&(I.setVisible(!0),(T=i._separatorPanes.get(I))===null||T===void 0||T.setVisible(!0))});break}}}}R(r.axis)&&(s=!0);var D=J({},r);if(delete D.state,p.setOptions(D),g===r.id)return"break"}},u=this;try{for(var d=Xe(this._drawPanes),h=d.next();!h.done;h=d.next()){var v=h.value,f=c(v);if(f==="break")break}}catch(p){e={error:p}}finally{try{h&&!h.done&&(a=d.return)&&a.call(d)}finally{if(e)throw e.error}}s&&this.layout({measureHeight:o,measureWidth:!0,update:!0,buildYAxisTick:!0,forceBuildYAxisTick:!0})},t.prototype.getPaneOptions=function(r){var e;if(R(r)){var a=this.getDrawPaneById(r);return(e=a==null?void 0:a.getOptions())!==null&&e!==void 0?e:null}return this._drawPanes.map(function(i){return i.getOptions()})},t.prototype.setZoomEnabled=function(r){this._chartStore.setZoomEnabled(r)},t.prototype.isZoomEnabled=function(){return this._chartStore.isZoomEnabled()},t.prototype.setScrollEnabled=function(r){this._chartStore.setScrollEnabled(r)},t.prototype.isScrollEnabled=function(){return this._chartStore.isScrollEnabled()},t.prototype.scrollByDistance=function(r,e){var a=this,i=vt(e)&&e>0?e:0;if(this._chartStore.startScroll(),i>0){var n=new Ja({duration:i});n.doFrame(function(o){var s=r*(o/i);a._chartStore.scroll(s)}),n.start()}else this._chartStore.scroll(r)},t.prototype.scrollToRealTime=function(r){var e=this._chartStore.getBarSpace().bar,a=this._chartStore.getLastBarRightSideDiffBarCount()-this._chartStore.getInitialOffsetRightDistance()/e,i=a*e;this.scrollByDistance(i,r)},t.prototype.scrollToDataIndex=function(r,e){var a=(this._chartStore.getLastBarRightSideDiffBarCount()+(this.getDataList().length-1-r))*this._chartStore.getBarSpace().bar;this.scrollByDistance(a,e)},t.prototype.scrollToTimestamp=function(r,e){var a=ti(this.getDataList(),"timestamp",r);this.scrollToDataIndex(a,e)},t.prototype.zoomAtCoordinate=function(r,e,a){var i=this,n=vt(a)&&a>0?a:0,o=this._chartStore.getBarSpace().bar,s=o*r,l=s-o;if(n>0){var c=0,u=new Ja({duration:n});u.doFrame(function(d){var h=l*(d/n),v=(h-c)/i._chartStore.getBarSpace().bar*ei;i._chartStore.zoom(v,e),c=h}),u.start()}else this._chartStore.zoom(l/o*ei,e)},t.prototype.zoomAtDataIndex=function(r,e,a){var i=this._chartStore.dataIndexToCoordinate(e);this.zoomAtCoordinate(r,{x:i,y:0},a)},t.prototype.zoomAtTimestamp=function(r,e,a){var i=ti(this.getDataList(),"timestamp",e);this.zoomAtDataIndex(r,i,a)},t.prototype.convertToPixel=function(r,e){var a=this,i,n=e??{},o=n.paneId,s=o===void 0?xt.CANDLE:o,l=n.absolute,c=l===void 0?!1:l,u=[];if(s!==xt.X_AXIS){var d=this.getDrawPaneById(s);if(d!==null){var h=d.getBounding(),v=[].concat(r),f=this._xAxisPane.getAxisComponent(),p=d.getAxisComponent();u=v.map(function(g){var m={},_=g.dataIndex;if(vt(g.timestamp)&&(_=a._chartStore.timestampToDataIndex(g.timestamp)),vt(_)&&(m.x=f.convertToPixel(_)),vt(g.value)){var x=p.convertToPixel(g.value);m.y=c?h.top+x:x}return m})}}return Ue(r)?u:(i=u[0])!==null&&i!==void 0?i:{}},t.prototype.convertFromPixel=function(r,e){var a=this,i,n=e??{},o=n.paneId,s=o===void 0?xt.CANDLE:o,l=n.absolute,c=l===void 0?!1:l,u=[];if(s!==xt.X_AXIS){var d=this.getDrawPaneById(s);if(d!==null){var h=d.getBounding(),v=[].concat(r),f=this._xAxisPane.getAxisComponent(),p=d.getAxisComponent();u=v.map(function(g){var m,_={};if(vt(g.x)){var x=f.convertFromPixel(g.x);_.dataIndex=x,_.timestamp=(m=a._chartStore.dataIndexToTimestamp(x))!==null&&m!==void 0?m:void 0}if(vt(g.y)){var S=c?g.y-h.top:g.y;_.value=p.convertFromPixel(S)}return _})}}return Ue(r)?u:(i=u[0])!==null&&i!==void 0?i:{}},t.prototype.executeAction=function(r,e){var a;switch(r){case"onCrosshairChange":{var i=J({},e);(a=i.paneId)!==null&&a!==void 0||(i.paneId=xt.CANDLE),this._chartStore.setCrosshair(i,{notExecuteAction:!0});break}}},t.prototype.subscribeAction=function(r,e){this._chartStore.subscribeAction(r,e)},t.prototype.unsubscribeAction=function(r,e){this._chartStore.unsubscribeAction(r,e)},t.prototype.getConvertPictureUrl=function(r,e,a){var i=this,n=this._chartBounding,o=n.width,s=n.height,l=sr("canvas",{width:"".concat(o,"px"),height:"".concat(s,"px"),boxSizing:"border-box"}),c=l.getContext("2d"),u=vr(l);l.width=o*u,l.height=s*u,c.scale(u,u),c.fillStyle=a??"#FFFFFF",c.fillRect(0,0,o,s);var d=r??!1;return this._drawPanes.forEach(function(h){var v=i._separatorPanes.get(h);if(R(v)){var f=v.getBounding();c.drawImage(v.getImage(d),f.left,f.top,f.width,f.height)}var p=h.getBounding();c.drawImage(h.getImage(d),0,p.top,o,p.height)}),l.toDataURL("image/".concat(e??"jpeg"))},t.prototype.setOrderFlowData=function(r){this._chartStore.setOrderFlowData(r)},t.prototype.getOrderFlowData=function(){return this._chartStore.getOrderFlowData()},t.prototype.resize=function(){this._cacheChartBounding(),this.layout({measureHeight:!0,measureWidth:!0,update:!0,buildYAxisTick:!0,forceBuildYAxisTick:!0})},t.prototype.destroy=function(){this._chartEvent.destroy(),this._drawPanes.forEach(function(r){r.destroy()}),this._drawPanes=[],this._separatorPanes.clear(),this._chartStore.destroy(),this._container.removeChild(this._chartContainer)},t.prototype.setCandleVisible=function(r){this._chartStore.setCandleVisible(r),this.updatePane(4)},t.prototype.isCandleVisible=function(){return this._chartStore.isCandleVisible()},t}(),Ca=new Map,M0=1;function T0(t,r){var e=null;if(Kt(t)?e=document.getElementById(t):e=t,e===null)return null;var a=Ca.get(e.id);if(R(a))return a;var i="k_line_chart_".concat(M0++);return a=new No(e,r),a.id=i,e.setAttribute("k-line-chart-id",i),Ca.set(i,a),a}function E0(t){var r,e,a=null;if(t instanceof No)a=t.id;else{var i=null;Kt(t)?i=document.getElementById(t):i=t,a=(r=i==null?void 0:i.getAttribute("k-line-chart-id"))!==null&&r!==void 0?r:null}a!==null&&((e=Ca.get(a))===null||e===void 0||e.destroy(),Ca.delete(a))}var Be={clone:Er,merge:ye,isString:Kt,isNumber:vt,isValid:R,isObject:He,isArray:Ue,isFunction:Ie,isBoolean:Qr,formatValue:be,formatPrecision:Me,formatBigNumber:ao,formatDate:ro,formatThousands:io,formatFoldDecimal:no,calcTextWidth:je,getLinearSlopeIntercept:Da,getLinearYFromSlopeIntercept:mi,getLinearYFromCoordinates:qr,checkCoordinateOnArc:Lo,checkCoordinateOnCircle:_o,checkCoordinateOnLine:fo,checkCoordinateOnPolygon:Co,checkCoordinateOnRect:Ci,checkCoordinateOnText:wo},I0=Object.defineProperty,A0=(t,r,e)=>r in t?I0(t,r,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[r]=e,ln=(t,r,e)=>(A0(t,typeof r!="symbol"?r+"":r,e),e);function Ur(t,r,e){const a=(t.x-r.x)*Math.cos(e)-(t.y-r.y)*Math.sin(e)+r.x,i=(t.x-r.x)*Math.sin(e)+(t.y-r.y)*Math.cos(e)+r.y;return{x:a,y:i}}function ri(t,r){if(t.length>1){let e;return t[0].x===t[1].x&&t[0].y!==t[1].y?t[0].y<t[1].y?e={x:t[0].x,y:r.height}:e={x:t[0].x,y:0}:t[0].x>t[1].x?e={x:0,y:Be.getLinearYFromCoordinates(t[0],t[1],{x:0,y:t[0].y})}:e={x:r.width,y:Be.getLinearYFromCoordinates(t[0],t[1],{x:r.width,y:t[0].y})},{coordinates:[t[0],e]}}return[]}function Oo(t,r){const e=Math.abs(t.x-r.x),a=Math.abs(t.y-r.y);return Math.sqrt(e*e+a*a)}const D0={name:"arrow",totalStep:3,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:({coordinates:t})=>{if(t.length>1){const r=t[1].x>t[0].x?0:1,e=Be.getLinearSlopeIntercept(t[0],t[1]);let a;e?a=Math.atan(e[0])+Math.PI*r:t[1].y>t[0].y?a=Math.PI/2:a=Math.PI/2*3;const i=Ur({x:t[1].x-8,y:t[1].y+4},t[1],a),n=Ur({x:t[1].x-8,y:t[1].y-4},t[1],a);return[{type:"line",attrs:{coordinates:t}},{type:"line",ignoreEvent:!0,attrs:{coordinates:[i,t[1],n]}}]}return[]}},P0={name:"circle",totalStep:3,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,styles:{circle:{color:"rgba(22, 119, 255, 0.15)"}},createPointFigures:({coordinates:t})=>{if(t.length>1){const r=Oo(t[0],t[1]);return{type:"circle",attrs:{...t[0],r},styles:{style:"stroke_fill"}}}return[]}},F0={name:"rect",totalStep:3,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,styles:{polygon:{color:"rgba(22, 119, 255, 0.15)"}},createPointFigures:({coordinates:t})=>t.length>1?[{type:"polygon",attrs:{coordinates:[t[0],{x:t[1].x,y:t[0].y},t[1],{x:t[0].x,y:t[1].y}]},styles:{style:"stroke_fill"}}]:[]},R0={name:"parallelogram",totalStep:4,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,styles:{polygon:{color:"rgba(22, 119, 255, 0.15)"}},createPointFigures:({coordinates:t})=>{if(t.length===2)return[{type:"line",ignoreEvent:!0,attrs:{coordinates:t}}];if(t.length===3){const r={x:t[0].x+(t[2].x-t[1].x),y:t[2].y};return[{type:"polygon",attrs:{coordinates:[t[0],t[1],t[2],r]},styles:{style:"stroke_fill"}}]}return[]},performEventPressedMove:({points:t,performPointIndex:r,performPoint:e})=>{r<2&&(t[0].price=e.price,t[1].price=e.price)},performEventMoveForDrawing:({currentStep:t,points:r,performPoint:e})=>{t===2&&(r[0].price=e.price)}},B0={name:"triangle",totalStep:4,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,styles:{polygon:{color:"rgba(22, 119, 255, 0.15)"}},createPointFigures:({coordinates:t})=>[{type:"polygon",attrs:{coordinates:t},styles:{style:"stroke_fill"}}]},N0={name:"fibonacciCircle",totalStep:3,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:({coordinates:t})=>{if(t.length>1){const r=Math.abs(t[0].x-t[1].x),e=Math.abs(t[0].y-t[1].y),a=Math.sqrt(r*r+e*e),i=[.236,.382,.5,.618,.786,1],n=[],o=[];return i.forEach(s=>{const l=a*s;n.push({...t[0],r:l}),o.push({x:t[0].x,y:t[0].y+l+6,text:`${(s*100).toFixed(1)}%`})}),[{type:"circle",attrs:n,styles:{style:"stroke"}},{type:"text",ignoreEvent:!0,attrs:o}]}return[]}},O0={name:"fibonacciSegment",totalStep:3,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:({coordinates:t,overlay:r})=>{const e=[],a=[];if(t.length>1){const i=t[1].x>t[0].x?t[0].x:t[1].x,n=[1,.786,.618,.5,.382,.236,0],o=t[0].y-t[1].y,s=r.points,l=s[0].value-s[1].value;n.forEach(c=>{const u=t[1].y+o*c,d=(s[1].value+l*c).toFixed(precision.price);e.push({coordinates:[{x:t[0].x,y:u},{x:t[1].x,y:u}]}),a.push({x:i,y:u,text:`${d} (${(c*100).toFixed(1)}%)`,baseline:"bottom"})})}return[{type:"line",attrs:e},{type:"text",ignoreEvent:!0,attrs:a}]}},V0={name:"fibonacciSpiral",totalStep:3,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:({coordinates:t,bounding:r})=>{if(t.length>1){const e=Oo(t[0],t[1])/Math.sqrt(24),a=t[1].x>t[0].x?0:1,i=Be.getLinearSlopeIntercept(t[0],t[1]);let n;i?n=Math.atan(i[0])+Math.PI*a:t[1].y>t[0].y?n=Math.PI/2:n=Math.PI/2*3;const o=Ur({x:t[0].x-e,y:t[0].y},t[0],n),s=Ur({x:t[0].x-e,y:t[0].y-e},t[0],n),l=[{...o,r:e,startAngle:n,endAngle:n+Math.PI/2},{...s,r:e*2,startAngle:n+Math.PI/2,endAngle:n+Math.PI}];let c=t[0].x-e,u=t[0].y-e;for(let d=2;d<9;d++){const h=l[d-2].r+l[d-1].r;let v=0;switch(d%4){case 0:{v=n,c-=l[d-2].r;break}case 1:{v=n+Math.PI/2,u-=l[d-2].r;break}case 2:{v=n+Math.PI,c+=l[d-2].r;break}case 3:{v=n+Math.PI/2*3,u+=l[d-2].r;break}}const f=v+Math.PI/2,p=Ur({x:c,y:u},t[0],n);l.push({...p,r:h,startAngle:v,endAngle:f})}return[{type:"arc",attrs:l},{type:"line",attrs:ri(t,r)}]}return[]}},z0={name:"fibonacciSpeedResistanceFan",totalStep:3,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:({coordinates:t,bounding:r})=>{const e=[];let a=[];const i=[];if(t.length>1){const n=t[1].x>t[0].x?-38:4,o=t[1].y>t[0].y?-2:20,s=t[1].x-t[0].x,l=t[1].y-t[0].y;[1,.75,.618,.5,.382,.25,0].forEach(c=>{const u=t[1].x-s*c,d=t[1].y-l*c;e.push({coordinates:[{x:u,y:t[0].y},{x:u,y:t[1].y}]}),e.push({coordinates:[{x:t[0].x,y:d},{x:t[1].x,y:d}]}),a=a.concat(ri([t[0],{x:u,y:t[1].y}],r)),a=a.concat(ri([t[0],{x:t[1].x,y:d}],r)),i.unshift({x:t[0].x+n,y:d+10,text:`${c.toFixed(3)}`}),i.unshift({x:u-18,y:t[0].y+o,text:`${c.toFixed(3)}`})})}return[{type:"line",attrs:e},{type:"line",attrs:a},{type:"text",ignoreEvent:!0,attrs:i}]}},$0={name:"fibonacciExtension",totalStep:4,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:({coordinates:t,overlay:r})=>{const e=[],a=[];if(t.length>2){const i=r.points,n=i[1].value-i[0].value,o=t[1].y-t[0].y,s=[0,.236,.382,.5,.618,.786,1],l=t[2].x>t[1].x?t[1].x:t[2].x;s.forEach(c=>{const u=t[2].y+o*c,d=(i[2].value+n*c).toFixed(precision.price);e.push({coordinates:[{x:t[1].x,y:u},{x:t[2].x,y:u}]}),a.push({x:l,y:u,text:`${d} (${(c*100).toFixed(1)}%)`,baseline:"bottom"})})}return[{type:"line",attrs:{coordinates:t},styles:{style:"dashed"}},{type:"line",attrs:e},{type:"text",ignoreEvent:!0,attrs:a}]}},W0={name:"gannBox",totalStep:3,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,styles:{polygon:{color:"rgba(22, 119, 255, 0.15)"}},createPointFigures:({coordinates:t})=>{if(t.length>1){const r=(t[1].y-t[0].y)/4,e=t[1].x-t[0].x,a=[{coordinates:[t[0],{x:t[1].x,y:t[1].y-r}]},{coordinates:[t[0],{x:t[1].x,y:t[1].y-r*2}]},{coordinates:[{x:t[0].x,y:t[1].y},{x:t[1].x,y:t[0].y+r}]},{coordinates:[{x:t[0].x,y:t[1].y},{x:t[1].x,y:t[0].y+r*2}]},{coordinates:[{...t[0]},{x:t[0].x+e*.236,y:t[1].y}]},{coordinates:[{...t[0]},{x:t[0].x+e*.5,y:t[1].y}]},{coordinates:[{x:t[0].x,y:t[1].y},{x:t[0].x+e*.236,y:t[0].y}]},{coordinates:[{x:t[0].x,y:t[1].y},{x:t[0].x+e*.5,y:t[0].y}]}],i=[{coordinates:[t[0],t[1]]},{coordinates:[{x:t[0].x,y:t[1].y},{x:t[1].x,y:t[0].y}]}];return[{type:"line",attrs:[{coordinates:[t[0],{x:t[1].x,y:t[0].y}]},{coordinates:[{x:t[1].x,y:t[0].y},t[1]]},{coordinates:[t[1],{x:t[0].x,y:t[1].y}]},{coordinates:[{x:t[0].x,y:t[1].y},t[0]]}]},{type:"polygon",ignoreEvent:!0,attrs:{coordinates:[t[0],{x:t[1].x,y:t[0].y},t[1],{x:t[0].x,y:t[1].y}]},styles:{style:"fill"}},{type:"line",attrs:a,styles:{style:"dashed"}},{type:"line",attrs:i}]}return[]}},Y0={name:"threeWaves",totalStep:5,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:({coordinates:t})=>{const r=t.map((e,a)=>({...e,text:`(${a})`,baseline:"bottom"}));return[{type:"line",attrs:{coordinates:t}},{type:"text",ignoreEvent:!0,attrs:r}]}},Z0={name:"fiveWaves",totalStep:7,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:({coordinates:t})=>{const r=t.map((e,a)=>({...e,text:`(${a})`,baseline:"bottom"}));return[{type:"line",attrs:{coordinates:t}},{type:"text",ignoreEvent:!0,attrs:r}]}},H0={name:"eightWaves",totalStep:10,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:({coordinates:t})=>{const r=t.map((e,a)=>({...e,text:`(${a})`,baseline:"bottom"}));return[{type:"line",attrs:{coordinates:t}},{type:"text",ignoreEvent:!0,attrs:r}]}},j0={name:"anyWaves",totalStep:Number.MAX_SAFE_INTEGER,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:({coordinates:t})=>{const r=t.map((e,a)=>({...e,text:`(${a})`,baseline:"bottom"}));return[{type:"line",attrs:{coordinates:t}},{type:"text",ignoreEvent:!0,attrs:r}]}},X0={name:"abcd",totalStep:5,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,createPointFigures:({coordinates:t})=>{let r=[],e=[];const a=["A","B","C","D"],i=t.map((n,o)=>({...n,baseline:"bottom",text:`(${a[o]})`}));return t.length>2&&(r=[t[0],t[2]],t.length>3&&(e=[t[1],t[3]])),[{type:"line",attrs:{coordinates:t}},{type:"line",attrs:[{coordinates:r},{coordinates:e}],styles:{style:"dashed"}},{type:"text",ignoreEvent:!0,attrs:i}]}},G0={name:"xabcd",totalStep:6,needDefaultPointFigure:!0,needDefaultXAxisFigure:!0,needDefaultYAxisFigure:!0,styles:{polygon:{color:"rgba(22, 119, 255, 0.15)"}},createPointFigures:({coordinates:t,overlay:r})=>{const e=[],a=[],i=["X","A","B","C","D"],n=t.map((o,s)=>({...o,baseline:"bottom",text:`(${i[s]})`}));return t.length>2&&(e.push({coordinates:[t[0],t[2]]}),a.push({coordinates:[t[0],t[1],t[2]]}),t.length>3&&(e.push({coordinates:[t[1],t[3]]}),t.length>4&&(e.push({coordinates:[t[2],t[4]]}),a.push({coordinates:[t[2],t[3],t[4]]})))),[{type:"line",attrs:{coordinates:t}},{type:"line",attrs:e,styles:{style:"dashed"}},{type:"polygon",ignoreEvent:!0,attrs:a},{type:"text",ignoreEvent:!0,attrs:n}]}},K0=[D0,P0,F0,B0,R0,N0,O0,V0,z0,$0,W0,Y0,Z0,H0,j0,X0,G0],U0=(t,r)=>t===r,ai=Symbol("solid-proxy"),Q0=typeof Proxy=="function",q0=Symbol("solid-track"),ba={equals:U0};let Vo=Zo;const ur=1,xa=2,zo={owned:null,cleanups:null,context:null,owner:null},ja={};var re=null;let Xa=null,te=null,Ee=null,or=null,Fa=0;function va(t,r){const e=te,a=re,i=t.length===0,n=r===void 0?a:r,o=i?zo:{owned:null,cleanups:null,context:n?n.context:null,owner:n},s=i?t:()=>t(()=>qe(()=>Jr(o)));re=o,te=null;try{return lr(s,!0)}finally{te=e,re=a}}function Mt(t,r){r=r?Object.assign({},ba,r):ba;const e={value:t,observers:null,observerSlots:null,comparator:r.equals||void 0},a=i=>(typeof i=="function"&&(i=i(e.value)),Yo(e,i));return[Wo.bind(e),a]}function J0(t,r,e){const a=Ra(t,r,!0,ur);Dr(a)}function _e(t,r,e){const a=Ra(t,r,!1,ur);Dr(a)}function ir(t,r,e){Vo=sc;const a=Ra(t,r,!1,ur);a.user=!0,or?or.push(a):Dr(a)}function Qe(t,r,e){e=e?Object.assign({},ba,e):ba;const a=Ra(t,r,!0,0);return a.observers=null,a.observerSlots=null,a.comparator=e.equals||void 0,Dr(a),Wo.bind(a)}function tc(t){return t&&typeof t=="object"&&"then"in t}function ec(t,r,e){let a,i,n;typeof r=="function"?(a=t,i=r,n={}):(a=!0,i=t,n=r||{});let o=null,s=ja,l=!1,c="initialValue"in n,u=typeof a=="function"&&Qe(a);const d=new Set,[h,v]=(n.storage||Mt)(n.initialValue),[f,p]=Mt(void 0),[g,m]=Mt(void 0,{equals:!1}),[_,x]=Mt(c?"ready":"unresolved");function S(E,I,T,L){return o===E&&(o=null,L!==void 0&&(c=!0),(E===s||I===s)&&n.onHydrated&&queueMicrotask(()=>n.onHydrated(L,{value:I})),s=ja,y(I,T)),I}function y(E,I){lr(()=>{I===void 0&&v(()=>E),x(I!==void 0?"errored":c?"ready":"unresolved"),p(I);for(const T of d.keys())T.decrement();d.clear()},!1)}function k(){const E=ic,I=h(),T=f();if(T!==void 0&&!o)throw T;return te&&te.user,I}function C(E=!0){if(E!==!1&&l)return;l=!1;const I=u?u():a;if(I==null||I===!1){S(o,qe(h));return}let T;const L=s!==ja?s:qe(()=>{try{return i(I,{value:h(),refetching:E})}catch(b){T=b}});if(T!==void 0){S(o,void 0,fa(T),I);return}else if(!tc(L))return S(o,L,void 0,I),L;return o=L,"v"in L?(L.s===1?S(o,L.v,void 0,I):S(o,void 0,fa(L.v),I),L):(l=!0,queueMicrotask(()=>l=!1),lr(()=>{x(c?"refreshing":"pending"),m()},!1),L.then(b=>S(L,b,void 0,I),b=>S(L,void 0,fa(b),I)))}Object.defineProperties(k,{state:{get:()=>_()},error:{get:()=>f()},loading:{get(){const E=_();return E==="pending"||E==="refreshing"}},latest:{get(){if(!c)return k();const E=f();if(E&&!o)throw E;return h()}}});let D=re;return u?J0(()=>(D=re,C(!1))):C(!1),[k,{refetch:E=>rc(D,()=>C(E)),mutate:v}]}function qe(t){if(te===null)return t();const r=te;te=null;try{return t()}finally{te=r}}function $o(t){ir(()=>qe(t))}function wa(t){return re===null||(re.cleanups===null?re.cleanups=[t]:re.cleanups.push(t)),t}function rc(t,r){const e=re,a=te;re=t,te=null;try{return lr(r,!0)}catch(i){xi(i)}finally{re=e,te=a}}function ac(t){const r=te,e=re;return Promise.resolve().then(()=>{te=r,re=e;let a;return lr(t,!1),te=re=null,a?a.done:void 0})}let ic;function Wo(){if(this.sources&&this.state)if(this.state===ur)Dr(this);else{const t=Ee;Ee=null,lr(()=>Sa(this),!1),Ee=t}if(te){const t=this.observers?this.observers.length:0;te.sources?(te.sources.push(this),te.sourceSlots.push(t)):(te.sources=[this],te.sourceSlots=[t]),this.observers?(this.observers.push(te),this.observerSlots.push(te.sources.length-1)):(this.observers=[te],this.observerSlots=[te.sources.length-1])}return this.value}function Yo(t,r,e){let a=t.value;return(!t.comparator||!t.comparator(a,r))&&(t.value=r,t.observers&&t.observers.length&&lr(()=>{for(let i=0;i<t.observers.length;i+=1){const n=t.observers[i],o=Xa&&Xa.running;o&&Xa.disposed.has(n),(o?!n.tState:!n.state)&&(n.pure?Ee.push(n):or.push(n),n.observers&&Ho(n)),o||(n.state=ur)}if(Ee.length>1e6)throw Ee=[],new Error},!1)),r}function Dr(t){if(!t.fn)return;Jr(t);const r=Fa;nc(t,t.value,r)}function nc(t,r,e){let a;const i=re,n=te;te=re=t;try{a=t.fn(r)}catch(o){return t.pure&&(t.state=ur,t.owned&&t.owned.forEach(Jr),t.owned=null),t.updatedAt=e+1,xi(o)}finally{te=n,re=i}(!t.updatedAt||t.updatedAt<=e)&&(t.updatedAt!=null&&"observers"in t?Yo(t,a):t.value=a,t.updatedAt=e)}function Ra(t,r,e,a=ur,i){const n={fn:t,state:a,updatedAt:null,owned:null,sources:null,sourceSlots:null,cleanups:null,value:r,owner:re,context:re?re.context:null,pure:e};return re===null||re!==zo&&(re.owned?re.owned.push(n):re.owned=[n]),n}function La(t){if(t.state===0)return;if(t.state===xa)return Sa(t);if(t.suspense&&qe(t.suspense.inFallback))return t.suspense.effects.push(t);const r=[t];for(;(t=t.owner)&&(!t.updatedAt||t.updatedAt<Fa);)t.state&&r.push(t);for(let e=r.length-1;e>=0;e--)if(t=r[e],t.state===ur)Dr(t);else if(t.state===xa){const a=Ee;Ee=null,lr(()=>Sa(t,r[0]),!1),Ee=a}}function lr(t,r){if(Ee)return t();let e=!1;r||(Ee=[]),or?e=!0:or=[],Fa++;try{const a=t();return oc(e),a}catch(a){e||(or=null),Ee=null,xi(a)}}function oc(t){if(Ee&&(Zo(Ee),Ee=null),t)return;const r=or;or=null,r.length&&lr(()=>Vo(r),!1)}function Zo(t){for(let r=0;r<t.length;r++)La(t[r])}function sc(t){let r,e=0;for(r=0;r<t.length;r++){const a=t[r];a.user?t[e++]=a:La(a)}for(r=0;r<e;r++)La(t[r])}function Sa(t,r){t.state=0;for(let e=0;e<t.sources.length;e+=1){const a=t.sources[e];if(a.sources){const i=a.state;i===ur?a!==r&&(!a.updatedAt||a.updatedAt<Fa)&&La(a):i===xa&&Sa(a,r)}}}function Ho(t){for(let r=0;r<t.observers.length;r+=1){const e=t.observers[r];e.state||(e.state=xa,e.pure?Ee.push(e):or.push(e),e.observers&&Ho(e))}}function Jr(t){let r;if(t.sources)for(;t.sources.length;){const e=t.sources.pop(),a=t.sourceSlots.pop(),i=e.observers;if(i&&i.length){const n=i.pop(),o=e.observerSlots.pop();a<i.length&&(n.sourceSlots[o]=a,i[a]=n,e.observerSlots[a]=o)}}if(t.tOwned){for(r=t.tOwned.length-1;r>=0;r--)Jr(t.tOwned[r]);delete t.tOwned}if(t.owned){for(r=t.owned.length-1;r>=0;r--)Jr(t.owned[r]);t.owned=null}if(t.cleanups){for(r=t.cleanups.length-1;r>=0;r--)t.cleanups[r]();t.cleanups=null}t.state=0}function fa(t){return t instanceof Error?t:new Error(typeof t=="string"?t:"Unknown error",{cause:t})}function xi(t,r=re){throw fa(t)}const lc=Symbol("fallback");function cn(t){for(let r=0;r<t.length;r++)t[r]()}function cc(t,r,e={}){let a=[],i=[],n=[],o=0,s=r.length>1?[]:null;return wa(()=>cn(n)),()=>{let l=t()||[],c=l.length,u,d;return l[q0],qe(()=>{let v,f,p,g,m,_,x,S,y;if(c===0)o!==0&&(cn(n),n=[],a=[],i=[],o=0,s&&(s=[])),e.fallback&&(a=[lc],i[0]=va(k=>(n[0]=k,e.fallback())),o=1);else if(o===0){for(i=new Array(c),d=0;d<c;d++)a[d]=l[d],i[d]=va(h);o=c}else{for(p=new Array(c),g=new Array(c),s&&(m=new Array(c)),_=0,x=Math.min(o,c);_<x&&a[_]===l[_];_++);for(x=o-1,S=c-1;x>=_&&S>=_&&a[x]===l[S];x--,S--)p[S]=i[x],g[S]=n[x],s&&(m[S]=s[x]);for(v=new Map,f=new Array(S+1),d=S;d>=_;d--)y=l[d],u=v.get(y),f[d]=u===void 0?-1:u,v.set(y,d);for(u=_;u<=x;u++)y=a[u],d=v.get(y),d!==void 0&&d!==-1?(p[d]=i[u],g[d]=n[u],s&&(m[d]=s[u]),d=f[d],v.set(y,d)):n[u]();for(d=_;d<c;d++)d in p?(i[d]=p[d],n[d]=g[d],s&&(s[d]=m[d],s[d](d))):i[d]=va(h);i=i.slice(0,o=c),a=l.slice(0)}return i});function h(v){if(n[d]=v,s){const[f,p]=Mt(d);return s[d]=p,r(l[d],f)}return r(l[d])}}}function ot(t,r){return qe(()=>t(r||{}))}function ia(){return!0}const uc={get(t,r,e){return r===ai?e:t.get(r)},has(t,r){return r===ai?!0:t.has(r)},set:ia,deleteProperty:ia,getOwnPropertyDescriptor(t,r){return{configurable:!0,enumerable:!0,get(){return t.get(r)},set:ia,deleteProperty:ia}},ownKeys(t){return t.keys()}};function Ga(t){return(t=typeof t=="function"?t():t)?t:{}}function dc(){for(let t=0,r=this.length;t<r;++t){const e=this[t]();if(e!==void 0)return e}}function jo(...t){let r=!1;for(let o=0;o<t.length;o++){const s=t[o];r=r||!!s&&ai in s,t[o]=typeof s=="function"?(r=!0,Qe(s)):s}if(Q0&&r)return new Proxy({get(o){for(let s=t.length-1;s>=0;s--){const l=Ga(t[s])[o];if(l!==void 0)return l}},has(o){for(let s=t.length-1;s>=0;s--)if(o in Ga(t[s]))return!0;return!1},keys(){const o=[];for(let s=0;s<t.length;s++)o.push(...Object.keys(Ga(t[s])));return[...new Set(o)]}},uc);const e={},a=Object.create(null);for(let o=t.length-1;o>=0;o--){const s=t[o];if(!s)continue;const l=Object.getOwnPropertyNames(s);for(let c=l.length-1;c>=0;c--){const u=l[c];if(u==="__proto__"||u==="constructor")continue;const d=Object.getOwnPropertyDescriptor(s,u);if(!a[u])a[u]=d.get?{enumerable:!0,configurable:!0,get:dc.bind(e[u]=[d.get.bind(s)])}:d.value!==void 0?d:void 0;else{const h=e[u];h&&(d.get?h.push(d.get.bind(s)):d.value!==void 0&&h.push(()=>d.value))}}}const i={},n=Object.keys(a);for(let o=n.length-1;o>=0;o--){const s=n[o],l=a[s];l&&l.get?Object.defineProperty(i,s,l):i[s]=l?l.value:void 0}return i}const hc=t=>`Stale read from <${t}>.`;function vc(t){const r="fallback"in t&&{fallback:()=>t.fallback};return Qe(cc(()=>t.each,t.children,r||void 0))}function ke(t){const r=t.keyed,e=Qe(()=>t.when,void 0,void 0),a=r?e:Qe(e,void 0,{equals:(i,n)=>!i==!n});return Qe(()=>{const i=a();if(i){const n=t.children;return typeof n=="function"&&n.length>0?qe(()=>n(r?i:()=>{if(!qe(a))throw hc("Show");return e()})):n}return t.fallback},void 0,void 0)}const Fe=t=>Qe(()=>t());function fc(t,r,e){let a=e.length,i=r.length,n=a,o=0,s=0,l=r[i-1].nextSibling,c=null;for(;o<i||s<n;){if(r[o]===e[s]){o++,s++;continue}for(;r[i-1]===e[n-1];)i--,n--;if(i===o){const u=n<a?s?e[s-1].nextSibling:e[n-s]:l;for(;s<n;)t.insertBefore(e[s++],u)}else if(n===s)for(;o<i;)(!c||!c.has(r[o]))&&r[o].remove(),o++;else if(r[o]===e[n-1]&&e[s]===r[i-1]){const u=r[--i].nextSibling;t.insertBefore(e[s++],r[o++].nextSibling),t.insertBefore(e[--n],u),r[i]=e[n]}else{if(!c){c=new Map;let d=s;for(;d<n;)c.set(e[d],d++)}const u=c.get(r[o]);if(u!=null)if(s<u&&u<n){let d=o,h=1,v;for(;++d<i&&d<n&&!((v=c.get(r[d]))==null||v!==u+h);)h++;if(h>u-s){const f=r[o];for(;s<u;)t.insertBefore(e[s++],f)}else t.replaceChild(e[s++],r[o++])}else o++;else r[o++].remove()}}}const un="_$DX_DELEGATE";function pc(t,r,e,a={}){let i;return va(n=>{i=n,r===document?t():st(r,t(),r.firstChild?null:void 0,e)},a.owner),()=>{i(),r.textContent=""}}function Q(t,r,e,a){let i;const n=()=>{const s=document.createElement("template");return s.innerHTML=t,s.content.firstChild},o=()=>(i||(i=n())).cloneNode(!0);return o.cloneNode=o,o}function Je(t,r=window.document){const e=r[un]||(r[un]=new Set);for(let a=0,i=t.length;a<i;a++){const n=t[a];e.has(n)||(e.add(n),r.addEventListener(n,gc))}}function Oe(t,r,e){e==null?t.removeAttribute(r):t.setAttribute(r,e)}function xr(t,r){r==null?t.removeAttribute("class"):t.className=r}function Ke(t,r,e,a){Array.isArray(e)?(t[`$$${r}`]=e[0],t[`$$${r}Data`]=e[1]):t[`$$${r}`]=e}function Pr(t,r,e){if(!r)return e?Oe(t,"style"):r;const a=t.style;if(typeof r=="string")return a.cssText=r;typeof e=="string"&&(a.cssText=e=void 0),e||(e={}),r||(r={});let i,n;for(n in e)r[n]==null&&a.removeProperty(n),delete e[n];for(n in r)i=r[n],i!==e[n]&&(a.setProperty(n,i),e[n]=i);return e}function wi(t,r,e){return qe(()=>t(r,e))}function st(t,r,e,a){if(e!==void 0&&!a&&(a=[]),typeof r!="function")return ka(t,r,a,e);_e(i=>ka(t,r(),i,e),a)}function gc(t){let r=t.target;const e=`$$${t.type}`,a=t.target,i=t.currentTarget,n=l=>Object.defineProperty(t,"target",{configurable:!0,value:l}),o=()=>{const l=r[e];if(l&&!r.disabled){const c=r[`${e}Data`];if(c!==void 0?l.call(r,c,t):l.call(r,t),t.cancelBubble)return}return r.host&&typeof r.host!="string"&&!r.host._$host&&r.contains(t.target)&&n(r.host),!0},s=()=>{for(;o()&&(r=r._$host||r.parentNode||r.host););};if(Object.defineProperty(t,"currentTarget",{configurable:!0,get(){return r||document}}),t.composedPath){const l=t.composedPath();n(l[0]);for(let c=0;c<l.length-2&&(r=l[c],!!o());c++){if(r._$host){r=r._$host,s();break}if(r.parentNode===i)break}}else s();n(a)}function ka(t,r,e,a,i){for(;typeof e=="function";)e=e();if(r===e)return e;const n=typeof r,o=a!==void 0;if(t=o&&e[0]&&e[0].parentNode||t,n==="string"||n==="number"){if(n==="number"&&(r=r.toString(),r===e))return e;if(o){let s=e[0];s&&s.nodeType===3?s.data!==r&&(s.data=r):s=document.createTextNode(r),e=Mr(t,e,a,s)}else e!==""&&typeof e=="string"?e=t.firstChild.data=r:e=t.textContent=r}else if(r==null||n==="boolean")e=Mr(t,e,a);else{if(n==="function")return _e(()=>{let s=r();for(;typeof s=="function";)s=s();e=ka(t,s,e,a)}),()=>e;if(Array.isArray(r)){const s=[],l=e&&Array.isArray(e);if(ii(s,r,e,i))return _e(()=>e=ka(t,s,e,a,!0)),()=>e;if(s.length===0){if(e=Mr(t,e,a),o)return e}else l?e.length===0?dn(t,s,a):fc(t,e,s):(e&&Mr(t),dn(t,s));e=s}else if(r.nodeType){if(Array.isArray(e)){if(o)return e=Mr(t,e,a,r);Mr(t,e,null,r)}else e==null||e===""||!t.firstChild?t.appendChild(r):t.replaceChild(r,t.firstChild);e=r}}return e}function ii(t,r,e,a){let i=!1;for(let n=0,o=r.length;n<o;n++){let s=r[n],l=e&&e[t.length],c;if(!(s==null||s===!0||s===!1))if((c=typeof s)=="object"&&s.nodeType)t.push(s);else if(Array.isArray(s))i=ii(t,s,l)||i;else if(c==="function")if(a){for(;typeof s=="function";)s=s();i=ii(t,Array.isArray(s)?s:[s],Array.isArray(l)?l:[l])||i}else t.push(s),i=!0;else{const u=String(s);l&&l.nodeType===3&&l.data===u?t.push(l):t.push(document.createTextNode(u))}}return i}function dn(t,r,e=null){for(let a=0,i=r.length;a<i;a++)t.insertBefore(r[a],e)}function Mr(t,r,e,a){if(e===void 0)return t.textContent="";const i=a||document.createTextNode("");if(r.length){let n=!1;for(let o=r.length-1;o>=0;o--){const s=r[o];if(i!==s){const l=s.parentNode===t;!n&&!o?l?t.replaceChild(i,s):t.insertBefore(i,e):l&&s.remove()}else n=!0}}else t.insertBefore(i,e);return[i]}var na=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Xo(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var mc=typeof na=="object"&&na&&na.Object===Object&&na,Go=mc,yc=Go,_c=typeof self=="object"&&self&&self.Object===Object&&self,Cc=yc||_c||Function("return this")(),tr=Cc,bc=tr,xc=bc.Symbol,Ba=xc,hn=Ba,Ko=Object.prototype,wc=Ko.hasOwnProperty,Lc=Ko.toString,Gr=hn?hn.toStringTag:void 0;function Sc(t){var r=wc.call(t,Gr),e=t[Gr];try{t[Gr]=void 0;var a=!0}catch{}var i=Lc.call(t);return a&&(r?t[Gr]=e:delete t[Gr]),i}var kc=Sc,Mc=Object.prototype,Tc=Mc.toString;function Ec(t){return Tc.call(t)}var Ic=Ec,vn=Ba,Ac=kc,Dc=Ic,Pc="[object Null]",Fc="[object Undefined]",fn=vn?vn.toStringTag:void 0;function Rc(t){return t==null?t===void 0?Fc:Pc:fn&&fn in Object(t)?Ac(t):Dc(t)}var ea=Rc;function Bc(t){var r=typeof t;return t!=null&&(r=="object"||r=="function")}var Fr=Bc,Nc=ea,Oc=Fr,Vc="[object AsyncFunction]",zc="[object Function]",$c="[object GeneratorFunction]",Wc="[object Proxy]";function Yc(t){if(!Oc(t))return!1;var r=Nc(t);return r==zc||r==$c||r==Vc||r==Wc}var Uo=Yc,Zc=tr,Hc=Zc["__core-js_shared__"],jc=Hc,Ka=jc,pn=function(){var t=/[^.]+$/.exec(Ka&&Ka.keys&&Ka.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}();function Xc(t){return!!pn&&pn in t}var Gc=Xc,Kc=Function.prototype,Uc=Kc.toString;function Qc(t){if(t!=null){try{return Uc.call(t)}catch{}try{return t+""}catch{}}return""}var Qo=Qc,qc=Uo,Jc=Gc,tu=Fr,eu=Qo,ru=/[\\^$.*+?()[\]{}|]/g,au=/^\[object .+?Constructor\]$/,iu=Function.prototype,nu=Object.prototype,ou=iu.toString,su=nu.hasOwnProperty,lu=RegExp("^"+ou.call(su).replace(ru,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function cu(t){if(!tu(t)||Jc(t))return!1;var r=qc(t)?lu:au;return r.test(eu(t))}var uu=cu;function du(t,r){return t==null?void 0:t[r]}var hu=du,vu=uu,fu=hu;function pu(t,r){var e=fu(t,r);return vu(e)?e:void 0}var wr=pu,gu=wr,mu=function(){try{var t=gu(Object,"defineProperty");return t({},"",{}),t}catch{}}(),yu=mu,gn=yu;function _u(t,r,e){r=="__proto__"&&gn?gn(t,r,{configurable:!0,enumerable:!0,value:e,writable:!0}):t[r]=e}var qo=_u;function Cu(t,r){return t===r||t!==t&&r!==r}var Jo=Cu,bu=qo,xu=Jo,wu=Object.prototype,Lu=wu.hasOwnProperty;function Su(t,r,e){var a=t[r];(!(Lu.call(t,r)&&xu(a,e))||e===void 0&&!(r in t))&&bu(t,r,e)}var Li=Su,ku=Array.isArray,Rr=ku;function Mu(t){return t!=null&&typeof t=="object"}var Br=Mu,Tu=ea,Eu=Br,Iu="[object Symbol]";function Au(t){return typeof t=="symbol"||Eu(t)&&Tu(t)==Iu}var Si=Au,Du=Rr,Pu=Si,Fu=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Ru=/^\w*$/;function Bu(t,r){if(Du(t))return!1;var e=typeof t;return e=="number"||e=="symbol"||e=="boolean"||t==null||Pu(t)?!0:Ru.test(t)||!Fu.test(t)||r!=null&&t in Object(r)}var Nu=Bu,Ou=wr,Vu=Ou(Object,"create"),Na=Vu,mn=Na;function zu(){this.__data__=mn?mn(null):{},this.size=0}var $u=zu;function Wu(t){var r=this.has(t)&&delete this.__data__[t];return this.size-=r?1:0,r}var Yu=Wu,Zu=Na,Hu="__lodash_hash_undefined__",ju=Object.prototype,Xu=ju.hasOwnProperty;function Gu(t){var r=this.__data__;if(Zu){var e=r[t];return e===Hu?void 0:e}return Xu.call(r,t)?r[t]:void 0}var Ku=Gu,Uu=Na,Qu=Object.prototype,qu=Qu.hasOwnProperty;function Ju(t){var r=this.__data__;return Uu?r[t]!==void 0:qu.call(r,t)}var t9=Ju,e9=Na,r9="__lodash_hash_undefined__";function a9(t,r){var e=this.__data__;return this.size+=this.has(t)?0:1,e[t]=e9&&r===void 0?r9:r,this}var i9=a9,n9=$u,o9=Yu,s9=Ku,l9=t9,c9=i9;function Nr(t){var r=-1,e=t==null?0:t.length;for(this.clear();++r<e;){var a=t[r];this.set(a[0],a[1])}}Nr.prototype.clear=n9;Nr.prototype.delete=o9;Nr.prototype.get=s9;Nr.prototype.has=l9;Nr.prototype.set=c9;var u9=Nr;function d9(){this.__data__=[],this.size=0}var h9=d9,v9=Jo;function f9(t,r){for(var e=t.length;e--;)if(v9(t[e][0],r))return e;return-1}var Oa=f9,p9=Oa,g9=Array.prototype,m9=g9.splice;function y9(t){var r=this.__data__,e=p9(r,t);if(e<0)return!1;var a=r.length-1;return e==a?r.pop():m9.call(r,e,1),--this.size,!0}var _9=y9,C9=Oa;function b9(t){var r=this.__data__,e=C9(r,t);return e<0?void 0:r[e][1]}var x9=b9,w9=Oa;function L9(t){return w9(this.__data__,t)>-1}var S9=L9,k9=Oa;function M9(t,r){var e=this.__data__,a=k9(e,t);return a<0?(++this.size,e.push([t,r])):e[a][1]=r,this}var T9=M9,E9=h9,I9=_9,A9=x9,D9=S9,P9=T9;function Or(t){var r=-1,e=t==null?0:t.length;for(this.clear();++r<e;){var a=t[r];this.set(a[0],a[1])}}Or.prototype.clear=E9;Or.prototype.delete=I9;Or.prototype.get=A9;Or.prototype.has=D9;Or.prototype.set=P9;var Va=Or,F9=wr,R9=tr,B9=F9(R9,"Map"),ki=B9,yn=u9,N9=Va,O9=ki;function V9(){this.size=0,this.__data__={hash:new yn,map:new(O9||N9),string:new yn}}var z9=V9;function $9(t){var r=typeof t;return r=="string"||r=="number"||r=="symbol"||r=="boolean"?t!=="__proto__":t===null}var W9=$9,Y9=W9;function Z9(t,r){var e=t.__data__;return Y9(r)?e[typeof r=="string"?"string":"hash"]:e.map}var za=Z9,H9=za;function j9(t){var r=H9(this,t).delete(t);return this.size-=r?1:0,r}var X9=j9,G9=za;function K9(t){return G9(this,t).get(t)}var U9=K9,Q9=za;function q9(t){return Q9(this,t).has(t)}var J9=q9,t2=za;function e2(t,r){var e=t2(this,t),a=e.size;return e.set(t,r),this.size+=e.size==a?0:1,this}var r2=e2,a2=z9,i2=X9,n2=U9,o2=J9,s2=r2;function Vr(t){var r=-1,e=t==null?0:t.length;for(this.clear();++r<e;){var a=t[r];this.set(a[0],a[1])}}Vr.prototype.clear=a2;Vr.prototype.delete=i2;Vr.prototype.get=n2;Vr.prototype.has=o2;Vr.prototype.set=s2;var ts=Vr,es=ts,l2="Expected a function";function Mi(t,r){if(typeof t!="function"||r!=null&&typeof r!="function")throw new TypeError(l2);var e=function(){var a=arguments,i=r?r.apply(this,a):a[0],n=e.cache;if(n.has(i))return n.get(i);var o=t.apply(this,a);return e.cache=n.set(i,o)||n,o};return e.cache=new(Mi.Cache||es),e}Mi.Cache=es;var c2=Mi,u2=c2,d2=500;function h2(t){var r=u2(t,function(a){return e.size===d2&&e.clear(),a}),e=r.cache;return r}var v2=h2,f2=v2,p2=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,g2=/\\(\\)?/g,m2=f2(function(t){var r=[];return t.charCodeAt(0)===46&&r.push(""),t.replace(p2,function(e,a,i,n){r.push(i?n.replace(g2,"$1"):a||e)}),r}),y2=m2;function _2(t,r){for(var e=-1,a=t==null?0:t.length,i=Array(a);++e<a;)i[e]=r(t[e],e,t);return i}var C2=_2,_n=Ba,b2=C2,x2=Rr,w2=Si,Cn=_n?_n.prototype:void 0,bn=Cn?Cn.toString:void 0;function rs(t){if(typeof t=="string")return t;if(x2(t))return b2(t,rs)+"";if(w2(t))return bn?bn.call(t):"";var r=t+"";return r=="0"&&1/t==-1/0?"-0":r}var L2=rs,S2=L2;function k2(t){return t==null?"":S2(t)}var M2=k2,T2=Rr,E2=Nu,I2=y2,A2=M2;function D2(t,r){return T2(t)?t:E2(t,r)?[t]:I2(A2(t))}var P2=D2,F2=9007199254740991,R2=/^(?:0|[1-9]\d*)$/;function B2(t,r){var e=typeof t;return r=r??F2,!!r&&(e=="number"||e!="symbol"&&R2.test(t))&&t>-1&&t%1==0&&t<r}var as=B2,N2=Si;function O2(t){if(typeof t=="string"||N2(t))return t;var r=t+"";return r=="0"&&1/t==-1/0?"-0":r}var V2=O2,z2=Li,$2=P2,W2=as,xn=Fr,Y2=V2;function Z2(t,r,e,a){if(!xn(t))return t;r=$2(r,t);for(var i=-1,n=r.length,o=n-1,s=t;s!=null&&++i<n;){var l=Y2(r[i]),c=e;if(l==="__proto__"||l==="constructor"||l==="prototype")return t;if(i!=o){var u=s[l];c=a?a(u,l,s):void 0,c===void 0&&(c=xn(u)?u:W2(r[i+1])?[]:{})}z2(s,l,c),s=s[l]}return t}var H2=Z2,j2=H2;function X2(t,r,e){return t==null?t:j2(t,r,e)}var G2=X2;const ni=Xo(G2);var K2=Va;function U2(){this.__data__=new K2,this.size=0}var Q2=U2;function q2(t){var r=this.__data__,e=r.delete(t);return this.size=r.size,e}var J2=q2;function t5(t){return this.__data__.get(t)}var e5=t5;function r5(t){return this.__data__.has(t)}var a5=r5,i5=Va,n5=ki,o5=ts,s5=200;function l5(t,r){var e=this.__data__;if(e instanceof i5){var a=e.__data__;if(!n5||a.length<s5-1)return a.push([t,r]),this.size=++e.size,this;e=this.__data__=new o5(a)}return e.set(t,r),this.size=e.size,this}var c5=l5,u5=Va,d5=Q2,h5=J2,v5=e5,f5=a5,p5=c5;function zr(t){var r=this.__data__=new u5(t);this.size=r.size}zr.prototype.clear=d5;zr.prototype.delete=h5;zr.prototype.get=v5;zr.prototype.has=f5;zr.prototype.set=p5;var g5=zr;function m5(t,r){for(var e=-1,a=t==null?0:t.length;++e<a&&r(t[e],e,t)!==!1;);return t}var y5=m5,_5=Li,C5=qo;function b5(t,r,e,a){var i=!e;e||(e={});for(var n=-1,o=r.length;++n<o;){var s=r[n],l=a?a(e[s],t[s],s,e,t):void 0;l===void 0&&(l=t[s]),i?C5(e,s,l):_5(e,s,l)}return e}var $a=b5;function x5(t,r){for(var e=-1,a=Array(t);++e<t;)a[e]=r(e);return a}var w5=x5,L5=ea,S5=Br,k5="[object Arguments]";function M5(t){return S5(t)&&L5(t)==k5}var T5=M5,wn=T5,E5=Br,is=Object.prototype,I5=is.hasOwnProperty,A5=is.propertyIsEnumerable,D5=wn(function(){return arguments}())?wn:function(t){return E5(t)&&I5.call(t,"callee")&&!A5.call(t,"callee")},P5=D5,Ma={exports:{}};function F5(){return!1}var R5=F5;Ma.exports;(function(t,r){var e=tr,a=R5,i=r&&!r.nodeType&&r,n=i&&!0&&t&&!t.nodeType&&t,o=n&&n.exports===i,s=o?e.Buffer:void 0,l=s?s.isBuffer:void 0,c=l||a;t.exports=c})(Ma,Ma.exports);var ns=Ma.exports,B5=9007199254740991;function N5(t){return typeof t=="number"&&t>-1&&t%1==0&&t<=B5}var os=N5,O5=ea,V5=os,z5=Br,$5="[object Arguments]",W5="[object Array]",Y5="[object Boolean]",Z5="[object Date]",H5="[object Error]",j5="[object Function]",X5="[object Map]",G5="[object Number]",K5="[object Object]",U5="[object RegExp]",Q5="[object Set]",q5="[object String]",J5="[object WeakMap]",t6="[object ArrayBuffer]",e6="[object DataView]",r6="[object Float32Array]",a6="[object Float64Array]",i6="[object Int8Array]",n6="[object Int16Array]",o6="[object Int32Array]",s6="[object Uint8Array]",l6="[object Uint8ClampedArray]",c6="[object Uint16Array]",u6="[object Uint32Array]",se={};se[r6]=se[a6]=se[i6]=se[n6]=se[o6]=se[s6]=se[l6]=se[c6]=se[u6]=!0;se[$5]=se[W5]=se[t6]=se[Y5]=se[e6]=se[Z5]=se[H5]=se[j5]=se[X5]=se[G5]=se[K5]=se[U5]=se[Q5]=se[q5]=se[J5]=!1;function d6(t){return z5(t)&&V5(t.length)&&!!se[O5(t)]}var h6=d6;function v6(t){return function(r){return t(r)}}var Ti=v6,Ta={exports:{}};Ta.exports;(function(t,r){var e=Go,a=r&&!r.nodeType&&r,i=a&&!0&&t&&!t.nodeType&&t,n=i&&i.exports===a,o=n&&e.process,s=function(){try{var l=i&&i.require&&i.require("util").types;return l||o&&o.binding&&o.binding("util")}catch{}}();t.exports=s})(Ta,Ta.exports);var Ei=Ta.exports,f6=h6,p6=Ti,Ln=Ei,Sn=Ln&&Ln.isTypedArray,g6=Sn?p6(Sn):f6,m6=g6,y6=w5,_6=P5,C6=Rr,b6=ns,x6=as,w6=m6,L6=Object.prototype,S6=L6.hasOwnProperty;function k6(t,r){var e=C6(t),a=!e&&_6(t),i=!e&&!a&&b6(t),n=!e&&!a&&!i&&w6(t),o=e||a||i||n,s=o?y6(t.length,String):[],l=s.length;for(var c in t)(r||S6.call(t,c))&&!(o&&(c=="length"||i&&(c=="offset"||c=="parent")||n&&(c=="buffer"||c=="byteLength"||c=="byteOffset")||x6(c,l)))&&s.push(c);return s}var ss=k6,M6=Object.prototype;function T6(t){var r=t&&t.constructor,e=typeof r=="function"&&r.prototype||M6;return t===e}var Ii=T6;function E6(t,r){return function(e){return t(r(e))}}var ls=E6,I6=ls,A6=I6(Object.keys,Object),D6=A6,P6=Ii,F6=D6,R6=Object.prototype,B6=R6.hasOwnProperty;function N6(t){if(!P6(t))return F6(t);var r=[];for(var e in Object(t))B6.call(t,e)&&e!="constructor"&&r.push(e);return r}var O6=N6,V6=Uo,z6=os;function $6(t){return t!=null&&z6(t.length)&&!V6(t)}var cs=$6,W6=ss,Y6=O6,Z6=cs;function H6(t){return Z6(t)?W6(t):Y6(t)}var Ai=H6,j6=$a,X6=Ai;function G6(t,r){return t&&j6(r,X6(r),t)}var K6=G6;function U6(t){var r=[];if(t!=null)for(var e in Object(t))r.push(e);return r}var Q6=U6,q6=Fr,J6=Ii,td=Q6,ed=Object.prototype,rd=ed.hasOwnProperty;function ad(t){if(!q6(t))return td(t);var r=J6(t),e=[];for(var a in t)a=="constructor"&&(r||!rd.call(t,a))||e.push(a);return e}var id=ad,nd=ss,od=id,sd=cs;function ld(t){return sd(t)?nd(t,!0):od(t)}var Di=ld,cd=$a,ud=Di;function dd(t,r){return t&&cd(r,ud(r),t)}var hd=dd,Ea={exports:{}};Ea.exports;(function(t,r){var e=tr,a=r&&!r.nodeType&&r,i=a&&!0&&t&&!t.nodeType&&t,n=i&&i.exports===a,o=n?e.Buffer:void 0,s=o?o.allocUnsafe:void 0;function l(c,u){if(u)return c.slice();var d=c.length,h=s?s(d):new c.constructor(d);return c.copy(h),h}t.exports=l})(Ea,Ea.exports);var vd=Ea.exports;function fd(t,r){var e=-1,a=t.length;for(r||(r=Array(a));++e<a;)r[e]=t[e];return r}var pd=fd;function gd(t,r){for(var e=-1,a=t==null?0:t.length,i=0,n=[];++e<a;){var o=t[e];r(o,e,t)&&(n[i++]=o)}return n}var md=gd;function yd(){return[]}var us=yd,_d=md,Cd=us,bd=Object.prototype,xd=bd.propertyIsEnumerable,kn=Object.getOwnPropertySymbols,wd=kn?function(t){return t==null?[]:(t=Object(t),_d(kn(t),function(r){return xd.call(t,r)}))}:Cd,Pi=wd,Ld=$a,Sd=Pi;function kd(t,r){return Ld(t,Sd(t),r)}var Md=kd;function Td(t,r){for(var e=-1,a=r.length,i=t.length;++e<a;)t[i+e]=r[e];return t}var ds=Td,Ed=ls,Id=Ed(Object.getPrototypeOf,Object),hs=Id,Ad=ds,Dd=hs,Pd=Pi,Fd=us,Rd=Object.getOwnPropertySymbols,Bd=Rd?function(t){for(var r=[];t;)Ad(r,Pd(t)),t=Dd(t);return r}:Fd,vs=Bd,Nd=$a,Od=vs;function Vd(t,r){return Nd(t,Od(t),r)}var zd=Vd,$d=ds,Wd=Rr;function Yd(t,r,e){var a=r(t);return Wd(t)?a:$d(a,e(t))}var fs=Yd,Zd=fs,Hd=Pi,jd=Ai;function Xd(t){return Zd(t,jd,Hd)}var Gd=Xd,Kd=fs,Ud=vs,Qd=Di;function qd(t){return Kd(t,Qd,Ud)}var Jd=qd,t3=wr,e3=tr,r3=t3(e3,"DataView"),a3=r3,i3=wr,n3=tr,o3=i3(n3,"Promise"),s3=o3,l3=wr,c3=tr,u3=l3(c3,"Set"),d3=u3,h3=wr,v3=tr,f3=h3(v3,"WeakMap"),p3=f3,oi=a3,si=ki,li=s3,ci=d3,ui=p3,ps=ea,$r=Qo,Mn="[object Map]",g3="[object Object]",Tn="[object Promise]",En="[object Set]",In="[object WeakMap]",An="[object DataView]",m3=$r(oi),y3=$r(si),_3=$r(li),C3=$r(ci),b3=$r(ui),_r=ps;(oi&&_r(new oi(new ArrayBuffer(1)))!=An||si&&_r(new si)!=Mn||li&&_r(li.resolve())!=Tn||ci&&_r(new ci)!=En||ui&&_r(new ui)!=In)&&(_r=function(t){var r=ps(t),e=r==g3?t.constructor:void 0,a=e?$r(e):"";if(a)switch(a){case m3:return An;case y3:return Mn;case _3:return Tn;case C3:return En;case b3:return In}return r});var Fi=_r,x3=Object.prototype,w3=x3.hasOwnProperty;function L3(t){var r=t.length,e=new t.constructor(r);return r&&typeof t[0]=="string"&&w3.call(t,"index")&&(e.index=t.index,e.input=t.input),e}var S3=L3,k3=tr,M3=k3.Uint8Array,T3=M3,Dn=T3;function E3(t){var r=new t.constructor(t.byteLength);return new Dn(r).set(new Dn(t)),r}var Ri=E3,I3=Ri;function A3(t,r){var e=r?I3(t.buffer):t.buffer;return new t.constructor(e,t.byteOffset,t.byteLength)}var D3=A3,P3=/\w*$/;function F3(t){var r=new t.constructor(t.source,P3.exec(t));return r.lastIndex=t.lastIndex,r}var R3=F3,Pn=Ba,Fn=Pn?Pn.prototype:void 0,Rn=Fn?Fn.valueOf:void 0;function B3(t){return Rn?Object(Rn.call(t)):{}}var N3=B3,O3=Ri;function V3(t,r){var e=r?O3(t.buffer):t.buffer;return new t.constructor(e,t.byteOffset,t.length)}var z3=V3,$3=Ri,W3=D3,Y3=R3,Z3=N3,H3=z3,j3="[object Boolean]",X3="[object Date]",G3="[object Map]",K3="[object Number]",U3="[object RegExp]",Q3="[object Set]",q3="[object String]",J3="[object Symbol]",t8="[object ArrayBuffer]",e8="[object DataView]",r8="[object Float32Array]",a8="[object Float64Array]",i8="[object Int8Array]",n8="[object Int16Array]",o8="[object Int32Array]",s8="[object Uint8Array]",l8="[object Uint8ClampedArray]",c8="[object Uint16Array]",u8="[object Uint32Array]";function d8(t,r,e){var a=t.constructor;switch(r){case t8:return $3(t);case j3:case X3:return new a(+t);case e8:return W3(t,e);case r8:case a8:case i8:case n8:case o8:case s8:case l8:case c8:case u8:return H3(t,e);case G3:return new a;case K3:case q3:return new a(t);case U3:return Y3(t);case Q3:return new a;case J3:return Z3(t)}}var h8=d8,v8=Fr,Bn=Object.create,f8=function(){function t(){}return function(r){if(!v8(r))return{};if(Bn)return Bn(r);t.prototype=r;var e=new t;return t.prototype=void 0,e}}(),p8=f8,g8=p8,m8=hs,y8=Ii;function _8(t){return typeof t.constructor=="function"&&!y8(t)?g8(m8(t)):{}}var C8=_8,b8=Fi,x8=Br,w8="[object Map]";function L8(t){return x8(t)&&b8(t)==w8}var S8=L8,k8=S8,M8=Ti,Nn=Ei,On=Nn&&Nn.isMap,T8=On?M8(On):k8,E8=T8,I8=Fi,A8=Br,D8="[object Set]";function P8(t){return A8(t)&&I8(t)==D8}var F8=P8,R8=F8,B8=Ti,Vn=Ei,zn=Vn&&Vn.isSet,N8=zn?B8(zn):R8,O8=N8,V8=g5,z8=y5,$8=Li,W8=K6,Y8=hd,Z8=vd,H8=pd,j8=Md,X8=zd,G8=Gd,K8=Jd,U8=Fi,Q8=S3,q8=h8,J8=C8,th=Rr,eh=ns,rh=E8,ah=Fr,ih=O8,nh=Ai,oh=Di,sh=1,lh=2,ch=4,gs="[object Arguments]",uh="[object Array]",dh="[object Boolean]",hh="[object Date]",vh="[object Error]",ms="[object Function]",fh="[object GeneratorFunction]",ph="[object Map]",gh="[object Number]",ys="[object Object]",mh="[object RegExp]",yh="[object Set]",_h="[object String]",Ch="[object Symbol]",bh="[object WeakMap]",xh="[object ArrayBuffer]",wh="[object DataView]",Lh="[object Float32Array]",Sh="[object Float64Array]",kh="[object Int8Array]",Mh="[object Int16Array]",Th="[object Int32Array]",Eh="[object Uint8Array]",Ih="[object Uint8ClampedArray]",Ah="[object Uint16Array]",Dh="[object Uint32Array]",ie={};ie[gs]=ie[uh]=ie[xh]=ie[wh]=ie[dh]=ie[hh]=ie[Lh]=ie[Sh]=ie[kh]=ie[Mh]=ie[Th]=ie[ph]=ie[gh]=ie[ys]=ie[mh]=ie[yh]=ie[_h]=ie[Ch]=ie[Eh]=ie[Ih]=ie[Ah]=ie[Dh]=!0;ie[vh]=ie[ms]=ie[bh]=!1;function pa(t,r,e,a,i,n){var o,s=r&sh,l=r&lh,c=r&ch;if(e&&(o=i?e(t,a,i,n):e(t)),o!==void 0)return o;if(!ah(t))return t;var u=th(t);if(u){if(o=Q8(t),!s)return H8(t,o)}else{var d=U8(t),h=d==ms||d==fh;if(eh(t))return Z8(t,s);if(d==ys||d==gs||h&&!i){if(o=l||h?{}:J8(t),!s)return l?X8(t,Y8(o,t)):j8(t,W8(o,t))}else{if(!ie[d])return i?t:{};o=q8(t,d,s)}}n||(n=new V8);var v=n.get(t);if(v)return v;n.set(t,o),ih(t)?t.forEach(function(g){o.add(pa(g,r,e,g,t,n))}):rh(t)&&t.forEach(function(g,m){o.set(m,pa(g,r,e,m,t,n))});var f=c?l?K8:G8:l?oh:nh,p=u?void 0:f(t);return z8(p||t,function(g,m){p&&(m=g,g=t[m]),$8(o,m,pa(g,r,e,m,t,n))}),o}var Ph=pa,Fh=Ph,Rh=1,Bh=4;function Nh(t){return Fh(t,Rh|Bh)}var Oh=Nh;const Vh=Xo(Oh);var zh=Q("<button>");const $h=t=>(()=>{var r=zh();return Ke(r,"click",t.onClick),st(r,()=>t.children),_e(e=>{var a=t.style,i=`klinecharts-pro-button ${t.type??"confirm"} ${t.class??""}`;return e.e=Pr(r,a,e.e),i!==e.t&&xr(r,e.t=i),e},{e:void 0,t:void 0}),r})();Je(["click"]);var Wh=Q('<svg viewBox="0 0 1024 1024"class=icon><path d="M810.666667 128H213.333333c-46.933333 0-85.333333 38.4-85.333333 85.333333v597.333334c0 46.933333 38.4 85.333333 85.333333 85.333333h597.333334c46.933333 0 85.333333-38.4 85.333333-85.333333V213.333333c0-46.933333-38.4-85.333333-85.333333-85.333333z m-353.706667 567.04a42.496 42.496 0 0 1-60.16 0L243.626667 541.866667c-8.106667-8.106667-12.373333-18.773333-12.373334-29.866667s4.693333-22.186667 12.373334-29.866667a42.496 42.496 0 0 1 60.16 0L426.666667 604.586667l293.546666-293.546667a42.496 42.496 0 1 1 60.16 60.16l-323.413333 323.84z">'),Yh=Q('<svg viewBox="0 0 1024 1024"class=icon><path d="M245.333333 128h533.333334A117.333333 117.333333 0 0 1 896 245.333333v533.333334A117.333333 117.333333 0 0 1 778.666667 896H245.333333A117.333333 117.333333 0 0 1 128 778.666667V245.333333A117.333333 117.333333 0 0 1 245.333333 128z m0 64c-29.44 0-53.333333 23.893333-53.333333 53.333333v533.333334c0 29.44 23.893333 53.333333 53.333333 53.333333h533.333334c29.44 0 53.333333-23.893333 53.333333-53.333333V245.333333c0-29.44-23.893333-53.333333-53.333333-53.333333H245.333333z">'),Zh=Q("<div>"),Hh=Q("<span class=label>");const jh=()=>Wh(),Xh=()=>Yh(),$n=t=>{const[r,e]=Mt(t.checked??!1);return ir(()=>{"checked"in t&&e(t.checked)}),(()=>{var a=Zh();return a.$$click=i=>{const n=!r();t.onChange&&t.onChange(n),e(n)},st(a,(()=>{var i=Fe(()=>!!r());return()=>i()?ot(jh,{}):ot(Xh,{})})(),null),st(a,(()=>{var i=Fe(()=>!!t.label);return()=>i()&&(()=>{var n=Hh();return st(n,()=>t.label),n})()})(),null),_e(i=>{var n=t.style,o=`klinecharts-pro-checkbox ${r()&&"checked"||""} ${t.class||""}`;return i.e=Pr(a,n,i.e),o!==i.t&&xr(a,i.t=o),i},{e:void 0,t:void 0}),a})()};Je(["click"]);var Gh=Q("<div class=klinecharts-pro-loading><i class=circle1></i><i class=circle2></i><i class=circle3>");const _s=()=>Gh();var Kh=Q('<div class=klinecharts-pro-empty><svg class=icon viewBox="0 0 1024 1024"><path d="M855.6 427.2H168.5c-12.7 0-24.4 6.9-30.6 18L4.4 684.7C1.5 689.9 0 695.8 0 701.8v287.1c0 19.4 15.7 35.1 35.1 35.1H989c19.4 0 35.1-15.7 35.1-35.1V701.8c0-6-1.5-11.8-4.4-17.1L886.2 445.2c-6.2-11.1-17.9-18-30.6-18zM673.4 695.6c-16.5 0-30.8 11.5-34.3 27.7-12.7 58.5-64.8 102.3-127.2 102.3s-114.5-43.8-127.2-102.3c-3.5-16.1-17.8-27.7-34.3-27.7H119c-26.4 0-43.3-28-31.1-51.4l81.7-155.8c6.1-11.6 18-18.8 31.1-18.8h622.4c13 0 25 7.2 31.1 18.8l81.7 155.8c12.2 23.4-4.7 51.4-31.1 51.4H673.4zM819.9 209.5c-1-1.8-2.1-3.7-3.2-5.5-9.8-16.6-31.1-22.2-47.8-12.6L648.5 261c-17 9.8-22.7 31.6-12.6 48.4 0.9 1.4 1.7 2.9 2.5 4.4 9.5 17 31.2 22.8 48 13L807 257.3c16.7-9.7 22.4-31 12.9-47.8zM375.4 261.1L255 191.6c-16.7-9.6-38-4-47.8 12.6-1.1 1.8-2.1 3.6-3.2 5.5-9.5 16.8-3.8 38.1 12.9 47.8L337.3 327c16.9 9.7 38.6 4 48-13.1 0.8-1.5 1.7-2.9 2.5-4.4 10.2-16.8 4.5-38.6-12.4-48.4zM512 239.3h2.5c19.5 0.3 35.5-15.5 35.5-35.1v-139c0-19.3-15.6-34.9-34.8-35.1h-6.4C489.6 30.3 474 46 474 65.2v139c0 19.5 15.9 35.4 35.5 35.1h2.5z">');const Uh=()=>Kh();var Qh=Q("<ul>"),qh=Q("<li>");const Ia=t=>(()=>{var r=Qh();return st(r,ot(ke,{get when(){return t.loading},get children(){return ot(_s,{})}}),null),st(r,ot(ke,{get when(){var e;return!t.loading&&!t.children&&!((e=t.dataSource)!=null&&e.length)},get children(){return ot(Uh,{})}}),null),st(r,ot(ke,{get when(){return t.children},get children(){return t.children}}),null),st(r,ot(ke,{get when(){return!t.children},get children(){var e;return(e=t.dataSource)==null?void 0:e.map(a=>{var i;return((i=t.renderItem)==null?void 0:i.call(t,a))??qh()})}}),null),_e(e=>{var a=t.style,i=`klinecharts-pro-list ${t.class??""}`;return e.e=Pr(r,a,e.e),i!==e.t&&xr(r,e.t=i),e},{e:void 0,t:void 0}),r})();var Jh=Q('<div class=klinecharts-pro-modal><div class=inner><div class=title-container><svg class=close-icon viewBox="0 0 1024 1024"><path d="M934.184927 199.723787 622.457206 511.452531l311.727721 311.703161c14.334473 14.229073 23.069415 33.951253 23.069415 55.743582 0 43.430138-35.178197 78.660524-78.735226 78.660524-21.664416 0-41.361013-8.865925-55.642275-23.069415L511.149121 622.838388 199.420377 934.490384c-14.204513 14.20349-33.901111 23.069415-55.642275 23.069415-43.482327 0-78.737272-35.230386-78.737272-78.660524 0-21.792329 8.864902-41.513486 23.094998-55.743582l311.677579-311.703161L88.135828 199.723787c-14.230096-14.255679-23.094998-33.92567-23.094998-55.642275 0-43.430138 35.254945-78.762855 78.737272-78.762855 21.741163 0 41.437761 8.813736 55.642275 23.069415l311.727721 311.727721L822.876842 88.389096c14.281261-14.255679 33.977859-23.069415 55.642275-23.069415 43.557028 0 78.735226 35.332716 78.735226 78.762855C957.254342 165.798117 948.5194 185.468109 934.184927 199.723787"></path></svg></div><div class=content-container>'),t7=Q("<div class=button-container>");const Wr=t=>(()=>{var r=Jh(),e=r.firstChild,a=e.firstChild,i=a.firstChild,n=a.nextSibling;return st(a,()=>t.title,i),Ke(i,"click",t.onClose),st(n,()=>t.children),st(e,(()=>{var o=Fe(()=>!!(t.buttons&&t.buttons.length>0));return()=>o()&&(()=>{var s=t7();return st(s,()=>t.buttons.map(l=>ot($h,jo(l,{get children(){return l.children}})))),s})()})(),null),_e(o=>(o=`${t.width??400}px`)!=null?e.style.setProperty("width",o):e.style.removeProperty("width")),r})();Je(["click"]);var e7=Q("<div tabindex=0><div class=selector-container><span class=value></span><i class=arrow>"),r7=Q("<div class=drop-down-container><ul>"),a7=Q("<li>");const Cs=t=>{const[r,e]=Mt(!1);return(()=>{var a=e7(),i=a.firstChild,n=i.firstChild;return a.addEventListener("blur",o=>{e(!1)}),a.$$click=o=>{e(s=>!s)},st(n,()=>t.value),st(a,(()=>{var o=Fe(()=>!!(t.dataSource&&t.dataSource.length>0));return()=>o()&&(()=>{var s=r7(),l=s.firstChild;return st(l,()=>t.dataSource.map(c=>{const u=c[t.valueKey??"text"]??c;return(()=>{var d=a7();return d.$$click=h=>{var v;h.stopPropagation(),t.value!==u&&((v=t.onSelected)==null||v.call(t,c)),e(!1)},st(d,u),d})()})),s})()})(),null),_e(o=>{var s=t.style,l=`klinecharts-pro-select ${t.class??""} ${r()?"klinecharts-pro-select-show":""}`;return o.e=Pr(a,s,o.e),l!==o.t&&xr(a,o.t=l),o},{e:void 0,t:void 0}),a})()};Je(["click"]);var i7=Q("<span class=prefix>"),n7=Q("<span class=suffix>"),o7=Q("<div><input class=value>");const bs=t=>{const r=jo({min:Number.MIN_SAFE_INTEGER,max:Number.MAX_SAFE_INTEGER},t);let e;const[a,i]=Mt("normal");return(()=>{var n=o7(),o=n.firstChild;return n.$$click=()=>{e==null||e.focus()},st(n,ot(ke,{get when(){return r.prefix},get children(){var s=i7();return st(s,()=>r.prefix),s}}),o),o.$$input=s=>{var l;const c=s.target.value;typeof c=="string"&&((l=r.onChange)==null||l.call(r,c))},o.addEventListener("blur",()=>{i("normal")}),o.addEventListener("focus",()=>{i("focus")}),wi(s=>{e=s},o),st(n,ot(ke,{get when(){return r.suffix},get children(){var s=n7();return st(s,()=>r.suffix),s}}),null),_e(s=>{var l=r.style,c=`klinecharts-pro-input ${r.class??""}`,u=a(),d=r.placeholder??"";return s.e=Pr(n,l,s.e),c!==s.t&&xr(n,s.t=c),u!==s.a&&Oe(n,"data-status",s.a=u),d!==s.o&&Oe(o,"placeholder",s.o=d),s},{e:void 0,t:void 0,a:void 0,o:void 0}),_e(()=>o.value=r.value),n})()};Je(["click","input"]);var s7=Q("<div><i class=thumb>");const l7=t=>(()=>{var r=s7();return r.$$click=e=>{t.onChange&&t.onChange()},_e(e=>{var a=t.style,i=`klinecharts-pro-switch ${t.open?"turn-on":"turn-off"} ${t.class??""}`;return e.e=Pr(r,a,e.e),i!==e.t&&xr(r,e.t=i),e},{e:void 0,t:void 0}),r})();Je(["click"]);const c7="指标",u7="主图指标",d7="副图指标",h7="设置",v7="时区",f7="截屏",p7="订单流",g7="AI预测",m7="全屏",y7="退出全屏",_7="保存",C7="确定",b7="取消",x7="MA(移动平均线)",w7="EMA(指数平滑移动平均线)",L7="SMA",S7="BOLL(布林线)",k7="BBI(多空指数)",M7="SAR(停损点指向指标)",T7="VOL(成交量)",E7="MACD(指数平滑异同移动平均线)",I7="KDJ(随机指标)",A7="RSI(相对强弱指标)",D7="BIAS(乖离率)",P7="BRAR(情绪指标)",F7="CCI(顺势指标)",R7="DMI(动向指标)",B7="CR(能量指标)",N7="PSY(心理线)",O7="DMA(平行线差指标)",V7="TRIX(三重指数平滑平均线)",z7="OBV(能量潮指标)",$7="VR(成交量变异率)",W7="WR(威廉指标)",Y7="MTM(动量指标)",Z7="EMV(简易波动指标)",H7="ROC(变动率指标)",j7="PVT(价量趋势指标)",X7="AO(动量震荡指标)",G7="世界统一时间",K7="(UTC-10) 檀香山",U7="(UTC-8) 朱诺",Q7="(UTC-7) 洛杉矶",q7="(UTC-5) 芝加哥",J7="(UTC-4) 多伦多",tv="(UTC-3) 圣保罗",ev="(UTC+1) 伦敦",rv="(UTC+2) 柏林",av="(UTC+3) 巴林",iv="(UTC+4) 迪拜",nv="(UTC+5) 阿什哈巴德",ov="(UTC+6) 阿拉木图",sv="(UTC+7) 曼谷",lv="(UTC+8) 上海",cv="(UTC+9) 东京",uv="(UTC+10) 悉尼",dv="(UTC+12) 诺福克岛",hv="水平直线",vv="水平射线",fv="水平线段",pv="垂直直线",gv="垂直射线",mv="垂直线段",yv="直线",_v="射线",Cv="线段",bv="箭头",xv="价格线",wv="价格通道线",Lv="平行直线",Sv="斐波那契回调直线",kv="斐波那契回调线段",Mv="斐波那契圆环",Tv="斐波那契螺旋",Ev="斐波那契速度阻力扇",Iv="斐波那契趋势扩展",Av="江恩箱",Dv="矩形",Pv="平行四边形",Fv="圆",Rv="三角形",Bv="三浪",Nv="五浪",Ov="八浪",Vv="任意浪",zv="ABCD形态",$v="XABCD形态",Wv="弱磁模式",Yv="强磁模式",Zv="商品搜索",Hv="商品代码",jv="参数1",Xv="参数2",Gv="参数3",Kv="参数4",Uv="参数5",Qv="周期",qv="标准差",Jv="蜡烛图类型",t4="全实心",e4="全空心",r4="涨空心",a4="跌空心",i4="OHLC",n4="面积图",o4="最新价显示",s4="最高价显示",l4="最低价显示",c4="指标最新值显示",u4="价格轴类型",d4="线性轴",h4="百分比轴",v4="对数轴",f4="倒置坐标",p4="网格线显示",g4="恢复默认",m4={indicator:c7,main_indicator:u7,sub_indicator:d7,setting:h7,timezone:v7,screenshot:f7,order_flow:p7,ai_prediction:g7,full_screen:m7,exit_full_screen:y7,save:_7,confirm:C7,cancel:b7,ma:x7,ema:w7,sma:L7,boll:S7,bbi:k7,sar:M7,vol:T7,macd:E7,kdj:I7,rsi:A7,bias:D7,brar:P7,cci:F7,dmi:R7,cr:B7,psy:N7,dma:O7,trix:V7,obv:z7,vr:$7,wr:W7,mtm:Y7,emv:Z7,roc:H7,pvt:j7,ao:X7,utc:G7,honolulu:K7,juneau:U7,los_angeles:Q7,chicago:q7,toronto:J7,sao_paulo:tv,london:ev,berlin:rv,bahrain:av,dubai:iv,ashkhabad:nv,almaty:ov,bangkok:sv,shanghai:lv,tokyo:cv,sydney:uv,norfolk:dv,horizontal_straight_line:hv,horizontal_ray_line:vv,horizontal_segment:fv,vertical_straight_line:pv,vertical_ray_line:gv,vertical_segment:mv,straight_line:yv,ray_line:_v,segment:Cv,arrow:bv,price_line:xv,price_channel_line:wv,parallel_straight_line:Lv,fibonacci_line:Sv,fibonacci_segment:kv,fibonacci_circle:Mv,fibonacci_spiral:Tv,fibonacci_speed_resistance_fan:Ev,fibonacci_extension:Iv,gann_box:Av,rect:Dv,parallelogram:Pv,circle:Fv,triangle:Rv,three_waves:Bv,five_waves:Nv,eight_waves:Ov,any_waves:Vv,abcd:zv,xabcd:$v,weak_magnet:Wv,strong_magnet:Yv,symbol_search:Zv,symbol_code:Hv,params_1:jv,params_2:Xv,params_3:Gv,params_4:Kv,params_5:Uv,period:Qv,standard_deviation:qv,candle_type:Jv,candle_solid:t4,candle_stroke:e4,candle_up_stroke:r4,candle_down_stroke:a4,ohlc:i4,area:n4,last_price_show:o4,high_price_show:s4,low_price_show:l4,indicator_last_value_show:c4,price_axis_type:u4,normal:d4,percentage:h4,log:v4,reverse_coordinate:f4,grid_show:p4,restore_default:g4},y4="Indicator",_4="Main Indicator",C4="Sub Indicator",b4="Setting",x4="Timezone",w4="Screenshot",L4="Order Flow",S4="AI Prediction",k4="Full Screen",M4="Exit",T4="Save",E4="Confirm",I4="Cancel",A4="MA(Moving Average)",D4="EMA(Exponential Moving Average)",P4="SMA",F4="BOLL(Bolinger Bands)",R4="BBI(Bull And Bearlndex)",B4="SAR(Stop and Reverse)",N4="VOL(Volume)",O4="MACD(Moving Average Convergence / Divergence)",V4="KDJ(KDJ Index)",z4="RSI(Relative Strength Index)",$4="BIAS(Bias Ratio)",W4="BRAR(情绪指标)",Y4="CCI(Commodity Channel Index)",Z4="DMI(Directional Movement Index)",H4="CR(能量指标)",j4="PSY(Psychological Line)",X4="DMA(Different of Moving Average)",G4="TRIX(Triple Exponentially Smoothed Moving Average)",K4="OBV(On Balance Volume)",U4="VR(Volatility Volume Ratio)",Q4="WR(Williams %R)",q4="MTM(Momentum Index)",J4="EMV(Ease of Movement Value)",tf="ROC(Price Rate of Change)",ef="PVT(Price and Volume Trend)",rf="AO(Awesome Oscillator)",af="UTC",nf="(UTC-10) Honolulu",of="(UTC-8) Juneau",sf="(UTC-7) Los Angeles",lf="(UTC-5) Chicago",cf="(UTC-4) Toronto",uf="(UTC-3) Sao Paulo",df="(UTC+1) London",hf="(UTC+2) Berlin",vf="(UTC+3) Bahrain",ff="(UTC+4) Dubai",pf="(UTC+5) Ashkhabad",gf="(UTC+6) Almaty",mf="(UTC+7) Bangkok",yf="(UTC+8) Shanghai",_f="(UTC+9) Tokyo",Cf="(UTC+10) Sydney",bf="(UTC+12) Norfolk",xf="Horizontal Line",wf="Horizontal Ray",Lf="Horizontal Segment",Sf="Vertical Line",kf="Vertical Ray",Mf="Vertical Segment",Tf="Trend Line",Ef="Ray",If="Segment",Af="Arrow",Df="Price Line",Pf="Price Channel Line",Ff="Parallel Line",Rf="Fibonacci Line",Bf="Fibonacci Segment",Nf="Fibonacci Circle",Of="Fibonacci Spiral",Vf="Fibonacci Sector",zf="Fibonacci Extension",$f="Gann Box",Wf="Rect",Yf="Parallelogram",Zf="Circle",Hf="Triangle",jf="Three Waves",Xf="Five Waves",Gf="Eight Waves",Kf="Any Waves",Uf="ABCD Pattern",Qf="XABCD Pattern",qf="Weak Magnet",Jf="Strong Magnet",tp="Symbol Search",ep="Symbol Code",rp="Parameter 1",ap="Parameter 2",ip="Parameter 3",np="Parameter 4",op="Parameter 5",sp="Period",lp="Standard Deviation",cp="Candle Type",up="Candle Solid",dp="Candle Stroke",hp="Candle Up Stroke",vp="Candle Down Stroke",fp="OHLC",pp="Area",gp="Show Last Price",mp="Show Highest Price",yp="Show Lowest Price",_p="Show indicator's last value",Cp="Price Axis Type",bp="Normal",xp="Percentage",wp="Log",Lp="Reverse Coordinate",Sp="Show Grids",kp="Restore Defaults",Mp={indicator:y4,main_indicator:_4,sub_indicator:C4,setting:b4,timezone:x4,screenshot:w4,order_flow:L4,ai_prediction:S4,full_screen:k4,exit_full_screen:M4,save:T4,confirm:E4,cancel:I4,ma:A4,ema:D4,sma:P4,boll:F4,bbi:R4,sar:B4,vol:N4,macd:O4,kdj:V4,rsi:z4,bias:$4,brar:W4,cci:Y4,dmi:Z4,cr:H4,psy:j4,dma:X4,trix:G4,obv:K4,vr:U4,wr:Q4,mtm:q4,emv:J4,roc:tf,pvt:ef,ao:rf,utc:af,honolulu:nf,juneau:of,los_angeles:sf,chicago:lf,toronto:cf,sao_paulo:uf,london:df,berlin:hf,bahrain:vf,dubai:ff,ashkhabad:pf,almaty:gf,bangkok:mf,shanghai:yf,tokyo:_f,sydney:Cf,norfolk:bf,horizontal_straight_line:xf,horizontal_ray_line:wf,horizontal_segment:Lf,vertical_straight_line:Sf,vertical_ray_line:kf,vertical_segment:Mf,straight_line:Tf,ray_line:Ef,segment:If,arrow:Af,price_line:Df,price_channel_line:Pf,parallel_straight_line:Ff,fibonacci_line:Rf,fibonacci_segment:Bf,fibonacci_circle:Nf,fibonacci_spiral:Of,fibonacci_speed_resistance_fan:Vf,fibonacci_extension:zf,gann_box:$f,rect:Wf,parallelogram:Yf,circle:Zf,triangle:Hf,three_waves:jf,five_waves:Xf,eight_waves:Gf,any_waves:Kf,abcd:Uf,xabcd:Qf,weak_magnet:qf,strong_magnet:Jf,symbol_search:tp,symbol_code:ep,params_1:rp,params_2:ap,params_3:ip,params_4:np,params_5:op,period:sp,standard_deviation:lp,candle_type:cp,candle_solid:up,candle_stroke:dp,candle_up_stroke:hp,candle_down_stroke:vp,ohlc:fp,area:pp,last_price_show:gp,high_price_show:mp,low_price_show:yp,indicator_last_value_show:_p,price_axis_type:Cp,normal:bp,percentage:xp,log:wp,reverse_coordinate:Lp,grid_show:Sp,restore_default:kp},Tp={"zh-CN":m4,"en-US":Mp},z=(t,r)=>{var e;return((e=Tp[r])==null?void 0:e[t])??t};var Ep=Q("<img alt=symbol>"),Ip=Q("<div class=symbol><span>"),Ap=Q('<div class=klinecharts-pro-period-bar><div class=menu-container><svg viewBox="0 0 1024 1024"><path d="M192.037 287.953h640.124c17.673 0 32-14.327 32-32s-14.327-32-32-32H192.037c-17.673 0-32 14.327-32 32s14.327 32 32 32zM832.161 479.169H438.553c-17.673 0-32 14.327-32 32s14.327 32 32 32h393.608c17.673 0 32-14.327 32-32s-14.327-32-32-32zM832.161 735.802H192.037c-17.673 0-32 14.327-32 32s14.327 32 32 32h640.124c17.673 0 32-14.327 32-32s-14.327-32-32-32zM319.028 351.594l-160 160 160 160z"></path></svg></div><div class="item tools"><svg viewBox="0 0 20 20"><path d=M15.873,20L3.65079,20C1.5873,20,0,18.3871,0,16.2903L0,3.70968C-3.78442e-7,1.6129,1.5873,0,3.65079,0L15.873,0C17.9365,0,19.5238,1.6129,19.5238,3.70968C19.5238,4.35484,19.2063,4.51613,18.5714,4.51613C17.9365,4.51613,17.619,4.19355,17.619,3.70968C17.619,2.74194,16.8254,1.93548,15.873,1.93548L3.65079,1.93548C2.69841,1.93548,1.90476,2.74194,1.90476,3.70968L1.90476,16.2903C1.90476,17.2581,2.69841,18.0645,3.65079,18.0645L15.873,18.0645C16.8254,18.0645,17.619,17.2581,17.619,16.2903C17.619,15.8065,18.0952,15.3226,18.5714,15.3226C19.0476,15.3226,19.5238,15.8065,19.5238,16.2903C19.5238,18.2258,17.9365,20,15.873,20ZM14.9206,12.9032C14.7619,12.9032,14.4444,12.9032,14.2857,12.7419L11.2698,9.35484C10.9524,9.03226,10.9524,8.54839,11.2698,8.22581C11.5873,7.90323,12.0635,7.90323,12.381,8.22581L15.3968,11.6129C15.7143,11.9355,15.7143,12.4194,15.3968,12.7419C15.3968,12.9032,15.2381,12.9032,14.9206,12.9032ZM11.4286,13.2258C11.2698,13.2258,11.1111,13.2258,10.9524,13.0645C10.6349,12.7419,10.6349,12.4194,10.9524,12.0968L15.0794,7.74193C15.3968,7.41935,15.7143,7.41935,16.0317,7.74193C16.3492,8.06452,16.3492,8.3871,16.0317,8.70968L11.9048,13.0645C11.746,13.2258,11.5873,13.2258,11.4286,13.2258ZM10.3175,3.70968C10.6349,3.70968,11.4286,3.87097,11.4286,4.67742C11.4286,5.32258,10.4762,5.16129,10.1587,5.16129C8.73016,5.16129,8.25397,5.96774,8.09524,6.6129L7.77778,8.54839L9.36508,8.54839C9.68254,8.54839,10,8.87097,10,9.19355C10,9.51613,9.68254,9.83871,9.36508,9.83871L7.61905,9.83871L6.50794,14.8387Q6.34921,16.2903,5.39683,16.2903Q4.44444,16.2903,4.92064,14.8387L6.03175,10L4.60317,10C4.28571,10,3.96825,9.67742,3.96825,9.35484C3.96825,8.70968,4.28571,8.54839,4.60317,8.54839L6.34921,8.54839L6.8254,6.45161C7.14286,3.70968,9.52381,3.54839,10.3175,3.70968ZM18.4127,6.6129C18.5714,6.12903,18.8889,5.96774,19.3651,5.96774C19.8413,6.12903,20,6.45161,20,6.93548L18.4127,13.3871C18.254,13.871,17.9365,14.0323,17.4603,14.0323C16.9841,13.871,16.8254,13.5484,16.8254,13.0645L18.4127,6.6129Z></path></svg><span></span></div><div class="item tools"><svg width=20 height=20 viewBox="0 0 20 20"><path d=M18.5446,9.09091C18.3333,6.61616,17.2887,4.31818,15.5751,2.63889C13.8498,0.94697,11.6197,0,9.28404,0C8.02817,0,6.81925,0.265151,5.66901,0.782828C5.65728,0.782828,5.65728,0.795454,5.64554,0.795454C5.6338,0.795454,5.6338,0.808081,5.62207,0.808081C4.53052,1.31313,3.55634,2.0202,2.71127,2.92929C1.85446,3.85101,1.18545,4.91162,0.715963,6.11111C0.246479,7.33586,0,8.64899,0,10C0,10.8712,0.105634,11.7172,0.305164,12.5379C0.305164,12.5631,0.316901,12.5884,0.328638,12.6136C0.739437,14.2298,1.51408,15.7197,2.62911,16.9571C4.07277,18.548,5.92723,19.5581,7.93427,19.8737C7.95775,19.8737,7.96948,19.8864,7.99296,19.8864C8.3216,19.9369,8.66197,19.9747,9.00235,19.9747L9.21362,19.9747C9.61268,19.9747,10.3756,19.9369,11.0094,19.697C11.1737,19.6338,11.3028,19.5076,11.3732,19.3434C11.4437,19.1793,11.4554,18.9899,11.3967,18.8131C11.3028,18.5354,11.0563,18.346,10.7864,18.346C10.716,18.346,10.6338,18.3586,10.5634,18.3838C10.0939,18.5606,9.46009,18.5859,9.20188,18.5859L9.09624,18.5859C9.20188,18.2702,9.23709,17.9167,9.15493,17.5505C9.00235,16.8939,8.50939,16.3384,7.58216,15.7955L7.19484,15.5682C6.57277,15.2146,6.23239,15.0253,6.03286,14.7348C5.83333,14.4444,5.69249,13.9899,5.51643,12.9798C5.38732,12.298,5.04695,11.7677,4.50704,11.4646C4.14319,11.2626,3.70892,11.149,3.19249,11.149C2.82864,11.149,2.42958,11.1995,2.00704,11.3005C1.79578,11.351,1.59624,11.4141,1.42019,11.4646C1.33803,10.9848,1.30282,10.4798,1.30282,9.97475C1.30282,6.93182,2.76995,4.26768,4.98826,2.72727C5,3.00505,5.05869,3.29545,5.17606,3.57323C5.48122,4.26768,6.10329,4.7096,7.01878,4.89899C7.06573,4.91162,7.10094,4.91162,7.13615,4.91162L7.1831,4.91162C7.26526,4.91162,7.57042,4.92424,7.88732,5.0505C8.3216,5.2399,8.56808,5.55555,8.65023,6.04798C8.84977,7.61364,9.07277,10.4293,8.79108,11.3384C8.76761,11.4141,8.75587,11.4899,8.75587,11.5657C8.75587,11.9444,9.0493,12.2601,9.40141,12.2601C9.57747,12.2601,9.74179,12.1843,9.85915,12.0581C9.97653,11.9318,12.6174,9.05303,13.3216,8.09343C13.4038,7.97979,13.4859,7.87878,13.5798,7.76515C13.9202,7.33586,14.2723,6.90656,14.4014,6.26262C14.554,5.56818,14.4014,4.79798,13.9437,3.85101C13.615,3.16919,13.5563,2.86616,13.5446,2.75252C13.5563,2.7399,13.5798,2.72727,13.6033,2.71464C15.6221,4.10353,17.0188,6.43939,17.2535,9.19192C17.2887,9.55808,17.5587,9.82323,17.8991,9.82323L17.9577,9.82323C18.3099,9.8106,18.5681,9.48232,18.5446,9.09091ZM3.19249,12.5631C3.48592,12.5631,3.72066,12.6136,3.89671,12.7146C4.08451,12.8283,4.19014,12.9924,4.23709,13.2702C4.43662,14.3434,4.61268,15.0631,5,15.6061C5.37559,16.1364,5.85681,16.4015,6.58451,16.8182L6.60798,16.8308C6.71362,16.8939,6.84272,16.9571,6.96009,17.0328C7.69953,17.4621,7.86385,17.7525,7.89906,17.8914C7.93427,18.0303,7.85211,18.2323,7.74648,18.4343C4.91784,17.8535,2.65258,15.6944,1.73709,12.8283C2.15962,12.702,2.71127,12.5631,3.19249,12.5631ZM12.7934,4.5202C13.4272,5.83333,13.1455,6.18687,12.5822,6.89394C12.4883,7.00758,12.3944,7.12121,12.3005,7.24747C11.9484,7.72727,11.0211,8.77525,10.2113,9.68434C10.2113,9.24242,10.1878,8.73737,10.1526,8.19444C10.0704,6.95707,9.92958,5.90909,9.92958,5.87121L9.92958,5.83333C9.75352,4.83586,9.20188,4.11616,8.3216,3.76263C7.82864,3.56061,7.37089,3.53535,7.19484,3.53535C6.73709,3.43434,6.4554,3.24495,6.33803,2.99242C6.19718,2.68939,6.29108,2.24747,6.38498,1.9697C7.28873,1.59091,8.26291,1.37626,9.28404,1.37626C10.3873,1.37626,11.4437,1.61616,12.4061,2.04545C12.3357,2.18434,12.277,2.34848,12.2535,2.5505C12.2066,3.04293,12.3709,3.64899,12.7934,4.5202Z></path><path d=M15.22299772857666,9.722223632261718C12.59389772857666,9.722223632261718,10.44600772857666,12.020201374511718,10.44600772857666,14.861111374511719C10.44600772857666,17.70202137451172,12.58215772857666,20.000021374511718,15.223007728576661,20.000021374511718C17.86384772857666,20.000021374511718,19.99999772857666,17.70202137451172,19.99999772857666,14.861111374511719C19.99999772857666,12.020201374511718,17.85211772857666,9.72222212709572,15.22299772857666,9.722223632261718ZM15.22299772857666,18.598491374511717C13.30985772857666,18.598491374511717,11.737087728576661,16.91919137451172,11.737087728576661,14.848481374511719C11.737087728576661,12.777781374511719,13.29811772857666,11.098491374511719,15.22299772857666,11.098491374511719C17.14787772857666,11.098491374511719,18.708917728576658,12.777781374511719,18.708917728576658,14.848481374511719C18.708917728576658,16.91919137451172,17.13614772857666,18.59848137451172,15.22299772857666,18.598491374511717Z></path><path d=M15.692486288146974,15.050496970825195L15.692486288146974,12.676760970825196C15.692486288146974,12.297972970825196,15.399058288146973,11.982316970825195,15.046945288146972,11.982316970825195C14.694833288146972,11.982316970825195,14.401406288146973,12.297972970825196,14.401406288146973,12.676760970825196L14.401406288146973,15.340896970825195C14.401406288146973,15.530296970825194,14.471829288146973,15.694436970825196,14.589200288146973,15.833326970825196L15.751176288146972,17.095956970825195C15.868546288146973,17.222216970825194,16.032866288146973,17.297976970825196,16.208916288146973,17.297976970825196C16.384976288146973,17.297976970825196,16.537556288146973,17.222216970825194,16.666666288146974,17.095956970825195C16.78403628814697,16.969686970825194,16.854456288146974,16.792916970825196,16.854456288146974,16.603526970825193C16.854456288146974,16.414136970825197,16.78403628814697,16.237366970825196,16.666666288146974,16.111106970825197L15.692486288146974,15.050496970825195Z></path></svg><span></span></div><div class="item tools"><svg viewBox="0 0 20 20"><path d=M19.7361,12.542L18.1916,11.2919C18.2647,10.8678,18.3025,10.4347,18.3025,10.0017C18.3025,9.56861,18.2647,9.13555,18.1916,8.71142L19.7361,7.46135C19.9743,7.26938,20.0615,6.95686,19.9554,6.6756L19.9342,6.61756C19.5074,5.49026,18.8755,4.45449,18.0549,3.53926L18.0124,3.49238C17.8096,3.26692,17.4819,3.1821,17.1848,3.28032L15.2677,3.92544C14.5603,3.3763,13.7704,2.94324,12.9168,2.63966L12.5466,0.742229C12.49,0.449802,12.2472,0.222111,11.9383,0.168536L11.8746,0.157375C10.6461,-0.0524583,9.35391,-0.0524583,8.1254,0.157375L8.06174,0.168536C7.75284,0.222111,7.50997,0.449802,7.45338,0.742229L7.08082,2.64859C6.2343,2.95217,5.44909,3.383,4.74641,3.92991L2.81522,3.28032C2.52047,3.1821,2.19036,3.26469,1.98757,3.49238L1.94513,3.53926C1.12455,4.45672,0.492609,5.49249,0.0658141,6.61756L0.0445921,6.6756C-0.0615171,6.95463,0.0257283,7.26715,0.263885,7.46135L1.82723,8.72482C1.75413,9.14448,1.71876,9.57308,1.71876,9.99944C1.71876,10.428,1.75413,10.8566,1.82723,11.2741L0.263885,12.5375C0.025729,12.7295,-0.0615164,13.042,0.0445929,13.3233L0.0658148,13.3813C0.49261,14.5064,1.12455,15.5444,1.94513,16.4596L1.98757,16.5065C2.19036,16.732,2.51812,16.8168,2.81522,16.7186L4.74641,16.069C5.44909,16.6159,6.2343,17.0489,7.08082,17.3503L7.45338,19.2567C7.50997,19.5491,7.75284,19.7768,8.06174,19.8303L8.1254,19.8415C8.74084,19.9464,9.37042,20,10,20C10.6296,20,11.2615,19.9464,11.8746,19.8415L11.9383,19.8303C12.2472,19.7768,12.49,19.5491,12.5466,19.2567L12.9168,17.3592C13.7704,17.0556,14.5603,16.6248,15.2677,16.0734L17.1848,16.7186C17.4795,16.8168,17.8096,16.7342,18.0124,16.5065L18.0549,16.4596C18.8755,15.5422,19.5074,14.5064,19.9342,13.3813L19.9554,13.3233C20.0615,13.0487,19.9743,12.7362,19.7361,12.542ZM16.5175,8.97483C16.5764,9.3119,16.6071,9.65791,16.6071,10.0039C16.6071,10.3499,16.5764,10.6959,16.5175,11.033L16.3618,11.9281L18.1233,13.3545C17.8568,13.9372,17.5196,14.4863,17.1188,14.9975L14.9305,14.2631L14.1901,14.839C13.6266,15.2765,12.9994,15.6203,12.3203,15.8614L11.4219,16.1806L10.9998,18.3459C10.3372,18.4173,9.66045,18.4173,8.9955,18.3459L8.57342,16.1761L7.6821,15.8524C7.01008,15.6114,6.38521,15.2676,5.82637,14.8323L5.08596,14.2541L2.88361,14.9953C2.48275,14.4841,2.14791,13.9327,1.8791,13.3523L3.65938,11.9125L3.50611,11.0196C3.44952,10.687,3.41887,10.3432,3.41887,10.0039C3.41887,9.66237,3.44716,9.32083,3.50611,8.98822L3.65938,8.09531L1.8791,6.6555C2.14556,6.07288,2.48275,5.52374,2.88361,5.01255L5.08596,5.75367L5.82637,5.17551C6.38521,4.74022,7.01008,4.39645,7.6821,4.15536L8.57578,3.83615L8.99786,1.66638C9.66045,1.59495,10.3372,1.59495,11.0021,1.66638L11.4242,3.83168L12.3226,4.1509C12.9994,4.39198,13.6289,4.73575,14.1925,5.17328L14.9329,5.7492L17.1211,5.01479C17.522,5.52598,17.8568,6.07734,18.1256,6.65773L16.3642,8.08416L16.5175,8.97483ZM10.0024,5.85189C7.7104,5.85189,5.85231,7.61092,5.85231,9.78068C5.85231,11.9504,7.7104,13.7095,10.0024,13.7095C12.2943,13.7095,14.1524,11.9504,14.1524,9.78068C14.1524,7.61092,12.2943,5.85189,10.0024,5.85189ZM11.8699,11.5486C11.37,12.0196,10.7074,12.2808,10.0024,12.2808C9.29732,12.2808,8.63473,12.0196,8.13483,11.5486C7.6373,11.0754,7.36142,10.4481,7.36142,9.78068C7.36142,9.11323,7.6373,8.48596,8.13483,8.01272C8.63473,7.53948,9.29732,7.28054,10.0024,7.28054C10.7074,7.28054,11.37,7.53948,11.8699,8.01272C12.3674,8.48596,12.6433,9.11323,12.6433,9.78068C12.6433,10.4481,12.3674,11.0754,11.8699,11.5486Z></path></svg><span></span></div><div class="item tools"><svg viewBox="0 0 20 20"><path d=M6.50977,1L13.4902,1C13.6406,1,13.7695,1.1104910000000001,13.7969,1.2631700000000001L14.0273,2.52277C14.1387,3.13147,14.6543,3.57143,15.2559,3.57143L17.5,3.57143C18.8809,3.57143,20,4.72254,20,6.14286L20,16.4286C20,17.8489,18.8809,19,17.5,19L2.5,19C1.11914,19,0,17.8489,0,16.4286L0,6.14286C0,4.72254,1.11914,3.57143,2.5,3.57143L4.74414,3.57143C5.3457,3.57143,5.86133,3.13147,5.97266,2.52277L6.20312,1.2631700000000001C6.23047,1.1104910000000001,6.35937,1,6.50977,1ZM15.2559,4.857139999999999C14.0547,4.857139999999999,13.0215,3.97522,12.7988,2.75982L12.7129,2.28571L7.28711,2.28571L7.20117,2.75982C6.98047,3.97522,5.94727,4.857139999999999,4.74414,4.857139999999999L2.5,4.857139999999999C1.81055,4.857139999999999,1.25,5.43371,1.25,6.14286L1.25,16.4286C1.25,17.1377,1.81055,17.7143,2.5,17.7143L17.5,17.7143C18.1895,17.7143,18.75,17.1377,18.75,16.4286L18.75,6.14286C18.75,5.43371,18.1895,4.857139999999999,17.5,4.857139999999999L15.2559,4.857139999999999ZM4.375,6.78571L3.125,6.78571C2.7793,6.78571,2.5,6.49844,2.5,6.14286C2.5,5.78728,2.7793,5.5,3.125,5.5L4.375,5.5C4.7207,5.5,5,5.78728,5,6.14286C5,6.49844,4.7207,6.78571,4.375,6.78571ZM10,6.14286C7.06641,6.14286,4.6875,8.58973,4.6875,11.6071C4.6875,14.6246,7.06641,17.0714,10,17.0714C12.9336,17.0714,15.3125,14.6246,15.3125,11.6071C15.3125,8.58973,12.9336,6.14286,10,6.14286ZM10,7.42857C11.0859,7.42857,12.1055,7.8625,12.873,8.65201C13.6406,9.44152,14.0625,10.49018,14.0625,11.6071C14.0625,12.7241,13.6406,13.7728,12.873,14.5623C12.1055,15.3518,11.0859,15.7857,10,15.7857C8.91406,15.7857,7.89453,15.3518,7.12695,14.5623C6.35937,13.7728,5.9375,12.7241,5.9375,11.6071C5.9375,10.49018,6.35938,9.44152,7.12695,8.65201C7.89453,7.8625,8.91406,7.42857,10,7.42857ZM10,9.67857C8.96484,9.67857,8.125,10.54241,8.125,11.6071C8.125,12.6719,8.96484,13.5357,10,13.5357C11.0352,13.5357,11.875,12.6719,11.875,11.6071C11.875,10.54241,11.0352,9.67857,10,9.67857ZM10,10.96429C10.3438,10.96429,10.625,11.2536,10.625,11.6071C10.625,11.9607,10.3438,12.25,10,12.25C9.65625,12.25,9.375,11.9607,9.375,11.6071C9.375,11.2536,9.65625,10.96429,10,10.96429Z></path></svg><span></span></div><div class="item tools"><svg viewBox="0 0 20 20"><path d="M2.22 11.78h1.6V18h-1.6zM6.76 9.78h1.6V18h-1.6zM11.3 11h1.6v7h-1.6zM15.84 7.43h1.6V18h-1.6z"></path><path d="M3.43 10.5l-0.78-0.92 4.75-3.93 0.74-0.02 3.83 2.93 4.35-5.9 0.96 0.7-4.73 6.5-0.84 0.12-3.95-3.03z"></path><path d="M16.62 3.23a1.2 1.2 0 1 1 2.4 0 1.2 1.2 0 1 1-2.4 0z"></path></svg><span></span></div><div class="item tools"><svg viewBox="0 0 18 18"><g stroke=none stroke-width=1 fill=none fill-rule=evenodd><g id=ai fill-rule=nonzero><path d="M1.69684615,10.0339932 L1.69684615,12.8915508 C1.69715587,13.1123425 1.80257882,13.3197686 1.98069231,13.4500356 L2.04992308,13.4950747 L8.65869231,17.2180759 C8.84222683,17.3216476 9.06289719,17.3359296 9.25823077,17.2568788 L9.33923077,17.217383 L15.9161538,13.4964606 C16.1077874,13.3877744 16.2363116,13.194306 16.2623077,12.9753928 L16.2678462,12.8929366 L16.2678462,12.1127209 C16.2678462,11.9213793 16.4228245,11.7662663 16.614,11.7662663 C16.8051755,11.7662663 16.9601538,11.9213793 16.9601538,12.1127209 L16.9601538,12.8929366 C16.9601538,13.392559 16.6913605,13.8534774 16.2567692,14.0992916 L9.67984615,17.820214 C9.25800827,18.0591858 8.74202651,18.0599743 8.31946154,17.8222927 L1.71,14.0992916 C1.27390962,13.8539498 1.00384615,13.3922483 1.00384615,12.8915508 L1.00384615,10.0339932 L1.69615385,10.0339932 L1.69684615,10.0339932 Z M9.67984615,0.179438505 L16.2567692,3.90036095 C16.6913605,4.14617516 16.9601538,4.60709349 16.9601538,5.10671588 L16.9594615,7.92616344 L16.2671538,7.92616344 L16.2671538,5.10671588 C16.2671093,4.88653928 16.1625175,4.67948868 15.9853846,4.54892397 L15.9161538,4.50388487 L9.33923077,0.782962424 C9.15609588,0.679157835 8.93572641,0.664368777 8.74038462,0.74277369 L8.65869231,0.781576606 L2.04992308,4.50527068 C1.85775848,4.61363299 1.72868378,4.8071559 1.70238462,5.02633841 L1.69753846,5.1087946 L1.69753846,5.87653801 C1.69753846,6.0678796 1.54256011,6.22299261 1.35138462,6.22299261 C1.16020913,6.22299261 1.00523069,6.0678796 1.00523069,5.87653801 L1.00523069,5.1087946 C1.00506058,4.60838699 1.27444351,4.14675398 1.71,3.90105386 L8.31876923,0.178052686 C8.74121306,-0.0598437213 9.25719473,-0.0593180928 9.67915385,0.179438505 L9.67984615,0.179438505 Z"id=形状 fill=#FFFFFF></path><path d="M1.38461538,7.60881102 C2.14931735,7.60881102 2.76923077,8.22926306 2.76923077,8.99462944 C2.76923077,9.75999581 2.14931735,10.3804479 1.38461538,10.3804479 C0.619913423,10.3804479 0,9.75999581 0,8.99462944 C0,8.22926306 0.619913423,7.60881102 1.38461538,7.60881102 Z M16.6153846,7.60881102 C17.3800866,7.60881102 18,8.22926306 18,8.99462944 C18,9.75999581 17.3800866,10.3804479 16.6153846,10.3804479 C15.8506827,10.3804479 15.2307692,9.75999581 15.2307692,8.99462944 C15.2307692,8.22926306 15.8506827,7.60881102 16.6153846,7.60881102 Z M1.38461538,8.30172023 C1.0022644,8.30172023 0.692307692,8.61194625 0.692307692,8.99462944 C0.692307692,9.37731263 1.0022644,9.68753865 1.38461538,9.68753865 C1.76696637,9.68753865 2.07692308,9.37731263 2.07692308,8.99462944 C2.07692308,8.61194625 1.76696637,8.30172023 1.38461538,8.30172023 Z M16.6153846,8.30172023 C16.2330336,8.30172023 15.9230769,8.61194625 15.9230769,8.99462944 C15.9230769,9.37731263 16.2330336,9.68753865 16.6153846,9.68753865 C16.9977356,9.68753865 17.3076923,9.37731263 17.3076923,8.99462944 C17.3076923,8.61194625 16.9977356,8.30172023 16.6153846,8.30172023 Z"id=形状 fill=#FFFFFF></path><path d="M5.382,12.4591755 L6.14353846,10.356196 L9.04430769,10.356196 L9.855,12.4591755 L10.9003846,12.4591755 L8.06192308,5.51553231 L7.07192308,5.51553231 L4.40723077,12.4591755 L5.382,12.4591755 Z M8.76046154,9.60785409 L6.40869231,9.60785409 L7.17023077,7.57070102 C7.33153846,7.13208949 7.45615385,6.69001342 7.54407692,6.24447279 C7.65207692,6.62072249 7.81615385,7.10021567 8.037,7.68433813 L8.76115385,9.60785409 L8.76046154,9.60785409 Z M12.7086923,12.4591755 L12.7086923,5.51553231 L11.79,5.51553231 L11.79,12.4591755 L12.708,12.4591755 L12.7086923,12.4591755 Z"id=形状 fill=#45D4FF></path></g></g></svg><span></span></div><div class="item tools">'),Ua=Q("<span>"),Dp=Q('<svg viewBox="0 0 20 20"><path d=M1.08108,0L0,1.079L4.18919,5.27938L2.54826,6.91715L6.9112,6.91715L6.9112,2.56262L5.28957,4.18112L1.08108,0ZM15.8108,5.27938L20,1.079L18.9189,0L14.7104,4.18112L13.0888,2.56262L13.0888,6.91715L17.4517,6.91715L15.8108,5.27938ZM4.16988,14.7014L0.07722,18.8054L1.1583,20L5.27027,15.7996L6.9112,17.4374L6.9112,13.0829L2.54826,13.0829L4.16988,14.7014ZM17.4517,13.0829L13.0888,13.0829L13.0888,17.4374L14.7297,15.7996L18.8417,20L19.9228,18.8054L15.8301,14.7013L17.4517,13.0829Z>'),Pp=Q('<svg viewBox="0 0 20 20"><path d=M2.93444,1.76899L7.57544,6.40999L6.38918,7.59626L1.76899,2.93444L0,4.70343L0,0L4.70343,0L2.93444,1.76899ZM6.40999,12.4037L1.76899,17.0447L0,15.2758L0,19.9792L4.70343,19.9792L2.93444,18.2102L7.57544,13.5692L6.40999,12.4037ZM15.2758,0L17.0447,1.76899L12.4037,6.40999L13.59,7.59626L18.231,2.95526L20,4.72425L20,0L15.2758,0ZM13.5692,12.4037L12.3829,13.59L17.0239,18.231L15.2549,20L19.9792,20L19.9792,15.2758L18.2102,17.0447L13.5692,12.4037Z>');const Fp=t=>{let r;const[e,a]=Mt(!1),i=()=>{a(n=>!n)};return $o(()=>{document.addEventListener("fullscreenchange",i),document.addEventListener("mozfullscreenchange",i),document.addEventListener("webkitfullscreenchange",i),document.addEventListener("msfullscreenchange",i)}),wa(()=>{document.removeEventListener("fullscreenchange",i),document.removeEventListener("mozfullscreenchange",i),document.removeEventListener("webkitfullscreenchange",i),document.removeEventListener("msfullscreenchange",i)}),(()=>{var n=Ap(),o=n.firstChild,s=o.firstChild,l=o.nextSibling,c=l.firstChild,u=c.nextSibling,d=l.nextSibling,h=d.firstChild,v=h.nextSibling,f=d.nextSibling,p=f.firstChild,g=p.nextSibling,m=f.nextSibling,_=m.firstChild,x=_.nextSibling,S=m.nextSibling,y=S.firstChild,k=y.nextSibling,C=S.nextSibling,D=C.firstChild,E=D.nextSibling,I=C.nextSibling;return wi(T=>{r=T},n),Ke(s,"click",t.onMenuClick),st(n,ot(ke,{get when(){return t.symbol},get children(){var T=Ip(),L=T.firstChild;return Ke(T,"click",t.onSymbolClick),st(T,ot(ke,{get when(){return t.symbol.logo},get children(){var b=Ep();return _e(()=>Oe(b,"src",t.symbol.logo)),b}}),L),st(L,()=>t.symbol.shortName??t.symbol.name??t.symbol.ticker),T}}),l),st(n,()=>t.periods.map(T=>(()=>{var L=Ua();return L.$$click=()=>{t.onPeriodChange(T)},st(L,()=>T.text),_e(()=>xr(L,`item period ${T.text===t.period.text?"selected":""}`)),L})()),l),Ke(l,"click",t.onIndicatorClick),st(u,()=>z("indicator",t.locale)),Ke(d,"click",t.onTimezoneClick),st(v,()=>z("timezone",t.locale)),Ke(f,"click",t.onSettingClick),st(g,()=>z("setting",t.locale)),Ke(m,"click",t.onScreenshotClick),st(x,()=>z("screenshot",t.locale)),Ke(S,"click",t.onOrderFlowClick),st(k,()=>z("order_flow",t.locale)),Ke(C,"click",t.onAiPredictionClick),st(E,()=>z("ai_prediction",t.locale)),I.$$click=()=>{if(e())(document.exitFullscreen??document.msExitFullscreen??document.mozCancelFullScreen??document.webkitExitFullscreen).call(document);else{const T=r==null?void 0:r.parentElement;T&&(T.requestFullscreen??T.webkitRequestFullscreen??T.mozRequestFullScreen??T.msRequestFullscreen).call(T)}},st(I,(()=>{var T=Fe(()=>!!e());return()=>T()?[Dp(),(()=>{var L=Ua();return st(L,()=>z("exit_full_screen",t.locale)),L})()]:[Pp(),(()=>{var L=Ua();return st(L,()=>z("full_screen",t.locale)),L})()]})()),_e(()=>Oe(s,"class",t.spread?"":"rotate")),n})()};Je(["click"]);var Rp=Q('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M12.41465,11L18.5,11C18.7761,11,19,11.22386,19,11.5C19,11.77614,18.7761,12,18.5,12L12.41465,12C12.20873,12.5826,11.65311,13,11,13C10.34689,13,9.79127,12.5826,9.58535,12L3.5,12C3.223857,12,3,11.77614,3,11.5C3,11.22386,3.223857,11,3.5,11L9.58535,11C9.79127,10.417404,10.34689,10,11,10C11.65311,10,12.20873,10.417404,12.41465,11Z stroke-opacity=0 stroke=none>');const Bp=()=>Rp();var Np=Q('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M6.91465,11L11.08535,11C11.29127,10.417404,11.84689,10,12.5,10C13.15311,10,13.70873,10.417404,13.91465,11L18.5,11C18.7761,11,19,11.22386,19,11.5C19,11.77614,18.7761,12,18.5,12L13.91465,12C13.70873,12.5826,13.15311,13,12.5,13C11.84689,13,11.29127,12.5826,11.08535,12L6.91465,12C6.70873,12.5826,6.15311,13,5.5,13C4.671573,13,4,12.32843,4,11.5C4,10.671573,4.671573,10,5.5,10C6.15311,10,6.70873,10.417404,6.91465,11Z stroke-opacity=0 stroke=none>');const Op=()=>Np();var Vp=Q('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M6.91465,12.5C6.70873,13.0826,6.15311,13.5,5.5,13.5C4.671573,13.5,4,12.82843,4,12C4,11.171573,4.671573,10.5,5.5,10.5C6.15311,10.5,6.70873,10.917404,6.91465,11.5L16.0853,11.5C16.2913,10.917404,16.846899999999998,10.5,17.5,10.5C18.328400000000002,10.5,19,11.171573,19,12C19,12.82843,18.328400000000002,13.5,17.5,13.5C16.846899999999998,13.5,16.2913,13.0826,16.0853,12.5L6.91465,12.5Z stroke-opacity=0 stroke=none>');const zp=()=>Vp();var $p=Q('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M11,12.41465L11,18.5C11,18.7761,11.22386,19,11.5,19C11.77614,19,12,18.7761,12,18.5L12,12.41465C12.5826,12.20873,13,11.65311,13,11C13,10.34689,12.5826,9.79127,12,9.58535L12,3.5C12,3.223857,11.77614,3,11.5,3C11.22386,3,11,3.223857,11,3.5L11,9.58535C10.417404,9.79127,10,10.34689,10,11C10,11.65311,10.417404,12.20873,11,12.41465Z stroke-opacity=0 stroke=none>');const Wp=()=>$p();var Yp=Q('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M11.66558837890625,19C10.83716137890625,19,10.16558837890625,18.328400000000002,10.16558837890625,17.5C10.16558837890625,16.846899999999998,10.58298437890625,16.2913,11.16557337890625,16.0854L11.16557337890625,11.91464C10.58298437890625,11.70872,10.16558837890625,11.1531,10.16558837890625,10.5C10.16558837890625,9.8469,10.58298437890625,9.29128,11.16557337890625,9.08536L11.16557337890625,4.5C11.16557337890625,4.223857,11.38942837890625,4,11.66556837890625,4C11.94171837890625,4,12.16556837890625,4.223857,12.16556837890625,4.5L12.16556837890625,9.08535C12.74817837890625,9.291260000000001,13.16558837890625,9.846879999999999,13.16558837890625,10.5C13.16558837890625,11.153120000000001,12.74817837890625,11.708739999999999,12.16556837890625,11.91465L12.16556837890625,16.0854C12.74817837890625,16.2913,13.16558837890625,16.846899999999998,13.16558837890625,17.5C13.16558837890625,18.328400000000002,12.49401837890625,19,11.66558837890625,19Z stroke-opacity=0 stroke=none>');const Zp=()=>Yp();var Hp=Q('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M11.165603637695312,6.91465C11.748203637695312,6.70873,12.165603637695312,6.15311,12.165603637695312,5.5C12.165603637695312,4.671573,11.494033637695313,4,10.665603637695312,4C9.837176637695313,4,9.165603637695312,4.671573,9.165603637695312,5.5C9.165603637695312,6.15311,9.583007637695312,6.70873,10.165603637695312,6.91465L10.165603637695312,16.0854C9.583007637695312,16.2913,9.165603637695312,16.846899999999998,9.165603637695312,17.5C9.165603637695312,18.328400000000002,9.837176637695313,19,10.665603637695312,19C11.494033637695313,19,12.165603637695312,18.328400000000002,12.165603637695312,17.5C12.165603637695312,16.846899999999998,11.748203637695312,16.2913,11.165603637695312,16.0854L11.165603637695312,6.91465Z stroke-opacity=0 fill-rule=evenodd fill-opacity=1>');const jp=()=>Hp();var Xp=Q('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M5.146447,15.753C4.9511845,15.9483,4.9511845,16.2649,5.146447,16.4602C5.341709,16.6554,5.658291,16.6554,5.853554,16.4602L8.156600000000001,14.15711C8.352409999999999,14.25082,8.57173,14.3033,8.8033,14.3033C9.631730000000001,14.3033,10.3033,13.63172,10.3033,12.80329C10.3033,12.57172,10.250820000000001,12.352409999999999,10.157119999999999,12.15659L12.156600000000001,10.15711C12.352409999999999,10.250820000000001,12.571729999999999,10.30329,12.8033,10.30329C13.63173,10.30329,14.3033,9.63172,14.3033,8.80329C14.3033,8.57172,14.25082,8.352409999999999,14.15712,8.15659L16.4602,5.853553C16.6554,5.658291,16.6554,5.341709,16.4602,5.146447C16.2649,4.9511843,15.9483,4.9511843,15.753,5.146447L13.45001,7.449479999999999C13.25419,7.35577,13.03487,7.3032900000000005,12.8033,7.3032900000000005C11.97487,7.3032900000000005,11.3033,7.97487,11.3033,8.80329C11.3033,9.03487,11.35578,9.254190000000001,11.44949,9.450009999999999L9.450009999999999,11.449480000000001C9.254190000000001,11.35577,9.03487,11.30329,8.8033,11.30329C7.97487,11.30329,7.3033,11.97487,7.3033,12.80329C7.3033,13.03487,7.35578,13.25419,7.44949,13.45001L5.146447,15.753Z stroke-opacity=0 stroke=none>');const Gp=()=>Xp();var Kp=Q('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M7.573332939453125,14.54567903564453C7.667042939453125,14.741499035644532,7.719512939453125,14.960809035644532,7.719512939453125,15.19239903564453C7.719512939453125,16.02079903564453,7.047942939453125,16.69239903564453,6.219512939453125,16.69239903564453C5.391085939453125,16.69239903564453,4.719512939453125,16.02079903564453,4.719512939453125,15.19239903564453C4.719512939453125,14.36394903564453,5.391085939453125,13.692379035644532,6.219512939453125,13.692379035644532C6.451092939453125,13.692379035644532,6.670412939453125,13.74485903564453,6.866232939453125,13.83856903564453L9.865702939453126,10.83909903564453C9.771992939453124,10.643279035644532,9.719512939453125,10.42395903564453,9.719512939453125,10.192379035644532C9.719512939453125,9.36394903564453,10.391082939453124,8.692379035644532,11.219512939453125,8.692379035644532C11.451092939453126,8.692379035644532,11.670412939453126,8.74485903564453,11.866232939453125,8.838569035644532L15.462112939453124,5.242645035644531C15.657412939453126,5.047383335644532,15.974012939453125,5.047383335644532,16.169212939453125,5.242645035644531C16.364512939453125,5.437907035644531,16.364512939453125,5.754489035644531,16.169212939453125,5.949752035644531L12.573332939453124,9.545679035644532C12.667042939453125,9.74149903564453,12.719512939453125,9.96080903564453,12.719512939453125,10.192379035644532C12.719512939453125,11.020809035644533,12.047942939453126,11.692379035644532,11.219512939453125,11.692379035644532C10.987942939453125,11.692379035644532,10.768632939453125,11.639909035644532,10.572812939453126,11.54619903564453L7.573332939453125,14.54567903564453Z stroke-opacity=0 stroke=none>');const Up=()=>Kp();var Qp=Q('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M15.719512939453125,8.461776733398438C16.547912939453127,8.461776733398438,17.219512939453125,7.7902067333984375,17.219512939453125,6.9617767333984375C17.219512939453125,6.133349733398438,16.547912939453127,5.4617767333984375,15.719512939453125,5.4617767333984375C14.891082939453124,5.4617767333984375,14.219512939453125,6.133349733398438,14.219512939453125,6.9617767333984375C14.219512939453125,7.193346733398437,14.271992939453124,7.412666733398438,14.365692939453124,7.608486733398438L7.366222939453126,14.607956733398437C7.170402939453125,14.514256733398437,6.951082939453125,14.461776733398438,6.719512939453125,14.461776733398438C5.891085939453125,14.461776733398438,5.219512939453125,15.133346733398437,5.219512939453125,15.961776733398438C5.219512939453125,16.79017673339844,5.891085939453125,17.461776733398438,6.719512939453125,17.461776733398438C7.547942939453125,17.461776733398438,8.219512939453125,16.79017673339844,8.219512939453125,15.961776733398438C8.219512939453125,15.730176733398437,8.167032939453126,15.510876733398437,8.073322939453124,15.315066733398437L15.072802939453124,8.315586733398437C15.268612939453124,8.409296733398438,15.487912939453125,8.461776733398438,15.719512939453125,8.461776733398438Z stroke-opacity=0 stroke=none>');const qp=()=>Qp();var Jp=Q('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M17.0643,7.033864912109375L18,3.585784912109375L14.5078,4.509695912109375L15.3537,5.344934912109375L6.02026,14.560584912109375C5.87635,14.517484912109374,5.72366,14.494284912109375,5.5655,14.494284912109375C4.7009,14.494284912109375,4,15.186384912109375,4,16.040084912109375C4,16.893784912109375,4.7009,17.585784912109375,5.5655,17.585784912109375C6.43011,17.585784912109375,7.13101,16.893784912109375,7.13101,16.040084912109375C7.13101,15.722284912109375,7.03392,15.426984912109376,6.86744,15.181384912109374L16.0917,6.073604912109375L17.0643,7.033864912109375Z stroke-opacity=0 stroke=none>');const tg=()=>Jp();var eg=Q('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M6.91465,13.00505L18.5,13.00505C18.7761,13.00505,19,13.228909999999999,19,13.50505C19,13.781189999999999,18.7761,14.00505,18.5,14.00505L6.91465,14.00505C6.70873,14.58765,6.15311,15.00505,5.5,15.00505C4.671573,15.00505,4,14.33348,4,13.50505C4,12.67662,4.671573,12.00505,5.5,12.00505C6.15311,12.00505,6.70873,12.422450000000001,6.91465,13.00505ZM7.81404,11.625L10.48591,11.625L10.48591,10.90625L9.65193,10.90625L9.65193,7.125L8.997630000000001,7.125C8.71443,7.306641,8.415600000000001,7.419922,7.96443,7.498047L7.96443,8.05078L8.77497,8.05078L8.77497,10.90625L7.81404,10.90625L7.81404,11.625ZM11.081620000000001,11.625L14.0562,11.625L14.0562,10.88281L13.09724,10.88281C12.8863,10.88281,12.59333,10.90625,12.36482,10.93555C13.17537,10.11328,13.84724,9.2207,13.84724,8.39062C13.84724,7.541016,13.28865,7,12.4488,7C11.84333,7,11.446850000000001,7.234375,11.03279,7.679688L11.52497,8.16797C11.747630000000001,7.914062,12.0113,7.697266,12.33552,7.697266C12.7613,7.697266,13.00154,7.982422,13.00154,8.43359C13.00154,9.14648,12.29255,10.00781,11.081620000000001,11.11523L11.081620000000001,11.625ZM15.9605,11.75C16.8121,11.75,17.526899999999998,11.2832,17.526899999999998,10.4375C17.526899999999998,9.82031,17.142200000000003,9.43945,16.6441,9.30078L16.6441,9.27148C17.1129,9.08594,17.3824,8.7207,17.3824,8.21289C17.3824,7.421875,16.8004,7,15.9429,7C15.4215,7,14.9957,7.210938,14.6109,7.541016L15.066,8.11133C15.3258,7.849609,15.5836,7.697266,15.9019,7.697266C16.2789,7.697266,16.4957,7.914062,16.4957,8.28125C16.4957,8.70898,16.2301,9,15.4215,9L15.4215,9.63672C16.3804,9.63672,16.6383,9.91992,16.6383,10.38086C16.6383,10.79688,16.3336,11.03125,15.8824,11.03125C15.4742,11.03125,15.1578,10.82227,14.8922,10.55078L14.4781,11.13281C14.7906,11.486329999999999,15.2652,11.75,15.9605,11.75Z stroke-opacity=0 stroke=none>');const rg=()=>eg();var ag=Q('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M3.146447,14.178126025390625C2.9511847,13.982826025390626,2.9511847,13.666226025390625,3.146447,13.470926025390625L7.39146,9.225966025390626C7.35417,9.095106025390624,7.33421,8.956946025390625,7.33421,8.814116025390625C7.33421,7.985696025390625,8.00578,7.314116025390625,8.834209999999999,7.314116025390625C8.97703,7.314116025390625,9.11519,7.334086025390625,9.24605,7.371366025390625L13.753,2.864373025390625C13.9483,2.669110325390625,14.2649,2.669110325390625,14.4602,2.864373025390625C14.6554,3.059635025390625,14.6554,3.376217025390625,14.4602,3.571479025390625L10.06916,7.962476025390625C10.23631,8.204386025390626,10.334209999999999,8.497826025390625,10.334209999999999,8.814116025390625C10.334209999999999,9.642546025390626,9.66264,10.314116025390625,8.834209999999999,10.314116025390625C8.51791,10.314116025390625,8.22448,10.216226025390625,7.98256,10.049076025390626L3.853554,14.178126025390625C3.658291,14.373326025390625,3.341709,14.373326025390625,3.146447,14.178126025390625ZM7.67736,19.188526025390626C7.4821,18.993226025390626,7.4821,18.676626025390625,7.67736,18.481426025390626L9.9804,16.178326025390625C9.88669,15.982526025390625,9.834209999999999,15.763226025390624,9.834209999999999,15.531626025390626C9.834209999999999,14.703226025390626,10.50578,14.031626025390626,11.33421,14.031626025390626C11.56579,14.031626025390626,11.78511,14.084126025390624,11.98093,14.177826025390624L13.9804,12.178356025390626C13.8867,11.982536025390624,13.8342,11.763216025390625,13.8342,11.531636025390625C13.8342,10.703206025390624,14.5058,10.031636025390625,15.3342,10.031636025390625C15.5658,10.031636025390625,15.7851,10.084116025390625,15.9809,10.177826025390626L18.284,7.874796025390625C18.4792,7.679536025390625,18.7958,7.679536025390625,18.9911,7.874796025390625C19.1863,8.070056025390624,19.1863,8.386636025390626,18.9911,8.581906025390625L16.688000000000002,10.884936025390624C16.7817,11.080756025390626,16.8342,11.300066025390626,16.8342,11.531636025390625C16.8342,12.360066025390624,16.162599999999998,13.031626025390626,15.3342,13.031626025390626C15.1026,13.031626025390626,14.8833,12.979126025390626,14.6875,12.885426025390625L12.68803,14.884926025390625C12.78174,15.080726025390625,12.83421,15.300026025390626,12.83421,15.531626025390626C12.83421,16.360026025390624,12.16264,17.031626025390626,11.33421,17.031626025390626C11.10264,17.031626025390626,10.88333,16.979126025390627,10.68751,16.885426025390625L8.38446,19.188526025390626C8.1892,19.383726025390626,7.87262,19.383726025390626,7.67736,19.188526025390626Z stroke-opacity=0 stroke=none>');const ig=()=>ag();var ng=Q('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M3.3367688759765626,12.63173C3.5320318759765623,12.82699,3.8486138759765627,12.82699,4.043876875976562,12.63173L11.822052875976562,4.853553C12.017312875976563,4.658291,12.017312875976563,4.341708,11.822052875976562,4.146446C11.626792875976562,3.9511843,11.310202875976563,3.9511843,11.114942875976563,4.146446L3.3367688759765626,11.92462C3.1415071759765625,12.11988,3.1415071759765625,12.43647,3.3367688759765626,12.63173ZM5.001492875976562,17.0351C4.806232875976562,16.8399,4.806232875976562,16.5233,5.001492875976562,16.328L7.304532875976562,14.025C7.210822875976563,13.82916,7.158352875976563,13.60984,7.158352875976563,13.37827C7.158352875976563,12.54984,7.829922875976562,11.87827,8.658352875976561,11.87827C8.889922875976563,11.87827,9.109232875976563,11.93075,9.305052875976562,12.02446L11.304532875976562,10.02498C11.210822875976563,9.82916,11.158352875976561,9.60984,11.158352875976561,9.37827C11.158352875976561,8.54984,11.829922875976562,7.8782700000000006,12.658352875976563,7.8782700000000006C12.889922875976563,7.8782700000000006,13.109232875976563,7.93075,13.305022875976562,8.024460000000001L15.608122875976562,5.72142C15.803322875976562,5.5261499999999995,16.119922875976563,5.5261499999999995,16.315222875976563,5.72142C16.510422875976563,5.9166799999999995,16.510422875976563,6.23326,16.315222875976563,6.42852L14.012122875976562,8.73156C14.105822875976562,8.92738,14.158322875976562,9.1467,14.158322875976562,9.37827C14.158322875976562,10.2067,13.486822875976562,10.87827,12.658352875976563,10.87827C12.426772875976562,10.87827,12.207452875976562,10.82579,12.011642875976563,10.73209L10.012162875976562,12.73156C10.105872875976562,12.92738,10.158352875976561,13.1467,10.158352875976561,13.37827C10.158352875976561,14.2067,9.486772875976563,14.8783,8.658352875976561,14.8783C8.426772875976562,14.8783,8.207452875976562,14.8258,8.011642875976563,14.7321L5.708602875976562,17.0351C5.513342875976562,17.2304,5.196752875976562,17.2304,5.001492875976562,17.0351ZM10.415712875976563,18.328C10.220452875976562,18.5233,9.903862875976563,18.5233,9.708602875976563,18.328C9.513342875976562,18.1328,9.513342875976562,17.816200000000002,9.708602875976563,17.6209L12.304532875976562,15.025C12.210822875976563,14.8292,12.158352875976563,14.6098,12.158352875976563,14.3783C12.158352875976563,13.54984,12.829922875976562,12.87827,13.658322875976562,12.87827C13.889922875976563,12.87827,14.109222875976563,12.93075,14.305022875976562,13.02446L17.486822875976564,9.84274C17.682022875976564,9.64747,17.99862287597656,9.64747,18.19392287597656,9.84274C18.38912287597656,10.038,18.38912287597656,10.35458,18.19392287597656,10.54984L15.012122875976562,13.73156C15.105822875976562,13.92738,15.158322875976562,14.1467,15.158322875976562,14.3783C15.158322875976562,15.2067,14.486822875976562,15.8783,13.658322875976562,15.8783C13.426822875976562,15.8783,13.207422875976562,15.8258,13.011642875976563,15.7321L10.415712875976563,18.328Z stroke-opacity=0 stroke=none>');const og=()=>ng();var sg=Q('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M13.1889,6C12.98303,6.582599999999999,12.42741,7,11.7743,7C11.12119,7,10.565570000000001,6.582599999999999,10.35965,6L3.5,6C3.223857,6,3,5.77614,3,5.5C3,5.22386,3.223857,5,3.5,5L10.35965,5C10.565570000000001,4.417404,11.12119,4,11.7743,4C12.42741,4,12.98303,4.417404,13.1889,5L18.5,5C18.7761,5,19,5.22386,19,5.5C19,5.77614,18.7761,6,18.5,6L13.1889,6ZM3,8.5C3,8.22386,3.223857,8,3.5,8L18.5,8C18.7761,8,19,8.22386,19,8.5C19,8.77614,18.7761,9,18.5,9L3.5,9C3.223857,9,3,8.77614,3,8.5ZM3.278549,11.5C3.278549,11.22386,3.502407,11,3.778549,11L18.7785,11C19.0547,11,19.2785,11.22386,19.2785,11.5C19.2785,11.77614,19.0547,12,18.7785,12L3.778549,12C3.502407,12,3.278549,11.77614,3.278549,11.5ZM3.139267,14.5C3.139267,14.2239,3.363124,14,3.6392670000000003,14L18.6393,14C18.915399999999998,14,19.1393,14.2239,19.1393,14.5C19.1393,14.7761,18.915399999999998,15,18.6393,15L3.6392670000000003,15C3.363124,15,3.139267,14.7761,3.139267,14.5ZM13.1889,18C12.98303,18.5826,12.42741,19,11.7743,19C11.12119,19,10.565570000000001,18.5826,10.35965,18L3.778549,18C3.502407,18,3.278549,17.7761,3.278549,17.5C3.278549,17.2239,3.502407,17,3.778549,17L10.35965,17C10.565570000000001,16.4174,11.12119,16,11.7743,16C12.42741,16,12.98303,16.4174,13.1889,17L18.7785,17C19.0547,17,19.2785,17.2239,19.2785,17.5C19.2785,17.7761,19.0547,18,18.7785,18L13.1889,18Z stroke-opacity=0 stroke=none>');const lg=()=>sg();var cg=Q('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M4.91465,6C4.70873,6.582599999999999,4.15311,7,3.5,7C2.671573,7,2,6.32843,2,5.5C2,4.671573,2.671573,4,3.5,4C4.15311,4,4.70873,4.417404,4.91465,5L18.2257,5C18.5018,5,18.7257,5.22386,18.7257,5.5C18.7257,5.77614,18.5018,6,18.2257,6L4.91465,6ZM2.7257,8.5C2.7257,8.22386,2.949558,8,3.2257,8L18.2257,8C18.5018,8,18.7257,8.22386,18.7257,8.5C18.7257,8.77614,18.5018,9,18.2257,9L3.2257,9C2.949558,9,2.7257,8.77614,2.7257,8.5ZM3.00425,11.5C3.00425,11.22386,3.22811,11,3.50425,11L18.5042,11C18.7804,11,19.0042,11.22386,19.0042,11.5C19.0042,11.77614,18.7804,12,18.5042,12L3.50425,12C3.22811,12,3.00425,11.77614,3.00425,11.5ZM2.864967,14.5C2.864967,14.2239,3.08882,14,3.36497,14L18.365,14C18.6411,14,18.865,14.2239,18.865,14.5C18.865,14.7761,18.6411,15,18.365,15L3.36497,15C3.08882,15,2.864967,14.7761,2.864967,14.5ZM20,17.5C20,18.328400000000002,19.3284,19,18.5,19C17.846899999999998,19,17.2913,18.5826,17.0854,18L3.50425,18C3.22811,18,3.00425,17.7761,3.00425,17.5C3.00425,17.2239,3.22811,17,3.50425,17L17.0854,17C17.2913,16.4174,17.846899999999998,16,18.5,16C19.3284,16,20,16.671599999999998,20,17.5Z stroke-opacity=0 stroke=none>');const ug=()=>cg();var dg=Q('<svg class=icon-overlay viewBox="0 0 22 22"><ellipse cx=10.5 cy=11.5 rx=1.5 ry=1.5 stroke-opacity=0 stroke=none></ellipse><ellipse cx=17.5 cy=11.5 rx=1.5 ry=1.5 stroke-opacity=0 stroke=none></ellipse><ellipse cx=10.5 cy=11.5 rx=7 ry=7 fill-opacity=0 stroke-opacity=1 fill=none stroke-width=1></ellipse><ellipse cx=10.5 cy=11.5 rx=5 ry=5 fill-opacity=0 stroke-opacity=1 fill=none stroke-width=1></ellipse><ellipse cx=10.5 cy=11.5 rx=3 ry=3 fill-opacity=0 stroke-opacity=1 fill=none stroke-width=1>');const hg=()=>dg();var vg=Q('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M3,7.32468C5.90649,3.3893050000000002,11.49833,2.81306,14.6674,6.31944C14.9056,6.1554199999999994,15.192,6.05979,15.5,6.05979C15.845,6.05979,16.1628,6.17974,16.4162,6.381349999999999L18.4509,4.23827L19,4.816615L16.8945,7.03429C16.962600000000002,7.21075,17,7.40319,17,7.60463C17,8.45782,16.328400000000002,9.14947,15.5,9.14947C14.6716,9.14947,14,8.45782,14,7.60463C14,7.36402,14.0534,7.13625,14.1487,6.93322C11.32695,3.748365,6.25159,4.253956,3.612785,7.82695L3,7.32468ZM14.09,15.4717C15.7427,13.78985,16.244500000000002,11.524740000000001,15.5633,9.30134L15.5618,9.30134L16.3012,9.0502C17.072400000000002,11.56646,16.497700000000002,14.158,14.6282,16.0599C12.28737,18.442,8.62386,18.6988,6.41348,16.4501C4.5526,14.5572,4.52076,11.19671,6.36766,9.3177C7.89069,7.76754,10.07544,7.706189999999999,11.56741,9.22363C11.95453,9.61742,12.24817,10.08363,12.43369,10.57677L14.1451,8.77421L14.6942,9.35256L12.64982,11.50582C12.65827,11.59712,12.66295,11.68839,12.66378,11.77936C12.87398,12.04523,13,12.38451,13,12.7541C13,13.60729,12.32843,14.2989,11.5,14.2989C10.67157,14.2989,10,13.60729,10,12.7541C10,11.90091,10.67157,11.20926,11.5,11.20926C11.60387,11.20926,11.70528,11.220130000000001,11.8032,11.240829999999999L11.81763,11.22564C11.69858,10.71874,11.42858,10.21929,11.0284,9.81179C9.844000000000001,8.60765,8.136890000000001,8.65592,6.90822,9.90586C5.37975,11.460930000000001,5.40693,14.288,6.95404,15.8619C8.84598,17.7867,12.03496,17.5626,14.09,15.4717Z stroke-opacity=0 stroke=none>');const fg=()=>vg();var pg=Q('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M4,17.0854L4,3.5C4,3.223858,4.22386,3,4.5,3C4.77614,3,5,3.223858,5,3.5L5,10L7.57584,10L9.8127,4.46359C9.91614,4.20756,10.20756,4.08386,10.46359,4.1873000000000005C10.71963,4.29075,10.84333,4.58216,10.73988,4.8382000000000005L8.65438,10L11.08535,10C11.29127,9.4174,11.84689,9,12.5,9C12.65154,9,12.79784,9.02247,12.93573,9.06427L16.6464,5.35355C16.8417,5.15829,17.1583,5.15829,17.3536,5.35355C17.5488,5.54882,17.5488,5.8654,17.3536,6.06066L13.7475,9.66675C13.907,9.90508,14,10.19168,14,10.5C14,11.15311,13.5826,11.70873,13,11.91465L13,14.3638L18.3714,12.1936C18.6274,12.09015,18.918799999999997,12.21385,19.0222,12.46989C19.1257,12.72592,19.002,13.0173,18.746000000000002,13.1208L13,15.4423L13,18L19.5,18C19.7761,18,20,18.2239,20,18.5C20,18.7761,19.7761,19,19.5,19L5.91465,19C5.70873,19.5826,5.15311,20,4.5,20C3.671573,20,3,19.3284,3,18.5C3,17.846899999999998,3.417404,17.2913,4,17.0854ZM6.3729499999999994,17.0413L12,14.7678L12,11.91465C11.88136,11.87271,11.76956,11.81627,11.66675,11.74746L6.3729499999999994,17.0413ZM12,15.8463L6.6694700000000005,18L12,18L12,15.8463ZM6.38629,15.6137L8.250350000000001,11L11,11L6.38629,15.6137ZM5,11L7.17182,11L5,16.3754L5,11Z stroke-opacity=0 fill-rule=evenodd fill-opacity=1>');const gg=()=>pg();var mg=Q('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M17,4.5C17,5.32843,16.328400000000002,6,15.5,6C15.0931,6,14.7241,5.83802,14.4539,5.57503L5.98992,8.32515C5.99658,8.38251,6,8.440850000000001,6,8.5C6,9.15311,5.582599999999999,9.70873,5,9.91465L5,11.08535C5.42621,11.236,5.763999999999999,11.57379,5.91465,12L19.5,12C19.7761,12,20,12.22386,20,12.5C20,12.77614,19.7761,13,19.5,13L5.91465,13C5.70873,13.5826,5.15311,14,4.5,14C3.671573,14,3,13.3284,3,12.5C3,11.84689,3.417404,11.29127,4,11.08535L4,9.91465C3.417404,9.70873,3,9.15311,3,8.5C3,7.67157,3.671573,7,4.5,7C4.90411,7,5.2709,7.15981,5.5406200000000005,7.41967L14.0093,4.66802C14.0032,4.6128599999999995,14,4.5568,14,4.5C14,3.671573,14.6716,3,15.5,3C16.328400000000002,3,17,3.671573,17,4.5ZM4,15.5C4,15.2239,4.22386,15,4.5,15L19.5,15C19.7761,15,20,15.2239,20,15.5C20,15.7761,19.7761,16,19.5,16L4.5,16C4.22386,16,4,15.7761,4,15.5ZM4,18.5C4,18.2239,4.22386,18,4.5,18L19.5,18C19.7761,18,20,18.2239,20,18.5C20,18.7761,19.7761,19,19.5,19L4.5,19C4.22386,19,4,18.7761,4,18.5Z stroke-opacity=0 stroke=none>');const yg=()=>mg();var _g=Q('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M20,3.5C20,4.15311,19.5826,4.70873,19,4.91465L19,18.5C19,18.7761,18.7761,19,18.5,19L4.91465,19C4.70873,19.5826,4.15311,20,3.5,20C2.671573,20,2,19.3284,2,18.5C2,17.846899999999998,2.417404,17.2913,3,17.0854L3,3.5C3,3.22386,3.22386,3,3.5,3L17.0854,3C17.2913,2.417404,17.846899999999998,2,18.5,2C19.3284,2,20,2.671573,20,3.5ZM17.0854,4C17.236,4.42621,17.5738,4.763999999999999,18,4.91465L18,8L14,8L14,4L17.0854,4ZM13,4L13,8L9,8L9,4L13,4ZM13,9L9,9L9,13L13,13L13,9ZM13,14L9,14L9,18L13,18L13,14ZM14,18L14,14L18,14L18,18L14,18ZM18,13L14,13L14,9L18,9L18,13ZM4.91465,18C4.763999999999999,17.5738,4.42621,17.236,4,17.0854L4,14L8,14L8,18L4.91465,18ZM4,8L4,4L8,4L8,8L4,8ZM8,9L8,13L4,13L4,9L8,9Z stroke-opacity=0 fill-rule=evenodd fill-opacity=1>');const Cg=()=>_g();var bg=Q('<svg class=icon-overlay viewBox="0 0 22 22"><ellipse cx=10.5 cy=11.5 rx=1.5 ry=1.5 stroke-opacity=0 stroke=none></ellipse><ellipse cx=17.5 cy=11.5 rx=1.5 ry=1.5 stroke-opacity=0 stroke=none></ellipse><ellipse cx=10.5 cy=11.5 rx=7 ry=7 fill-opacity=0 fill=none stroke-opacity=1 stroke-width=1>');const xg=()=>bg();var wg=Q('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M11.57625,6.9981C11.55099,6.999359999999999,11.52557,7,11.5,7C11.34,7,11.18584,6.97495,11.04125,6.9285499999999995L5.55401,16.4327C5.713760000000001,16.5905,5.83826,16.7839,5.91465,17L16.0854,17C16.2187,16.622700000000002,16.4987,16.314700000000002,16.8569,16.1445L11.57625,6.9981ZM12.50759,6.611219999999999C12.81005,6.336790000000001,13,5.94058,13,5.5C13,4.671573,12.32843,4,11.5,4C10.67157,4,10,4.671573,10,5.5C10,5.80059,10.08841,6.08052,10.24066,6.31522L4.64514,16.0069C4.59738,16.002299999999998,4.54896,16,4.5,16C3.671573,16,3,16.671599999999998,3,17.5C3,18.328400000000002,3.671573,19,4.5,19C5.15311,19,5.70873,18.5826,5.91465,18L16.0854,18C16.2913,18.5826,16.846899999999998,19,17.5,19C18.328400000000002,19,19,18.328400000000002,19,17.5C19,16.8365,18.5691,16.2735,17.971899999999998,16.075699999999998L12.50759,6.611219999999999Z stroke-opacity=0 fill-rule=evenodd fill-opacity=1>');const Lg=()=>wg();var Sg=Q('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M19,4.5C19,5.15311,18.5826,5.70873,18,5.91465L18,18.5C18,18.7761,17.7761,19,17.5,19L5.91465,19C5.70873,19.5826,5.15311,20,4.5,20C3.671573,20,3,19.3284,3,18.5C3,17.846899999999998,3.417404,17.2913,4,17.0854L4,4.5C4,4.22386,4.22386,4,4.5,4L16.0854,4C16.2913,3.417404,16.846899999999998,3,17.5,3C18.328400000000002,3,19,3.671573,19,4.5ZM5,5L16.0854,5C16.236,5.42621,16.5738,5.763999999999999,17,5.91465L17,18L5.91465,18C5.763999999999999,17.5738,5.42621,17.236,5,17.0854L5,5Z stroke-opacity=0 fill-rule=evenodd fill-opacity=1>');const kg=()=>Sg();var Mg=Q('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M19.6401,7.99355C20.4028,7.92291,21,7.2811900000000005,21,6.5C21,5.671573,20.3284,5,19.5,5C18.8469,5,18.2913,5.417404,18.0854,6L7.62067,6C7.34453,6,7.12067,6.22386,7.12067,6.5C7.12067,6.5479,7.12741,6.59423,7.13999,6.63809L3.2294099999999997,15.0243C2.530138,15.1517,2,15.764,2,16.5C2,17.328400000000002,2.671573,18,3.5,18C4.15311,18,4.70873,17.5826,4.91465,17L14.5963,17C14.6456,17.076,14.7162,17.1396,14.8044,17.1807C15.0546,17.2974,15.3521,17.1891,15.4688,16.9388L19.6401,7.99355ZM14.7896,16.0293L18.6551,7.739599999999999C18.3942,7.56144,18.1925,7.30307,18.0854,7L8.0746,7L4.25044,15.2009C4.55701,15.3784,4.79493,15.6613,4.91465,16L14.6207,16C14.68,16,14.7368,16.0103,14.7896,16.0293Z stroke-opacity=0 fill-rule=evenodd fill-opacity=1>');const Tg=()=>Mg();var Eg=Q('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M8.134443814697265,7.494615087890625L8.764323814697265,7.494615087890625L8.764323814697265,3.414215087890625L8.310223814697267,3.414215087890625L7.294603814697266,4.005035087890625L7.289713814697266,4.634915087890625L8.134443814697265,4.149892087890625L8.134443814697265,7.494615087890625ZM18.832003814697266,6.933095087890624Q19.004603814697266,6.635245087890625,19.004603814697266,6.2543850878906255Q19.004603814697266,5.884915087890625,18.845103814697264,5.593575087890625Q18.685503814697267,5.3006050878906255,18.399103814697266,5.136225087890625Q18.114303814697266,4.9702050878906245,17.754603814697266,4.9653250878906245L18.820603814697265,3.840647087890625L18.820603814697265,3.414215087890625L16.519203814697264,3.414215087890625L16.519203814697264,3.939931087890625L18.050803814697264,3.939931087890625L16.719403814697266,5.334785087890625L17.074203814697263,5.7205350878906245Q17.254903814697265,5.484525087890625,17.619503814697268,5.484525087890625Q17.980803814697268,5.484525087890625,18.187503814697266,5.689605087890625Q18.394203814697267,5.894685087890625,18.394203814697267,6.2543850878906255Q18.394203814697267,6.604315087890625,18.187503814697266,6.822415087890625Q17.980803814697268,7.0405150878906255,17.640603814697265,7.0405150878906255Q17.334603814697267,7.0405150878906255,17.124703814697266,6.890775087890625Q16.914703814697265,6.739415087890626,16.820303814697265,6.469225087890624L16.354803814697263,6.744295087890626Q16.480103814697266,7.125155087890625,16.821903814697265,7.341625087890625Q17.165403814697264,7.559725087890625,17.640603814697265,7.559725087890625Q18.039403814697266,7.559725087890625,18.348603814697267,7.393705087890625Q18.659503814697267,7.229315087890625,18.832003814697266,6.933095087890624ZM10.000003814697266,10.634915087890626C10.000003814697266,11.024655087890626,9.851363814697265,11.379685087890625,9.607683814697266,11.646395087890625L12.168903814697266,15.171615087890626C12.275403814697265,15.147615087890625,12.386203814697266,15.134915087890626,12.500003814697266,15.134915087890626C12.596503814697266,15.134915087890626,12.690803814697265,15.144015087890624,12.782303814697265,15.161415087890624L16.108803814697268,11.196955087890625C16.038703814697264,11.023375087890624,16.000003814697266,10.833655087890625,16.000003814697266,10.634915087890626C16.000003814697266,9.806495087890625,16.671603814697264,9.134915087890626,17.500003814697266,9.134915087890626C18.328403814697264,9.134915087890626,19.000003814697266,9.806495087890625,19.000003814697266,10.634915087890626C19.000003814697266,11.463345087890625,18.328403814697264,12.134915087890626,17.500003814697266,12.134915087890626C17.239503814697265,12.134915087890626,16.994503814697268,12.068495087890625,16.781003814697264,11.951675087890624L13.654703814697266,15.677415087890624C13.870303814697266,15.937215087890625,14.000003814697266,16.270915087890625,14.000003814697266,16.634915087890626C14.000003814697266,17.463315087890624,13.328403814697266,18.134915087890626,12.500003814697266,18.134915087890626C11.671573814697265,18.134915087890626,11.000003814697266,17.463315087890624,11.000003814697266,16.634915087890626C11.000003814697266,16.284415087890626,11.120193814697265,15.962015087890626,11.321603814697266,15.706715087890625L8.715393814697265,12.119565087890624C8.645053814697267,12.129685087890625,8.573143814697266,12.134915087890626,8.500003814697266,12.134915087890626C8.162103814697264,12.134915087890626,7.8503038146972655,12.023195087890626,7.599523814697266,11.834665087890626L4.505583814697266,15.521915087890624C4.809213814697266,15.796415087890624,5.000003814697266,16.193415087890624,5.000003814697266,16.634915087890626C5.000003814697266,17.463315087890624,4.328433814697266,18.134915087890626,3.5000038146972656,18.134915087890626C2.6715768146972656,18.134915087890626,2.0000038146972656,17.463315087890624,2.0000038146972656,16.634915087890626C2.0000038146972656,15.806515087890626,2.6715768146972656,15.134915087890626,3.5000038146972656,15.134915087890626C3.508253814697266,15.134915087890626,3.5164838146972657,15.135015087890626,3.524703814697266,15.135115087890625L7.033823814697266,10.953115087890625C7.011673814697265,10.850565087890626,7.000003814697266,10.744105087890624,7.000003814697266,10.634915087890626C7.000003814697266,9.806495087890625,7.671573814697266,9.134915087890626,8.500003814697266,9.134915087890626C9.328433814697267,9.134915087890626,10.000003814697266,9.806495087890625,10.000003814697266,10.634915087890626Z stroke-opacity=0 stroke=none>');const Ig=()=>Eg();var Ag=Q('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M8.13444,7.494615087890625L8.76432,7.494615087890625L8.76432,3.414215087890625L8.310220000000001,3.414215087890625L7.2946,4.005035087890625L7.28971,4.634915087890625L8.13444,4.149892087890625L8.13444,7.494615087890625ZM18.832,6.929835087890625Q19.0046,6.635245087890625,19.0046,6.2543850878906255Q19.0046,5.889805087890625,18.8451,5.5952050878906245Q18.6855,5.3006050878906255,18.3975,5.132965087890625Q18.1094,4.9653250878906245,17.7399,4.9653250878906245Q17.435499999999998,4.9653250878906245,17.1556,5.149245087890625L17.2793,3.939931087890625L18.8304,3.939931087890625L18.8304,3.414215087890625L16.7406,3.414215087890625L16.5094,5.665195087890625L17.0156,5.795405087890625Q17.095399999999998,5.655425087890626,17.2516,5.570795087890625Q17.4095,5.484525087890625,17.6357,5.484525087890625Q17.9694,5.484525087890625,18.1842,5.697745087890625Q18.4007,5.909335087890625,18.4007,6.2543850878906255Q18.4007,6.604315087890625,18.1842,6.822415087890625Q17.9694,7.0405150878906255,17.6292,7.0405150878906255Q17.3298,7.0405150878906255,17.119799999999998,6.890775087890625Q16.9098,6.739415087890626,16.825200000000002,6.474115087890625L16.3597,6.749175087890626Q16.470399999999998,7.110505087890624,16.807299999999998,7.335115087890625Q17.144199999999998,7.559725087890625,17.6292,7.559725087890625Q18.0296,7.559725087890625,18.3438,7.392075087890625Q18.6595,7.224435087890625,18.832,6.929835087890625ZM10,10.634915087890626C10,11.024655087890626,9.85136,11.379685087890625,9.60768,11.646395087890625L12.1689,15.171615087890626C12.2754,15.147615087890625,12.3862,15.134915087890626,12.5,15.134915087890626C12.5965,15.134915087890626,12.6908,15.144015087890624,12.7823,15.161415087890624L16.108800000000002,11.196955087890625C16.0387,11.023375087890624,16,10.833655087890625,16,10.634915087890626C16,9.806495087890625,16.671599999999998,9.134915087890626,17.5,9.134915087890626C18.3284,9.134915087890626,19,9.806495087890625,19,10.634915087890626C19,11.463345087890625,18.3284,12.134915087890626,17.5,12.134915087890626C17.2395,12.134915087890626,16.994500000000002,12.068505087890625,16.781,11.951675087890624L13.6547,15.677415087890624C13.8703,15.937215087890625,14,16.270915087890625,14,16.634915087890626C14,17.463315087890624,13.3284,18.134915087890626,12.5,18.134915087890626C11.67157,18.134915087890626,11,17.463315087890624,11,16.634915087890626C11,16.284415087890626,11.12019,15.962015087890626,11.3216,15.706715087890625L8.71539,12.119565087890624C8.645050000000001,12.129685087890625,8.57314,12.134915087890626,8.5,12.134915087890626C8.162099999999999,12.134915087890626,7.8503,12.023195087890626,7.59952,11.834665087890626L4.50558,15.521915087890624C4.80921,15.796415087890624,5,16.193415087890624,5,16.634915087890626C5,17.463315087890624,4.32843,18.134915087890626,3.5,18.134915087890626C2.671573,18.134915087890626,2,17.463315087890624,2,16.634915087890626C2,15.806515087890626,2.671573,15.134915087890626,3.5,15.134915087890626C3.5082500000000003,15.134915087890626,3.51648,15.135015087890626,3.5247,15.135115087890625L7.03382,10.953115087890625C7.01167,10.850565087890626,7,10.744105087890624,7,10.634915087890626C7,9.806495087890625,7.67157,9.134915087890626,8.5,9.134915087890626C9.32843,9.134915087890626,10,9.806495087890625,10,10.634915087890626Z stroke-opacity=0 stroke=none>');const Dg=()=>Ag();var Pg=Q('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M18.8532,7.020985087890625Q19.0257,6.734525087890625,19.0257,6.369945087890625Q19.0257,6.020005087890625,18.8499,5.754705087890625Q18.6758,5.489415087890626,18.3649,5.339675087890625Q18.5944,5.209465087890625,18.7214,4.994615087890625Q18.8499,4.779775087890625,18.8499,4.5193550878906255Q18.8499,4.2003480878906245,18.7002,3.951324087890625Q18.5505,3.700673087890625,18.277,3.557444087890625Q18.0052,3.414215087890625,17.6455,3.414215087890625Q17.285800000000002,3.414215087890625,17.0107,3.557444087890625Q16.7357,3.700673087890625,16.5843,3.951324087890625Q16.4346,4.2003480878906245,16.4346,4.5193550878906255Q16.4346,4.779775087890625,16.561500000000002,4.994615087890625Q16.6901,5.209465087890625,16.919600000000003,5.339675087890625Q16.6055,5.489415087890626,16.4297,5.757965087890625Q16.255499999999998,6.024895087890625,16.255499999999998,6.369945087890625Q16.255499999999998,6.734525087890625,16.4297,7.020985087890625Q16.6055,7.305815087890625,16.919600000000003,7.465325087890625Q17.2354,7.624825087890625,17.6455,7.624825087890625Q18.0557,7.624825087890625,18.3682,7.465325087890625Q18.6807,7.305815087890625,18.8532,7.020985087890625ZM8.76432,7.559725087890625L8.13444,7.559725087890625L8.13444,4.214996087890625L7.28971,4.700025087890625L7.2946,4.070139087890625L8.310220000000001,3.479319087890625L8.76432,3.479319087890625L8.76432,7.559725087890625ZM17.1816,4.955555087890625Q17.0042,4.784655087890625,17.0042,4.5095950878906255Q17.0042,4.229645087890625,17.18,4.057119087890625Q17.355800000000002,3.884592087890625,17.6455,3.884592087890625Q17.935200000000002,3.884592087890625,18.1077,4.057119087890625Q18.2803,4.229645087890625,18.2803,4.5095950878906255Q18.2803,4.784655087890625,18.1045,4.955555087890625Q17.930300000000003,5.124825087890625,17.6455,5.124825087890625Q17.3607,5.124825087890625,17.1816,4.955555087890625ZM18.2217,5.7953950878906255Q18.4398,6.005365087890625,18.4398,6.3552950878906245Q18.4398,6.705235087890625,18.2217,6.915195087890625Q18.0052,7.125155087890625,17.6455,7.125155087890625Q17.285800000000002,7.125155087890625,17.067700000000002,6.915195087890625Q16.849600000000002,6.705235087890625,16.849600000000002,6.3552950878906245Q16.849600000000002,6.005365087890625,17.064500000000002,5.7953950878906255Q17.2793,5.585435087890625,17.6455,5.585435087890625Q18.0052,5.585435087890625,18.2217,5.7953950878906255ZM9.60768,11.711495087890626C9.85136,11.444785087890626,10,11.089765087890626,10,10.700025087890625C10,9.871595087890626,9.32843,9.200025087890625,8.5,9.200025087890625C7.67157,9.200025087890625,7,9.871595087890626,7,10.700025087890625C7,10.809205087890625,7.01167,10.915665087890625,7.03382,11.018215087890624L3.5247,15.200215087890625C3.51648,15.200115087890625,3.5082500000000003,15.200015087890625,3.5,15.200015087890625C2.671573,15.200015087890625,2,15.871615087890625,2,16.700015087890627C2,17.528415087890625,2.671573,18.200015087890627,3.5,18.200015087890627C4.32843,18.200015087890627,5,17.528415087890625,5,16.700015087890627C5,16.258515087890625,4.80921,15.861515087890625,4.50558,15.587015087890626L7.59952,11.899765087890625C7.8503,12.088295087890625,8.162099999999999,12.200025087890625,8.5,12.200025087890625C8.57314,12.200025087890625,8.645050000000001,12.194785087890626,8.71539,12.184675087890625L11.3216,15.771815087890625C11.12019,16.027215087890625,11,16.349515087890623,11,16.700015087890627C11,17.528415087890625,11.67157,18.200015087890627,12.5,18.200015087890627C13.3284,18.200015087890627,14,17.528415087890625,14,16.700015087890627C14,16.336015087890623,13.8703,16.002315087890626,13.6547,15.742515087890625L16.781,12.016775087890625C16.994500000000002,12.133605087890626,17.2395,12.200025087890625,17.5,12.200025087890625C18.3284,12.200025087890625,19,11.528445087890624,19,10.700025087890625C19,9.871595087890626,18.3284,9.200025087890625,17.5,9.200025087890625C16.671599999999998,9.200025087890625,16,9.871595087890626,16,10.700025087890625C16,10.898765087890624,16.0387,11.088475087890625,16.108800000000002,11.262055087890625L12.7823,15.226515087890625C12.6908,15.209115087890625,12.5965,15.200015087890625,12.5,15.200015087890625C12.3862,15.200015087890625,12.2754,15.212715087890626,12.1689,15.236715087890625L9.60768,11.711495087890626Z stroke-opacity=0 stroke=none>');const Fg=()=>Pg();var Rg=Q('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M9.474616630859375,7.494615087890625L8.844736630859375,7.494615087890625L8.844736630859375,4.149892087890625L8.000006630859374,4.634915087890625L8.004896630859374,4.005035087890625L9.020516630859376,3.414215087890625L9.474616630859375,3.414215087890625L9.474616630859375,7.494615087890625ZM18.529296630859378,4.8318550878906255Q18.307996630859375,5.028795087890625,18.122396630859377,5.385245087890625Q17.868496630859376,5.019035087890625,17.629196630859376,4.8269750878906255Q17.389996630859375,4.634915087890625,17.168596630859376,4.634915087890625Q16.794296630859375,4.634915087890625,16.522496630859376,4.976715087890625Q16.252296630859377,5.3168850878906255,16.252296630859377,5.7856350878906255Q16.252296630859377,6.218575087890625,16.502896630859375,6.521315087890625Q16.755196630859373,6.822415087890625,17.114896630859377,6.822415087890625Q17.368796630859375,6.822415087890625,17.588596630859374,6.625475087890624Q17.809896630859377,6.428535087890625,17.998696630859374,6.0688350878906245Q18.249396630859373,6.439935087890625,18.488596630859377,6.631985087890625Q18.727896630859377,6.822415087890625,18.952496630859375,6.822415087890625Q19.326796630859373,6.822415087890625,19.596996630859376,6.482245087890625Q19.868796630859375,6.140455087890626,19.868796630859375,5.671705087890626Q19.868796630859375,5.238755087890625,19.618196630859376,4.937655087890625Q19.367496630859375,4.634915087890625,19.006196630859375,4.634915087890625Q18.750696630859377,4.634915087890625,18.529296630859378,4.8318550878906255ZM18.337296630859377,5.674955087890625L18.278696630859375,5.596835087890625Q18.449596630859375,5.272935087890625,18.622096630859374,5.1101750878906245Q18.794596630859374,4.947415087890625,18.967096630859373,4.947415087890625Q19.194996630859375,4.947415087890625,19.346396630859374,5.1345950878906255Q19.497696630859377,5.320135087890625,19.497696630859377,5.598455087890625Q19.497696630859377,5.8914250878906245,19.360996630859376,6.096505087890625Q19.224296630859374,6.301585087890626,19.027396630859375,6.301585087890626Q18.915096630859374,6.301585087890626,18.742496630859375,6.146965087890624Q18.569996630859375,5.992335087890625,18.337296630859377,5.674955087890625ZM17.785496630859377,5.779125087890625L17.842496630859372,5.857245087890625Q17.668296630859373,6.186025087890625,17.495796630859374,6.348785087890625Q17.324896630859374,6.509915087890625,17.153996630859375,6.509915087890625Q16.926096630859377,6.509915087890625,16.774796630859377,6.324375087890624Q16.623396630859375,6.137195087890625,16.623396630859375,5.858875087890625Q16.623396630859375,5.565905087890625,16.761696630859376,5.360825087890625Q16.900096630859373,5.1557550878906255,17.095396630859376,5.1557550878906255Q17.228896630859374,5.1557550878906255,17.365596630859375,5.2778250878906245Q17.502296630859377,5.399895087890625,17.785496630859377,5.779125087890625ZM10.710296630859375,10.634915087890626C10.710296630859375,11.024655087890626,10.561656630859375,11.379685087890625,10.317976630859375,11.646395087890625L12.879196630859376,15.171615087890626C12.985696630859374,15.147615087890625,13.096496630859376,15.134915087890626,13.210296630859375,15.134915087890626C13.306796630859376,15.134915087890626,13.401096630859374,15.144015087890624,13.492596630859374,15.161415087890624L16.819096630859377,11.196955087890625C16.748996630859374,11.023375087890624,16.710296630859375,10.833655087890625,16.710296630859375,10.634915087890626C16.710296630859375,9.806495087890625,17.381896630859373,9.134915087890626,18.210296630859375,9.134915087890626C19.038696630859373,9.134915087890626,19.710296630859375,9.806495087890625,19.710296630859375,10.634915087890626C19.710296630859375,11.463345087890625,19.038696630859373,12.134915087890626,18.210296630859375,12.134915087890626C17.949796630859375,12.134915087890626,17.704796630859377,12.068505087890625,17.491296630859374,11.951675087890624L14.364996630859375,15.677415087890624C14.580596630859375,15.937215087890625,14.710296630859375,16.270915087890625,14.710296630859375,16.634915087890626C14.710296630859375,17.463315087890624,14.038696630859375,18.134915087890626,13.210296630859375,18.134915087890626C12.381866630859374,18.134915087890626,11.710296630859375,17.463315087890624,11.710296630859375,16.634915087890626C11.710296630859375,16.284415087890626,11.830486630859374,15.962015087890626,12.031896630859375,15.706715087890625L9.425686630859374,12.119565087890624C9.355346630859376,12.129685087890625,9.283436630859375,12.134915087890626,9.210296630859375,12.134915087890626C8.872396630859374,12.134915087890626,8.560596630859376,12.023195087890626,8.309816630859375,11.834665087890626L5.215876630859375,15.521915087890624C5.519506630859375,15.796415087890624,5.710296630859375,16.193415087890624,5.710296630859375,16.634915087890626C5.710296630859375,17.463315087890624,5.038726630859375,18.134915087890626,4.210296630859375,18.134915087890626C3.381869630859375,18.134915087890626,2.710296630859375,17.463315087890624,2.710296630859375,16.634915087890626C2.710296630859375,15.806515087890626,3.381869630859375,15.134915087890626,4.210296630859375,15.134915087890626C4.218546630859375,15.134915087890626,4.226776630859375,15.135015087890626,4.234996630859375,15.135115087890625L7.744116630859375,10.953115087890625C7.721966630859375,10.850565087890626,7.710296630859375,10.744105087890624,7.710296630859375,10.634915087890626C7.710296630859375,9.806495087890625,8.381866630859374,9.134915087890626,9.210296630859375,9.134915087890626C10.038726630859376,9.134915087890626,10.710296630859375,9.806495087890625,10.710296630859375,10.634915087890626Z stroke-opacity=0 stroke=none>');const Bg=()=>Rg();var Ng=Q('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M21,5.5C21,6.32843,20.3284,7,19.5,7C19.4136,7,19.3289,6.99269,19.2465,6.97866L15.6257,15.5086C15.8587,15.7729,16,16.119999999999997,16,16.5C16,17.328400000000002,15.3284,18,14.5,18C13.8469,18,13.2913,17.5826,13.0854,17L3.91465,17C3.70873,17.5826,3.15311,18,2.5,18C1.671573,18,1,17.328400000000002,1,16.5C1,15.6716,1.671573,15,2.5,15C2.5840199999999998,15,2.66643,15.0069,2.74668,15.0202L6.36934,6.48574C6.13933,6.22213,6,5.87733,6,5.5C6,4.671573,6.67157,4,7.5,4C8.15311,4,8.70873,4.417404,8.91465,5L18.0854,5C18.2913,4.417404,18.8469,4,19.5,4C20.3284,4,21,4.671573,21,5.5ZM18.0854,6L8.91465,6C8.892579999999999,6.06243,8.8665,6.12296,8.83672,6.18128L13.9814,15.0921C14.143,15.0325,14.3177,15,14.5,15C14.584,15,14.6664,15.0069,14.7467,15.0202L18.3693,6.48574C18.2462,6.3446,18.149,6.1802,18.0854,6ZM13.2036,15.745L8.0861,6.8811800000000005C7.90605,6.95768,7.70797,7,7.5,7C7.41359,7,7.32888,6.99269,7.24647,6.97866L3.62571,15.5086C3.7512,15.651,3.8501,15.8174,3.91465,16L13.0854,16C13.1169,15.9108,13.1566,15.8255,13.2036,15.745Z stroke-opacity=0 stroke=none>');const Og=()=>Ng();var Vg=Q('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M5.92159,5.93994C6.04014,5.90529,6.152620000000001,5.85639,6.25704,5.79523L9.12729,9.89437C9.045449999999999,10.07959,9,10.28449,9,10.5C9,10.79522,9.08529,11.07053,9.232569999999999,11.30262L4.97573,16.7511L5.92159,5.93994ZM4.92259,5.8848400000000005C4.38078,5.658659999999999,4,5.1238,4,4.5C4,3.671573,4.67157,3,5.5,3C6.2157,3,6.81433,3.50124,6.96399,4.17183L15.1309,4.88634C15.3654,4.36387,15.8902,4,16.5,4C17.328400000000002,4,18,4.67157,18,5.5C18,6.08983,17.659599999999998,6.60015,17.1645,6.84518L18.4264,14.0018C18.4508,14.0006,18.4753,14,18.5,14C19.3284,14,20,14.6716,20,15.5C20,16.328400000000002,19.3284,17,18.5,17C17.932499999999997,17,17.4386,16.6849,17.183799999999998,16.22L5.99686,18.5979C5.946429999999999,19.3807,5.29554,20,4.5,20C3.671573,20,3,19.3284,3,18.5C3,17.869300000000003,3.389292,17.3295,3.94071,17.1077L4.92259,5.8848400000000005ZM5.72452,17.6334C5.69799,17.596,5.6698,17.5599,5.64004,17.525100000000002L10.01843,11.92103C10.16958,11.97223,10.33155,12,10.5,12C10.80059,12,11.08052,11.91158,11.31522,11.75934L17.0606,15.0765C17.0457,15.1271,17.0335,15.1789,17.023899999999998,15.2317L5.72452,17.6334ZM11.92855,10.95875L17.4349,14.1379L16.1699,6.96356C15.9874,6.92257,15.8174,6.8483,15.6667,6.74746L11.99771,10.4165C11.99923,10.44414,12,10.47198,12,10.5C12,10.66,11.97495,10.814160000000001,11.92855,10.95875ZM10.5,9C10.259830000000001,9,10.03285,9.05644,9.83159,9.15679L7.04919,5.1831L15.0493,5.88302C15.054,5.90072,15.059,5.91829,15.0643,5.9357299999999995L11.56066,9.43934C11.28921,9.16789,10.91421,9,10.5,9Z stroke-opacity=0 fill-rule=evenodd fill-opacity=1>');const zg=()=>Vg();var $g=Q('<svg viewBox="0 0 22 22"><path d=M4.727219638671875,8.007996215820313L9.973849638671876,2.7629472158203123C10.167279638671875,2.5696791158203123,10.480729638671875,2.5696791158203123,10.674169638671875,2.7629472158203123L13.223329638671874,5.311756215820313C13.416929638671874,5.505236215820313,13.416929638671874,5.8189862158203125,13.223329638671874,6.012466215820313L7.977129638671875,11.257906215820313C7.379859638671875,11.855176215820313,7.407609638671875,12.909396215820312,8.033809638671876,13.535596215820313C8.660409638671876,14.162596215820313,9.713849638671874,14.189996215820312,10.311129638671876,13.591896215820313L15.556929638671875,8.346066215820311C15.750429638671875,8.152526215820313,16.064229638671875,8.152526215820313,16.257629638671872,8.346066215820311L18.806529638671876,10.895266215820312C19.000029638671876,11.088746215820313,19.000029638671876,11.402496215820312,18.806529638671876,11.595976215820313L13.560629638671875,16.841796215820313C11.165619638671876,19.237196215820312,7.197149638671875,19.19919621582031,4.783499638671875,16.785496215820313C2.3698426386718747,14.371896215820312,2.331397638671875,10.403416215820313,4.727219638671875,8.007996215820313ZM12.172299638671875,5.662106215820312L10.323809638671875,3.8136162158203124L5.4287196386718755,8.709096215820313C3.422893638671875,10.714536215820312,3.4549956386718748,14.055196215820313,5.484999638671875,16.08479621582031C7.514609638671875,18.114796215820313,10.855289638671875,18.146496215820314,12.860719638671876,16.141096215820312L15.465629638671874,13.535796215820312L14.090929638671875,12.160756215820312L14.791629638671875,11.460436215820312L16.166229638671876,12.834996215820313L17.755829638671877,11.245226215820313L15.907729638671874,9.396736215820312L11.011839638671875,14.292596215820312C10.042809638671875,15.262396215820312,8.418249638671874,15.243796215820312,7.406019638671875,14.306496215820312L7.333099638671875,14.236296215820312C6.327599638671876,13.230796215820313,6.284009638671876,11.550396215820312,7.276419638671875,10.557586215820312L9.882199638671874,7.952026215820313L8.501079638671875,6.570906215820313L9.201789638671876,5.870186215820313L10.582939638671874,7.251336215820312L12.172299638671875,5.662106215820312Z stroke-opacity=0 fill-rule=evenodd fill-opacity=1>');const Wg=t=>(()=>{var r=$g();return Oe(r,"class",`icon-overlay ${t??""}`),r})();var Yg=Q('<svg viewBox="0 0 22 22"><defs><clipPath id=master_svg0_151_615><rect x=0 y=0 width=22 height=22 rx=0></rect></clipPath></defs><g clip-path=url(#master_svg0_151_615)><path d=M19.672,3.0673368C19.4417,2.9354008,19.1463,3.00292252,18.9994,3.2210900000000002L17.4588,5.50622L16.743299999999998,3.781253L13.9915,7.4662L13.9618,7.51108C13.8339,7.72862,13.8936,8.005659999999999,14.1004,8.15391L14.1462,8.183430000000001C14.3683,8.308720000000001,14.6511,8.25001,14.8022,8.047229999999999L16.4907,5.78571L17.246299999999998,7.60713L19.8374,3.7635389999999997L19.8651,3.717088C19.9871,3.484615,19.9023,3.199273,19.672,3.0673368ZM4.79974,8.462530000000001L10.117740000000001,3.252975C10.31381,3.0610145,10.63152,3.0610145,10.82759,3.252975L13.4115,5.78453C13.6076,5.976710000000001,13.6076,6.28833,13.4115,6.4805L8.093869999999999,11.69045C7.48847,12.28368,7.51659,13.3308,8.151309999999999,13.9528C8.786439999999999,14.5755,9.85421,14.6027,10.45961,14.0087L15.7768,8.79831C15.9729,8.60609,16.2909,8.60609,16.487099999999998,8.79831L19.0705,11.33026C19.2667,11.52244,19.2667,11.83406,19.0705,12.02623L13.7533,17.2366C11.32572,19.6158,7.30328,19.578,4.85679,17.1807C2.410298,14.7834,2.371331,10.84174,4.79974,8.462530000000001ZM12.3461,6.1325199999999995L10.47246,4.29654L5.51079,9.15889C3.477674,11.15076,3.510214,14.4688,5.56784,16.4847C7.62506,18.500999999999998,11.01117,18.5325,13.0439,16.540599999999998L15.6842,13.9529L14.2908,12.58718L15.0011,11.89161L16.394399999999997,13.2569L18.0056,11.67786L16.1323,9.84188L11.16985,14.7046C10.18764,15.6679,8.540980000000001,15.6494,7.51498,14.7184L7.44107,14.6487C6.4219,13.65,6.37771,11.98096,7.38362,10.994869999999999L10.02485,8.40693L8.624939999999999,7.03516L9.335180000000001,6.33919L10.73512,7.71099L12.3461,6.1325199999999995Z stroke-opacity=0 fill-rule=evenodd fill-opacity=1>');const Zg=t=>(()=>{var r=Yg();return Oe(r,"class",`icon-overlay ${t??""}`),r})();var Hg=Q('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M11,17C5.80945,17,3.667717,12.85,3.113386,11.575C2.9622047,11.2,2.9622047,10.8,3.113386,10.425C3.667717,9.15,5.80945,5,11,5C16.165399999999998,5,18.3323,9.15,18.8866,10.425C19.0378,10.8,19.0378,11.2,18.8866,11.575C18.3323,12.85,16.165399999999998,17,11,17ZM4.04567,10.8C3.995276,10.925,3.995276,11.05,4.04567,11.175C4.52441,12.325,6.43937,16,11,16C15.5606,16,17.4756,12.325,17.9543,11.2C18.0047,11.075,18.0047,10.95,17.9543,10.825C17.4756,9.675,15.5606,6,11,6C6.43937,6,4.52441,9.675,4.04567,10.8ZM11,13.5C9.61417,13.5,8.480319999999999,12.375,8.480319999999999,11C8.480319999999999,9.625,9.61417,8.5,11,8.5C12.38583,8.5,13.5197,9.625,13.5197,11C13.5197,12.375,12.38583,13.5,11,13.5ZM11,9.5C10.1685,9.5,9.48819,10.175,9.48819,11C9.48819,11.825,10.1685,12.5,11,12.5C11.8315,12.5,12.51181,11.825,12.51181,11C12.51181,10.175,11.8315,9.5,11,9.5Z stroke-opacity=0 fill-opacity=1>');const jg=()=>Hg();var Xg=Q('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M5.80417,14.9887L4.62563,16.167299999999997C4.43037,16.3625,4.43037,16.6791,4.62563,16.8744C4.82089,17.0696,5.13748,17.0696,5.332739999999999,16.8744L6.62638,15.5807C7.75595,16.290100000000002,9.19328,16.7929,11,16.7929C16.165399999999998,16.7929,18.3323,12.64289,18.8866,11.36789C19.0378,10.99289,19.0378,10.59289,18.8866,10.21789C18.5549,9.45486,17.6456,7.66212,15.8617,6.34545L17.3536,4.853553C17.5488,4.658291,17.5488,4.341709,17.3536,4.146447C17.1583,3.9511845,16.8417,3.9511845,16.6464,4.146447L15.0014,5.7915399999999995C13.9314,5.1969,12.61166,4.792893,11,4.792893C5.80945,4.792893,3.667717,8.94289,3.113386,10.21789C2.9622049,10.59289,2.9622049,10.99289,3.113386,11.36789C3.424435,12.08333,4.2353000000000005,13.70399,5.80417,14.9887ZM7.36012,14.847C8.32327,15.4074,9.52286,15.7929,11,15.7929C15.5606,15.7929,17.4756,12.11789,17.9543,10.99289C18.0047,10.86789,18.0047,10.74289,17.9543,10.61789C17.659,9.90846,16.8171,8.23812,15.1447,7.06241L12.96929,9.23782C13.3134,9.66543,13.5197,10.20642,13.5197,10.79289C13.5197,12.16789,12.38583,13.29289,11,13.29289C10.41596,13.29289,9.87667,13.09308,9.44815,12.75896L7.36012,14.847ZM8.794609999999999,11.99829L6.520099999999999,14.2728C5.06905,13.12119,4.32057,11.628250000000001,4.04567,10.96789C3.995275,10.84289,3.995275,10.71789,4.04567,10.59289C4.52441,9.46789,6.43937,5.79289,11,5.79289C12.28868,5.79289,13.3661,6.086320000000001,14.2596,6.53329L12.19759,8.5953C11.84086,8.40257,11.43271,8.29289,11,8.29289C9.61417,8.29289,8.480319999999999,9.41789,8.480319999999999,10.79289C8.480319999999999,11.22918,8.594470000000001,11.64029,8.794609999999999,11.99829ZM10.16528,12.04183C10.404869999999999,12.20032,10.692070000000001,12.29289,11,12.29289C11.8315,12.29289,12.51181,11.61789,12.51181,10.79289C12.51181,10.48318,12.41593,10.194600000000001,12.25216,9.95494L10.16528,12.04183ZM11.43602,9.35687L9.55616,11.236740000000001C9.512,11.09633,9.48819,10.94724,9.48819,10.79289C9.48819,9.96789,10.1685,9.29289,11,9.29289C11.15142,9.29289,11.29782,9.31528,11.43602,9.35687Z stroke-opacity=0 fill-rule=evenodd fill-opacity=1>');const Gg=()=>Xg();var Kg=Q('<svg class=icon-overlay viewBox="0 0 22 22"><defs><clipPath id=master_svg0_151_625><rect x=0 y=0 width=22 height=22 rx=0></rect></clipPath></defs><g clip-path=url(#master_svg0_151_625)><path d=M14.5385,9.76923L15.6538,9.76923C16.6538,9.76923,17.4615,10.576920000000001,17.4615,11.576920000000001L17.4615,17.1923C17.4615,18.1923,16.6538,19,15.6538,19L5.80769,19C4.807692,19,4,18.1923,4,17.1923L4,11.576920000000001C4,10.576920000000001,4.807692,9.76923,5.80769,9.76923L7.23077,9.76923L7.23077,7.576919999999999C7.23077,5.61538,8.88462,4,10.88462,4C12.88462,4,14.5385,5.61538,14.5385,7.576919999999999L14.5385,9.76923ZM10.88461,5.15385C9.5,5.15385,8.38461,6.23077,8.38461,7.576919999999999L8.38461,9.76923L13.38462,9.76923L13.38462,7.576919999999999C13.38462,6.23077,12.26923,5.15385,10.88461,5.15385ZM15.6538,17.8462C16,17.8462,16.3077,17.5385,16.3077,17.1923L16.3077,11.576920000000001C16.3077,11.23077,16,10.923079999999999,15.6538,10.923079999999999L5.80769,10.923079999999999C5.46154,10.923079999999999,5.15385,11.23077,5.15385,11.576920000000001L5.15385,17.1923C5.15385,17.5385,5.46154,17.8462,5.80769,17.8462L15.6538,17.8462ZM10.153839999999999,12.65385C10.153839999999999,12.34615,10.42307,12.07692,10.73076,12.07692C11.038450000000001,12.07692,11.307680000000001,12.34615,11.307680000000001,12.65385L11.307680000000001,14.5769C11.307680000000001,14.8846,11.038450000000001,15.1538,10.73076,15.1538C10.42307,15.1538,10.153839999999999,14.8846,10.153839999999999,14.5769L10.153839999999999,12.65385Z stroke-opacity=0 fill-rule=evenodd fill-opacity=1>');const Ug=()=>Kg();var Qg=Q('<svg class=icon-overlay viewBox="0 0 22 22"><defs><clipPath id=master_svg0_151_620><rect x=0 y=0 width=22 height=22 rx=0></rect></clipPath></defs><g clip-path=url(#master_svg0_151_620)><path d=M8.38461,9.76923L15.6538,9.76923C16.6538,9.76923,17.4615,10.576920000000001,17.4615,11.576920000000001L17.4615,17.1923C17.4615,18.1923,16.6538,19,15.6538,19L5.80769,19C4.807692,19,4,18.1923,4,17.1923L4,11.576920000000001C4,10.576920000000001,4.807693,9.76923,5.80769,9.76923L7.23077,9.76923L7.23077,7.576919999999999C7.23077,5.61538,8.88462,4,10.88462,4C12.46154,4,13.84615,4.961539,14.3462,6.423080000000001C14.4615,6.73077,14.3077,7.038460000000001,14,7.15385C13.69231,7.26923,13.38461,7.11538,13.26923,6.80769C12.92308,5.80769,11.96154,5.15385,10.88462,5.15385C9.5,5.15385,8.38461,6.23077,8.38461,7.576919999999999L8.38461,9.76923ZM15.6538,17.8462C16,17.8462,16.3077,17.5385,16.3077,17.1923L16.3077,11.576920000000001C16.3077,11.23077,16,10.923079999999999,15.6538,10.923079999999999L5.80769,10.923079999999999C5.46154,10.923079999999999,5.15385,11.23077,5.15385,11.576920000000001L5.15385,17.1923C5.15385,17.5385,5.46154,17.8462,5.80769,17.8462L15.6538,17.8462ZM10.153839999999999,12.65385C10.153839999999999,12.34615,10.42307,12.07692,10.73076,12.07692C11.03846,12.07692,11.307690000000001,12.34615,11.307690000000001,12.65385L11.307690000000001,14.5769C11.307690000000001,14.8846,11.03846,15.1538,10.73076,15.1538C10.42307,15.1538,10.153839999999999,14.8846,10.153839999999999,14.5769L10.153839999999999,12.65385Z stroke-opacity=0 fill-rule=evenodd fill-opacity=1>');const qg=()=>Qg();var Jg=Q('<svg class=icon-overlay viewBox="0 0 22 22"><path d=M16.966900000000003,8.67144C16.6669,8.67144,16.4247,8.91558,16.4247,9.21802L16.4247,16.631500000000003C16.4247,17.322,16.007199999999997,17.9068,15.5139,17.9068L13.93072,17.9068L13.93072,9.2162C13.93072,8.91741,13.68675,8.67144,13.38855,8.67144C13.09036,8.67144,12.84639,8.91741,12.84639,9.21802L12.84639,17.9068L10.151810000000001,17.9068L10.151810000000001,9.21802C10.151810000000001,8.91741,9.90783,8.67144,9.609639999999999,8.67144C9.31145,8.67144,9.06747,8.91741,9.06747,9.219850000000001L9.06747,17.9068L7.48614,17.9068C6.99277,17.9068,6.5753,17.322,6.5753,16.631500000000003L6.5753,9.21802C6.5753,8.91558,6.333130000000001,8.67144,6.03313,8.67144C5.73313,8.67144,5.49096,8.91558,5.49096,9.21802L5.49096,16.631500000000003C5.49096,17.9378,6.385540000000001,19,7.48614,19L15.512,19C16.6127,19,17.509,17.9378,17.509,16.631500000000003L17.509,9.21802C17.509,8.91558,17.2669,8.67144,16.966900000000003,8.67144ZM18.4578,6.21183L4.542169,6.21183C4.243976,6.21183,4,6.45779,4,6.75841C4,7.05903,4.243976,7.30499,4.542169,7.30499L18.4578,7.30499C18.756,7.30499,19,7.05903,19,6.75841C19,6.45779,18.756,6.21183,18.4578,6.21183ZM8.68072,5.10045L14.3193,5.10045C14.6175,5.10045,14.8614,4.852666,14.8614,4.550225C14.8614,4.247783,14.6175,4,14.3193,4L8.68072,4C8.38253,4,8.13855,4.247783,8.13855,4.550225C8.13855,4.852666,8.38253,5.10045,8.68072,5.10045Z stroke-opacity=0 fill-opacity=1>');const tm=()=>Jg(),em={horizontalStraightLine:Bp,horizontalRayLine:Op,horizontalSegment:zp,verticalStraightLine:Wp,verticalRayLine:Zp,verticalSegment:jp,straightLine:Gp,rayLine:Up,segment:qp,arrow:tg,priceLine:rg,priceChannelLine:ig,parallelStraightLine:og,fibonacciLine:lg,fibonacciSegment:ug,fibonacciCircle:hg,fibonacciSpiral:fg,fibonacciSpeedResistanceFan:gg,fibonacciExtension:yg,gannBox:Cg,circle:xg,triangle:Lg,rect:kg,parallelogram:Tg,threeWaves:Ig,fiveWaves:Dg,eightWaves:Fg,anyWaves:Bg,abcd:Og,xabcd:zg,weak_magnet:Wg,strong_magnet:Zg,lock:Ug,unlock:qg,visible:jg,invisible:Gg,remove:tm};function rm(t){return[{key:"horizontalStraightLine",text:z("horizontal_straight_line",t)},{key:"horizontalRayLine",text:z("horizontal_ray_line",t)},{key:"horizontalSegment",text:z("horizontal_segment",t)},{key:"verticalStraightLine",text:z("vertical_straight_line",t)},{key:"verticalRayLine",text:z("vertical_ray_line",t)},{key:"verticalSegment",text:z("vertical_segment",t)},{key:"straightLine",text:z("straight_line",t)},{key:"rayLine",text:z("ray_line",t)},{key:"segment",text:z("segment",t)},{key:"arrow",text:z("arrow",t)},{key:"priceLine",text:z("price_line",t)}]}function am(t){return[{key:"priceChannelLine",text:z("price_channel_line",t)},{key:"parallelStraightLine",text:z("parallel_straight_line",t)}]}function im(t){return[{key:"circle",text:z("circle",t)},{key:"rect",text:z("rect",t)},{key:"parallelogram",text:z("parallelogram",t)},{key:"triangle",text:z("triangle",t)}]}function nm(t){return[{key:"fibonacciLine",text:z("fibonacci_line",t)},{key:"fibonacciSegment",text:z("fibonacci_segment",t)},{key:"fibonacciCircle",text:z("fibonacci_circle",t)},{key:"fibonacciSpiral",text:z("fibonacci_spiral",t)},{key:"fibonacciSpeedResistanceFan",text:z("fibonacci_speed_resistance_fan",t)},{key:"fibonacciExtension",text:z("fibonacci_extension",t)},{key:"gannBox",text:z("gann_box",t)}]}function om(t){return[{key:"xabcd",text:z("xabcd",t)},{key:"abcd",text:z("abcd",t)},{key:"threeWaves",text:z("three_waves",t)},{key:"fiveWaves",text:z("five_waves",t)},{key:"eightWaves",text:z("eight_waves",t)},{key:"anyWaves",text:z("any_waves",t)}]}function sm(t){return[{key:"weak_magnet",text:z("weak_magnet",t)},{key:"strong_magnet",text:z("strong_magnet",t)}]}const Ve=t=>em[t.name](t.class);var lm=Q('<div class=klinecharts-pro-drawing-bar><span class=split-line></span><div class=item tabindex=0><span style=width:32px;height:32px></span><div class=icon-arrow><svg viewBox="0 0 4 6"><path d=M1.07298,0.159458C0.827521,-0.0531526,0.429553,-0.0531526,0.184094,0.159458C-0.0613648,0.372068,-0.0613648,0.716778,0.184094,0.929388L2.61275,3.03303L0.260362,5.07061C0.0149035,5.28322,0.0149035,5.62793,0.260362,5.84054C0.505822,6.05315,0.903789,6.05315,1.14925,5.84054L3.81591,3.53075C4.01812,3.3556,4.05374,3.0908,3.92279,2.88406C3.93219,2.73496,3.87113,2.58315,3.73964,2.46925L1.07298,0.159458Z stroke=none stroke-opacity=0></path></svg></div></div><div class=item><span style=width:32px;height:32px></span></div><div class=item><span style=width:32px;height:32px></span></div><span class=split-line></span><div class=item><span style=width:32px;height:32px>'),cm=Q('<div class=item tabindex=0><span style=width:32px;height:32px></span><div class=icon-arrow><svg viewBox="0 0 4 6"><path d=M1.07298,0.159458C0.827521,-0.0531526,0.429553,-0.0531526,0.184094,0.159458C-0.0613648,0.372068,-0.0613648,0.716778,0.184094,0.929388L2.61275,3.03303L0.260362,5.07061C0.0149035,5.28322,0.0149035,5.62793,0.260362,5.84054C0.505822,6.05315,0.903789,6.05315,1.14925,5.84054L3.81591,3.53075C4.01812,3.3556,4.05374,3.0908,3.92279,2.88406C3.93219,2.73496,3.87113,2.58315,3.73964,2.46925L1.07298,0.159458Z stroke=none stroke-opacity=0>'),Wn=Q("<li><span style=padding-left:8px>");const Yn="drawing_tools",um=t=>{const[r,e]=Mt("horizontalStraightLine"),[a,i]=Mt("priceChannelLine"),[n,o]=Mt("circle"),[s,l]=Mt("fibonacciLine"),[c,u]=Mt("xabcd"),[d,h]=Mt("weak_magnet"),[v,f]=Mt("normal"),[p,g]=Mt(!1),[m,_]=Mt(!0),[x,S]=Mt(""),y=Qe(()=>[{key:"singleLine",icon:r(),list:rm(t.locale),setter:e},{key:"moreLine",icon:a(),list:am(t.locale),setter:i},{key:"polygon",icon:n(),list:im(t.locale),setter:o},{key:"fibonacci",icon:s(),list:nm(t.locale),setter:l},{key:"wave",icon:c(),list:om(t.locale),setter:u}]),k=Qe(()=>sm(t.locale));return(()=>{var C=lm(),D=C.firstChild,E=D.nextSibling,I=E.firstChild,T=I.nextSibling,L=T.firstChild,b=E.nextSibling,M=b.firstChild,A=b.nextSibling,P=A.firstChild,N=A.nextSibling,W=N.nextSibling,X=W.firstChild;return st(C,()=>y().map($=>(()=>{var ct=cm(),at=ct.firstChild,gt=at.nextSibling,Tt=gt.firstChild;return ct.addEventListener("blur",()=>{S("")}),at.$$click=()=>{t.onDrawingItemClick({groupId:Yn,name:$.icon,visible:m(),lock:p(),mode:v()})},st(at,ot(Ve,{get name(){return $.icon}})),gt.$$click=()=>{$.key===x()?S(""):S($.key)},st(ct,(()=>{var Wt=Fe(()=>$.key===x());return()=>Wt()&&ot(Ia,{class:"list",get children(){return $.list.map(V=>(()=>{var G=Wn(),O=G.firstChild;return G.$$click=()=>{$.setter(V.key),t.onDrawingItemClick({name:V.key,lock:p(),mode:v()}),S("")},st(G,ot(Ve,{get name(){return V.key}}),O),st(O,()=>V.text),G})())}})})(),null),_e(()=>Oe(Tt,"class",$.key===x()?"rotate":"")),ct})()),D),E.addEventListener("blur",()=>{S("")}),I.$$click=()=>{let $=d();v()!=="normal"&&($="normal"),f($),t.onModeChange($)},st(I,(()=>{var $=Fe(()=>d()==="weak_magnet");return()=>$()?Fe(()=>v()==="weak_magnet")()?ot(Ve,{name:"weak_magnet",class:"selected"}):ot(Ve,{name:"weak_magnet"}):Fe(()=>v()==="strong_magnet")()?ot(Ve,{name:"strong_magnet",class:"selected"}):ot(Ve,{name:"strong_magnet"})})()),T.$$click=()=>{x()==="mode"?S(""):S("mode")},st(E,(()=>{var $=Fe(()=>x()==="mode");return()=>$()&&ot(Ia,{class:"list",get children(){return k().map(ct=>(()=>{var at=Wn(),gt=at.firstChild;return at.$$click=()=>{h(ct.key),f(ct.key),t.onModeChange(ct.key),S("")},st(at,ot(Ve,{get name(){return ct.key}}),gt),st(gt,()=>ct.text),at})())}})})(),null),M.$$click=()=>{const $=!p();g($),t.onLockChange($)},st(M,(()=>{var $=Fe(()=>!!p());return()=>$()?ot(Ve,{name:"lock"}):ot(Ve,{name:"unlock"})})()),P.$$click=()=>{const $=!m();_($),t.onVisibleChange($)},st(P,(()=>{var $=Fe(()=>!!m());return()=>$()?ot(Ve,{name:"visible"}):ot(Ve,{name:"invisible"})})()),X.$$click=()=>{t.onRemoveClick(Yn)},st(X,ot(Ve,{name:"remove"})),_e(()=>Oe(L,"class",x()==="mode"?"rotate":"")),C})()};Je(["click"]);var Zn=Q("<li class=title>"),Hn=Q("<li class=row>");const dm=t=>ot(Wr,{get title(){return z("indicator",t.locale)},width:400,get onClose(){return t.onClose},get children(){return ot(Ia,{class:"klinecharts-pro-indicator-modal-list",get children(){return[(()=>{var r=Zn();return st(r,()=>z("main_indicator",t.locale)),r})(),Fe(()=>["MA","EMA","SMA","BOLL","SAR","BBI"].map(r=>{const e=t.mainIndicators.includes(r);return(()=>{var a=Hn();return a.$$click=i=>{t.onMainIndicatorChange({name:r,paneId:"candle_pane",added:!e})},st(a,ot($n,{checked:e,get label(){return z(r.toLowerCase(),t.locale)}})),a})()})),(()=>{var r=Zn();return st(r,()=>z("sub_indicator",t.locale)),r})(),Fe(()=>["MA","EMA","VOL","MACD","BOLL","KDJ","RSI","BIAS","BRAR","CCI","DMI","CR","PSY","DMA","TRIX","OBV","VR","WR","MTM","EMV","SAR","SMA","ROC","PVT","BBI","AO"].map(r=>{const e=r in t.subIndicators;return(()=>{var a=Hn();return a.$$click=i=>{t.onSubIndicatorChange({name:r,paneId:t.subIndicators[r]??"",added:!e})},st(a,ot($n,{checked:e,get label(){return z(r.toLowerCase(),t.locale)}})),a})()}))]}})}});Je(["click"]);function jn(t,r){switch(t){case"Etc/UTC":return z("utc",r);case"Pacific/Honolulu":return z("honolulu",r);case"America/Juneau":return z("juneau",r);case"America/Los_Angeles":return z("los_angeles",r);case"America/Chicago":return z("chicago",r);case"America/Toronto":return z("toronto",r);case"America/Sao_Paulo":return z("sao_paulo",r);case"Europe/London":return z("london",r);case"Europe/Berlin":return z("berlin",r);case"Asia/Bahrain":return z("bahrain",r);case"Asia/Dubai":return z("dubai",r);case"Asia/Ashkhabad":return z("ashkhabad",r);case"Asia/Almaty":return z("almaty",r);case"Asia/Bangkok":return z("bangkok",r);case"Asia/Shanghai":return z("shanghai",r);case"Asia/Tokyo":return z("tokyo",r);case"Australia/Sydney":return z("sydney",r);case"Pacific/Norfolk":return z("norfolk",r)}return t}function hm(t){return[{key:"Etc/UTC",text:z("utc",t)},{key:"Pacific/Honolulu",text:z("honolulu",t)},{key:"America/Juneau",text:z("juneau",t)},{key:"America/Los_Angeles",text:z("los_angeles",t)},{key:"America/Chicago",text:z("chicago",t)},{key:"America/Toronto",text:z("toronto",t)},{key:"America/Sao_Paulo",text:z("sao_paulo",t)},{key:"Europe/London",text:z("london",t)},{key:"Europe/Berlin",text:z("berlin",t)},{key:"Asia/Bahrain",text:z("bahrain",t)},{key:"Asia/Dubai",text:z("dubai",t)},{key:"Asia/Ashkhabad",text:z("ashkhabad",t)},{key:"Asia/Almaty",text:z("almaty",t)},{key:"Asia/Bangkok",text:z("bangkok",t)},{key:"Asia/Shanghai",text:z("shanghai",t)},{key:"Asia/Tokyo",text:z("tokyo",t)},{key:"Australia/Sydney",text:z("sydney",t)},{key:"Pacific/Norfolk",text:z("norfolk",t)}]}const vm=t=>{const[r,e]=Mt(t.timezone),a=Qe(()=>hm(t.locale));return ot(Wr,{get title(){return z("timezone",t.locale)},width:320,get buttons(){return[{children:z("confirm",t.locale),onClick:()=>{t.onConfirm(r()),t.onClose()}}]},get onClose(){return t.onClose},get children(){return ot(Cs,{style:{width:"100%","margin-top":"20px"},get value(){return r().text},onSelected:i=>{e(i)},get dataSource(){return a()}})}})};function Xn(t){return[{key:"candle.type",text:z("candle_type",t),component:"select",dataSource:[{key:"candle_solid",text:z("candle_solid",t)},{key:"candle_stroke",text:z("candle_stroke",t)},{key:"candle_up_stroke",text:z("candle_up_stroke",t)},{key:"candle_down_stroke",text:z("candle_down_stroke",t)},{key:"ohlc",text:z("ohlc",t)},{key:"area",text:z("area",t)}]},{key:"candle.priceMark.last.show",text:z("last_price_show",t),component:"switch"},{key:"candle.priceMark.high.show",text:z("high_price_show",t),component:"switch"},{key:"candle.priceMark.low.show",text:z("low_price_show",t),component:"switch"},{key:"indicator.lastValueMark.show",text:z("indicator_last_value_show",t),component:"switch"},{key:"yAxis.type",text:z("price_axis_type",t),component:"select",dataSource:[{key:"normal",text:z("normal",t)},{key:"percentage",text:z("percentage",t)},{key:"log",text:z("log",t)}]},{key:"yAxis.reverse",text:z("reverse_coordinate",t),component:"switch"},{key:"grid.show",text:z("grid_show",t),component:"switch"}]}var fm=Q("<div class=klinecharts-pro-setting-modal-content>"),pm=Q("<span>");const gm=t=>{const[r,e]=Mt(t.currentStyles),[a,i]=Mt(Xn(t.locale));ir(()=>{i(Xn(t.locale))});const n=(o,s)=>{const l={};ni(l,o.key,s);const c=Be.clone(r());ni(c,o.key,s),e(c),i(a().map(u=>({...u}))),t.onChange(l)};return ot(Wr,{get title(){return z("setting",t.locale)},width:560,get buttons(){return[{children:z("restore_default",t.locale),onClick:()=>{t.onRestoreDefault(a()),t.onClose()}}]},get onClose(){return t.onClose},get children(){var o=fm();return st(o,ot(vc,{get each(){return a()},children:s=>{let l;const c=Be.formatValue(r(),s.key);switch(s.component){case"select":{l=ot(Cs,{style:{width:"120px"},get value(){return z(c,t.locale)},get dataSource(){return s.dataSource},onSelected:u=>{const d=u.key;n(s,d)}});break}case"switch":{const u=!!c;l=ot(l7,{open:u,onChange:()=>{n(s,!u)}});break}}return[(()=>{var u=pm();return st(u,()=>s.text),u})(),l]}})),o}})};var mm=Q("<img style=width:500px;margin-top:20px>");const ym=t=>ot(Wr,{get title(){return z("screenshot",t.locale)},width:540,get buttons(){return[{type:"confirm",children:z("save",t.locale),onClick:()=>{const r=document.createElement("a");r.download="screenshot",r.href=t.url,document.body.appendChild(r),r.click(),r.remove()}}]},get onClose(){return t.onClose},get children(){var r=mm();return _e(()=>Oe(r,"src",t.url)),r}}),_m={AO:[{paramNameKey:"params_1",precision:0,min:1,default:5},{paramNameKey:"params_2",precision:0,min:1,default:34}],BIAS:[{paramNameKey:"BIAS1",precision:0,min:1,styleKey:"lines[0].color"},{paramNameKey:"BIAS2",precision:0,min:1,styleKey:"lines[1].color"},{paramNameKey:"BIAS3",precision:0,min:1,styleKey:"lines[2].color"},{paramNameKey:"BIAS4",precision:0,min:1,styleKey:"lines[3].color"},{paramNameKey:"BIAS5",precision:0,min:1,styleKey:"lines[4].color"}],BOLL:[{paramNameKey:"period",precision:0,min:1,default:20},{paramNameKey:"standard_deviation",precision:2,min:1,default:2}],BRAR:[{paramNameKey:"period",precision:0,min:1,default:26}],BBI:[{paramNameKey:"params_1",precision:0,min:1,default:3},{paramNameKey:"params_2",precision:0,min:1,default:6},{paramNameKey:"params_3",precision:0,min:1,default:12},{paramNameKey:"params_4",precision:0,min:1,default:24}],CCI:[{paramNameKey:"params_1",precision:0,min:1,default:20}],CR:[{paramNameKey:"params_1",precision:0,min:1,default:26},{paramNameKey:"params_2",precision:0,min:1,default:10},{paramNameKey:"params_3",precision:0,min:1,default:20},{paramNameKey:"params_4",precision:0,min:1,default:40},{paramNameKey:"params_5",precision:0,min:1,default:60}],DMA:[{paramNameKey:"params_1",precision:0,min:1,default:10},{paramNameKey:"params_2",precision:0,min:1,default:50},{paramNameKey:"params_3",precision:0,min:1,default:10}],DMI:[{paramNameKey:"params_1",precision:0,min:1,default:14},{paramNameKey:"params_2",precision:0,min:1,default:6}],EMV:[{paramNameKey:"params_1",precision:0,min:1,default:14},{paramNameKey:"params_2",precision:0,min:1,default:9}],EMA:[{paramNameKey:"EMA1",precision:0,min:1,styleKey:"lines[0].color"},{paramNameKey:"EMA2",precision:0,min:1,styleKey:"lines[1].color"},{paramNameKey:"EMA3",precision:0,min:1,styleKey:"lines[2].color"},{paramNameKey:"EMA4",precision:0,min:1,styleKey:"lines[3].color"},{paramNameKey:"EMA5",precision:0,min:1,styleKey:"lines[4].color"}],MTM:[{paramNameKey:"params_1",precision:0,min:1,default:12},{paramNameKey:"params_2",precision:0,min:1,default:6}],MA:[{paramNameKey:"MA1",precision:0,min:1,styleKey:"lines[0].color"},{paramNameKey:"MA2",precision:0,min:1,styleKey:"lines[1].color"},{paramNameKey:"MA3",precision:0,min:1,styleKey:"lines[2].color"},{paramNameKey:"MA4",precision:0,min:1,styleKey:"lines[3].color"},{paramNameKey:"MA5",precision:0,min:1,styleKey:"lines[4].color"}],MACD:[{paramNameKey:"params_1",precision:0,min:1,default:12},{paramNameKey:"params_2",precision:0,min:1,default:26},{paramNameKey:"params_2",precision:0,min:1,default:9}],OBV:[{paramNameKey:"params_1",precision:0,min:1,default:30}],PVT:[],PSY:[{paramNameKey:"params_1",precision:0,min:1,default:12},{paramNameKey:"params_2",precision:0,min:1,default:6}],ROC:[{paramNameKey:"params_1",precision:0,min:1,default:12},{paramNameKey:"params_2",precision:0,min:1,default:6}],RSI:[{paramNameKey:"RSI1",precision:0,min:1,styleKey:"lines[0].color"},{paramNameKey:"RSI2",precision:0,min:1,styleKey:"lines[1].color"},{paramNameKey:"RSI3",precision:0,min:1,styleKey:"lines[2].color"},{paramNameKey:"RSI4",precision:0,min:1,styleKey:"lines[3].color"},{paramNameKey:"RSI5",precision:0,min:1,styleKey:"lines[4].color"}],SMA:[{paramNameKey:"params_1",precision:0,min:1,default:12},{paramNameKey:"params_2",precision:0,min:1,default:2}],KDJ:[{paramNameKey:"params_1",precision:0,min:1,default:9},{paramNameKey:"params_2",precision:0,min:1,default:3},{paramNameKey:"params_3",precision:0,min:1,default:3}],SAR:[{paramNameKey:"params_1",precision:0,min:1,default:2},{paramNameKey:"params_2",precision:0,min:1,default:2},{paramNameKey:"params_3",precision:0,min:1,default:20}],TRIX:[{paramNameKey:"params_1",precision:0,min:1,default:12},{paramNameKey:"params_2",precision:0,min:1,default:9}],VOL:[{paramNameKey:"MA1",precision:0,min:1,styleKey:"lines[0].color"},{paramNameKey:"MA2",precision:0,min:1,styleKey:"lines[1].color"},{paramNameKey:"MA3",precision:0,min:1,styleKey:"lines[2].color"},{paramNameKey:"MA4",precision:0,min:1,styleKey:"lines[3].color"},{paramNameKey:"MA5",precision:0,min:1,styleKey:"lines[4].color"}],VR:[{paramNameKey:"params_1",precision:0,min:1,default:26},{paramNameKey:"params_2",precision:0,min:1,default:6}],WR:[{paramNameKey:"WR1",precision:0,min:1,styleKey:"lines[0].color"},{paramNameKey:"WR2",precision:0,min:1,styleKey:"lines[1].color"},{paramNameKey:"WR3",precision:0,min:1,styleKey:"lines[2].color"},{paramNameKey:"WR4",precision:0,min:1,styleKey:"lines[3].color"},{paramNameKey:"WR5",precision:0,min:1,styleKey:"lines[4].color"}]};var Cm=Q("<div class=klinecharts-pro-indicator-setting-modal-content>"),bm=Q("<span>");const xm=t=>{const[r,e]=Mt(Be.clone(t.params.calcParams)),a=i=>_m[i];return ot(Wr,{get title(){return t.params.indicatorName},width:360,get buttons(){return[{type:"confirm",children:z("confirm",t.locale),onClick:()=>{const i=a(t.params.indicatorName),n=[];Be.clone(r()).forEach((o,s)=>{!Be.isValid(o)||o===""?"default"in i[s]&&n.push(i[s].default):n.push(o)}),t.onConfirm(n),t.onClose()}}]},get onClose(){return t.onClose},get children(){var i=Cm();return st(i,()=>a(t.params.indicatorName).map((n,o)=>[(()=>{var s=bm();return st(s,()=>z(n.paramNameKey,t.locale)),s})(),ot(bs,{style:{width:"200px"},get value(){return r()[o]??""},get precision(){return n.precision},get min(){return n.min},onChange:s=>{const l=Be.clone(r());l[o]=s,e(l)}})])),i}})};var wm=Q('<svg viewBox="0 0 1024 1024"><path d="M945.066667 898.133333l-189.866667-189.866666c55.466667-64 87.466667-149.333333 87.466667-241.066667 0-204.8-168.533333-373.333333-373.333334-373.333333S96 264.533333 96 469.333333 264.533333 842.666667 469.333333 842.666667c91.733333 0 174.933333-34.133333 241.066667-87.466667l189.866667 189.866667c6.4 6.4 14.933333 8.533333 23.466666 8.533333s17.066667-2.133333 23.466667-8.533333c8.533333-12.8 8.533333-34.133333-2.133333-46.933334zM469.333333 778.666667C298.666667 778.666667 160 640 160 469.333333S298.666667 160 469.333333 160 778.666667 298.666667 778.666667 469.333333 640 778.666667 469.333333 778.666667z">'),Lm=Q("<img alt=symbol>"),Sm=Q("<li><div><span>");const km=t=>{const[r,e]=Mt(""),[a]=ec(r,t.datafeed.searchSymbols.bind(t.datafeed));return ot(Wr,{get title(){return z("symbol_search",t.locale)},width:460,get onClose(){return t.onClose},get children(){return[ot(bs,{class:"klinecharts-pro-symbol-search-modal-input",get placeholder(){return z("symbol_code",t.locale)},get suffix(){return wm()},get value(){return r()},onChange:i=>{const n=`${i}`;e(n)}}),ot(Ia,{class:"klinecharts-pro-symbol-search-modal-list",get loading(){return a.loading},get dataSource(){return a()??[]},renderItem:i=>(()=>{var n=Sm(),o=n.firstChild,s=o.firstChild;return n.$$click=()=>{t.onSymbolSelected(i),t.onClose()},st(o,ot(ke,{get when(){return i.logo},get children(){var l=Lm();return _e(()=>Oe(l,"src",i.logo)),l}}),s),st(s,()=>i.shortName??i.ticker,null),st(s,()=>`${i.name?`(${i.name})`:""}`,null),st(n,()=>i.exchange??"",null),_e(()=>Oe(s,"title",i.name??"")),n})()})]}})};Je(["click"]);var Mm=Q('<i class="icon-close klinecharts-pro-load-icon">'),Tm=Q("<div class=klinecharts-pro-content><div class=klinecharts-pro-widget>");const oa=()=>Be||{formatDate:(t,r,e)=>{const a=new Date(r);switch(e){case"HH:mm":return a.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit",hour12:!1});case"MM-DD HH:mm":return a.toLocaleDateString("zh-CN",{month:"2-digit",day:"2-digit"})+" "+a.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit",hour12:!1});case"YYYY-MM-DD HH:mm":return a.toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit"})+" "+a.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit",hour12:!1});case"YYYY-MM-DD":return a.toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit"});case"YYYY-MM":return a.toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit"});case"YYYY":return a.getFullYear().toString();default:return a.toLocaleString("zh-CN")}},isString:t=>typeof t=="string",clone:t=>Vh(t),formatValue:(t,r,e)=>{const a=r.split(".");let i=t;for(const n of a)if(i&&typeof i=="object"&&n in i)i=i[n];else return e;return i}};function sa(t,r,e,a){return r==="VOL"&&(a={...a}),(t==null?void 0:t.createIndicator({name:r,onClick:i=>(console.log("Indicator onClick event:",i),!1),createTooltipDataSource:({indicator:i})=>{const n=[];return i.visible?n.push({id:"invisible",position:"middle",marginLeft:8,marginTop:3,marginRight:0,marginBottom:0,paddingLeft:0,paddingTop:0,paddingRight:0,paddingBottom:0,type:"icon_font",content:{family:"icomoon",code:""},size:14,color:"#76808F",activeColor:"#76808F",backgroundColor:"transparent",activeBackgroundColor:"rgba(22, 119, 255, 0.15)",borderRadius:0}):n.push({id:"visible",position:"middle",marginLeft:8,marginTop:3,marginRight:0,marginBottom:0,paddingLeft:0,paddingTop:0,paddingRight:0,paddingBottom:0,type:"icon_font",content:{family:"icomoon",code:""},size:14,color:"#76808F",activeColor:"#76808F",backgroundColor:"transparent",activeBackgroundColor:"rgba(22, 119, 255, 0.15)",borderRadius:0}),n.push({id:"setting",position:"middle",marginLeft:6,marginTop:3,marginBottom:0,marginRight:0,paddingLeft:0,paddingTop:0,paddingRight:0,paddingBottom:0,type:"icon_font",content:{family:"icomoon",code:""},size:14,color:"#76808F",activeColor:"#76808F",backgroundColor:"transparent",activeBackgroundColor:"rgba(22, 119, 255, 0.15)",borderRadius:0}),n.push({id:"close",position:"middle",marginLeft:6,marginTop:3,marginRight:0,marginBottom:0,paddingLeft:0,paddingTop:0,paddingRight:0,paddingBottom:0,type:"icon_font",content:{family:"icomoon",code:""},size:14,color:"#76808F",activeColor:"#76808F",backgroundColor:"transparent",activeBackgroundColor:"rgba(22, 119, 255, 0.15)",borderRadius:0}),{features:n}}},e,a))??null}const Em=t=>{let r,e=null,a,i=!1,n=null;const[o,s]=Mt(t.theme),[l,c]=Mt(t.styles),[u,d]=Mt(t.locale),[h,v]=Mt(t.symbol),[f,p]=Mt(t.period),[g,m]=Mt(!1),[_,x]=Mt([...t.mainIndicators]),[S,y]=Mt({}),[k,C]=Mt(!1),[D,E]=Mt({key:t.timezone,text:jn(t.timezone,t.locale)}),[I,T]=Mt(!1),[L,b]=Mt(),[M,A]=Mt(""),[P,N]=Mt(t.drawingBarVisible),[W,X]=Mt(!1),[$,ct]=Mt(!1),[at,gt]=Mt({visible:!1,indicatorName:"",paneId:"",calcParams:[]}),[Tt,Wt]=Mt(!1);t.ref({setTheme:s,getTheme:()=>o(),setStyles:c,getStyles:()=>e.getStyles(),setLocale:d,getLocale:()=>u(),setTimezone:O=>{E({key:O,text:jn(t.timezone,u())})},getTimezone:()=>D().key,setSymbol:v,getSymbol:()=>h(),setPeriod:p,getPeriod:()=>f(),getChart:()=>e});const V=()=>{e==null||e.resize()},G=(O,q,dt)=>{let tt=q,mt=tt;switch(O.timespan){case"minute":{tt=tt-tt%(60*1e3),mt=tt-dt*O.multiplier*60*1e3;break}case"hour":{tt=tt-tt%(60*60*1e3),mt=tt-dt*O.multiplier*60*60*1e3;break}case"day":{const Lt=new Date(tt);tt=new Date(Lt.getFullYear(),Lt.getMonth(),Lt.getDate()).getTime(),mt=tt-dt*O.multiplier*24*60*60*1e3;break}case"week":{const Lt=new Date(tt).getDay(),Ht=Lt===0?6:Lt-1;tt=tt-Ht*60*60*24;const jt=new Date(tt);tt=new Date(`${jt.getFullYear()}-${jt.getMonth()+1}-${jt.getDate()}`).getTime(),mt=dt*O.multiplier*7*24*60*60*1e3;break}case"month":{const Lt=new Date(tt),Ht=Lt.getFullYear(),jt=Lt.getMonth()+1;tt=new Date(`${Ht}-${jt}-01`).getTime(),mt=dt*O.multiplier*30*24*60*60*1e3;const It=new Date(mt);mt=new Date(`${It.getFullYear()}-${It.getMonth()+1}-01`).getTime();break}case"year":{const Lt=new Date(tt).getFullYear();tt=new Date(`${Lt}-01-01`).getTime(),mt=dt*O.multiplier*365*24*60*60*1e3;const Ht=new Date(mt);mt=new Date(`${Ht.getFullYear()}-01-01`).getTime();break}}return[mt,tt]};return $o(()=>{var O,q,dt;if(window.addEventListener("resize",V),e=T0(r,{formatter:{formatDate:({timestamp:et,template:ut,type:Dt})=>{if(!et||et<=0||!Number.isFinite(et))return console.warn("无效的时间戳:",et),"--";const Ot=new Date(et),yt=f();if(isNaN(Ot.getTime()))return console.warn("无效的日期对象:",et,Ot),"--";const ce=we=>{const Zt=Ot.getFullYear(),Qt=String(Ot.getMonth()+1).padStart(2,"0"),qt=String(Ot.getDate()).padStart(2,"0"),Jt=String(Ot.getHours()).padStart(2,"0"),De=String(Ot.getMinutes()).padStart(2,"0");if(isNaN(Zt)||isNaN(parseInt(Qt))||isNaN(parseInt(qt)))return console.warn("日期字段解析失败:",{timestamp:et,year:Zt,month:Qt,day:qt}),"--";switch(we){case"HH:mm":return`${Jt}:${De}`;case"MM-DD HH:mm":return`${Qt}-${qt} ${Jt}:${De}`;case"YYYY-MM-DD HH:mm":return`${Zt}-${Qt}-${qt} ${Jt}:${De}`;case"YYYY-MM-DD":return`${Zt}-${Qt}-${qt}`;case"YYYY-MM":return`${Zt}-${Qt}`;case"YYYY":return Zt.toString();default:return`${Zt}-${Qt}-${qt} ${Jt}:${De}`}};switch(yt.timespan){case"minute":return ce(Dt==="xAxis"?"HH:mm":"YYYY-MM-DD HH:mm");case"hour":return ce(Dt==="xAxis"?"MM-DD HH:mm":"YYYY-MM-DD HH:mm");case"day":case"week":return ce("YYYY-MM-DD");case"month":return ce(Dt==="xAxis"?"YYYY-MM":"YYYY-MM-DD");case"year":return ce(Dt==="xAxis"?"YYYY":"YYYY-MM-DD")}return ce("YYYY-MM-DD HH:mm")}}}),e){const et=e.getDom("candle_pane","main");if(et){let Dt=document.createElement("div");if(Dt.className="klinecharts-pro-watermark",oa().isString(t.watermark)){const Ot=t.watermark.replace(/(^\s*)|(\s*$)/g,"");Dt.innerHTML=Ot}else Dt.appendChild(t.watermark);et.appendChild(Dt)}const ut=e.getDom("candle_pane","yAxis");a=document.createElement("span"),a.className="klinecharts-pro-price-unit",ut==null||ut.appendChild(a)}_().forEach(et=>{sa(e,et,!0,{id:"candle_pane"})});const tt={};t.subIndicators.forEach(et=>{const ut=sa(e,et,!0);ut&&(tt[et]=ut)}),y(tt);const mt=((q=(O=t.datafeed)==null?void 0:O.constructor)==null?void 0:q.name)==="DefaultDatafeed";let Lt=null;mt?typeof((dt=t.datafeed)==null?void 0:dt.getHistoryKLineData)=="function"&&(Lt={getBars:({type:et,timestamp:ut,symbol:Dt,period:Ot,callback:yt})=>{if(et!=="backward"){yt([],{backward:!1,forward:!1});return}if(!ut){yt([],{backward:!1,forward:!1});return}const ce=t.symbol,we=t.period,Zt=ut-1,Qt=Zt-500*60*1e3;t.datafeed.getHistoryKLineData(ce,we,Qt,Zt).then(qt=>{const Jt=qt.length>=100;yt(qt,{backward:Jt,forward:!1})}).catch(()=>{yt([],{backward:!1,forward:!1})})}}):Lt={getBars:({type:et,timestamp:ut,symbol:Dt,period:Ot,callback:yt})=>{if(et!=="backward"){yt([],{backward:!1,forward:!1});return}if(i){yt([],{backward:!1,forward:!1});return}if(!ut){yt([],{backward:!1,forward:!1});return}i=!0,(async()=>{var ce;try{const we=f(),Zt=h();let Qt=500;try{const ae=e;if(ae.getVisibleRange&&ae.getDataList){const oe=ae.getVisibleRange(),fe=ae.getDataList();if(oe&&fe){const Se=Math.abs(oe.to-oe.from),er=fe.length;Se>=er*.5?Qt=Math.max(800,Se*2):Qt=Math.max(300,Se),Qt=Math.min(Qt,1e3)}}}catch{}const[qt]=G(we,ut-1,Qt),Jt=ut-1;if(!t||!((ce=t.datafeed)!=null&&ce.getHistoryKLineData)){yt([],{backward:!1,forward:!1});return}const De=await t.datafeed.getHistoryKLineData(Zt,we,qt,Jt),he=De.length>0&&De.length>=Math.min(100,Qt*.5);yt(De,{backward:he,forward:!1})}catch{yt([],{backward:!1,forward:!1})}finally{i=!1}})()}};try{typeof(e==null?void 0:e.setDataLoader)=="function"&&Lt&&(e==null||e.setDataLoader(Lt))}catch{}let Ht=0,jt=!1,It=-1;const Xt=500,ee=150,me=()=>{if(!(!e||jt||i))try{const et=e;if(!et.getVisibleRange||!et.getDataList)return;const ut=et.getVisibleRange(),Dt=et.getDataList();if(!ut||!Dt||Dt.length===0)return;const Ot=ut.from<=3,yt=Math.abs(ut.to-ut.from),ce=Dt.length,we=yt>=ce*.8&&ut.from<=5,Zt=ce<yt+20,Qt=Date.now(),qt=Qt-Ht>=Xt,Jt=It>ut.from||It===-1||Math.abs(It-ut.from)<=1;if(It=ut.from,(Ot&&Jt||we||Zt)&&qt){Ht=Qt,jt=!0;const De={visibleRange:{...ut},dataLength:Dt.length,scrollPosition:et.getScrollOffset?et.getScrollOffset():0},he=Dt[0].timestamp;Lt==null||Lt.getBars({type:"backward",timestamp:he,symbol:null,period:null,callback:(ae,oe)=>{try{if(ae&&ae.length>0){const fe=et.getDataList(),Se=[...ae,...fe];et.applyNewData(Se,{backward:ae.length>=30,forward:!1})}}catch{}finally{setTimeout(()=>{jt=!1},200)}}})}}catch{jt=!1}},de=setInterval(me,ee);let xe=-1;const ue=setInterval(()=>{if(e)try{const et=e,ut=et.getVisibleRange(),Dt=et.getDataList();if(ut&&Dt){const Ot=Math.abs(ut.to-ut.from);Math.abs(Ot-xe)>5&&(It=-1,Ot>xe*1.5&&setTimeout(me,50),xe=Ot)}}catch{}},200);wa(()=>{de&&clearInterval(de),ue&&clearInterval(ue)}),e==null||e.subscribeAction("onCandleTooltipFeatureClick",et=>{if(et.indicatorName)switch(et.featureId||et.iconId||et.id){case"visible":{e==null||e.overrideIndicator({name:et.indicatorName,visible:!0,paneId:et.paneId});break}case"invisible":{e==null||e.overrideIndicator({name:et.indicatorName,visible:!1,paneId:et.paneId});break}case"setting":{const ut=e==null?void 0:e.getIndicators({paneId:et.paneId,name:et.indicatorName});if(ut&&ut.length>0){const Dt=ut[0];gt({visible:!0,indicatorName:et.indicatorName,paneId:et.paneId,calcParams:Dt.calcParams})}break}case"close":{if(et.paneId==="candle_pane"){const ut=[..._()];e==null||e.removeIndicator({paneId:et.paneId,name:et.indicatorName}),ut.splice(ut.indexOf(et.indicatorName),1),x(ut)}else{const ut={...S()};e==null||e.removeIndicator({paneId:et.paneId,name:et.indicatorName}),delete ut[et.indicatorName],y(ut)}break}}}),e==null||e.subscribeAction("onIndicatorTooltipFeatureClick",et=>{const ut=et.indicator,Dt=et.feature,Ot=et.paneId;if(ut&&Dt)switch(Dt.id){case"visible":{e==null||e.overrideIndicator({name:ut.name,visible:!0,paneId:Ot});break}case"invisible":{e==null||e.overrideIndicator({name:ut.name,visible:!1,paneId:Ot});break}case"setting":{gt({visible:!0,indicatorName:ut.name,paneId:Ot,calcParams:ut.calcParams});break}case"close":{if(Ot==="candle_pane"){const yt=[..._()];e==null||e.removeIndicator({paneId:Ot,name:ut.name}),yt.splice(yt.indexOf(ut.name),1),x(yt)}else{const yt={...S()};e==null||e.removeIndicator({paneId:Ot,name:ut.name}),delete yt[ut.name],y(yt)}break}}})}),wa(()=>{n&&(n.abort(),n=null),window.removeEventListener("resize",V),E0(r)}),ir(()=>{const O=h();O!=null&&O.priceCurrency?(a.innerHTML=O==null?void 0:O.priceCurrency.toLocaleUpperCase(),a.style.display="flex"):a.style.display="none",e==null||e.setSymbol({ticker:(O==null?void 0:O.ticker)||"",pricePrecision:(O==null?void 0:O.pricePrecision)??2,volumePrecision:(O==null?void 0:O.volumePrecision)??0})}),ir(O=>{var q;if(t&&t.datafeed){console.log("currentDataRequest"),n&&(n.abort(),n=null),O&&(q=t.datafeed)!=null&&q.unsubscribe&&t.datafeed.unsubscribe(O.symbol,O.period);const dt=h(),tt=f();n=new AbortController;const mt=n;return ct(!0),(async()=>{var Lt,Ht;try{if(mt.signal.aborted)return;const[jt,It]=G(tt,new Date().getTime(),500);if(!((Lt=t.datafeed)!=null&&Lt.getHistoryKLineData)){console.error("props.datafeed.getHistoryKLineData 未定义"),ct(!1);return}const Xt=await t.datafeed.getHistoryKLineData(dt,tt,jt,It);if(mt.signal.aborted||(e==null||e.applyNewData(Xt,{backward:!0,forward:!1}),mt.signal.aborted))return;(Ht=t.datafeed)!=null&&Ht.subscribe&&t.datafeed.subscribe(dt,tt,ee=>{e==null||e.updateData(ee)})}catch(jt){(jt==null?void 0:jt.name)!=="AbortError"&&console.error("获取历史数据时出错:",jt)}finally{mt.signal.aborted||ct(!1)}})(),{symbol:dt,period:tt}}return O}),ir(()=>{const O=o();e==null||e.setStyles(O);const q=O==="dark"?"#929AA5":"#76808F";e==null||e.setStyles({indicator:{tooltip:{features:[{id:"visible",position:"middle",marginLeft:8,marginTop:3,marginRight:0,marginBottom:0,paddingLeft:0,paddingTop:0,paddingRight:0,paddingBottom:0,type:"icon_font",content:{family:"icomoon",code:""},size:14,color:q,activeColor:q,backgroundColor:"transparent",activeBackgroundColor:"rgba(22, 119, 255, 0.15)",borderRadius:0},{id:"invisible",position:"middle",marginLeft:8,marginTop:3,marginRight:0,marginBottom:0,paddingLeft:0,paddingTop:0,paddingRight:0,paddingBottom:0,type:"icon_font",content:{family:"icomoon",code:""},size:14,color:q,activeColor:q,backgroundColor:"transparent",activeBackgroundColor:"rgba(22, 119, 255, 0.15)",borderRadius:0},{id:"setting",position:"middle",marginLeft:6,marginTop:3,marginBottom:0,marginRight:0,paddingLeft:0,paddingTop:0,paddingRight:0,paddingBottom:0,type:"icon_font",content:{family:"icomoon",code:""},size:14,color:q,activeColor:q,backgroundColor:"transparent",activeBackgroundColor:"rgba(22, 119, 255, 0.15)",borderRadius:0},{id:"close",position:"middle",marginLeft:6,marginTop:3,marginRight:0,marginBottom:0,paddingLeft:0,paddingTop:0,paddingRight:0,paddingBottom:0,type:"icon_font",content:{family:"icomoon",code:""},size:14,color:q,activeColor:q,backgroundColor:"transparent",activeBackgroundColor:"rgba(22, 119, 255, 0.15)",borderRadius:0}]}}})}),ir(()=>{e==null||e.setLocale(u())}),ir(()=>{e==null||e.setTimezone(D().key)}),ir(()=>{l()&&(e==null||e.setStyles(l()),b(oa().clone(e.getStyles())))}),[Mm(),ot(ke,{get when(){return W()},get children(){return ot(km,{get locale(){return t.locale},get datafeed(){return t.datafeed},onSymbolSelected:O=>{v(O)},onClose:()=>{X(!1)}})}}),ot(ke,{get when(){return g()},get children(){return ot(dm,{get locale(){return t.locale},get mainIndicators(){return _()},get subIndicators(){return S()},onClose:()=>{m(!1)},onMainIndicatorChange:O=>{const q=[..._()];O.added?(sa(e,O.name,!0,{id:"candle_pane"}),q.push(O.name)):(e==null||e.removeIndicator({paneId:"candle_pane",name:O.name}),q.splice(q.indexOf(O.name),1)),x(q)},onSubIndicatorChange:O=>{const q={...S()};if(O.added){const dt=sa(e,O.name);dt&&(q[O.name]=dt)}else O.paneId&&(e==null||e.removeIndicator({paneId:O.paneId,name:O.name}),delete q[O.name]);y(q)}})}}),ot(ke,{get when(){return k()},get children(){return ot(vm,{get locale(){return t.locale},get timezone(){return D()},onClose:()=>{C(!1)},onConfirm:E})}}),ot(ke,{get when(){return I()},get children(){return ot(gm,{get locale(){return t.locale},get currentStyles(){return oa().clone(e.getStyles())},onClose:()=>{T(!1)},onChange:O=>{e==null||e.setStyles(O)},onRestoreDefault:O=>{const q={};O.forEach(dt=>{const tt=dt.key;ni(q,tt,oa().formatValue(L(),tt))}),e==null||e.setStyles(q)}})}}),ot(ke,{get when(){return M().length>0},get children(){return ot(ym,{get locale(){return t.locale},get url(){return M()},onClose:()=>{A("")}})}}),ot(ke,{get when(){return at().visible},get children(){return ot(xm,{get locale(){return t.locale},get params(){return at()},onClose:()=>{gt({visible:!1,indicatorName:"",paneId:"",calcParams:[]})},onConfirm:O=>{const q=at();e==null||e.overrideIndicator({name:q.indicatorName,calcParams:O,paneId:q.paneId})}})}}),ot(Fp,{get locale(){return t.locale},get symbol(){return h()},get spread(){return P()},get period(){return f()},get periods(){return t.periods},onMenuClick:async()=>{try{await ac(()=>N(!P())),e==null||e.resize()}catch{}},onSymbolClick:()=>{X(!W())},onPeriodChange:p,onIndicatorClick:()=>{m(O=>!O)},onTimezoneClick:()=>{C(O=>!O)},onSettingClick:()=>{T(O=>!O)},onScreenshotClick:()=>{if(e){const O=e.getConvertPictureUrl(!0,"jpeg",t.theme==="dark"?"#151517":"#ffffff");A(O)}},onOrderFlowClick:async()=>{var O;if(!e)return;const q=Tt();try{if(q)typeof e.clearOrderFlowData=="function"?e.clearOrderFlowData():typeof e.setOrderFlowData=="function"&&e.setOrderFlowData([]),e.setCandleVisible(!0),Wt(!1);else{const dt=h();if(!((O=t.datafeed)!=null&&O.getOrderFlowData))return;const tt=await t.datafeed.getOrderFlowData(dt);typeof e.setOrderFlowData=="function"&&(e.setOrderFlowData(tt),e.setCandleVisible(!1),Wt(!0))}}catch{}},onAiPredictionClick:()=>{console.log("ai prediction(ai预测)");const O=new CustomEvent("ai-prediction",{detail:{message:"ai-prediction-click",sender:"ai-prediction-click",time:new Date().toLocaleTimeString()},bubbles:!0});window.dispatchEvent(O)}}),(()=>{var O=Tm(),q=O.firstChild;st(O,ot(ke,{get when(){return $()},get children(){return ot(_s,{})}}),q),st(O,ot(ke,{get when(){return P()},get children(){return ot(um,{get locale(){return t.locale},onDrawingItemClick:tt=>{e==null||e.createOverlay(tt)},onModeChange:tt=>{e==null||e.overrideOverlay({mode:tt})},onLockChange:tt=>{e==null||e.overrideOverlay({lock:tt})},onVisibleChange:tt=>{e==null||e.overrideOverlay({visible:tt})},onRemoveClick:tt=>{e==null||e.removeOverlay({groupId:tt})}})}}),q);var dt=r;return typeof dt=="function"?wi(dt,q):r=q,_e(()=>Oe(q,"data-drawing-bar-visible",P())),O})()]};var Im=Q('<svg class=logo width=992px height=618px viewBox="0 0 992 618"version=1.1 xmlns=http://www.w3.org/2000/svg xmlns:xlink=http://www.w3.org/1999/xlink><title>编组</title><defs><polygon id=path-1 points="0 0 992 0 992 127.017578 0 127.017578"></polygon></defs><g id=页面-1 stroke=none stroke-width=1 fill=none fill-rule=evenodd><g id=编组><g transform="translate(0, 490.9824)"><mask id=mask-2 fill=white><use href=#path-1></use></mask><g id=Clip-1></g><g id=PandaAI-Trade mask=url(#mask-2) fill=#444444 fill-rule=nonzero><g transform="translate(9.1679, 13.6664)"><path d="M73.0736523,46.3481979 C73.0736523,49.1571796 72.1507011,51.5046857 70.3047989,53.3907163 C68.4588966,55.2767468 66.1314547,56.2197621 63.322473,56.2197621 L16.6131203,56.2197621 L16.6131203,90.0479273 L0,90.0479273 L0,0 L73.0736523,0 L73.0736523,46.3481979 Z M53.8120635,40.9308761 C54.6146297,40.9308761 55.2767468,40.6499779 55.7984149,40.0881816 C56.3200829,39.5263852 56.5809169,38.8843323 56.5809169,38.1620227 L56.5809169,15.1685011 L16.6131203,15.1685011 L16.6131203,40.9308761 L53.8120635,40.9308761 Z"id=形状></path><path d="M150.721932,24.9196804 L150.721932,80.296748 C150.721932,83.1057297 149.798981,85.4331717 147.953078,87.2790739 C146.107176,89.1249762 143.779734,90.0479273 140.970753,90.0479273 L90.1683123,90.0479273 L90.1683123,50.2005156 L134.229196,50.2005156 L134.229196,39.365872 L97.8729478,39.365872 L97.8729478,24.9196804 L150.721932,24.9196804 Z M131.580728,75.9628906 C132.303038,75.9628906 132.925026,75.6819924 133.446694,75.1201961 C133.968362,74.5583997 134.229196,73.9163468 134.229196,73.1940372 L134.229196,64.2855524 L106.420278,64.2855524 L106.420278,75.9628906 L131.580728,75.9628906 Z"id=形状></path><path d="M184.670482,90.0479273 L184.670482,40.3289514 L209.349393,40.3289514 C210.151959,40.3289514 210.814076,40.5897854 211.335744,41.1114535 C211.857412,41.6331215 212.118246,42.2551103 212.118246,42.9774199 L212.118246,90.0479273 L228.009057,90.0479273 L228.009057,34.6708597 C228.009057,31.861878 227.066041,29.5344361 225.180011,27.6885338 C223.29398,25.8426316 220.946474,24.9196804 218.137492,24.9196804 L168.779671,24.9196804 L168.779671,90.0479273 L184.670482,90.0479273 Z"id=路径></path><path d="M291.451915,11.3161834 L307.824265,11.3161834 L307.824265,80.296748 C307.824265,83.1057297 306.901314,85.4331717 305.055412,87.2790739 C303.209509,89.1249762 300.882067,90.0479273 298.073086,90.0479273 L247.029876,90.0479273 L247.029876,25.1604503 L291.451915,25.1604503 L291.451915,11.3161834 Z M288.803446,75.240581 C289.525756,75.240581 290.147744,74.979747 290.669412,74.4580789 C291.191081,73.9364109 291.451915,73.2742938 291.451915,72.4717276 L291.451915,39.9677966 L263.402226,39.9677966 L263.402226,75.240581 L288.803446,75.240581 Z"id=形状></path><path d="M384.14831,24.9196804 L384.14831,80.296748 C384.14831,83.1057297 383.225359,85.4331717 381.379457,87.2790739 C379.533555,89.1249762 377.206113,90.0479273 374.397131,90.0479273 L323.594691,90.0479273 L323.594691,50.2005156 L367.655575,50.2005156 L367.655575,39.365872 L331.299326,39.365872 L331.299326,24.9196804 L384.14831,24.9196804 Z M365.007107,75.9628906 C365.729416,75.9628906 366.351405,75.6819924 366.873073,75.1201961 C367.394741,74.5583997 367.655575,73.9163468 367.655575,73.1940372 L367.655575,64.2855524 L339.846656,64.2855524 L339.846656,75.9628906 L365.007107,75.9628906 Z"id=形状></path><path d="M456.258883,58.9886155 L421.588023,58.9886155 L415.929932,90.0479273 L400.159506,90.0479273 L417.013396,0.12038493 L452.647335,0.12038493 C455.13529,0.12038493 457.282155,0.862758662 459.087929,2.34750613 C460.893703,3.83225359 461.997231,5.77847662 462.398515,8.18617521 L478.40971,90.0479273 L462.27813,90.0479273 L456.258883,58.9886155 Z M424.356877,43.8201144 L453.369645,43.8201144 L447.831938,15.0481162 L429.653814,15.0481162 L424.356877,43.8201144 Z"id=形状></path><path d="M514.164034,90.0479273 C516.973016,90.0479273 519.300458,89.1249762 521.14636,87.2790739 C522.992262,85.4331717 523.915214,83.1057297 523.915214,80.296748 L523.915214,0 L507.422478,0 L507.422478,90.0479273 L514.164034,90.0479273 Z"id=路径></path><path d="M685.231019,0 L685.231019,5.41732183 C685.231019,8.22630352 684.308068,10.5537455 682.462166,12.3996477 C680.616264,14.24555 678.288822,15.1685011 675.47984,15.1685011 L655.977481,15.1685011 L655.977481,90.0479273 L639.725516,90.0479273 L639.725516,15.1685011 L611.073903,15.1685011 L611.073903,0 L685.231019,0 Z"id=路径></path><path d="M709.308005,25.5216051 C706.499023,25.5216051 704.171581,26.4445562 702.325679,28.2904585 C700.479777,30.1363607 699.556826,32.4638027 699.556826,35.2727844 L699.556826,90.0479273 L716.049561,90.0479273 L716.049561,43.5793445 C716.049561,42.7767783 716.310395,42.1146612 716.832063,41.5929932 C717.353731,41.0713251 718.015848,40.8104911 718.818415,40.8104911 L743.256555,40.8104911 L743.256555,25.5216051 L709.308005,25.5216051 Z"id=路径></path><path d="M815.728283,24.9196804 L815.728283,80.296748 C815.728283,83.1057297 814.805332,85.4331717 812.959429,87.2790739 C811.113527,89.1249762 808.786085,90.0479273 805.977104,90.0479273 L755.174663,90.0479273 L755.174663,50.2005156 L799.235548,50.2005156 L799.235548,39.365872 L762.879299,39.365872 L762.879299,24.9196804 L815.728283,24.9196804 Z M796.587079,75.9628906 C797.309389,75.9628906 797.931377,75.6819924 798.453045,75.1201961 C798.974714,74.5583997 799.235548,73.9163468 799.235548,73.1940372 L799.235548,64.2855524 L771.426629,64.2855524 L771.426629,75.9628906 L796.587079,75.9628906 Z"id=形状></path><path d="M878.208061,11.3161834 L894.580412,11.3161834 L894.580412,80.296748 C894.580412,83.1057297 893.657461,85.4331717 891.811558,87.2790739 C889.965656,89.1249762 887.638214,90.0479273 884.829232,90.0479273 L833.786022,90.0479273 L833.786022,25.1604503 L878.208061,25.1604503 L878.208061,11.3161834 Z M875.559593,75.240581 C876.281902,75.240581 876.903891,74.979747 877.425559,74.4580789 C877.947227,73.9364109 878.208061,73.2742938 878.208061,72.4717276 L878.208061,39.9677966 L850.158373,39.9677966 L850.158373,75.240581 L875.559593,75.240581 Z"id=形状></path><path d="M975.238315,53.4509087 C975.238315,56.2598904 974.295299,58.5873324 972.409269,60.4332347 C970.523238,62.2791369 968.175732,63.202088 965.36675,63.202088 L929.010502,63.202088 L929.010502,74.9998111 L975.238315,74.9998111 L975.238315,80.296748 C975.238315,83.1057297 974.295299,85.4331717 972.409269,87.2790739 C970.523238,89.1249762 968.175732,90.0479273 965.36675,90.0479273 L912.638151,90.0479273 L912.638151,25.5216051 L975.238315,25.5216051 L975.238315,53.4509087 Z M956.097111,49.8393608 C956.81942,49.8393608 957.461473,49.5785268 958.02327,49.0568588 C958.585066,48.5351908 958.865964,47.913202 958.865964,47.1908924 L958.865964,39.245487 L929.010502,39.245487 L929.010502,49.8393608 L956.097111,49.8393608 Z"id=形状></path></g></g></g><path d="M491.846918,213.063281 L489.366601,222.635023 L476.827173,265.14217 L444.337622,363.998857 L424.991819,425.628459 L458.844662,444.99233 C461.367845,446.435788 495.432494,464.010479 495.707338,464.010479 C495.939316,464.010479 532.59775,445.360765 534.879709,444.081337 L570.438768,424.152195 L567.315468,417.972916 L532.575897,317.701371 C532.143879,316.414371 500.961307,218.838795 500.961307,218.772342 C500.961307,217.407955 498.579328,211.956293 497.643851,211.179046 C494.995434,208.979371 493.827978,209.358742 491.846918,213.063281"id=Fill-2 fill=#444444></path><path d="M480.578243,2.28089145 L435.35931,12.8527915 C411.438663,17.9107829 393.884808,22.8081096 391.542333,25.0776025 C390.917,25.6840904 340.602112,124.849489 331.635652,143.149273 L275.635618,257.433969 L286.658783,277.754258 L319.626579,336.367403 L347.02942,382.006248 C350.70577,386.954887 362.832513,394.729877 384.181228,405.824147 L416.581686,421.588627 L408.062372,395.449923 L383.669363,324.021462 C377.846375,306.362654 368.945474,275.05156 369.221998,273.19929 C369.763281,269.57298 385.068797,259.754774 430.813883,233.690094 L491.685458,199.006725 L491.685458,99.4299912 C491.685458,21.237634 491.062647,-0.114777823 488.786572,0 L480.578243,2.28089145 Z"id=Fill-4 fill=#444444></path><path d="M500.995263,97.6583904 C501.009888,135.65347 502.077324,197.477383 502.735436,198.4338 L518.352777,208.659136 L578.065302,242.910981 C616.724128,265.279534 623.86334,270.246678 623.86334,274.771364 C623.86334,275.235693 610.353133,320.123369 609.07221,323.91539 L584.5422,394.011428 L575.563973,420.95009 L609.670647,405.079622 L643.014987,388.585002 L672.8889,337.5273 L710.012971,272.512806 L717.26397,258.555173 L683.934759,190.124663 L626.220104,72.9580981 C602.0036,24.5601951 601.769101,24.2035365 592.558054,21.5395321 L545.310153,10.3317029 L504.149158,0.581631795 C501.474685,-0.445444113 500.967022,15.1995885 500.995263,97.6583904"id=Fill-6 fill=#444444>');const Am=Im(),Dm=()=>Be||(console.warn("klinecharts.utils not available, using fallback functions"),{isString:t=>typeof t=="string"});class Pm{constructor(r){if(ln(this,"_container"),ln(this,"_chartApi",null),Dm().isString(r.container)){if(this._container=document.getElementById(r.container),!this._container)throw new Error("Container is null")}else this._container=r.container;this._container.classList.add("klinecharts-pro"),this._container.setAttribute("data-theme",r.theme??"light"),pc(()=>{const e=this;return ot(Em,{ref:a=>{e._chartApi=a},get styles(){return r.styles??{}},get watermark(){return r.watermark??Am},get theme(){return r.theme??"light"},get locale(){return r.locale??"zh-CN"},get drawingBarVisible(){return r.drawingBarVisible??!0},get symbol(){return r.symbol},get period(){return r.period},get periods(){return r.periods??[{multiplier:1,timespan:"minute",text:"1m"},{multiplier:5,timespan:"minute",text:"5m"},{multiplier:15,timespan:"minute",text:"15m"},{multiplier:1,timespan:"hour",text:"1h"},{multiplier:1,timespan:"day",text:"1d"}]},get timezone(){return r.timezone??"Asia/Shanghai"},get mainIndicators(){return r.mainIndicators??["MA"]},get subIndicators(){return r.subIndicators??["VOL"]},get datafeed(){return r.datafeed}})},this._container)}setTheme(r){var e;(e=this._container)==null||e.setAttribute("data-theme",r),this._chartApi.setTheme(r)}getTheme(){return this._chartApi.getTheme()}setStyles(r){this._chartApi.setStyles(r)}getStyles(){return this._chartApi.getStyles()}setLocale(r){this._chartApi.setLocale(r)}getLocale(){return this._chartApi.getLocale()}setTimezone(r){this._chartApi.setTimezone(r)}getTimezone(){return this._chartApi.getTimezone()}setSymbol(r){this._chartApi.setSymbol(r)}getSymbol(){return this._chartApi.getSymbol()}setPeriod(r){this._chartApi.setPeriod(r)}getPeriod(){return this._chartApi.getPeriod()}getChart(){return this._chartApi.getChart()}}_a?K0.forEach(t=>{_a(t)}):console.warn("registerOverlay method not found in klinecharts");const xs="http://api.pandaai.online",ws="token",la=ta();function Ls(t,r){const e={},a=localStorage.getItem(ws);a&&(e.Authorization=`${a}`);let i="";return r&&(i=`?${new URLSearchParams(Object.entries(r).reduce((o,[s,l])=>(o[s]=l.toString(),o),{})).toString()}`),fetch(xs+t+i,{headers:e,method:"GET"})}function Wa(t,r){const e={"Content-Type":"application/json"},a=localStorage.getItem(ws);return a&&(e.Authorization=`${a}`),fetch(xs+t,{headers:e,method:"POST",body:JSON.stringify(r)})}async function Ss(t){return t?(la.type=t.type,la.quotationType=t.type):(la.type=void 0,la.quotationType="future"),(await Ls("/instrument/getQuotation",t)).json()}async function ks(t,r,e){return(await Wa("/orderFlow/getHistoryMarketData",{symbolCode:t,cycle:r,baseBarCount:e})).json()}async function Ms(t,r){const e={underlyingSymbol:t,...r};return(await Wa("/instrument/addInstrument",e)).json()}async function Aa(t){return(await Ls("/instrument/queryInstrument",t)).json()}async function Ts(t,r){const e={underlyingSymbol:t,...r};return(await Wa("/instrument/deleteInstrument",e)).json()}async function Fm(t,r,e,a,i,n,o){return(await Wa("/predict",{model:t,exchange:r,contract:e,aggregation:a,indicator:i,startDate:n,endDate:o})).json()}const Rm="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAkCAYAAADo6zjiAAAAAXNSR0IArs4c6QAAA1hJREFUWEftmEtoE0EYx//f2NZHSUGKiIiNIIrQgyCigooP8KAeRLRFEN9QLFJIZrelIMh6kUJ3pj140IJP0INR8SRIEWpVFFRQsPgGLXsQRdRoL7bOZyYkksY0D2ykh+5pyUy+/e33/M8S/tPV0tIyq6qq6j4RvfZ9f1P6sZT9/Pb29pkdHR1fxpvLdd0NzHzL2g2FQlM9z/tp70cBOI6zA8BlAOeVUvvHE6JYgKMJqGMAHiqllk94ACnlPGauD4KgNxaL/coHXBYPSCkfE9FSZm7UWsf+O4DjOO8AhAE0K6VOTkiAhoaGKRYsHA6vZ+Zee2+MmREEwU8btuwqKCkJC3nAdd1OZnbzeOY+tbW1hYaHh+uSNUnUTESHATwzxuwUQnAoFHrled5ILiOFAKSUd4loFYA3AASAuSk7bwEstr9RhpGcoMx8SWu96x8AViqlKgFwpg0pZT8RrbEAdmEIwM0U4TwAP4joDTNvZuZ7WuvV/whQkf1/13X7mHltEoCIbvu+vy57k+M4I8z8IA3Q1NRUWVNTs52Za1IhO87MtQAuArBvFI/H41d7enqG7XoqBNYD4wOQatV56x1Ag1LqSlkApJQHiOg0gOvMfIeIVjDzNCHEK2PMQiLayswHtdZnygrAzHu11heykmoPEZ2fBJj0wKQHcnjAzoIjtlwBLEk1sH4ADoA5JXXCdB8otgwdx7kB4I8CztHOv6dnwdeEtIoJIazEmp/oonEALwHszpwFpQJEIpH5FRUVG5nZjn07/aIpCDtxR4joqQX4AGB2npl9TSm1PdXZkp2wWA9k2hxTE0YikTlEtMhuFkLsBWDl+AtjzCGrB4jose/7dlra4ZIG2K+1PpfVCfcR0dnMHCgKIHOT4zh5FVHGLPgMwHrO6sHKhIj5xMzTAdSWFSAajS4TQvQBqB4jZEPGmHVdXV2Pcsz/ok5GBTWh53liYGAgqSXr6uqstAobYw4HQXCqvr6ePc8zueDKci4opAnHPQdyKKaizwVSyiVE9CRRit8GBwdr0yepssrybOBoNLqWmT92d3c/T6+NApBSJkUFEcV832/M0xuSS6WEYCxbf30faG1tXVBdXf1+rLNAVtkWHYKiAQq99UQDOAFgmxBiS2dnp02wkq/fFReAx7X4uMUAAAAASUVORK5CYII=",Bm={class:"header"},Nm={class:"dropdown-menu"},Om={class:"filter-right"},Vm=Ne({__name:"nav",emits:["view-change","filter-change"],setup(t,{emit:r}){const e=B(null),a=B(!1),i=B("market"),n=B("all");function o(u){var v;const d=u.target,h=(v=e.value)==null?void 0:v.querySelector(".dropdown-menu");h!=null&&h.contains(d)||e.value&&!e.value.contains(d)&&(a.value=!1)}function s(u){i.value=u,a.value=!1,u==="optional"&&(n.value="all"),c("view-change",u)}function l(u){n.value=u,c("filter-change",u)}const c=r;return Ge(()=>{document.addEventListener("click",o)}),Ar(()=>{document.removeEventListener("click",o)}),(u,d)=>(H(),j("div",Bm,[w("div",{class:"title",ref_key:"titleRef",ref:e},[w("span",{class:"title-text",onClick:d[0]||(d[0]=h=>a.value=!a.value)},[d[6]||(d[6]=w("img",{src:Rm,alt:"market",class:"market-icon",width:"16px"},null,-1)),pr(" "+rt(i.value==="market"?"行情-期货":"行情-股票")+" ",1),w("span",{class:le(["dropdown-arrow",{active:a.value}])},d[5]||(d[5]=[w("svg",{t:"1742451321748",class:"icon",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"32302",width:"16",height:"16"},[w("path",{d:"M722.438 510.815c-0.246-0.246-0.513-0.454-0.766-0.69l-299.893-299.892c-14.231-14.24-37.291-14.24-51.529 0-14.241 14.231-14.241 37.291 0 51.529l274.851 274.859-274.849 274.851c-14.241 14.237-14.241 37.301 0 51.532 7.116 7.119 16.436 10.682 25.764 10.682 9.321 0 18.644-3.563 25.763-10.682l299.905-299.901c0.246-0.234 0.51-0.438 0.751-0.678 7.129-7.13 10.685-16.469 10.674-25.804 0.006-9.337-3.55-18.676-10.674-25.804z",fill:"#ffffff","p-id":"32303"})],-1)]),2)]),di(w("div",Nm,[w("div",{class:le(["dropdown-item",{active:i.value==="market"}]),onClick:d[1]||(d[1]=h=>s("market"))},"行情-期货",2),w("div",{class:le(["dropdown-item",{active:i.value==="optional"}]),onClick:d[2]||(d[2]=h=>s("optional"))},"行情-股票",2)],512),[[Kn,a.value]])],512),w("div",Om,[w("span",{class:le(["filter-item",{active:n.value==="all"}]),onClick:d[3]||(d[3]=h=>l("all"))},"全部",2),w("span",{class:le(["filter-item",{active:n.value==="optional"}]),onClick:d[4]||(d[4]=h=>l("optional"))},"自选",2)])]))}}),zm=Te(Vm,[["__scopeId","data-v-a54a866e"]]),Bi=hi("orderflow",()=>{const t=As(null);return{tick:t,setTick:i=>{t.value=i},getTick:()=>t.value,removeTick:()=>{t.value&&(t.value.destructor(),t.value=null)}}}),Ni=hi("orderflow2",()=>{const t=B("sa"),r=B("SA509"),e=B(1),a=B("CZCE"),i=B(0),n=B(0),o=B(0),s=B(0),l=B(0),c=B(0),u=B(!1);return{contract:t,mainContract:r,period:e,exchange:a,priceChangeRatio:i,priceDifference:n,lastPrice:o,volume:s,openInterest:l,preSettlement:c,isShowAIPredict:u}}),Oi=hi("marketList",()=>{const t=B(!1),r=B(0),e=B(""),a=B("ONE_MIN"),i=B("");function n(){const s=new Date,l=s.getTime()+s.getTimezoneOffset()*6e4,c=new Date(l+8*60*60*1e3),u=c.getDay(),d=c.getHours(),h=c.getMinutes();if(u===0)return!1;if(u===6){if(d<2||d===2&&h<=35)return!0}else if(d===8&&h>=50||d>8&&d<15||d===15&&h<=20||d===20&&h>=50||d>20||d<2||d===2&&h<=40)return!0;return!1}function o(s=!1,l){if(t.value){if(!s&&!n()){r.value=setTimeout(()=>o(s,l),5e3);return}Fm(l,i.value,e.value,a.value,"","","").then(c=>{c.code=="200"&&console.log("-x-x-x-",c.data)}).catch(c=>{console.log(e.value,"AI预测数据获取失败,","错误原因:",c)}).finally(()=>{r.value=setTimeout(()=>o(s,l),5e3)})}}return{isShowAIPredict:t,timerId:r,currentCode:e,predictionPeriod:a,exchange:i,StartUpdateAIPredictData:o}}),$m={class:"marketlist-container"},Wm={class:"marketlist-wrapper"},Ym={class:"marketlist-header"},Zm=["onClick"],Hm={class:"col2 code",style:{"padding-left":"5px",gap:"5px"}},jm=["onClick"],Xm={class:"col2 name"},Gm={class:"col2 price"},Km=30,Um=Ne({__name:"MarketList",setup(t,{expose:r}){const e=B("all"),a=vi("child_futureListHeight");cr(),ta(),r({activeFilter:e});const i=B("code"),n=B(!0),o=B({}),s=B({}),l=Ze(()=>Object.values(o.value).filter(b=>!isNaN(Number(b.lastPrice))&&!isNaN(Number(b.priceDifference))&&!isNaN(Number(b.priceChangeRatio))).map(b=>({...b,lastPrice:Number(b.lastPrice.toFixed(2)),priceDifference:Number(b.priceDifference.toFixed(2)),priceChangeRatio:Number((b.priceChangeRatio*100).toFixed(2))}))),c=Ze(()=>{let L;return e.value==="optional"?L=Object.values(s.value).filter(b=>!isNaN(Number(b.lastPrice))&&!isNaN(Number(b.priceDifference))&&!isNaN(Number(b.priceChangeRatio))).map(b=>({...b,lastPrice:Number(b.lastPrice.toFixed(2)),priceDifference:Number(b.priceDifference.toFixed(2)),priceChangeRatio:Number((b.priceChangeRatio*100).toFixed(2))})):L=l.value,L.sort((b,M)=>{if(i.value==="name"){const N=p[b.underlyingSymbol]||"",W=p[M.underlyingSymbol]||"";return n.value?N.localeCompare(W,"zh"):W.localeCompare(N,"zh")}if(i.value==="code")return n.value?b.code.localeCompare(M.code):M.code.localeCompare(b.code);const A=b[i.value],P=M[i.value];return n.value?Number(A)-Number(P):Number(P)-Number(A)})}),u=B(1),d=Ze(()=>c.value.slice(0,u.value*Km)),h=B(null);function v(){const L=h.value;L&&L.scrollTop+L.clientHeight>=L.scrollHeight-10&&d.value.length<c.value.length&&(u.value+=1)}function f(L){i.value===L?n.value=!n.value:(i.value=L,n.value=!0)}const p={ih:"上证50主连",if:"沪深300主连",ic:"中证500主连",im:"中证1000主连",ts:"2年期国债",tf:"5年期国债",t:"10年期国债",tl:"30年期国债",sc:"原油主连",nr:"20号胶",lu:"低硫燃料油",bc:"国际铜",ec:"欧线集运主连",au:"沪金主连",ag:"沪银主连",cu:"沪铜主连",al:"沪铝主连",zn:"沪锌主连",pb:"沪铅主连",rb:"螺纹钢主连",ru:"橡胶主连",br:"合成橡胶主连",ni:"沪镍主连",sn:"沪锡主连",sp:"纸浆主连",fu:"燃油主连",bu:"沥青主连",ss:"不锈钢主连",hc:"热轧卷板",ao:"氧化铝主连",a:"豆一主连",b:"豆二主连",c:"玉米主连",cs:"玉米淀粉主连",m:"豆粕主连",i:"铁矿石主连",pg:"液化石油气主连",y:"豆油主连",p:"棕榈油主连",jd:"鸡蛋主连",l:"塑料主连",pp:"聚丙烯主连",v:"PVC主连",eb:"苯乙烯主连",eg:"乙二醇主连",j:"焦炭主连",jm:"焦煤主连",lh:"生猪主连",lg:"原木主连",ap:"苹果主连",sr:"白糖主连",oi:"菜籽油主连",cf:"棉花主连",cy:"棉纱主连",ta:"PTA主连",px:"对二甲苯主连",pr:"瓶片主连",ma:"甲醇主连",fg:"玻璃主连",sa:"纯碱主连",sh:"烧碱主连",rm:"菜粨主连",sf:"硅铁主连",sm:"锰硅主连",ur:"尿素主连",pk:"花生主连",cj:"红枣主连",pf:"短纤主连",lc:"碳酸锂主连",si:"工业硅主连",ps:"多晶硅主连"},g={ih:.2,if:.2,ic:.2,im:.2,ts:.005,tf:.005,t:.005,tl:.01,sc:.1,nr:5,lu:1,bc:10,ec:.1,au:.02,ag:1,cu:10,al:5,zn:5,pb:5,rb:1,ru:5,br:5,ni:10,sn:10,sp:2,fu:1,bu:1,ss:5,hc:1,ao:1,a:1,b:1,c:1,cs:1,m:1,i:.5,pg:1,y:2,p:2,jd:1,l:1,pp:1,v:1,eb:1,eg:1,j:.5,jm:.5,lh:5,lg:.5,ap:1,sr:1,oi:1,cf:5,cy:5,ta:2,px:1,pr:1,ma:1,fg:1,sa:1,sh:1,rm:1,sf:2,sm:2,ur:1,pk:2,cj:5,pf:2,lc:20,si:5,ps:5};function m(){const L=new Date,b=L.getTime()+L.getTimezoneOffset()*6e4,M=new Date(b+8*60*60*1e3),A=M.getDay(),P=M.getHours(),N=M.getMinutes();if(A===0)return!1;if(A===6){if(P<2||P===2&&N<=35)return!0}else if(P===8&&N>=50||P>8&&P<15||P===15&&N<=20||P===20&&N>=50||P>20||P<2||P===2&&N<=40)return!0;return!1}const _=B("");function x(L=!1){if(!(!L&&!m()))return Ss().then(b=>{if(b.code==="200"){o.value=b.data;const M=Object.values(b.data);M.sort((P,N)=>{if(i.value==="name"){const $=p[P.underlyingSymbol]||"",ct=p[N.underlyingSymbol]||"";return n.value?$.localeCompare(ct,"zh"):ct.localeCompare($,"zh")}if(i.value==="code")return n.value?P.code.localeCompare(N.code):N.code.localeCompare(P.code);const W=P[i.value],X=N[i.value];return n.value?Number(W)-Number(X):Number(X)-Number(W)}),M.length>0&&(_.value=M[0].code);const A=o.value[C.contract];A&&(C.lastPrice=A.lastPrice,C.priceDifference=A.priceDifference,C.priceChangeRatio=A.priceChangeRatio,C.volume=A.volume,C.openInterest=A.openInterest)}})}let S;Ge(async()=>{await x(!0),S=setInterval(x,1e3),Aa().then(L=>{L.code=="200"&&(s.value=L.data)}),h.value&&h.value.addEventListener("scroll",v)}),Ar(()=>{clearInterval(S),h.value&&h.value.removeEventListener("scroll",v)});const y=L=>{const b=typeof L=="string"?parseFloat(L):L;return isNaN(b)?"":b>0?"up":b<0?"down":""},k=Bi(),C=Ni();function D(L){C.contract=L,C.mainContract=o.value[L].code,C.exchange=o.value[L].exchange,C.priceChangeRatio=o.value[L].priceChangeRatio,C.priceDifference=o.value[L].priceDifference,C.lastPrice=o.value[L].lastPrice,C.volume=o.value[L].volume,C.openInterest=o.value[L].openInterest,C.preSettlement=o.value[L].preSettlement,ks(o.value[L].code,1,200).then(b=>{if(b.code=="200"){const M=k.getTick();M&&(M.updateBaseData(b.data.baseBar,g[L],o.value[L].code),C.isShowAIPredict=!1,M.ClearChart(1),M.DrawChart(1))}})}function E(L){L in s.value?T(L):I(L)}function I(L){Ms(L).then(b=>{b.code=="200"&&Aa().then(M=>{M.code=="200"&&(s.value=M.data)})})}function T(L){Ts(L).then(b=>{b.code=="200"&&delete s.value[L]})}return(L,b)=>(H(),j("div",$m,[w("div",Wm,[w("div",Ym,[w("span",{class:"col code",onClick:b[0]||(b[0]=M=>f("code"))},"商品代码"+rt(i.value==="code"?n.value?"↑":"↓":""),1),w("span",{class:"col name",onClick:b[1]||(b[1]=M=>f("name"))},"名称"+rt(i.value==="name"?n.value?"↑":"↓":""),1),w("span",{class:"col price",onClick:b[2]||(b[2]=M=>f("lastPrice"))},"最新价"+rt(i.value==="lastPrice"?n.value?"↑":"↓":""),1),w("span",{class:"col change",onClick:b[3]||(b[3]=M=>f("priceDifference"))},"涨跌"+rt(i.value==="priceDifference"?n.value?"↑":"↓":""),1),w("span",{class:"col changePercent",onClick:b[4]||(b[4]=M=>f("priceChangeRatio"))},"涨跌幅"+rt(i.value==="priceChangeRatio"?n.value?"↑":"↓":""),1)]),w("div",{class:"future-list",ref_key:"futureListRef",ref:h,style:Ce({"max-height":`calc(${pe(a)}vh - 120px)`})},[(H(!0),j(ge,null,Ae(d.value,M=>di((H(),j("div",{key:M.code,class:"marketlist-row",onClick:A=>D(M.underlyingSymbol)},[w("span",Hm,[w("span",{class:le(["optional-svg",{added:M.underlyingSymbol in s.value}]),onClick:Un(A=>E(M.underlyingSymbol),["stop"])},b[5]||(b[5]=[w("svg",{t:"1743647836070",class:"icon",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"1646",width:"32",height:"32"},[w("path",{d:"M818.56 50.432a128 128 0 0 1 128 128v667.136a128 128 0 0 1-179.84 117.056L512 849.792l-254.72 112.832a128 128 0 0 1-179.84-117.12V178.496a128 128 0 0 1 128-128h613.12zM528 242.176a32 32 0 0 0-40.384 6.848l-3.328 4.736-56.512 97.28-109.952 23.68a32 32 0 0 0-20.544 48l3.456 4.608 75.008 83.776-11.456 111.872a32 32 0 0 0 39.296 34.368l5.504-1.856L512 610.048l102.848 45.44a32 32 0 0 0 44.8-26.752v-5.76l-11.456-111.872 75.008-83.84a32 32 0 0 0-11.52-50.88l-5.568-1.728-109.952-23.68-56.512-97.28a32 32 0 0 0-11.52-11.52z",fill:"#E1E2E3","p-id":"1647"})],-1)]),10,jm),pr(" "+rt(M.code),1)]),w("span",Xm,rt(p[M.underlyingSymbol]),1),w("span",Gm,rt(M.lastPrice),1),w("span",{class:le(["col2 change",y(M.priceDifference)])},rt(M.priceDifference),3),w("span",{class:le(["col2 changePercent",y(M.priceChangeRatio)])},rt(M.priceChangeRatio)+"% ",3)],8,Zm)),[[Kn,M.underlyingSymbol in p]])),128))],4)])]))}}),Qm=Te(Um,[["__scopeId","data-v-1a2e1bce"]]),qm={class:"marketlist-container"},Jm={class:"marketlist-wrapper"},ty={class:"marketlist-header"},ey=["onClick"],ry={class:"col2 code",style:{"padding-left":"5px",gap:"5px"}},ay=["onClick"],iy={class:"col2 name"},ny={class:"col2 price"},oy=30,sy=Ne({__name:"StockList",setup(t,{expose:r}){const e=B("all"),a=vi("child_futureListHeight");cr();const i=ta();r({activeFilter:e});const n=B("code"),o=B(!0),s=B({}),l=B({}),c=Ze(()=>Object.entries(s.value).filter(([T,L])=>!isNaN(Number(L.last))).map(([T,L])=>{var b,M,A;return{...L,code:T,last:Number((b=L.last)==null?void 0:b.toFixed(2)),priceDifference:Number((M=L.priceDifference)==null?void 0:M.toFixed(2)),priceChangeRatio:Number((L.priceChangeRatio*100).toFixed(2)),preClose:Number((A=L.preClose)==null?void 0:A.toFixed(2))}})),u=Ze(()=>{let T;return e.value==="optional"?T=Object.entries(l.value).filter(([L,b])=>!isNaN(Number(b.last))).map(([L,b])=>{var M,A,P;return{...b,code:L,last:Number((M=b.last)==null?void 0:M.toFixed(2)),priceDifference:Number((A=b.priceDifference)==null?void 0:A.toFixed(2)),priceChangeRatio:Number((b.priceChangeRatio*100).toFixed(2)),preClose:Number((P=b.preClose)==null?void 0:P.toFixed(2))}}):T=c.value,T.sort((L,b)=>{if(n.value==="name"||n.value==="code"){const P=L[n.value]||"",N=b[n.value]||"";return o.value?P.localeCompare(N,"zh"):N.localeCompare(P,"zh")}const M=L[n.value],A=b[n.value];return o.value?Number(M)-Number(A):Number(A)-Number(M)})});function d(T){n.value===T?o.value=!o.value:(n.value=T,o.value=!0)}function h(){const T=new Date,L=T.getTime()+T.getTimezoneOffset()*6e4,b=new Date(L+8*60*60*1e3),M=b.getDay(),A=b.getHours(),P=b.getMinutes();if(M===0)return!1;if(M===6){if(A<2||A===2&&P<=35)return!0}else if(A===8&&P>=50||A>8&&A<15||A===15&&P<=20||A===20&&P>=50||A>20||A<2||A===2&&P<=40)return!0;return!1}const v=B("");function f(T=!1){if(!(!T&&!h()))return Ss({type:"stock"}).then(L=>{if(L.code==="200"){s.value=L.data;const b=Object.values(L.data);b.sort((A,P)=>{if(n.value==="name"||n.value==="code"){const X=A[n.value]||"",$=P[n.value]||"";return o.value?X.localeCompare($,"zh"):$.localeCompare(X,"zh")}const N=A[n.value],W=P[n.value];return o.value?Number(N)-Number(W):Number(W)-Number(N)}),b.length>0&&(v.value=b[0].code),i.symbol={exchange:"",market:"stocks",name:"000016.SZ",shortName:"000016.SZ",ticker:"000016.SZ",priceCurrency:"",type:"stock"};const M=s.value[k.contract];M&&(k.lastPrice=M.last,k.priceDifference=M.priceDifference,k.priceChangeRatio=M.priceChangeRatio,k.volume=M.volume)}})}let p;const g=B(1),m=Ze(()=>u.value.slice(0,g.value*oy)),_=B(null);function x(){const T=_.value;T&&T.scrollTop+T.clientHeight>=T.scrollHeight-10&&m.value.length<u.value.length&&(g.value+=1)}Ge(async()=>{await f(!0),p=setInterval(f,1e3),Aa({quotationType:"stock"}).then(T=>{T.code=="200"&&(l.value=T.data)}),_.value&&_.value.addEventListener("scroll",x)}),Ar(()=>{clearInterval(p),_.value&&_.value.removeEventListener("scroll",x)});const S=T=>{const L=typeof T=="string"?parseFloat(T):T;return isNaN(L)?"":L>0?"up":L<0?"down":""},y=Bi(),k=Ni();function C(T){const L=s.value[T];L&&(k.contract=T,k.mainContract=T,k.priceChangeRatio=L.priceChangeRatio,k.priceDifference=L.priceDifference,k.lastPrice=L.last,k.volume=L.volume,k.preSettlement=L.preClose,ks(T,1,200).then(b=>{if(b.code=="200"){const M=y.getTick();M&&(M.updateBaseData(b.data.baseBar,.01,T),k.isShowAIPredict=!1,M.ClearChart(1),M.DrawChart(1))}}))}function D(T){T in l.value?I(T):E(T)}function E(T){Ms(T,{quotationType:"stock"}).then(L=>{L.code=="200"&&Aa({quotationType:"stock"}).then(b=>{b.code=="200"&&(l.value=b.data)})})}function I(T){Ts(T,{quotationType:"stock"}).then(L=>{L.code=="200"&&delete l.value[T]})}return(T,L)=>(H(),j("div",qm,[w("div",Jm,[w("div",ty,[w("span",{class:"col code",onClick:L[0]||(L[0]=b=>d("code"))},"商品代码"+rt(n.value==="code"?o.value?"↑":"↓":""),1),w("span",{class:"col name",onClick:L[1]||(L[1]=b=>d("name"))},"名称"+rt(n.value==="name"?o.value?"↑":"↓":""),1),w("span",{class:"col price",onClick:L[2]||(L[2]=b=>d("last"))},"最新价"+rt(n.value==="last"?o.value?"↑":"↓":""),1),w("span",{class:"col change",onClick:L[3]||(L[3]=b=>d("priceDifference"))},"涨跌"+rt(n.value==="priceDifference"?o.value?"↑":"↓":""),1),w("span",{class:"col changePercent",onClick:L[4]||(L[4]=b=>d("priceChangeRatio"))},"涨跌幅"+rt(n.value==="priceChangeRatio"?o.value?"↑":"↓":""),1)]),w("div",{class:"future-list",ref_key:"futureListRef",ref:_,style:Ce({"max-height":`calc(${pe(a)}vh - 120px)`})},[(H(!0),j(ge,null,Ae(m.value,b=>(H(),j("div",{key:b.code,class:"marketlist-row",onClick:M=>C(b.code)},[w("span",ry,[w("span",{class:le(["optional-svg",{added:b.code in l.value}]),onClick:Un(M=>D(b.code),["stop"])},L[5]||(L[5]=[w("svg",{t:"1743647836070",class:"icon",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"1646",width:"32",height:"32"},[w("path",{d:"M818.56 50.432a128 128 0 0 1 128 128v667.136a128 128 0 0 1-179.84 117.056L512 849.792l-254.72 112.832a128 128 0 0 1-179.84-117.12V178.496a128 128 0 0 1 128-128h613.12zM528 242.176a32 32 0 0 0-40.384 6.848l-3.328 4.736-56.512 97.28-109.952 23.68a32 32 0 0 0-20.544 48l3.456 4.608 75.008 83.776-11.456 111.872a32 32 0 0 0 39.296 34.368l5.504-1.856L512 610.048l102.848 45.44a32 32 0 0 0 44.8-26.752v-5.76l-11.456-111.872 75.008-83.84a32 32 0 0 0-11.52-50.88l-5.568-1.728-109.952-23.68-56.512-97.28a32 32 0 0 0-11.52-11.52z",fill:"#E1E2E3","p-id":"1647"})],-1)]),10,ay),pr(" "+rt(b.code),1)]),w("span",iy,rt(b.name),1),w("span",ny,rt(b.last),1),w("span",{class:le(["col2 change",S(b.priceDifference)])},rt(b.priceDifference),3),w("span",{class:le(["col2 changePercent",S(b.priceChangeRatio)])},rt(b.priceChangeRatio)+"% ",3)],8,ey))),128))],4)])]))}}),ly=Te(sy,[["__scopeId","data-v-8103f563"]]),cy={class:"list-container"},uy=Ne({__name:"ListContainer",setup(t){const r=B("market"),e=B(),a=B();function i(o){r.value=o}function n(o){r.value==="market"&&e.value?e.value.activeFilter=o:r.value==="optional"&&a.value&&(a.value.activeFilter=o)}return(o,s)=>(H(),j("div",cy,[wt(zm,{onViewChange:i,onFilterChange:n}),r.value==="market"?(H(),Qa(Qm,{key:0,ref_key:"marketListRef",ref:e},null,512)):(H(),Qa(ly,{key:1,ref_key:"stockListRef",ref:a},null,512))]))}}),dy=Te(uy,[["__scopeId","data-v-e0d3373b"]]),hy={class:"aiprediction-container"},vy={class:"aiprediction-wrapper"},fy=["onClick"],py={key:0},gy={key:1},my={key:0,class:"model-options"},yy=["onClick"],_y=Ne({__name:"AiPrediction",setup(t){const r=Bi(),e=Ni(),a=["SVM","RandomForest","LSTM","MLP","Transformer","XGBoost"],i={LSTM:[{value:"LSTM_SET_1",label:"2个layer，每个layer有50个units"},{value:"LSTM_SET_2",label:"2个layer，每个layer有100个units"},{value:"LSTM_SET_3",label:"2个layer，每个layer有25个units"}],MLP:[{value:"MLP_SET_1",label:"1个hidden layer，64个神经元"},{value:"MLP_SET_2",label:"1个hidden layer，64个神经元"},{value:"MLP_SET_3",label:"1个hidden layer，64个神经元"}],RandomForest:[{value:"RF_SET_1",label:"15个estimators，max_depth无限制"},{value:"RF_SET_2",label:"25个estimators，max_depth为5"},{value:"RF_SET_3",label:"10个estimators，max_depth为3"}],SVM:[{value:"SVM_SET_1",label:"RBF核，C=1.0，epsilon=0.1"},{value:"SVM_SET_2",label:"RBF核，C=10.0，epsilon=0.01"},{value:"SVM_SET_3",label:"RBF核，C=0.1，epsilon=0.5"}],Transformer:[{value:"TRANSFORMER_SET_1",label:"Head_size为64，单层结构，dropout是10%"},{value:"TRANSFORMER_SET_2",label:"Head_size为128，layer为2，dropout为20%"},{value:"TRANSFORMER_SET_3",label:"Head_size为32，layer为1，dropout为5%"}],XGBoost:[{value:"XGBOOST_SET_1",label:"n_estimators为5，最大深度为1，学习率为0.1，行采样比例为0.5，列采样比例为0.5"},{value:"XGBOOST_SET_2",label:"n_estimators为10，最大深度为2，学习率为0.15，行采样比例为0.5，列采样比例为0.6"},{value:"XGBOOST_SET_3",label:"n_estimators为15，最大深度为3，学习率为0.2，行采样比例为0.6，列采样比例为0.5"}]},n=B(0),o=B(1),s=B(null);function l(v){n.value=v}function c(v){o.value=v}Re(n,v=>{if(!s.value)return;const p=s.value.querySelectorAll(".card")[v],g=s.value,m=g.getBoundingClientRect(),_=p.getBoundingClientRect(),x=_.left-m.left-m.width/2+_.width/2;g.scrollBy({left:x,behavior:"smooth"})}),Ge(()=>{l(2)});function u(v){const f=Math.abs(v-n.value);return{margin:"-12px",transform:`scale(${1.3-f*.14})`,zIndex:6-f}}function d(){e.isShowAIPredict=!0;const v=r.getTick();if(v){v.ifDrawAIPredict=!0;const f=i[a[n.value]][o.value].value;v.modelSetting=f,v.exchange=e.exchange,v.StartUpdateAIPredictData(!0)}}function h(){e.isShowAIPredict=!1;const v=r.getTick();v&&(v.ifDrawAIPredict=!1,v.ClearChart(1),v.DrawChart(2))}return(v,f)=>(H(),j("div",hy,[w("div",vy,[f[5]||(f[5]=w("div",{class:"title"}," Ai预测 ",-1)),w("div",{class:"card-container",ref_key:"container",ref:s},[f[1]||(f[1]=w("div",{class:"spacer"},null,-1)),(H(),j(ge,null,Ae(a,(p,g)=>w("div",{class:le(["card",{"selected-card":n.value===g}]),key:g,style:Ce(u(g)),onClick:m=>l(g)},[p==="RandomForest"?(H(),j("span",py,f[0]||(f[0]=[pr("Random"),w("br",null,null,-1),pr("Forest")]))):(H(),j("span",gy,rt(p),1))],14,fy)),64)),f[2]||(f[2]=w("div",{class:"spacer"},null,-1))],512),f[6]||(f[6]=w("div",{class:"sub-title"}," 预测模型 ",-1)),i[a[n.value]]?(H(),j("div",my,[(H(!0),j(ge,null,Ae(i[a[n.value]],(p,g)=>(H(),j("div",{key:p.value,class:le(["option-item",{"selected-option":o.value===g}]),onClick:m=>c(g)},rt(p.label),11,yy))),128))])):ne("",!0),pe(e).isShowAIPredict?ne("",!0):(H(),j("div",{key:1,class:"start-btn",onClick:d},f[3]||(f[3]=[w("div",null,[w("svg",{width:"16px",height:"15px",viewBox:"0 0 16 15",xmlns:"http://www.w3.org/2000/svg"},[w("g",{fill:"#2767EE","fill-rule":"nonzero"},[w("path",{d:"M6.212,12 L0,12 L0,0 L15,0 L15,6.258 C14.691518,6.0047443 14.3561779,5.78610256 14,5.606 L14,1 L1,1 L1,9 L6.212,9 C6.1189172,9.32684284 6.05631633,9.66160683 6.025,10 L1,10 L1,11 L6.025,11 C6.056,11.344 6.12,11.677 6.212,12 Z M4,13 L4,14 L7.257,14 C7.0040793,13.6912725 6.78546229,13.3559704 6.605,13 L4,13 Z M7.685,13.438 L8.392,14.145 L9.806,12.731 L9.099,12.024 L7.685,13.438 Z M13.341,9.196 L14.755,7.782 L14.048,7.075 L12.634,8.489 L13.341,9.196 Z M7.685,7.782 L9.099,9.196 L9.806,8.489 L8.392,7.075 L7.685,7.782 L7.685,7.782 Z M12.634,12.731 L14.048,14.145 L14.755,13.438 L13.341,12.024 L12.634,12.731 Z M11,15 L12,15 L12,13 L11,13 L11,15 Z M11,8 L12,8 L12,6 L11,6 L11,8 Z M9,10 L7,10 L7,11 L9,11 L9,10 Z M14,10 L14,11 L16,11 L16,10 L14,10 Z"})])])],-1),w("div",null,"启动",-1)]))),pe(e).isShowAIPredict?(H(),j("div",{key:2,class:"stop-btn",onClick:h},f[4]||(f[4]=[w("div",{class:"rotate-svg"},[w("svg",{t:"1747576575240",class:"icon",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"1505",width:"16",height:"16"},[w("path",{d:"M512 0a51.2 51.2 0 0 1 51.2 51.2v153.6a51.2 51.2 0 0 1-102.4 0V51.2a51.2 51.2 0 0 1 51.2-51.2z m0 768a51.2 51.2 0 0 1 51.2 51.2v153.6a51.2 51.2 0 0 1-102.4 0v-153.6a51.2 51.2 0 0 1 51.2-51.2z m443.392-512a51.2 51.2 0 0 1-18.7392 69.9392l-133.0176 76.8a51.2 51.2 0 1 1-51.2-88.6784l133.0176-76.8A51.2 51.2 0 0 1 955.392 256zM290.304 640a51.2 51.2 0 0 1-18.7392 69.9392l-133.0176 76.8a51.2 51.2 0 1 1-51.2-88.6784l133.0176-76.8a51.2 51.2 0 0 1 69.9392 18.7392zM955.392 768a51.2 51.2 0 0 1-69.9392 18.7392l-133.0176-76.8a51.2 51.2 0 0 1 51.2-88.6784l133.0176 76.8A51.2 51.2 0 0 1 955.392 768zM290.304 384a51.2 51.2 0 0 1-69.9392 18.7392l-133.0176-76.8a51.2 51.2 0 1 1 51.2-88.6784l133.0176 76.8A51.2 51.2 0 0 1 290.304 384z",fill:"#ffffff","p-id":"1506"})])],-1),w("div",null,"停止",-1)]))):ne("",!0)])]))}}),Cy=Te(_y,[["__scopeId","data-v-aecc3abd"]]),by={class:"toppanel-container"},xy={class:"toppanel-wrapper"},wy={key:0,class:"item"},Ly={key:1,class:"item"},Sy=Ne({__name:"index",props:{currentCustomPanelIndex:{},currentCustomPanelIndexModifiers:{}},emits:["update:currentCustomPanelIndex"],setup(t){const r=Ds(t,"currentCustomPanelIndex");return(e,a)=>(H(),j("div",by,[w("div",xy,[r.value==1?(H(),j("div",wy,[wt(dy)])):ne("",!0),r.value==2?(H(),j("div",Ly,[wt(Cy)])):ne("",!0)])]))}}),ky=Te(Sy,[["__scopeId","data-v-9f840650"]]),My={class:"header"},Ty={class:"date-wrapper"},Ey={style:{width:"0",height:"0",overflow:"hidden",position:"absolute","z-index":"99999999"}},Iy={key:0,class:"es"},Ay=["onClick"],Dy={__name:"table-right",setup(t){const r=cr(),e=vi("child_dailyPositionHeight"),a=B([]),i=B([]),n=B(!1),o=B(1),s=B(10),l=B(0),c=B(1),u=B(new Set),d=k=>k==null?"¥0.00":`¥${(k/100).toFixed(2)}`;Re(()=>[r.timeRange,r.startDate,r.endDate],async([k,C,D])=>{C&&D&&(f.value=D,await h())},{immediate:!0}),Re(()=>r.rightTableData,async k=>{k&&k.node_output&&await h()},{immediate:!0});const h=async()=>{var k;if(console.log("Fetching position data with:",{rightTableData:r.rightTableData,nodeOutput:(k=r.rightTableData)==null?void 0:k.node_output,selectedDate:f.value,isInitialized:n.value}),!r.rightTableData||!r.rightTableData.node_output){console.log("Missing required data for fetch");return}try{await r.fetchBacktestPosition(r.rightTableData.node_output,1,1e4,""),r.backtestPosition&&r.backtestPosition.data&&(i.value=r.backtestPosition.data.items||[],u.value=new Set(i.value.map(C=>typeof C.gmt_create=="string"&&C.gmt_create.length===8?`${C.gmt_create.slice(0,4)}-${C.gmt_create.slice(4,6)}-${C.gmt_create.slice(6,8)}`:m(new Date(C.gmt_create)))),(!f.value||!n.value)&&u.value.size>0&&(f.value=Array.from(u.value)[0],n.value=!0),v())}catch(C){console.error("获取持仓数据失败",C)}},v=()=>{if(console.log("Filtering data with date:",f.value),console.log("Available data:",i.value),!f.value||!i.value){a.value=[],l.value=0;return}const k=i.value.filter(E=>{const I=typeof E.gmt_create=="string"&&E.gmt_create.length===8?`${E.gmt_create.slice(0,4)}-${E.gmt_create.slice(4,6)}-${E.gmt_create.slice(6,8)}`:m(new Date(E.gmt_create)),T=m(new Date(f.value));return console.log("Comparing dates:",{itemDate:I,selectedDateStr:T,original:E.gmt_create}),I===T});console.log("Filtered data:",k),l.value=k.length,c.value=Math.ceil(l.value/s.value);const C=(o.value-1)*s.value,D=C+s.value;a.value=k.slice(C,D),console.log("Final position data:",a.value)},f=B(""),p=B(null);Re(f,()=>{o.value=1,v()});const g=()=>{var k;(k=p.value)==null||k.focus()},m=k=>{if(!k)return"";const C=new Date(k),D=C.getFullYear(),E=String(C.getMonth()+1).padStart(2,"0"),I=String(C.getDate()).padStart(2,"0");return`${D}-${E}-${I}`},_=k=>{const C=m(k);return!u.value.has(C)},x=k=>{if(!k)return"";const C=k.substring(0,4),D=k.substring(4,6),E=k.substring(6,8);return`${C}-${D}-${E}`},S=async k=>{const C=k.indexOf("."),D=k.substring(0,C);await r.fetchBacktestTrade(r.rightTableData.node_output,1,1e4);const E=r.backtestTrade.data.items,I=E.filter(b=>b.business===0),T=E.filter(b=>b.business===1),L={buyFigure:I.map(b=>({timestamp:new Date(x(b.gmt_create)).getTime(),value:b.price})),sellFigure:T.map(b=>({timestamp:new Date(x(b.gmt_create)).getTime(),value:b.price}))};console.log("overlay",L),r.klineCharthandleContractChange(D,L)},y=()=>{const k=document.querySelectorAll('[id^="el-popper-container-"]');console.log("找到popper元素数量:",k.length),k.forEach(C=>{C.style.position="relative",C.style.zIndex="99999999999999999",console.log(`已设置元素 ${C.id} 的样式`)})};return Ge(async()=>{console.log("Component mounted"),y(),await h()}),(k,C)=>{const D=Zr("el-icon"),E=Zr("el-date-picker");Zr("el-pagination");const I=Zr("el-table-column"),T=Zr("el-table");return H(),j("div",{class:"table-right",style:Ce({maxHeight:pe(e)+"vh"})},[w("div",My,[w("div",Ty,[w("span",{class:"date-text",onClick:g},"每日持仓"),f.value?(H(),j("span",{key:0,class:"selected-date",onClick:g},"（"+rt(m(f.value))+"）",1)):ne("",!0),wt(D,{class:"arrow-icon",onClick:g},{default:dr(()=>[wt(pe(Ps))]),_:1}),w("div",Ey,[wt(E,{modelValue:f.value,"onUpdate:modelValue":C[0]||(C[0]=L=>f.value=L),type:"date",placeholder:"请选择日期",size:"small",clearable:!1,class:"table-right-date-picker",ref_key:"datePicker",ref:p,"disabled-date":_},null,8,["modelValue"])])]),a.value.length>0?(H(),j("div",Iy,"已持仓"+rt(l.value),1)):ne("",!0),ne("",!0)]),wt(T,{data:a.value,border:"",style:{width:"100%"},"max-height":`calc(${pe(e)}vh - 20px)`,class:"custom-table-r"},{default:dr(()=>[wt(I,{fixed:"",prop:"contract_name",label:"标的"}),wt(I,{width:"100",prop:"contract_code",label:"代码"},{default:dr(L=>[w("span",{class:"contract-code",onClick:b=>S(L.row.contract_code)},rt(L.row.contract_code),9,Ay)]),_:1}),wt(I,{label:"最新价"},{default:dr(L=>[w("span",null,rt(d(L.row.last_price)),1)]),_:1}),wt(I,{label:"仓位"},{default:dr(L=>[w("span",{class:le(L.row.direction===0?"position-long":"position-short")},rt(L.row.direction===0?"多":"空")+" "+rt(Math.abs(L.row.position)),3)]),_:1}),wt(I,{label:"开仓均价"},{default:dr(L=>[w("span",null,rt(d(L.row.price)),1)]),_:1}),wt(I,{fixed:"right",label:"累积盈亏"},{default:dr(L=>[w("span",{class:le(["profit",L.row.accumulate_profit>0?"profit-positive":"profit-negative"])},rt(d(L.row.accumulate_profit)),3)]),_:1})]),_:1},8,["data","max-height"])],4)}}},Py=Te(Dy,[["__scopeId","data-v-dc4d211b"]]),Fy={class:"foot-head"},Ry={class:"menu"},By=["onClick"],Ny=Ne({__name:"foot-head",props:{activeTab:{},tabs:{}},emits:["tabChange"],setup(t,{emit:r}){const e=r,a=i=>{e("tabChange",i)};return(i,n)=>(H(),j("div",Fy,[ne("",!0),w("div",Ry,[(H(!0),j(ge,null,Ae(i.tabs,(o,s)=>(H(),j("div",{key:o.key,class:le(["menu-item",{active:i.activeTab===o.key}]),onClick:l=>a(o.key)},rt(o.label),11,By))),128))]),ne("",!0)]))}}),Oy=Te(Ny,[["__scopeId","data-v-e19dc403"]]),Vy=Oi(),ca="http://api.pandaai.online",yr=ta();class zy{constructor(r){Yr(this,"_apiKey");Yr(this,"_subscriptions",new Map);Yr(this,"_currentSubscription",null);Yr(this,"_debounceTimer",null);this._apiKey=r}async searchSymbols(r){try{console.log("searchSymbols.kDataStore.type",yr.type),console.log("searchSymbols.kDataStore.quotationType",yr.quotationType);const e=await fetch(`${ca}/historyData/querySymobl?type=${yr.type}`);if(!e.ok)return console.error(`API 请求失败: ${e.status} ${e.statusText}`),[];const a=await e.json();if(console.log("API 返回结果:",a),a.code!=="200"||!a.data)return console.error("API 返回错误:",a.message),[];const i=Object.values(a.data);console.log(`获取到 ${i.length} 个合约`);let n=i;if(r&&r.trim()){const o=r.toLowerCase();n=i.filter(s=>s.symbol.includes(o)||s.tradingCode.toLowerCase().includes(o)),console.log(`搜索 "${r}" 匹配到 ${n.length} 个合约`)}return n.map(o=>({ticker:o.orderBookID||"",name:o.tradingCode||o.code||"",shortName:o.symbol||"",exchange:o.exchange||"",market:"futures",pricePrecision:2,volumePrecision:0,priceCurrency:"CNY",type:"future"}))}catch(e){return console.error("搜索代码时发生错误:",e),[]}}async getHistoryKLineData(r,e,a,i){console.log("getHistoryKLineData",r,e,a,i),Vy.exchange=r.exchange,a=a-5*24*60*60*1e3,console.log("getHistoryKLineData.kDataStore.type",yr.type),console.log("getHistoryKLineData.kDataStore.quotationType",yr.quotationType);const o=await(await fetch(`${ca}/historyData/queryHistoryData?startDate=${a}&endDate=${i}&symbol=${r.ticker.toUpperCase()}&period=${e.text}&quotationType=${yr.quotationType}`)).json();return o.data=o.data.reverse(),await(o.data||[]).map(s=>({timestamp:parseInt(s.date),open:s.open,high:s.high,low:s.low,close:s.close,volume:s.volume,turnover:s.volume}))}async subscribe(r,e,a){const i=`${r.ticker}_${e.text}`;console.log(`订阅数据: ${i}`),this._debounceTimer&&(clearTimeout(this._debounceTimer),this._debounceTimer=null),this._debounceTimer=setTimeout(async()=>{try{if(this._subscriptions.has(i)){console.log(`已存在订阅 ${i}，先取消再重新订阅`);const o=this._subscriptions.get(i);o&&clearInterval(o),this._subscriptions.delete(i)}this._subscriptions.forEach((o,s)=>{s!==i&&(console.log(`取消其他订阅: ${s}`),clearInterval(o))}),this._subscriptions.clear(),this._currentSubscription=i;const n=setInterval(async()=>{if(this._currentSubscription!==i){console.log(`订阅 ${i} 已过期，停止数据获取`);return}try{const o={symbolCode:r.ticker||"SA509",cycle:1,baseBarCount:1};let s=localStorage.getItem("token");s="***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";const l=await fetch(`${ca}/instrument/queryLiveData?quotation=${r.ticker}&quotationType=${yr.quotationType}&period=${e.text}`,{method:"GET",headers:{"Content-Type":"application/json",Authorization:`${s}`}});if(!l.ok)throw new Error(`API请求失败: ${l.status} ${l.statusText}`);const c=await l.json();if(c.code!=="200")throw new Error(`API返回错误: ${c.code} - ${c.message}`);if(Object.keys(c.data).length===0)return;const u=Object.keys(c.data)[0],d=c.data[u],h={timestamp:this.parseTickTime(d.ticktime),open:d.open,high:d.high,low:d.low,close:d.close,volume:d.volume};this._currentSubscription===i&&a(h)}catch(o){console.error(`订阅 ${i} 数据获取失败:`,o)}},1e3);this._subscriptions.set(i,n),console.log(`✅ 订阅创建成功: ${i}`)}catch(n){console.error(`创建订阅失败: ${i}`,n)}},300)}unsubscribe(r,e){const a=`${r.ticker}_${e.text}`;if(console.log(`取消订阅: ${a}`),this._debounceTimer&&(clearTimeout(this._debounceTimer),this._debounceTimer=null),this._subscriptions.has(a)){const i=this._subscriptions.get(a);i&&(clearInterval(i),console.log(`✅ 已取消订阅: ${a}`)),this._subscriptions.delete(a)}this._currentSubscription===a&&(this._currentSubscription=null)}unsubscribeAll(){console.log("取消所有订阅"),this._debounceTimer&&(clearTimeout(this._debounceTimer),this._debounceTimer=null),this._subscriptions.forEach((r,e)=>{clearInterval(r),console.log(`✅ 已取消订阅: ${e}`)}),this._subscriptions.clear(),this._currentSubscription=null}async getOrderFlowData(r){try{console.log(`📡 调用PandaAI订单流API: ${r.name}`);debugger;const e={symbolCode:r.name||"SA509",cycle:1,baseBarCount:1e4};let a=localStorage.getItem("token");a="***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";const i=await fetch(`${ca}/orderFlow/getHistoryMarketData`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`${a}`},body:JSON.stringify(e)});if(!i.ok)throw new Error(`API请求失败: ${i.status} ${i.statusText}`);const n=await i.json();if(n.code!=="200")throw new Error(`API返回错误: ${n.code} - ${n.message}`);const o=this.convertPandaApiDataToOrderFlow(n.data,r);return console.log(`✅ 转换完成，共${o.length}条订单流数据`),o}catch(e){return console.error("❌ 获取订单流数据失败:",e),console.warn("🔄 使用模拟数据作为降级方案"),this.generateMockOrderFlowData(r)}}convertPandaApiDataToOrderFlow(r,e){const a=Object.keys(r)[0],i=r[a];if(i.baseBar=[r[a]],!i)return console.warn("⚠️ API数据格式不正确，使用模拟数据"),this.generateMockOrderFlowData(e);const n=[];for(const o of r.baseBar)try{const s=this.parseTickTime(o.ticktime),l=[],c=o.price||[],u=o.Ask||[],d=o.Bid||[];for(let v=0;v<c.length;v++){const f=c[v],p=u[v]||0,g=d[v]||0;f&&(p>0||g>0)&&l.push({price:Number(f.toFixed(2)),buyVolume:g,sellVolume:p,totalVolume:g+p})}l.sort((v,f)=>v.price-f.price);const h={timestamp:s,priceLevels:l,tickSize:.5,open:o.open,high:o.high,low:o.low,close:o.close,volume:o.volume,delta:o.delta};n.push(h)}catch(s){console.error("❌ 转换单个Bar数据失败:",s,o)}return n}parseTickTime(r){try{if(!r)return Date.now();const e=new Date(r);return isNaN(e.getTime())?(console.warn(`⚠️ 无法解析时间格式: ${r}，使用当前时间`),Date.now()):e.getTime()}catch(e){return console.error("❌ 时间解析失败:",e,r),Date.now()}}generateMockOrderFlowData(r){console.log(`🔄 生成 ${r.ticker} 的模拟订单流数据`);const e=[],a=Date.now()-5*24*60*60*1e3;for(let i=100;i>=0;i--){const n=a-i*60*1e3,o=100+Math.random()*20,s=[];for(let l=0;l<10;l++){const c=o+(l-5)*.1,u=Math.floor(Math.random()*1e3),d=Math.floor(Math.random()*1e3);s.push({price:Number(c.toFixed(2)),buyVolume:u,sellVolume:d,totalVolume:u+d})}e.push({timestamp:n,priceLevels:s,tickSize:.01})}return console.log("mockData",e),e}}const $y={class:"aiprediction-container"},Wy={class:"aiprediction-wrapper"},Yy=["onClick"],Zy={key:0},Hy={key:1},jy={key:0,class:"model-options"},Xy=["onClick"],Gy=Ne({__name:"Ai",emits:["clicks"],setup(t,{emit:r}){const e=Oi(),a=["SVM","RandomForest","LSTM","MLP","Transformer","XGBoost"],i={LSTM:[{value:"LSTM_SET_1",label:"2个layer，每个layer有50个units"},{value:"LSTM_SET_2",label:"2个layer，每个layer有100个units"},{value:"LSTM_SET_3",label:"2个layer，每个layer有25个units"}],MLP:[{value:"MLP_SET_1",label:"1个hidden layer，64个神经元"},{value:"MLP_SET_2",label:"1个hidden layer，64个神经元"},{value:"MLP_SET_3",label:"1个hidden layer，64个神经元"}],RandomForest:[{value:"RF_SET_1",label:"15个estimators，max_depth无限制"},{value:"RF_SET_2",label:"25个estimators，max_depth为5"},{value:"RF_SET_3",label:"10个estimators，max_depth为3"}],SVM:[{value:"SVM_SET_1",label:"RBF核，C=1.0，epsilon=0.1"},{value:"SVM_SET_2",label:"RBF核，C=10.0，epsilon=0.01"},{value:"SVM_SET_3",label:"RBF核，C=0.1，epsilon=0.5"}],Transformer:[{value:"TRANSFORMER_SET_1",label:"Head_size为64，单层结构，dropout是10%"},{value:"TRANSFORMER_SET_2",label:"Head_size为128，layer为2，dropout为20%"},{value:"TRANSFORMER_SET_3",label:"Head_size为32，layer为1，dropout为5%"}],XGBoost:[{value:"XGBOOST_SET_1",label:"n_estimators为5，最大深度为1，学习率为0.1，行采样比例为0.5，列采样比例为0.5"},{value:"XGBOOST_SET_2",label:"n_estimators为10，最大深度为2，学习率为0.15，行采样比例为0.5，列采样比例为0.6"},{value:"XGBOOST_SET_3",label:"n_estimators为15，最大深度为3，学习率为0.2，行采样比例为0.6，列采样比例为0.5"}]},n=B(0),o=B(1),s=B(null);function l(f){n.value=f}function c(f){o.value=f}const u=f=>{if(!s.value)return;const g=s.value.querySelectorAll(".card")[f],m=s.value,_=m.getBoundingClientRect(),x=g.getBoundingClientRect(),S=x.left-_.left-_.width/2+x.width/2;m.scrollBy({left:S,behavior:"smooth"})};Re(n,f=>{u(f)}),Ge(()=>{u(n.value)});function d(f){const p=Math.abs(f-n.value);return{margin:"-12px",transform:`scale(${1.3-p*.14})`,zIndex:6-p}}function h(){e.isShowAIPredict=!0,e.StartUpdateAIPredictData(!0,i[a[n.value]][o.value].value)}function v(){e.isShowAIPredict=!1,e.StartUpdateAIPredictData(!1)}return(f,p)=>(H(),j("div",$y,[w("div",Wy,[p[5]||(p[5]=w("div",{class:"title"}," Ai预测 ",-1)),w("div",{class:"card-container",ref_key:"container",ref:s},[p[1]||(p[1]=w("div",{class:"spacer"},null,-1)),(H(),j(ge,null,Ae(a,(g,m)=>w("div",{class:le(["card",{"selected-card":n.value===m}]),key:m,style:Ce(d(m)),onClick:_=>l(m)},[g==="RandomForest"?(H(),j("span",Zy,p[0]||(p[0]=[pr("Random"),w("br",null,null,-1),pr("Forest")]))):(H(),j("span",Hy,rt(g),1))],14,Yy)),64)),p[2]||(p[2]=w("div",{class:"spacer"},null,-1))],512),p[6]||(p[6]=w("div",{class:"sub-title"}," 预测模型 ",-1)),i[a[n.value]]?(H(),j("div",jy,[(H(!0),j(ge,null,Ae(i[a[n.value]],(g,m)=>(H(),j("div",{key:g.value,class:le(["option-item",{"selected-option":o.value===m}]),onClick:_=>c(m)},rt(g.label),11,Xy))),128))])):ne("",!0),pe(e).isShowAIPredict?ne("",!0):(H(),j("div",{key:1,class:"start-btn",onClick:h},p[3]||(p[3]=[w("div",null,[w("svg",{width:"16px",height:"15px",viewBox:"0 0 16 15",xmlns:"http://www.w3.org/2000/svg"},[w("g",{fill:"#2767EE","fill-rule":"nonzero"},[w("path",{d:"M6.212,12 L0,12 L0,0 L15,0 L15,6.258 C14.691518,6.0047443 14.3561779,5.78610256 14,5.606 L14,1 L1,1 L1,9 L6.212,9 C6.1189172,9.32684284 6.05631633,9.66160683 6.025,10 L1,10 L1,11 L6.025,11 C6.056,11.344 6.12,11.677 6.212,12 Z M4,13 L4,14 L7.257,14 C7.0040793,13.6912725 6.78546229,13.3559704 6.605,13 L4,13 Z M7.685,13.438 L8.392,14.145 L9.806,12.731 L9.099,12.024 L7.685,13.438 Z M13.341,9.196 L14.755,7.782 L14.048,7.075 L12.634,8.489 L13.341,9.196 Z M7.685,7.782 L9.099,9.196 L9.806,8.489 L8.392,7.075 L7.685,7.782 L7.685,7.782 Z M12.634,12.731 L14.048,14.145 L14.755,13.438 L13.341,12.024 L12.634,12.731 Z M11,15 L12,15 L12,13 L11,13 L11,15 Z M11,8 L12,8 L12,6 L11,6 L11,8 Z M9,10 L7,10 L7,11 L9,11 L9,10 Z M14,10 L14,11 L16,11 L16,10 L14,10 Z"})])])],-1),w("div",null,"启动",-1)]))),pe(e).isShowAIPredict?(H(),j("div",{key:2,class:"stop-btn",onClick:v},p[4]||(p[4]=[w("div",{class:"rotate-svg"},[w("svg",{t:"1747576575240",class:"icon",viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"1505",width:"16",height:"16"},[w("path",{d:"M512 0a51.2 51.2 0 0 1 51.2 51.2v153.6a51.2 51.2 0 0 1-102.4 0V51.2a51.2 51.2 0 0 1 51.2-51.2z m0 768a51.2 51.2 0 0 1 51.2 51.2v153.6a51.2 51.2 0 0 1-102.4 0v-153.6a51.2 51.2 0 0 1 51.2-51.2z m443.392-512a51.2 51.2 0 0 1-18.7392 69.9392l-133.0176 76.8a51.2 51.2 0 1 1-51.2-88.6784l133.0176-76.8A51.2 51.2 0 0 1 955.392 256zM290.304 640a51.2 51.2 0 0 1-18.7392 69.9392l-133.0176 76.8a51.2 51.2 0 1 1-51.2-88.6784l133.0176-76.8a51.2 51.2 0 0 1 69.9392 18.7392zM955.392 768a51.2 51.2 0 0 1-69.9392 18.7392l-133.0176-76.8a51.2 51.2 0 0 1 51.2-88.6784l133.0176 76.8A51.2 51.2 0 0 1 955.392 768zM290.304 384a51.2 51.2 0 0 1-69.9392 18.7392l-133.0176-76.8a51.2 51.2 0 1 1 51.2-88.6784l133.0176 76.8A51.2 51.2 0 0 1 290.304 384z",fill:"#ffffff","p-id":"1506"})])],-1),w("div",null,"停止",-1)]))):ne("",!0)])]))}}),Ky=Te(Gy,[["__scopeId","data-v-8b322147"]]),Uy={key:0,class:"table-container"},Qy={class:"table-wrapper"},qy={class:"custom-table"},Jy=["title"],t_={class:"name-cell table-cell"},e_=["onBlur","onKeyup"],r_=["title"],a_=["onClick"],i_={class:"table-cell"},n_={class:"table-cell"},o_={style:{"text-align":"right",width:"190px"}},s_=["onClick"],l_=["onClick"],c_={key:0,class:"loading-spinner"},u_={class:"pagination"},d_={class:"page-nav"},h_=["disabled"],v_={style:{transform:"rotate(180deg)"},viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"32302",width:"16",height:"16"},f_={key:0,class:"dots"},p_=["onClick"],g_=["disabled"],m_={style:{transform:"rotate(0deg)"},viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"32302",width:"16",height:"16"},y_={key:1,class:"table-container"},__={key:2,class:"table-container"},C_={__name:"tableb",props:{tableData:{type:Array,default:()=>[]}},emits:["apply"],setup(t,{expose:r,emit:e}){const a=Qn(),i=e,n=cr(),o=qn(),s=b=>{b?o.push(`/editor?workflow_id=${b}`):o.push("/editor")},l=B(null),c=B(""),u=B(null),d=t,h=Ze(()=>!d.tableData||!d.tableData.data||!d.tableData.data.workflows?[]:d.tableData.data.workflows.map(b=>({id:b._id,name:b.name,createTime:Wi(b.create_at),modifyTime:Wi(b.update_at),item:b}))),v=B([]);Re(h,b=>{v.value=b},{immediate:!0});const f=B(1),p=B(10),g=Ze(()=>v.value.length),m=Ze(()=>Math.ceil(g.value/p.value)),_=Ze(()=>{const b=(f.value-1)*p.value,M=b+p.value;return v.value.slice(b,M)}),x=Ze(()=>{const b=[],M=m.value,A=f.value;if(M<=5){for(let P=1;P<=M;P++)b.push(P);return b}if(b.push(1),A>3&&b.push("..."),A===1||A===2)b.includes(2)||b.push(2),b.includes(3)||b.push(3);else if(A===M||A===M-1)for(let P=M-2;P<M;P++)b.includes(P)||b.push(P);else b.push(A-1,A,A+1);return A<M-2&&b.push("..."),b.includes(M)||b.push(M),[...new Set(b)]}),S=b=>{b!=="..."&&(f.value=b)},y=()=>{f.value>1&&f.value--},k=()=>{f.value<m.value&&f.value++},C=B({}),D=(b,M)=>{C.value[M]=!0,i("apply",{workflow_id:b._id,title:b.name,feature_tag:"factor",locator:"task_id"}),setTimeout(()=>{C.value[M]=!1},5e3),console.log("应用项目：",b)},E=b=>{b?C.value[b]=!1:Object.keys(C.value).forEach(M=>{C.value[M]=!1})};Re(h,()=>{E()},{deep:!0}),r({resetLoadingState:E});const I=async b=>{var M;l.value=b.id,c.value=b.name,await ar(),(M=u.value)==null||M.focus()},T=async b=>{if(!c.value.trim()){a.error("名称不能为空"),c.value=b.name;return}if(c.value===b.name){l.value=null;return}try{await il(b.id,c.value);const M=v.value.findIndex(A=>A.id===b.id);M!==-1&&(v.value[M].name=c.value),l.value=null,a.success("名称修改成功")}catch(M){console.error("重命名失败:",M),c.value=b.name,a.error("名称修改失败，请稍后重试")}},L=()=>{l.value=null,c.value=""};return(b,M)=>v.value.length>0?(H(),j("div",Uy,[w("div",Qy,[w("table",qy,[M[3]||(M[3]=w("thead",null,[w("tr",null,[w("th",null,"ID"),w("th",null,"名称"),w("th",null,"创建时间"),w("th",null,"修改时间"),w("th")])],-1)),w("tbody",null,[(H(!0),j(ge,null,Ae(_.value,(A,P)=>(H(),j("tr",{key:A.id},[w("td",{class:"table-cell",title:A.id},rt(A.id),9,Jy),w("td",t_,[l.value===A.id?di((H(),j("input",{key:0,"onUpdate:modelValue":M[0]||(M[0]=N=>c.value=N),type:"text",class:"edit-input",onBlur:N=>T(A),onKeyup:[$i(N=>T(A),["enter"]),$i(L,["esc"])],ref_for:!0,ref_key:"editInput",ref:u},null,40,e_)),[[Fs,c.value]]):(H(),j(ge,{key:1},[w("span",{class:"table-cell",title:A.name},rt(A.name),9,r_),A.owner!=="*"?(H(),j("span",{key:0,class:"copy-icon",onClick:N=>I(A)},M[2]||(M[2]=[Rs('<svg width="12px" height="12px" viewBox="0 0 12 12" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" data-v-56353224><title data-v-56353224>修改名称</title><g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" data-v-56353224><g id="超级图表-工作流" transform="translate(-364, -729)" fill="#2767EE" fill-rule="nonzero" data-v-56353224><g id="编辑" transform="translate(364, 729)" data-v-56353224><path d="M5.98554217,5.31036145 L10.9359036,0.361445783 C11.1310843,0.16626506 11.4477108,0.16626506 11.6428916,0.361445783 C11.8380723,0.556626506 11.8380723,0.873253012 11.6428916,1.06843373 L6.69253012,6.0173494 C6.4973494,6.21253012 6.18072289,6.21253012 5.98554217,6.0173494 C5.79036145,5.82216867 5.79036145,5.50554217 5.98554217,5.31036145 Z M10.9995181,5.83373494 C10.9995181,5.55759036 11.2236145,5.33349398 11.499759,5.33349398 C11.7759036,5.33349398 12,5.55759036 12,5.83373494 L12,9.50024096 C12,10.8809639 10.8809639,12 9.50024096,12 L2.49975904,12 C1.11903614,12 0,10.8809639 0,9.50024096 L0,2.49975904 C0,1.11903614 1.11903614,0 2.49975904,0 L6.16626506,0 C6.44240964,0 6.66650602,0.224096386 6.66650602,0.500240964 C6.66650602,0.776385542 6.44240964,1.00048193 6.16626506,1.00048193 L2.49975904,1.00048193 C1.6713253,1.00048193 1.00048193,1.67277108 1.00048193,2.49975904 L1.00048193,9.50024096 C1.00048193,10.3286747 1.67277108,11.0009639 2.49975904,11.0009639 L9.50024096,11.0009639 C10.3286747,11.0009639 11.0009639,10.3286747 11.0009639,9.50024096 L11.0009639,5.83373494 L10.9995181,5.83373494 Z" id="形状" data-v-56353224></path></g></g></g></svg>',1)]),8,a_)):ne("",!0)],64))]),w("td",i_,rt(A.createTime),1),w("td",n_,rt(A.modifyTime),1),w("td",o_,[w("button",{class:le(["apply-btn"]),onClick:N=>s(A.id),style:{"margin-right":"10px"}}," 编辑 ",8,s_),w("button",{style:{width:"100px"},class:le(["apply-btn",{loading:C.value[A.id]}]),onClick:N=>D(A.item,A.id)},[pr(rt(C.value[A.id]?"正在应用":"应用")+" ",1),C.value[A.id]?(H(),j("span",c_)):ne("",!0)],10,l_)])]))),128))])])]),w("div",u_,[w("div",d_,[w("button",{disabled:f.value===1,onClick:y},[(H(),j("svg",v_,M[4]||(M[4]=[w("path",{"data-v-da8ede72":"",d:"M722.438 510.815c-0.246-0.246-0.513-0.454-0.766-0.69l-299.893-299.892c-14.231-14.24-37.291-14.24-51.529 0-14.241 14.231-14.241 37.291 0 51.529l274.851 274.859-274.849 274.851c-14.241 14.237-14.241 37.301 0 51.532 7.116 7.119 16.436 10.682 25.764 10.682 9.321 0 18.644-3.563 25.763-10.682l299.905-299.901c0.246-0.234 0.51-0.438 0.751-0.678 7.129-7.13 10.685-16.469 10.674-25.804 0.006-9.337-3.55-18.676-10.674-25.804z",fill:"#ffffff","p-id":"32303"},null,-1)])))],8,h_),(H(!0),j(ge,null,Ae(x.value,(A,P)=>(H(),j(ge,{key:P},[A==="..."?(H(),j("span",f_,"...")):(H(),j("button",{key:1,class:le({active:A===f.value}),onClick:N=>S(A)},rt(A),11,p_))],64))),128)),w("button",{disabled:f.value===m.value,onClick:k},[(H(),j("svg",m_,M[5]||(M[5]=[w("path",{"data-v-da8ede72":"",d:"M722.438 510.815c-0.246-0.246-0.513-0.454-0.766-0.69l-299.893-299.892c-14.231-14.24-37.291-14.24-51.529 0-14.241 14.231-14.241 37.291 0 51.529l274.851 274.859-274.849 274.851c-14.241 14.237-14.241 37.301 0 51.532 7.116 7.119 16.436 10.682 25.764 10.682 9.321 0 18.644-3.563 25.763-10.682l299.905-299.901c0.246-0.234 0.51-0.438 0.751-0.678 7.129-7.13 10.685-16.469 10.674-25.804 0.006-9.337-3.55-18.676-10.674-25.804z",fill:"#ffffff","p-id":"32303"},null,-1)])))],8,g_)])])])):v.value.length===0&&!pe(n).loading.factorList?(H(),j("div",y_,[wt(to,{type:"no-data",title:"暂无数据，快创建自己的工作流吧。"},{default:dr(()=>[w("div",{class:"content-btn active",onClick:M[1]||(M[1]=A=>s())},"创建工作流")]),_:1})])):(H(),j("div",__,[wt($e,{visible:pe(n).loading.factorList},null,8,["visible"])]))}},b_=Te(C_,[["__scopeId","data-v-56353224"]]),x_={class:"factor-analysis"},w_={class:"metrics-bar"},L_={class:"metric"},S_={class:"metric"},k_={class:"metric"},M_={class:"metric"},T_={class:"value"},E_={__name:"earnings",props:{taskId:{type:String,required:!0}},setup(t){const r=cr(),e=t,a=B({annualized_ratio:0,maximum_drawdown:0,return_ratio:0,sharpe_ratio:0}),i=h=>{const v=parseFloat(h);return v>0?"#f92855":v<0?"#2dc08e":"#fff"},n=B(null);let o=null,s=null;const l=B({}),c=()=>{if(o=ze(n.value),Object.keys(l.value).length===0){console.log("data为空");return}let h=JSON.parse(JSON.stringify(l.value));h.simple_return_chart.y[0].data=l.value.simple_return_chart.y[0].data.map(f=>Ns(f*100)),h.simple_return_chart.x[0].data=l.value.simple_return_chart.x[0].data.map(f=>Os(new Date(f))),console.log("data:",h.simple_return_chart.y[0].data.length);const v={backgroundColor:"transparent",title:{},tooltip:{trigger:"axis",axisPointer:{type:"cross",label:{backgroundColor:"#1e1e1e"}},formatter:function(f){const p=f[0].axisValue,g=f[0].value;return`${p}<br/>${g}%`}},grid:{left:"3%",right:"2%",bottom:"5%",top:"10%",containLabel:!0},xAxis:[{type:"category",data:h.simple_return_chart.x[0].data,onZero:!0,show:!1,axisPointer:{type:"none"},lineStyle:{color:"#333",width:0}},{type:"category",data:h.simple_return_chart.x[0].data,boundaryGap:!1,axisTick:{alignWithLabel:!0,show:!0,length:5,lineStyle:{color:"#777"}},axisLabel:{color:"#777",fontSize:10},axisLine:{show:!0,lineStyle:{color:"#444",width:1}},position:"bottom"}],yAxis:{type:"value",min:Math.min(0,Math.min(...h.simple_return_chart.y[0].data)),max:Math.max(0,Math.max(...h.simple_return_chart.y[0].data)),splitNumber:6,axisLabel:{color:"#858585",formatter:"{value}%",fontSize:10},axisLine:{show:!1,lineStyle:{color:"#333"}},splitLine:{show:!0,lineStyle:{type:"dashed",width:1,color:"rgba(255, 255, 255, 0.05)"}},axisTick:{alignWithLabel:!0,show:!1,length:5,lineStyle:{color:"rgba(255, 255, 255, 0.05)"}}},dataZoom:[{type:"slider",xAxisIndex:[0,1],start:0,end:100,backgroundColor:"rgba(134, 27, 206, 0.3)",borderColor:"rgba(134, 27, 206, 0.3)",bottom:10,height:10},{type:"inside",xAxisIndex:[0,1],start:0,end:100}],series:[{type:"line",showSymbol:!1,data:h.simple_return_chart.y[0].data,lineStyle:{color:"rgba(134, 27, 206, 1)",width:1},areaStyle:{color:new Bs(0,0,0,1,[{offset:0,color:"rgba(134, 27, 206, 0.3)"},{offset:1,color:"rgba(31, 83, 182, 0.1)"}])}}]};o.setOption(v),window.addEventListener("resize",u)},u=()=>{o==null||o.resize()},d=async()=>{if(!e.taskId)return;const h=await r.getFactorEarnings(e.taskId);console.log("查看单个因子收益数据:",h),h.data&&(a.value=h.data.one_group_data);const v=await r.getFactorEarningsChart(e.taskId);console.log("查看单个因子收益数据图标:",v),v.data&&(l.value=v.data,c())};return Re(()=>e.taskId,async()=>{await d()}),Ge(async()=>{await d(),s=new ResizeObserver(()=>{o==null||o.resize()}),n.value&&s.observe(n.value.parentElement)}),Ar(()=>{window.removeEventListener("resize",u),s&&(s.disconnect(),s=null),o==null||o.dispose(),o=null}),(h,v)=>(H(),j("div",x_,[w("div",w_,[w("div",L_,[v[0]||(v[0]=w("span",{class:"label"},"分组盈亏",-1)),w("span",{class:"value",style:Ce({color:i(a.value.return_ratio)})},rt(a.value.return_ratio),5)]),w("div",S_,[v[1]||(v[1]=w("span",{class:"label"},"夏普比率",-1)),w("span",{class:"value",style:Ce({color:i(a.value.sharpe_ratio)})},rt(a.value.sharpe_ratio),5)]),w("div",k_,[v[2]||(v[2]=w("span",{class:"label"},"年化收益",-1)),w("span",{class:"value",style:Ce({color:i(a.value.annualized_ratio)})},rt(a.value.annualized_ratio),5)]),w("div",M_,[v[3]||(v[3]=w("span",{class:"label"},"最大回撤",-1)),w("span",T_,rt(a.value.maximum_drawdown),1)])]),w("div",{ref_key:"echartsRef",ref:n,class:"chart-container"},null,512)]))}},I_=Te(E_,[["__scopeId","data-v-17daf2f9"]]),A_={class:"table-container"},D_={class:"table-wrapper"},P_={key:0,class:"fixed-column"},F_={class:"scroll-area"},R_={style:{display:"flex",position:"sticky",top:"0","z-index":"99",background:"#121212",width:"max-content"}},B_=["title"],N_={__name:"TablesScroll",props:{tableData:{type:Array,required:!0},headers:{type:Array,required:!0},isFixed:{type:Boolean,default:!0},columnWidth:{type:[Number,String],default:0},customHeader:{type:Array,default:[]}},setup(t){Jn(s=>({"7a2b27e2":`${t.columnWidth}px`}));const r=t,e=B(null),a=B(null),i=s=>!isNaN(s)&&Number.isFinite(Number(s))&&String(s).includes(".")?Number(s).toFixed(2):s,n=s=>s?s.replace(/(\d{4})(\d{2})(\d{2})/g,"$1-$2-$3"):"-",o=()=>{e.value&&a.value&&r.isFixed&&(console.log("-------------"),e.value.addEventListener("scroll",()=>{console.log(e.value.scrollTop),a.value.style.top=`-${e.value.scrollTop}px`}))};return Ge(()=>{o()}),(s,l)=>(H(),j("div",A_,[w("div",D_,[r.isFixed?(H(),j("div",P_,[w("div",{class:"fixed-header",style:Ce({width:`${t.customHeader.length>0?t.customHeader[0].width:t.columnWidth}px`})},rt(t.customHeader.length>0?t.customHeader[0].key:r.headers[0]),5),w("div",{class:"fixed-body",ref_key:"fixedBody",ref:a},[(H(!0),j(ge,null,Ae(r.tableData,(c,u)=>(H(),j("div",{key:u,class:"fixed-cell",style:Ce({width:`${t.customHeader.length>0?t.customHeader[0].width:t.columnWidth}px`})},rt(n(c[r.headers[0]])),5))),128))],512)])):ne("",!0),w("div",F_,[w("div",{class:"scroll-body",ref_key:"scrollBody",ref:e},[w("div",R_,[(H(!0),j(ge,null,Ae([...r.headers].splice(r.isFixed?1:0),(c,u)=>(H(),j("div",{key:u,class:"header-cell",style:Ce({width:`${t.customHeader.length>0?t.customHeader[u+1].width:t.columnWidth}px`}),title:c},rt(t.customHeader.length>0?t.customHeader[u+1].key:c),13,B_))),128))]),(H(!0),j(ge,null,Ae(r.tableData,(c,u)=>(H(),j("div",{key:u,class:"table-row"},[(H(!0),j(ge,null,Ae([...r.headers].splice(r.isFixed?1:0),(d,h)=>(H(),j("div",{key:h,class:"table-cell",style:Ce({width:`${t.customHeader.length>0?t.customHeader[h+1].width:t.columnWidth}px`})},rt(i(c[d])),5))),128))]))),128))],512)])])]))}},Gn=Te(N_,[["__scopeId","data-v-9a0230d5"]]),O_={class:"factor-deep"},V_={class:"factor-deep-header"},z_={class:"factor-item"},$_={class:"factor-item-title"},W_={class:"factor-item-content"},Y_={style:{display:"flex","justify-content":"space-between"}},Z_={class:"factor-deep-content",style:{"flex-shrink":"0","flex-grow":"1","flex-basis":"368px","margin-top":"20px"}},H_={class:"data-card",style:{height:"100%"}},j_={class:"data-item"},X_={class:"data-item"},G_={class:"data-item"},K_={class:"data-item"},U_={class:"value"},Q_={key:0,class:"value"},q_={key:1,class:"label"},J_={class:"factor-item",style:{margin:"0","margin-top":"20px",width:"auto","flex-shrink":"0","flex-grow":"1","flex-basis":"460px","margin-left":"20px"}},tC={class:"factor-item-title"},eC={class:"factor-item-content"},rC={style:{width:"100%",height:"400%"}},aC={class:"factor-item",style:{"margin-left":"20px"}},iC={class:"factor-item-title"},nC={class:"factor-item-content"},oC={style:{width:"100%",height:"400px"}},sC={class:"factor-item-container"},lC={class:"factor-item"},cC={class:"factor-item-title"},uC={class:"factor-item-content"},dC={class:"factor-item"},hC={class:"factor-item-title"},vC={class:"factor-item-content"},fC={class:"factor-item"},pC={class:"factor-item-title"},gC={class:"factor-item-content"},mC={class:"factor-item"},yC={class:"factor-item-title"},_C={class:"factor-item-content"},CC={class:"factor-item"},bC={class:"factor-item-title"},xC={class:"factor-item-content"},wC={class:"factor-item"},LC={class:"factor-item-title"},SC={class:"factor-item-content"},kC={class:"factor-item"},MC={class:"factor-item-title"},TC={class:"factor-item-content"},EC={class:"factor-item"},IC={class:"factor-item-title"},AC={class:"factor-item-content"},DC={class:"factor-item"},PC={class:"factor-item-title"},FC={class:"factor-item-content"},RC={class:"factor-item"},BC={class:"factor-item-title"},NC={class:"factor-item-content"},ve="#7d7d7d",OC=Ne({__name:"deepAnalysis",props:{factorId:{type:String,required:!0},taskId:{type:String,requfactorIdired:!0},into:{type:Boolean,required:!0,default:!1}},setup(t){const r=F=>"",e=F=>F,a=F=>!isNaN(F)&&Number.isFinite(Number(F))&&String(F).includes(".")?Number(F).toFixed(2):F,i=t;qn();const n=B(!1),o=F=>{const Ut=parseFloat(F);return Ut>0?"#ff4851":Ut<0?"#2fae34":"#333"},s=B({annualized_ratio:0,maximum_drawdown:0,return_ratio:0,sharpe_ratio:0}),l=async()=>{var Ct,bt,Et,kt,Pt,Ft,Rt,Bt;const{json:F,httpController:Ut}=await zs(i.taskId||r());s.value={annualized_ratio:e((bt=(Ct=F==null?void 0:F.data)==null?void 0:Ct.one_group_data)==null?void 0:bt.annualized_ratio),maximum_drawdown:e((kt=(Et=F==null?void 0:F.data)==null?void 0:Et.one_group_data)==null?void 0:kt.maximum_drawdown),return_ratio:e((Ft=(Pt=F==null?void 0:F.data)==null?void 0:Pt.one_group_data)==null?void 0:Ft.return_ratio),sharpe_ratio:e((Bt=(Rt=F==null?void 0:F.data)==null?void 0:Rt.one_group_data)==null?void 0:Bt.sharpe_ratio)}};let c=null;const u=B(null),d=B(""),h=B({}),v=async()=>{var bt,Et,kt,Pt,Ft,Rt,Bt,Yt,Vt,At,ht,lt,it,K,Z,Y;const{json:F,httpController:Ut}=await $s((i==null?void 0:i.taskId)||r());if(F.code==="200"&&(d.value=(Et=(bt=F==null?void 0:F.data)==null?void 0:bt.return_chart)==null?void 0:Et.title,h.value=(kt=F==null?void 0:F.data)==null?void 0:kt.return_chart,u.value)){if(c=ze(u.value),Object.keys(h.value).length==0)return;var Ct={code:"200",message:"查询成功",data:{task_id:"bbf3bbc030a94849bea4810058eec027",return_chart:{title:"python 5 groups return",x:[{name:"date",data:(Ft=(Pt=h.value)==null?void 0:Pt.x[0])==null?void 0:Ft.data}],y:(Rt=h.value)==null?void 0:Rt.y}}};const U=(At=(Vt=(Yt=(Bt=Ct==null?void 0:Ct.data)==null?void 0:Bt.return_chart)==null?void 0:Yt.x[0])==null?void 0:Vt.data)==null?void 0:At.map(_t=>_t.split(" ")[0]),pt=(it=(lt=(ht=Ct==null?void 0:Ct.data)==null?void 0:ht.return_chart)==null?void 0:lt.y)==null?void 0:it.map(_t=>_t.name),nt=["#FF6B6B","#4ECDC4","#45B7D1","#96CEB4","#FFCC5C","#FF6F69","#88D8B0","#6C88C4","#FFA07A","#98FB98","#87CEEB","#DDA0DD"].sort(()=>Math.random()-.5),Gt=(Y=(Z=(K=Ct==null?void 0:Ct.data)==null?void 0:K.return_chart)==null?void 0:Z.y)==null?void 0:Y.map((_t,$t)=>{var Pe;return(Pe=_t==null?void 0:_t.data)==null?void 0:Pe.map((gr,mr)=>({value:[mr,$t,Number(gr).toFixed(2)],itemStyle:{color:nt[$t%nt.length]}}))}).flat(),zt={title:{left:"center"},tooltip:{show:!1,axisPointer:{show:!1}},grid3D:{viewControl:{projection:"orthographic",autoRotate:!0,distance:200,beta:45,alpha:25},boxWidth:70,boxHeight:70,boxDepth:200,light:{main:{intensity:1.2}},top:"0%",bottom:"10%"},xAxis3D:{type:"category",data:U==null?void 0:U.map(_t=>_t.split(" ")[0]),name:"",axisLabel:{interval:Math.floor((U==null?void 0:U.length)/10),rotate:45,margin:20,textStyle:{fontSize:10,color:ve}},nameTextStyle:{fontSize:14,margin:30}},yAxis3D:{type:"category",data:pt,name:"",axisLabel:{color:ve}},zAxis3D:{type:"value",name:"",axisLabel:{color:ve}},dataZoom:[{type:"slider",xAxisIndex:0,start:0,end:100},{type:"inside",xAxisIndex:0,start:0,end:100}],series:[{type:"bar3D",data:Gt,shading:"lambert",label:{show:!1}}]};c.setOption(zt)}},f=B("分组收益"),p=B([]),g=B({}),m=async()=>{var Ct,bt;const{json:F,httpController:Ut}=await Ws((i==null?void 0:i.taskId)||r());F.code==="200"&&(g.value=(Ct=F==null?void 0:F.data)==null?void 0:Ct.group_return_analysis,p.value=Object.keys((bt=F==null?void 0:F.data)==null?void 0:bt.group_return_analysis[0]))},_=B("最新数据"),x=B([]),S=B({}),y=B([{key:"时间",width:130},{key:"股票代码",width:100},{key:"名称",width:100},{key:"因子值",width:80}]),k=async()=>{var Ct,bt;const{json:F,httpController:Ut}=await Ys((i==null?void 0:i.taskId)||r());F.code==="200"&&(S.value=(Ct=F==null?void 0:F.data)==null?void 0:Ct.last_date_top_factor,x.value=Object.keys((bt=F==null?void 0:F.data)==null?void 0:bt.last_date_top_factor[0]))};let C=null;const D=B(null),E=B(""),I=B({}),T=async()=>{var bt,Et,kt,Pt,Ft,Rt,Bt,Yt;const{json:F,httpController:Ut}=await Zs((i==null?void 0:i.taskId)||r());if(F.code==="200"&&(E.value=(Et=(bt=F==null?void 0:F.data)==null?void 0:bt.ic_decay_chart)==null?void 0:Et.title,I.value=(kt=F==null?void 0:F.data)==null?void 0:kt.ic_decay_chart,D.value)){if(C=ze(D.value),Object.keys(I.value).length==0)return;const Vt=(Ft=(Pt=I.value)==null?void 0:Pt.x[0])==null?void 0:Ft.data,At=(Yt=(Bt=(Rt=I.value)==null?void 0:Rt.y[0])==null?void 0:Bt.data)==null?void 0:Yt.map((ht,lt)=>({value:ht,itemStyle:{color:ht>0?"#ff0000":"#3498db"}}));var Ct={title:{},tooltip:{trigger:"axis",formatter:function(ht){return ht.map(lt=>{let it=lt.value;const K=lt.color;if(Array.isArray(it)){const Z=it.map(Y=>Y==null||isNaN(Y)?"--":Number(Y).toFixed(4));return`<span style="color:${K}">${lt.seriesName}</span>: ${Z[0]}, ${Z[1]}`}return it=it==null||isNaN(it)?"--":Number(it).toFixed(4),`<span style="color:${K}">${lt.seriesName}</span>: ${it}`}).join("<br/>")}},grid:{left:"3%",right:"4%",bottom:"15%",top:"15%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:0,start:0,end:100}],xAxis:[{type:"category",data:Vt,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(ht,lt){const it=Vt.length,K=40,Z=C.getWidth(),Y=Math.floor(Z/K),U=Math.floor((it-1)/(Y-1));return(it-1-ht)%U===0}},show:!1},{type:"category",data:Vt,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(ht,lt){const it=Vt.length,K=40,Z=C.getWidth(),Y=Math.floor(Z/K),U=Math.floor((it-1)/(Y-1));return(it-1-ht)%U===0}},position:"bottom"}],yAxis:[{type:"value",splitLine:{show:!0,lineStyle:{type:"dashed",color:ve}}}],series:[{name:"IC值",type:"bar",data:At,label:{show:!1,position:"bottom",formatter:function(ht){return ht.value?ht.value.toFixed(2):""}}},{name:"IC趋势",type:"line",smooth:!1,showSymbol:!1,data:I.value.y[0].data,lineStyle:{color:"#ff0000",width:0}}]};C.setOption(Ct)}};let L=null;const b=B(null),M=B(""),A=B({}),P=async()=>{var bt,Et,kt,Pt,Ft,Rt,Bt,Yt,Vt;const{json:F,httpController:Ut}=await Hs((i==null?void 0:i.taskId)||r());if(F.code==="200"&&(console.log("json",F),M.value=(Et=(bt=F==null?void 0:F.data)==null?void 0:bt.ic_den_chart)==null?void 0:Et.title,A.value=(kt=F==null?void 0:F.data)==null?void 0:kt.ic_den_chart,b.value)){if(L=ze(b.value),Object.keys(A.value).length==0)return;var Ct={title:{},tooltip:{trigger:"axis",formatter:function(At){return At.map(ht=>{let lt=ht.value;const it=ht.color;if(Array.isArray(lt)){const K=lt.map(Z=>Z==null||isNaN(Z)?"--":Number(Z).toFixed(4));return`<span style="color:${it}">${ht.seriesName}</span>: ${K[0]}, ${K[1]}`}return lt=lt==null||isNaN(lt)?"--":Number(lt).toFixed(4),`<span style="color:${it}">${ht.seriesName}</span>: ${lt}`}).join("<br/>")}},legend:{data:["Histogram","Density Curve"],top:"0px",textStyle:{color:ve}},grid:{left:"3%",right:"4%",bottom:"15%",top:"15%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:0,start:0,end:100},{type:"inside",xAxisIndex:0,start:0}],xAxis:{type:"value",boundaryGap:!1,axisLine:{onZero:!1},splitLine:{show:!0,lineStyle:{type:"dashed",color:ve}}},yAxis:{type:"value",axisLine:{onZero:!1,show:!1},axisTick:{show:!1},position:"left",splitLine:{show:!0,lineStyle:{type:"dashed",color:ve}}},series:[{name:"Histogram",type:"bar",data:(Rt=(Ft=(Pt=A.value)==null?void 0:Pt.y[0])==null?void 0:Ft.data)==null?void 0:Rt.map((At,ht)=>{var lt,it;return[(it=(lt=A.value)==null?void 0:lt.x[0])==null?void 0:it.data[ht],At]}),itemStyle:{color:"rgba(135, 206, 235, 0.6)"}},{name:"Density Curve",type:"line",smooth:!0,data:(Vt=(Yt=(Bt=A.value)==null?void 0:Bt.y[1])==null?void 0:Yt.data)==null?void 0:Vt.map((At,ht)=>{var lt,it;return[(it=(lt=A.value)==null?void 0:lt.x[0])==null?void 0:it.data[ht],At]}),itemStyle:{color:"#1E90FF"},lineStyle:{width:2}}]};L.setOption(Ct)}};let N=null;const W=B(null),X=B(""),$=B({}),ct=async()=>{var bt,Et,kt,Pt,Ft,Rt,Bt,Yt,Vt,At,ht,lt,it;const{json:F,httpController:Ut}=await js((i==null?void 0:i.taskId)||r());if(F.code==="200"&&(X.value=(Et=(bt=F==null?void 0:F.data)==null?void 0:bt.ic_seq_chart)==null?void 0:Et.title,$.value=(kt=F==null?void 0:F.data)==null?void 0:kt.ic_seq_chart,console.log("echartsRefICSequenceData",$.value),W.value)){if(N=ze(W.value),Object.keys($.value).length==0)return;var Ct={title:{left:"center"},tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter:function(K){return K.map(Z=>{let Y=Z.value;const U=Z.color;if(Array.isArray(Y)){const pt=Y.map(nt=>nt==null||isNaN(nt)?"--":Number(nt).toFixed(4));return`<span style="color:${U}">${Z.seriesName}</span>: ${pt[0]}, ${pt[1]}`}return Y=Y==null||isNaN(Y)?"--":Number(Y).toFixed(4),`<span style="color:${U}">${Z.seriesName}</span>: ${Y}`}).join("<br/>")}},legend:{data:(Ft=(Pt=$.value)==null?void 0:Pt.y)==null?void 0:Ft.map(K=>K.name),top:"0px",selected:{IC:!0,Cum_IC:!1},textStyle:{color:ve}},grid:{left:"3%",right:"4%",bottom:"2%",top:"15%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:[0,1],start:0,end:100}],xAxis:[{type:"category",data:(Yt=(Bt=(Rt=$.value)==null?void 0:Rt.x[0])==null?void 0:Bt.data)==null?void 0:Yt.map(K=>K.split(" ")[0]),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(K,Z){var zt,_t,$t;const Y=($t=(_t=(zt=$.value)==null?void 0:zt.x[0])==null?void 0:_t.data)==null?void 0:$t.length,U=40,pt=N.getWidth(),nt=Math.floor(pt/U),Gt=Math.floor((Y-1)/(nt-1));return(Y-1-K)%Gt===0}},show:!1},{type:"category",data:(ht=(At=(Vt=$.value)==null?void 0:Vt.x[0])==null?void 0:At.data)==null?void 0:ht.map(K=>K.split(" ")[0]),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(K,Z){var zt,_t,$t;const Y=($t=(_t=(zt=$.value)==null?void 0:zt.x[0])==null?void 0:_t.data)==null?void 0:$t.length,U=40,pt=N.getWidth(),nt=Math.floor(pt/U),Gt=Math.floor((Y-1)/(nt-1));return(Y-1-K)%Gt===0}},position:"bottom"}],yAxis:{type:"value",axisLabel:{interval:1e3},splitLine:{show:!0,lineStyle:{type:"dashed",color:ve}}},series:(it=(lt=$.value)==null?void 0:lt.y)==null?void 0:it.map(K=>({name:K.name,data:K.data,type:K.name==="IC"?"bar":"line",itemStyle:{color:K.name==="IC"?"#3498db":"#e74c3c"},label:{show:!1,position:"top",formatter:function(Z){return Z.data.toFixed(3)}}}))};N.setOption(Ct)}};let at=null;const gt=B(null),Tt=B(""),Wt=B({}),V=async()=>{var bt,Et,kt,Pt,Ft,Rt,Bt,Yt,Vt,At,ht,lt,it;const{json:F,httpController:Ut}=await Xs((i==null?void 0:i.taskId)||r());if(F.code==="200"&&(Tt.value=(Et=(bt=F==null?void 0:F.data)==null?void 0:bt.ic_self_correlation_chart)==null?void 0:Et.title,Wt.value=(kt=F==null?void 0:F.data)==null?void 0:kt.ic_self_correlation_chart,gt.value)){if(at=ze(gt.value),Object.keys(Wt.value).length==0)return;var Ct={title:{},tooltip:{trigger:"axis",formatter:function(K){return K.map(Z=>{let Y=Z.value;const U=Z.color;if(Array.isArray(Y)){const pt=Y.map(nt=>nt==null||isNaN(nt)?"--":Number(nt).toFixed(4));return`<span style="color:${U}">${Z.seriesName}</span>: ${pt[0]}, ${pt[1]}`}return Y=Y==null||isNaN(Y)?"--":Number(Y).toFixed(4),`<span style="color:${U}">${Z.seriesName}</span>: ${Y}`}).join("<br/>")}},legend:{data:["自相关系数","下限(95%置信区间)","上限(95%置信区间)"],top:"0px",textStyle:{color:ve}},grid:{left:"3%",right:"4%",bottom:"15%",top:"15%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:[0,1],start:0,end:100}],xAxis:[{type:"category",data:(Ft=(Pt=Wt.value)==null?void 0:Pt.x[0])==null?void 0:Ft.data,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(K,Z){var zt,_t,$t;const Y=($t=(_t=(zt=Wt.value)==null?void 0:zt.x[0])==null?void 0:_t.data)==null?void 0:$t.length,U=40,pt=at.getWidth(),nt=Math.floor(pt/U),Gt=Math.floor((Y-1)/(nt-1));return(Y-1-K)%Gt===0}},show:!1},{type:"category",data:(Bt=(Rt=Wt.value)==null?void 0:Rt.x[0])==null?void 0:Bt.data,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(K,Z){var zt,_t,$t;const Y=($t=(_t=(zt=Wt.value)==null?void 0:zt.x[0])==null?void 0:_t.data)==null?void 0:$t.length,U=40,pt=at.getWidth(),nt=Math.floor(pt/U),Gt=Math.floor((Y-1)/(nt-1));return(Y-1-K)%Gt===0}},position:"bottom"}],yAxis:{type:"value",min:-1,max:1,interval:.2,splitLine:{show:!0,lineStyle:{type:"dashed",color:ve}}},series:[{name:"自相关系数",type:"line",data:(Vt=(Yt=Wt.value)==null?void 0:Yt.y[0])==null?void 0:Vt.data,symbol:"circle",symbolSize:8,lineStyle:{width:2},itemStyle:{color:"#3498db"},label:{show:!1,position:"top",formatter:function(K){return K.value.toFixed(3)}}},{name:"下限(95%置信区间)",type:"line",data:(ht=(At=Wt.value)==null?void 0:At.y[1])==null?void 0:ht.data,lineStyle:{type:"dashed",width:1},itemStyle:{color:"#95a5a6"},symbol:"none"},{name:"上限(95%置信区间)",type:"line",data:(it=(lt=Wt.value)==null?void 0:lt.y[2])==null?void 0:it.data,lineStyle:{type:"dashed",width:1},itemStyle:{color:"#95a5a6"},symbol:"none"}]};at.setOption(Ct)}};let G=null;const O=B(null),q=B(""),dt=B({}),tt=async()=>{var bt,Et,kt,Pt,Ft,Rt,Bt,Yt,Vt,At;const{json:F,httpController:Ut}=await Gs((i==null?void 0:i.taskId)||r());if(F.code==="200"&&(q.value=(Et=(bt=F==null?void 0:F.data)==null?void 0:bt.rank_ic_decay_chart)==null?void 0:Et.title,dt.value=(kt=F==null?void 0:F.data)==null?void 0:kt.rank_ic_decay_chart,O.value)){if(G=ze(O.value),Object.keys(dt.value).length==0)return;const ht=(Ft=(Pt=dt.value)==null?void 0:Pt.x[0])==null?void 0:Ft.data,lt=(Yt=(Bt=(Rt=dt.value)==null?void 0:Rt.y[0])==null?void 0:Bt.data)==null?void 0:Yt.map((it,K)=>({value:it,itemStyle:{color:it>0?"#ff0000":"#3498db"}}));var Ct={title:{},tooltip:{trigger:"axis",formatter:function(it){return it.map(K=>{let Z=K.value;const Y=K.color;if(Array.isArray(Z)){const U=Z.map(pt=>pt==null||isNaN(pt)?"--":Number(pt).toFixed(4));return`<span style="color:${Y}">${K.seriesName}</span>: ${U[0]}, ${U[1]}`}return Z=Z==null||isNaN(Z)?"--":Number(Z).toFixed(4),`<span style="color:${Y}">${K.seriesName}</span>: ${Z}`}).join("<br/>")}},grid:{left:"3%",right:"4%",bottom:"15%",top:"15%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:0,start:0,end:100}],xAxis:[{type:"category",data:ht,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(it,K){const Z=ht.length,Y=40,U=G.getWidth(),pt=Math.floor(U/Y),nt=Math.floor((Z-1)/(pt-1));return(Z-1-it)%nt===0}},show:!1},{type:"category",data:ht,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(it,K){const Z=ht.length,Y=40,U=G.getWidth(),pt=Math.floor(U/Y),nt=Math.floor((Z-1)/(pt-1));return(Z-1-it)%nt===0}},position:"bottom"}],yAxis:[{type:"value",splitLine:{show:!0,lineStyle:{type:"dashed",color:ve}}}],series:[{name:"IC值",type:"bar",data:lt,label:{show:!1,position:"bottom",formatter:function(it){return it.value?it.value.toFixed(3):""}}},{name:"IC趋势",type:"line",smooth:!1,showSymbol:!1,data:(At=(Vt=dt.value)==null?void 0:Vt.y[0])==null?void 0:At.data,lineStyle:{color:"#000000",width:0}}]};G.setOption(Ct)}};let mt=null;const Lt=B(null),Ht=B(""),jt=B({}),It=async()=>{var bt,Et,kt,Pt,Ft,Rt,Bt,Yt,Vt;const{json:F,httpController:Ut}=await Ks((i==null?void 0:i.taskId)||r());if(F.code==="200"&&(Ht.value=(Et=(bt=F==null?void 0:F.data)==null?void 0:bt.rank_ic_den_chart)==null?void 0:Et.title,jt.value=(kt=F==null?void 0:F.data)==null?void 0:kt.rank_ic_den_chart,Lt.value)){if(mt=ze(Lt.value),Object.keys(jt.value).length==0)return;var Ct={title:{},tooltip:{trigger:"axis",formatter:function(At){return At.map(ht=>{let lt=ht.value;const it=ht.color;if(Array.isArray(lt)){const K=lt.map(Z=>Z==null||isNaN(Z)?"--":Number(Z).toFixed(4));return`<span style="color:${it}">${ht.seriesName}</span>: ${K[0]}, ${K[1]}`}return lt=lt==null||isNaN(lt)?"--":Number(lt).toFixed(4),`<span style="color:${it}">${ht.seriesName}</span>: ${lt}`}).join("<br/>")}},legend:{data:["Histogram","Density Curve"],top:"0px",textStyle:{color:ve}},grid:{left:"3%",right:"4%",bottom:"15%",top:"15%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:0,start:0,end:100},{type:"inside",xAxisIndex:0,zoomOnMouseWheel:!1,start:0}],xAxis:{type:"value",boundaryGap:!1,axisLine:{onZero:!1},axisLabel:{formatter:"{value}"},splitLine:{show:!0,lineStyle:{type:"dashed",color:ve}}},yAxis:{type:"value",axisLine:{onZero:!1,show:!1},axisTick:{show:!1},position:"left",splitLine:{show:!0,lineStyle:{type:"dashed",color:ve}}},series:[{name:"Histogram",type:"bar",data:(Rt=(Ft=(Pt=jt.value)==null?void 0:Pt.y[0])==null?void 0:Ft.data)==null?void 0:Rt.map((At,ht)=>{var lt,it;return[(it=(lt=jt.value)==null?void 0:lt.x[0])==null?void 0:it.data[ht],At]}),itemStyle:{color:"rgba(135, 206, 235, 0.6)"}},{name:"Density Curve",type:"line",smooth:!0,data:(Vt=(Yt=(Bt=jt.value)==null?void 0:Bt.y[1])==null?void 0:Yt.data)==null?void 0:Vt.map((At,ht)=>{var lt,it;return[(it=(lt=jt.value)==null?void 0:lt.x[0])==null?void 0:it.data[ht],At]}),itemStyle:{color:"#1E90FF"},lineStyle:{width:2}}]};mt.setOption(Ct)}};let Xt=null;const ee=B(null),me=B(""),de=B({}),xe=async()=>{var bt,Et,kt,Pt,Ft,Rt,Bt,Yt,Vt,At,ht,lt,it;const{json:F,httpController:Ut}=await Us(i.taskId||r());if(F.code==="200"&&(me.value=(Et=(bt=F==null?void 0:F.data)==null?void 0:bt.rank_ic_seq_chart)==null?void 0:Et.title,de.value=(kt=F==null?void 0:F.data)==null?void 0:kt.rank_ic_seq_chart,ee.value)){if(Xt=ze(ee.value),Object.keys(de.value).length==0)return;var Ct={title:{left:"center"},tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter:function(K){return K.map(Z=>{let Y=Z.value;const U=Z.color;if(Array.isArray(Y)){const pt=Y.map(nt=>nt==null||isNaN(nt)?"--":Number(nt).toFixed(4));return`<span style="color:${U}">${Z.seriesName}</span>: ${pt[0]}, ${pt[1]}`}return Y=Y==null||isNaN(Y)?"--":Number(Y).toFixed(4),`<span style="color:${U}">${Z.seriesName}</span>: ${Y}`}).join("<br/>")}},legend:{data:(Ft=(Pt=de.value)==null?void 0:Pt.y)==null?void 0:Ft.map(K=>K.name),top:"0px",selected:{IC:!0,Cum_IC:!1},textStyle:{color:ve}},grid:{left:"3%",right:"4%",bottom:"1%",top:"15%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:[0,1],start:0,end:100}],xAxis:[{type:"category",data:(Yt=(Bt=(Rt=de.value)==null?void 0:Rt.x[0])==null?void 0:Bt.data)==null?void 0:Yt.map(K=>K.split(" ")[0]),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(K,Z){var zt,_t,$t;const Y=($t=(_t=(zt=de.value)==null?void 0:zt.x[0])==null?void 0:_t.data)==null?void 0:$t.length,U=40,pt=Xt.getWidth(),nt=Math.floor(pt/U),Gt=Math.floor((Y-1)/(nt-1));return(Y-1-K)%Gt===0}},show:!1},{type:"category",data:(ht=(At=(Vt=de.value)==null?void 0:Vt.x[0])==null?void 0:At.data)==null?void 0:ht.map(K=>K.split(" ")[0]),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(K,Z){var zt,_t,$t;const Y=($t=(_t=(zt=de.value)==null?void 0:zt.x[0])==null?void 0:_t.data)==null?void 0:$t.length,U=40,pt=Xt.getWidth(),nt=Math.floor(pt/U),Gt=Math.floor((Y-1)/(nt-1));return(Y-1-K)%Gt===0}},position:"bottom"}],yAxis:{type:"value",splitLine:{show:!0,lineStyle:{type:"dashed",color:ve}}},series:(it=(lt=de.value)==null?void 0:lt.y)==null?void 0:it.map(K=>({name:K.name,data:K.data,type:K.name==="Rank_IC"?"bar":"line",itemStyle:{color:K.name==="IC"?"#3498db":"#e74c3c"},showSymbol:!1,label:{show:!1,position:"top",formatter:function(Z){return Z.data.toFixed(3)}}}))};Xt.setOption(Ct)}};let ue=null;const et=B(null),ut=B(""),Dt=B({}),Ot=async()=>{var bt,Et,kt,Pt,Ft,Rt,Bt,Yt,Vt,At,ht,lt,it;const{json:F,httpController:Ut}=await Qs((i==null?void 0:i.taskId)||r());if(F.code==="200"&&(ut.value=(Et=(bt=F==null?void 0:F.data)==null?void 0:bt.rank_ic_self_correlation_chart)==null?void 0:Et.title,Dt.value=(kt=F==null?void 0:F.data)==null?void 0:kt.rank_ic_self_correlation_chart,et.value)){if(ue=ze(et.value),Object.keys(Dt.value).length==0)return;var Ct={title:{},tooltip:{trigger:"axis",formatter:function(K){return K.map(Z=>{let Y=Z.value;const U=Z.color;if(Array.isArray(Y)){const pt=Y.map(nt=>nt==null||isNaN(nt)?"--":Number(nt).toFixed(4));return`<span style="color:${U}">${Z.seriesName}</span>: ${pt[0]}, ${pt[1]}`}return Y=Y==null||isNaN(Y)?"--":Number(Y).toFixed(4),`<span style="color:${U}">${Z.seriesName}</span>: ${Y}`}).join("<br/>")}},legend:{data:["自相关系数","下限(95%置信区间)","上限(95%置信区间)"],top:"0px",textStyle:{color:ve}},grid:{left:"3%",right:"4%",bottom:"10%",top:"10%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:0,start:0,end:100}],xAxis:[{type:"category",data:(Ft=(Pt=Dt.value)==null?void 0:Pt.x[0])==null?void 0:Ft.data,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(K,Z){var zt,_t,$t;const Y=($t=(_t=(zt=Dt.value)==null?void 0:zt.x[0])==null?void 0:_t.data)==null?void 0:$t.length,U=40,pt=ue.getWidth(),nt=Math.floor(pt/U),Gt=Math.floor((Y-1)/(nt-1));return(Y-1-K)%Gt===0}},show:!1},{type:"category",data:(Bt=(Rt=Dt.value)==null?void 0:Rt.x[0])==null?void 0:Bt.data,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(K,Z){var zt,_t,$t;const Y=($t=(_t=(zt=Dt.value)==null?void 0:zt.x[0])==null?void 0:_t.data)==null?void 0:$t.length,U=40,pt=ue.getWidth(),nt=Math.floor(pt/U),Gt=Math.floor((Y-1)/(nt-1));return(Y-1-K)%Gt===0}},position:"bottom"}],yAxis:{type:"value",min:-1,max:1,interval:.2,splitLine:{show:!0,lineStyle:{type:"dashed",color:ve}}},series:[{name:"自相关系数",type:"line",data:(Vt=(Yt=Dt.value)==null?void 0:Yt.y[0])==null?void 0:Vt.data,symbol:"circle",symbolSize:8,lineStyle:{width:2},itemStyle:{color:"#3498db"},label:{show:!1,position:"top",formatter:function(K){return K.value.toFixed(3)}}},{name:"下限(95%置信区间)",type:"line",data:(ht=(At=Dt.value)==null?void 0:At.y[1])==null?void 0:ht.data,lineStyle:{type:"dashed",width:1},itemStyle:{color:"#95a5a6"},symbol:"none"},{name:"上限(95%置信区间)",type:"line",data:(it=(lt=Dt.value)==null?void 0:lt.y[2])==null?void 0:it.data,lineStyle:{type:"dashed",width:1},itemStyle:{color:"#95a5a6"},symbol:"none"}]};ue.setOption(Ct)}};let yt=null;const ce=B(null),we=B(""),Zt=B({}),Qt=async()=>{var Ct,bt,Et,kt,Pt,Ft,Rt,Bt,Yt,Vt,At,ht,lt,it,K,Z;const{json:F,httpController:Ut}=await qs((i==null?void 0:i.taskId)||r());if(F.code==="200"&&((Et=(bt=(Ct=F==null?void 0:F.data)==null?void 0:Ct.return_chart)==null?void 0:bt.y)==null||Et.forEach(Y=>{Y.data=Y.data.map(U=>a(U*100))}),we.value=(Pt=(kt=F==null?void 0:F.data)==null?void 0:kt.return_chart)==null?void 0:Pt.title,Zt.value=(Ft=F==null?void 0:F.data)==null?void 0:Ft.return_chart,ce.value)){if(yt=ze(ce.value),Object.keys(Zt.value).length==0)return;const Y={title:{},tooltip:{trigger:"axis",formatter:function(U){return U.map(pt=>{let nt=pt.value;const Gt=pt.color;if(Array.isArray(nt)){const zt=nt.map(_t=>_t==null||isNaN(_t)?"--":Number(_t).toFixed(4));return`<span style="color:${Gt}">${pt.seriesName}</span>: ${zt[0]}, ${zt[1]}`}return nt=nt==null||isNaN(nt)?"--":Number(nt).toFixed(4),`<span style="color:${Gt}">${pt.seriesName}</span>: ${nt}%`}).join("<br/>")}},legend:{data:(Bt=(Rt=Zt.value)==null?void 0:Rt.y)==null?void 0:Bt.map(U=>U.name),textStyle:{color:ve}},xAxis:[{type:"category",data:(At=(Vt=(Yt=Zt.value)==null?void 0:Yt.x[0])==null?void 0:Vt.data)==null?void 0:At.map(U=>zi(U)),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(U,pt){var Pe,gr,mr;const nt=(mr=(gr=(Pe=Zt.value)==null?void 0:Pe.x[0])==null?void 0:gr.data)==null?void 0:mr.length,Gt=60,zt=yt.getWidth(),_t=Math.floor(zt/Gt),$t=Math.floor((nt-1)/(_t-1));return(nt-1-U)%$t===0}},show:!1},{type:"category",data:(it=(lt=(ht=Zt.value)==null?void 0:ht.x[0])==null?void 0:lt.data)==null?void 0:it.map(U=>zi(U)),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(U,pt){var Pe,gr,mr;const nt=(mr=(gr=(Pe=Zt.value)==null?void 0:Pe.x[0])==null?void 0:gr.data)==null?void 0:mr.length,Gt=60,zt=yt.getWidth(),_t=Math.floor(zt/Gt),$t=Math.floor((nt-1)/(_t-1));return(nt-1-U)%$t===0}},position:"bottom"}],yAxis:{type:"value",axisLabel:{formatter:"{value}%"},splitLine:{show:!0,lineStyle:{type:"dashed",color:ve}}},grid:{left:"3%",right:"4%",bottom:"0%",top:"10%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:[0,1],start:0,end:100}],series:(Z=(K=Zt.value)==null?void 0:K.y)==null?void 0:Z.map(U=>({name:U.name,type:"line",data:U.data,showSymbol:!1}))};yt.setOption(Y)}};let qt=null;const Jt=B(null),De=B(""),he=B({}),ae=async()=>{var bt,Et,kt,Pt,Ft,Rt,Bt,Yt,Vt,At,ht,lt,it;const{json:F,httpController:Ut}=await Js((i==null?void 0:i.taskId)||r());if(F.code==="200"&&(De.value=(Et=(bt=F==null?void 0:F.data)==null?void 0:bt.excess_chart)==null?void 0:Et.title,he.value=(kt=F==null?void 0:F.data)==null?void 0:kt.excess_chart,Jt.value)){if(qt=ze(Jt.value),Object.keys(he.value).length==0)return;const K=["#1f77b4","#ff7f0e","#2ca02c","#d62728","#9467bd","#0099cc","#ff00ff"];var Ct={title:{},tooltip:{trigger:"axis",formatter:function(Z){return Z.map(Y=>{let U=Y.value;const pt=Y.color;if(Array.isArray(U)){const nt=U.map(Gt=>Gt==null||isNaN(Gt)?"--":Number(Gt).toFixed(4));return`<span style="color:${pt}">${Y.seriesName}</span>: ${nt[0]}, ${nt[1]}`}return U=U==null||isNaN(U)?"--":Number(U).toFixed(4),`<span style="color:${pt}">${Y.seriesName}</span>: ${U}`}).join("<br/>")}},legend:{data:(Ft=(Pt=he.value)==null?void 0:Pt.y)==null?void 0:Ft.map(Z=>Z.name),top:"0px",textStyle:{color:ve,fontSize:10}},grid:{left:"3%",right:"4%",bottom:"0%",top:"10%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:[0,1],start:0,end:100}],xAxis:[{type:"category",data:(Yt=(Bt=(Rt=he.value)==null?void 0:Rt.x[0])==null?void 0:Bt.data)==null?void 0:Yt.map(Z=>Z.split(" ")[0]),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(Z,Y){var _t,$t,Pe;const U=(Pe=($t=(_t=he.value)==null?void 0:_t.x[0])==null?void 0:$t.data)==null?void 0:Pe.length,pt=40,nt=qt.getWidth(),Gt=Math.floor(nt/pt),zt=Math.floor((U-1)/(Gt-1));return(U-1-Z)%zt===0}},show:!1},{type:"category",data:(ht=(At=(Vt=he.value)==null?void 0:Vt.x[0])==null?void 0:At.data)==null?void 0:ht.map(Z=>Z.split(" ")[0]),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(Z,Y){var _t,$t,Pe;const U=(Pe=($t=(_t=he.value)==null?void 0:_t.x[0])==null?void 0:$t.data)==null?void 0:Pe.length,pt=40,nt=qt.getWidth(),Gt=Math.floor(nt/pt),zt=Math.floor((U-1)/(Gt-1));return(U-1-Z)%zt===0}},position:"bottom"}],yAxis:{type:"value",axisLabel:{formatter:Z=>(Z*100).toFixed(1)+"%"}},series:(it=(lt=he.value)==null?void 0:lt.y)==null?void 0:it.map((Z,Y)=>({name:Z.name,type:"line",data:Z.data,symbol:"circle",symbolSize:6,showSymbol:!1,lineStyle:{width:(Z.name.includes("多空组合"),1)},itemStyle:{color:K[Y]}}))};qt.setOption(Ct)}};Re(()=>i.factorId,async()=>{i.into&&(console.log("加载mc3"),await Lr(),await l(),await m(),await k(),await v(),await T(),await P(),await ct(),await V(),await tt(),await It(),await xe(),await Ot(),await Qt(),await ae(),await ar(),oe())}),Re(()=>i.taskId,async()=>{i.into&&(console.log("加载mc2"),await ar(),oe(),await Lr(),await l(),await ar(),await m(),await k(),await v(),await T(),await P(),await ct(),await V(),await tt(),await It(),await xe(),await Ot(),await Qt(),await ae(),await ar(),setTimeout(()=>{oe()},1200))}),Re(()=>i.into,async()=>{i.into&&(console.log("加载mc1"),await Lr(),await l(),await m(),await k(),await v(),await T(),await P(),await ct(),await V(),await tt(),await It(),await xe(),await Ot(),await Qt(),await ae(),await ar(),oe())}),Re(()=>i.into,async()=>{await ar(),oe()});const oe=()=>{c==null||c.resize(),C==null||C.resize(),L==null||L.resize(),N==null||N.resize(),at==null||at.resize(),G==null||G.resize(),mt==null||mt.resize(),Xt==null||Xt.resize(),ue==null||ue.resize(),yt==null||yt.resize(),qt==null||qt.resize()};let fe=null;Ge(async()=>{var F,Ut,Ct,bt,Et,kt,Pt,Ft,Rt,Bt,Yt;try{i.into&&(await Lr(),await l(),await m(),await k(),await v(),await T(),await P(),await ct(),await V(),await tt(),await It(),await xe(),await Ot(),await Qt(),await ae(),Vi.value=!1,await ar(),fe=new ResizeObserver(()=>{oe()}),[(F=u.value)==null?void 0:F.parentElement,(Ut=D.value)==null?void 0:Ut.parentElement,(Ct=b.value)==null?void 0:Ct.parentElement,(bt=W.value)==null?void 0:bt.parentElement,(Et=gt.value)==null?void 0:Et.parentElement,(kt=O.value)==null?void 0:kt.parentElement,(Pt=Lt.value)==null?void 0:Pt.parentElement,(Ft=ee.value)==null?void 0:Ft.parentElement,(Rt=et.value)==null?void 0:Rt.parentElement,(Bt=ce.value)==null?void 0:Bt.parentElement,(Yt=Jt.value)==null?void 0:Yt.parentElement].forEach(At=>{At&&fe.observe(At)}),oe())}catch{n.value,Vi.value=!0}}),Ar(()=>{fe&&(fe.disconnect(),fe=null),Se()});const Se=()=>{c==null||c.dispose(),C==null||C.dispose(),L==null||L.dispose(),N==null||N.dispose(),at==null||at.dispose(),G==null||G.dispose(),mt==null||mt.dispose(),Xt==null||Xt.dispose(),ue==null||ue.dispose(),yt==null||yt.dispose(),qt==null||qt.dispose()},er=B([]),Lr=async()=>{var Ct;const{json:F,httpController:Ut}=await Vs((i==null?void 0:i.taskId)||r());er.value=(Ct=F==null?void 0:F.data)==null?void 0:Ct.factor_data_analysis},Vi=B(!0),zi=F=>F&&F.split(" ")[0];return(F,Ut)=>(H(),j("div",O_,[w("div",V_,[w("div",z_,[w("div",$_,rt(d.value),1),w("div",W_,[w("div",{ref_key:"echartsRefEarningsBigChart",ref:ce,style:{width:"100%",height:"400px"}},[wt($e,{visible:!0})],512)])]),w("div",Y_,[w("div",Z_,[w("div",H_,[w("div",j_,[w("div",{class:"value",style:Ce({color:o(s.value.return_ratio)})},rt(s.value.return_ratio),5),Ut[0]||(Ut[0]=w("div",{class:"label"},"因子收益",-1))]),w("div",X_,[w("div",{class:"value",style:Ce({color:o(s.value.sharpe_ratio)})},rt(s.value.sharpe_ratio),5),Ut[1]||(Ut[1]=w("div",{class:"label"},"夏普比率",-1))]),w("div",G_,[w("div",{class:"value",style:Ce({color:o(s.value.annualized_ratio)})},rt(s.value.annualized_ratio),5),Ut[2]||(Ut[2]=w("div",{class:"label"},"年化收益",-1))]),w("div",K_,[w("div",U_,rt(s.value.maximum_drawdown),1),Ut[3]||(Ut[3]=w("div",{class:"label"},"最大回撤",-1))]),(H(!0),j(ge,null,Ae(er.value,(Ct,bt)=>(H(),j("div",{key:bt,class:"data-item"},[(H(!0),j(ge,null,Ae(Object.values(Ct),(Et,kt)=>(H(),j(ge,{key:kt},[kt==1?(H(),j("div",Q_,rt(Et),1)):ne("",!0),kt==0?(H(),j("div",q_,rt(Et),1)):ne("",!0)],64))),128))]))),128))])]),w("div",J_,[w("div",tC,rt(_.value),1),w("div",eC,[w("div",rC,[wt(Gn,{tableData:S.value,headers:x.value,isFixed:!0,"custom-header":y.value},null,8,["tableData","headers","custom-header"])])])]),w("div",aC,[w("div",iC,rt(f.value),1),w("div",nC,[w("div",oC,[wt(Gn,{tableData:g.value,headers:p.value,isFixed:!0,"column-width":110},null,8,["tableData","headers"])])])])]),w("div",sC,[w("div",lC,[w("div",cC,rt(E.value),1),w("div",uC,[w("div",{ref_key:"echartsRefSimDietrich",ref:D,style:{width:"100%",height:"400px"}},[wt($e,{visible:!0})],512)])]),w("div",dC,[w("div",hC,rt(M.value),1),w("div",vC,[w("div",{ref_key:"echartsRefICDistribution",ref:b,style:{width:"100%",height:"400px"}},[wt($e,{visible:!0})],512)])]),w("div",fC,[w("div",pC,rt(X.value),1),w("div",gC,[w("div",{ref_key:"echartsRefICSequence",ref:W,style:{width:"100%",height:"400px"}},[wt($e,{visible:!0})],512)])]),w("div",mC,[w("div",yC,rt(Tt.value),1),w("div",_C,[w("div",{ref_key:"echartsRefICCcorrelation",ref:gt,style:{width:"100%",height:"400px"}},[wt($e,{visible:!0})],512)])]),w("div",CC,[w("div",bC,rt(q.value),1),w("div",xC,[w("div",{ref_key:"echartsRefRankICDecay",ref:O,style:{width:"100%",height:"400px"}},[wt($e,{visible:!0})],512)])]),w("div",wC,[w("div",LC,rt(Ht.value),1),w("div",SC,[w("div",{ref_key:"echartsRefRankICDistribution",ref:Lt,style:{width:"100%",height:"400px"}},[wt($e,{visible:!0})],512)])]),w("div",kC,[w("div",MC,rt(me.value),1),w("div",TC,[w("div",{ref_key:"echartsRefRankICSequence",ref:ee,style:{width:"100%",height:"400px"}},[wt($e,{visible:!0})],512)])]),w("div",EC,[w("div",IC,rt(ut.value),1),w("div",AC,[w("div",{ref_key:"echartsRefRankICCcorrelation",ref:et,style:{width:"100%",height:"400px"}},[wt($e,{visible:!0})],512)])]),w("div",DC,[w("div",PC,rt(we.value),1),w("div",FC,[w("div",{ref_key:"echartsRefCurrentDeepAnalysis",ref:u,style:{width:"100%",height:"400px"}},[wt($e,{visible:!0})],512)])]),w("div",RC,[w("div",BC,rt(De.value),1),w("div",NC,[w("div",{ref_key:"echartsRefExcessReturnBigChart",ref:Jt,style:{width:"100%",height:"400px"}},[wt($e,{visible:!0})],512)])])])])]))}}),VC=Te(OC,[["__scopeId","data-v-617db5de"]]),zC={class:"strategy-container"},$C={key:1,class:"strategy-container-content"},WC={class:"strategy-header"},YC={class:"strategy-content"},ZC={class:"strategy-content"},HC={key:0,class:"tab-content"},jC={key:1,class:"tab-content"},XC=Ne({__name:"index",setup(t){const r=Qn(),e=cr(),a=B("我的工作流"),i=B(null),n=B(null),o=B(!0),s=[{label:"收益",value:"factor_overview"},{label:"深度分析",value:"factor_analysis"}];e.child_currentFootTab="factor_overview";const l=v=>{console.log("当前选中的tab:",v)},c=B(null),u=B(null),d=async v=>{var f,p,g,m,_,x;console.log("应用项目：",v);try{const S=await e.fetchLastRunId(v.workflow_id,v.feature_tag,v.locator);if(S.code===0)c.value=S.data;else if(S.code===-1001){r.warning("工作流正在运行，请等待完成后再点击应用！"),(f=n.value)==null||f.resetLoadingState();return}else if(S.code===-1002){r.error("工作流运行错误，请重新去工作流重新运行！"),(p=n.value)==null||p.resetLoadingState();return}else if(S.code===-1003){r.warning("工作流没有运行过，请去工作流运行！"),(g=n.value)==null||g.resetLoadingState();return}S.data.length>0&&(u.value=S.data[0],await e.getFactorLastDateData((m=u.value)==null?void 0:m.node_output),console.log("因子最新日期数据:",e.factorLastDateData))}catch(S){console.log("请求接口出错:",S),r.error("请求接口出错，请检查网络！"),(_=n.value)==null||_.resetLoadingState();return}(x=n.value)==null||x.resetLoadingState(),a.value=v.title,o.value=!1,e.child_currentFootTab="factor_overview"},h=()=>{o.value=!0};return Ge(async()=>{await e.fetchFactorList(),console.log("因子列表返回的数据:",e.factorList)}),(v,f)=>{var p,g;return H(),j(ge,null,[w("div",zC,[o.value?(H(),Qa(b_,{key:0,ref_key:"tableaRef",ref:n,onApply:d,tableData:pe(e).factorList},null,8,["tableData"])):(H(),j("div",$C,[w("div",WC,[wt(tl,{onBack:h,backs:!0,title:a.value},null,8,["title"]),wt(el,{"tab-list":s,tab:pe(e).child_currentFootTab,"onUpdate:tab":[f[0]||(f[0]=m=>pe(e).child_currentFootTab=m),l]},null,8,["tab"])]),w("div",YC,[w("div",ZC,[pe(e).child_currentFootTab==="factor_overview"?(H(),j("div",HC,[wt(I_,{taskId:(p=u.value)==null?void 0:p.node_output},null,8,["taskId"])])):ne("",!0),pe(e).child_currentFootTab==="factor_analysis"?(H(),j("div",jC,[wt(VC,{into:!0,factorId:"",taskId:(g=u.value)==null?void 0:g.node_output},null,8,["taskId"])])):ne("",!0)])])]))]),wt(eo,{ref_key:"logsDialog",ref:i},null,512)],64)}}}),GC=Te(XC,[["__scopeId","data-v-1c4a531a"]]),KC=Ne({__name:"index",setup(t){const r=cr(),e=B(null);return B(!0),r.child_currentFootTab="trade_all",(a,i)=>(H(),j(ge,null,[wt(to,{title:"实盘功能开通请联系小助理，开通对应期货公司权限",type:"no-use"}),ne("",!0),wt(eo,{ref_key:"logsDialog",ref:e},null,512)],64))}}),UC=Te(KC,[["__scopeId","data-v-cd6d393a"]]),QC={class:"strategy-container"},qC=Ne({__name:"index",setup(t){const r=cr();return r.child_currentFootTab="signal_all",(e,a)=>(H(),j("div",QC," 信号指标内容... "))}}),JC=Te(qC,[["__scopeId","data-v-960ae904"]]);$1({name:"bsCircle",draw:(t,r,e)=>{const{x:a,y:i,width:n,text:o}=r,{color:s,textColor:l}=e,c=n/2;t.beginPath(),t.arc(a,i,c,0,2*Math.PI),t.fillStyle=s||"#ff0000",t.fill(),t.strokeStyle="#ffffff",t.lineWidth=2,t.stroke(),t.fillStyle=l||"#ffffff",t.font=`bold ${c}px Arial`,t.textAlign="center",t.textBaseline="middle",t.fillText(o,a,i)},checkEventOn:(t,r)=>{const{x:e,y:a}=t,{x:i,y:n,width:o}=r,s=e-i,l=a-n,c=o/2;return Math.sqrt(s*s+l*l)<=c}});_a({name:"buyFigure",totalStep:2,createPointFigures:({coordinates:t})=>t.map(r=>({type:"bsCircle",attrs:{x:r.x,y:r.y,width:20,text:"B"},styles:{color:"#e9414d",textColor:"#ffffff"}}))});_a({name:"sellFigure",totalStep:2,createPointFigures:({coordinates:t})=>t.map(r=>({type:"bsCircle",attrs:{x:r.x,y:r.y,width:20,text:"S"},styles:{color:"#45b486",textColor:"#ffffff"}}))});console.log("figure.ts");Kl({name:"factorIndicatorDraw",shortName:"Factor",zLevel:-1,figures:[],calc:t=>t.map(r=>({volume:r.volume,close:r.close,open:r.open})),createTooltipDataSource:({indicator:t,crosshair:r})=>{const a=t.result[r.dataIndex];if(a){const i=a.open<a.close?"rgb(224, 152, 199)":"rgb(143, 211, 232)";return{legends:[{title:"",value:{text:a.volume,color:i}}]}}return{}},draw:({ctx:t,chart:r,indicator:e,bounding:a,xAxis:i})=>{const{realFrom:n,realTo:o}=r.getVisibleRange(),{gapBar:s,halfGapBar:l}=r.getBarSpace(),{result:c}=e;let u=0;for(let v=n;v<o;v++){const f=c[v];f&&(u=Math.max(u,f.volume))}const d=a.height*.4,h=Y1("rect");for(let v=n;v<o;v++){const f=c[v];if(f){const p=Math.round(f.volume/u*d),g=f.open<f.close?"rgba(224, 152, 199, 0.6)":"rgba(143, 211, 232, 0.6)";new h({name:"rect",attrs:{x:i.convertToPixel(v)-l,y:a.height-p,width:s,height:p},styles:{color:g}}).draw(t)}}return!0}});const tb={class:"left-bottom-content-tab"},eb={key:0,class:"left-bottom-content-tab-item"},rb={key:1,class:"left-bottom-content-tab-item"},ab={key:2,class:"left-bottom-content-tab-item"},ib={key:3,class:"left-bottom-content-tab-item"},nb={class:"right"},ob={class:"right-table"},sb={key:0,class:"ai-prediction"},lb={class:"box"},cb=Ne({__name:"index",setup(t){Jn(V=>({"51dc98b7":g.value+"px",b6ed1ee2:x.value+"px"}));const r=cr(),e=ta(),a=Oi(),i=B(!0);Ya("child_isShowFuture",i);const n=B(100);Ya("child_futureListHeight",n);const o=B(0);Ya("child_dailyPositionHeight",o);const s=V=>{console.log("应用项目：",V),(V==null?void 0:V.type)==="strategy"&&(V.positionHeight?(n.value=60,o.value=V.positionHeight||0):(n.value=100,o.value=0),r.rightTableData=V.selectedApplyData||null,V.timeRange&&r.updateTimeRange(V.timeRange,V.startDate||"",V.endDate||""))};Re(()=>r.currentFootTab,()=>{r.currentFootTab!=="strategy"&&(n.value=100,o.value=0)});let l=null;const c=B(null),u=B(null);let d=null;const h=B(1),v=[{key:"strategy",label:"策略"},{key:"factor",label:"因子"},{key:"trade",label:"实盘"}];r.currentFootTab="strategy";const f=B(null),p=B(!0),g=B(350),m=()=>{p.value=!p.value,g.value=p.value?350:0},_=B(!0),x=B(300),S=()=>{_.value=!_.value,x.value=_.value?300:0},y=B(!1),k=B(0),C=B(0),D=V=>{y.value=!0,k.value=V.clientY,C.value=x.value,window.addEventListener("mousemove",E,{capture:!0}),window.addEventListener("mouseup",I,{capture:!0}),document.body.style.cursor="s-resize",document.body.style.userSelect="none"},E=V=>{y.value&&(x.value=Math.max(50,Math.min(800,C.value-(V.clientY-k.value))),V.preventDefault(),V.stopPropagation())},I=()=>{y.value=!1,window.removeEventListener("mousemove",E,{capture:!0}),window.removeEventListener("mouseup",I,{capture:!0}),document.body.style.cursor="",document.body.style.userSelect=""},T=()=>{ar(()=>{var G;const V=((G=f.value)==null?void 0:G.$el)||f.value;V&&V.addEventListener("click",L,!0)})},L=V=>{var dt;if(V.target.closest(".optional-svg"))return;const G=V.target.closest(".marketlist-row");if(!G)return;V.preventDefault(),V.stopPropagation();const O=G.querySelector(".col2.code");if(!O)return;const q=(dt=O.textContent)==null?void 0:dt.trim();q&&(console.log("点击了合约:",q),M(q))};let b=null;const M=async(V,G)=>{a.currentCode=V.toUpperCase(),console.log("准备切换到合约:",V),b&&clearTimeout(b),b=setTimeout(async()=>{console.log("执行合约切换:",V);try{if(l&&typeof l.setSymbol=="function"){G&&l.setPeriod({multiplier:1,timespan:"day",text:"1d"});const O=A(V);O.ticker=V.replace(/\d+$/,"").toUpperCase(),console.log("symbol.ticker",O.ticker),l.setSymbol(O),l.getChart().setOrderFlowData([]),l.getChart().setCandleVisible(!0),console.log("====================symbol=======================",O),G&&(l.getChart().createOverlay({name:"buyFigure",points:G==null?void 0:G.buyFigure}),l.getChart().createOverlay({name:"sellFigure",points:G==null?void 0:G.sellFigure})),l.getChart().createIndicator("factorIndicatorDraw",!1,{id:"candle_pane"})}console.log("✅ 合约切换完成:",V)}catch(O){console.error("❌ 合约切换失败:",O)}},300)};r.klineCharthandleContractChange=M;const A=V=>{const G={BABA2503:"BABA",IF2503:"IF"},O=V;return G[V]?{exchange:"XNYS",market:"stocks",ticker:G[V],name:G[V],shortName:G[V],priceCurrency:"CNY",type:"stock"}:{exchange:"XNYS",market:"stocks",ticker:O,name:O,shortName:O,priceCurrency:"CNY",type:"stock"}},P=()=>{if(l){const V=l.getChart();V&&V.resize()}},N=V=>{r.currentFootTab=V,console.log("当前选中的tab:",V)},W=B(!1),X=B(0),$=B(0),ct=V=>{W.value=!0,X.value=V.clientX,$.value=g.value,window.addEventListener("mousemove",at,{capture:!0}),window.addEventListener("mouseup",gt,{capture:!0}),document.body.style.cursor="w-resize",document.body.style.userSelect="none"},at=V=>{W.value&&(g.value=Math.max(50,Math.min(800,$.value+(X.value-V.clientX))),V.preventDefault(),V.stopPropagation())},gt=()=>{W.value=!1,window.removeEventListener("mousemove",at,{capture:!0}),window.removeEventListener("mouseup",gt,{capture:!0}),document.body.style.cursor="",document.body.style.userSelect=""};Re(x,V=>{});const Tt=B(!1),Wt=()=>{Tt.value=!1,console.log("关闭ai预测")};return Ge(async()=>{window.addEventListener("ai-prediction",V=>{Tt.value=!0});try{const V=x.value/window.innerHeight*100;c.value&&(l=new Pm({container:c.value,symbol:e.symbol,period:{multiplier:15,timespan:"minute",text:"1m"},datafeed:new zy("Q0aVaOIdjbaHHu5UZM5MQ0XcOEU3mSIJ")}),l.setTheme("dark"),r.klineChartRef=l,d=new ResizeObserver(()=>{P()}),u.value&&d.observe(u.value)),T()}catch(V){console.error("Failed to load klinecharts:",V)}}),Ar(()=>{var G;if(b&&(clearTimeout(b),b=null),l&&l.getDatafeed){const O=l.getDatafeed();O&&typeof O.unsubscribeAll=="function"&&O.unsubscribeAll()}d&&(d.disconnect(),d=null);const V=((G=f.value)==null?void 0:G.$el)||f.value;V&&V.removeEventListener("click",L,!0),window.removeEventListener("mousemove",E,{capture:!0}),window.removeEventListener("mouseup",I,{capture:!0}),window.removeEventListener("mousemove",at,{capture:!0}),window.removeEventListener("mouseup",gt,{capture:!0})}),(V,G)=>(H(),j(ge,null,[w("div",{class:le(["kline-container",{dragging:y.value}])},[w("div",{ref_key:"leftTopRef",ref:u,class:"left-top"},[w("div",{ref_key:"chartContainerRef",ref:c,class:"chart-container"},null,512)],512),w("div",{class:le(["left-bottom",{dragging:y.value}])},[w("div",{class:"left-bottom-toggle-btn",onClick:S},[(H(),j("svg",{style:Ce({transform:_.value?"rotate(90deg)":"rotate(270deg)"}),viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"32302",width:"16",height:"16"},G[0]||(G[0]=[w("path",{"data-v-da8ede72":"",d:"M722.438 510.815c-0.246-0.246-0.513-0.454-0.766-0.69l-299.893-299.892c-14.231-14.24-37.291-14.24-51.529 0-14.241 14.231-14.241 37.291 0 51.529l274.851 274.859-274.849 274.851c-14.241 14.237-14.241 37.301 0 51.532 7.116 7.119 16.436 10.682 25.764 10.682 9.321 0 18.644-3.563 25.763-10.682l299.905-299.901c0.246-0.234 0.51-0.438 0.751-0.678 7.129-7.13 10.685-16.469 10.674-25.804 0.006-9.337-3.55-18.676-10.674-25.804z",fill:"#777777","p-id":"32303"},null,-1)]),4))]),w("div",{class:le(["left-bottom-drag",{dragging:y.value}]),onMousedown:D},G[1]||(G[1]=[w("span",null,null,-1),w("span",null,null,-1),w("span",null,null,-1)]),34),w("div",{class:"left-bottom-content",style:Ce({opacity:_.value?1:0})},[wt(Oy,{onTabChange:N,activeTab:pe(r).currentFootTab,tabs:v},null,8,["activeTab"]),w("div",tb,[pe(r).currentFootTab==="strategy"?(H(),j("div",eb,[wt(rl,{onApply:s})])):ne("",!0),pe(r).currentFootTab==="factor"?(H(),j("div",rb,[wt(GC)])):ne("",!0),pe(r).currentFootTab==="trade"?(H(),j("div",ab,[wt(UC)])):ne("",!0),pe(r).currentFootTab==="indicator"?(H(),j("div",ib,[wt(JC)])):ne("",!0)])],4)],2),w("div",nb,[w("div",{class:"right-toggle-btn",onClick:m},[(H(),j("svg",{style:Ce({transform:p.value?"rotate(0deg)":"rotate(180deg)"}),viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"32302",width:"16",height:"16"},G[2]||(G[2]=[w("path",{"data-v-da8ede72":"",d:"M722.438 510.815c-0.246-0.246-0.513-0.454-0.766-0.69l-299.893-299.892c-14.231-14.24-37.291-14.24-51.529 0-14.241 14.231-14.241 37.291 0 51.529l274.851 274.859-274.849 274.851c-14.241 14.237-14.241 37.301 0 51.532 7.116 7.119 16.436 10.682 25.764 10.682 9.321 0 18.644-3.563 25.763-10.682l299.905-299.901c0.246-0.234 0.51-0.438 0.751-0.678 7.129-7.13 10.685-16.469 10.674-25.804 0.006-9.337-3.55-18.676-10.674-25.804z",fill:"#777777","p-id":"32303"},null,-1)]),4))]),w("div",{class:le(["right-drag",{dragging:W.value}]),onMousedown:ct},G[3]||(G[3]=[w("span",null,null,-1),w("span",null,null,-1),w("span",null,null,-1)]),34),w("div",{class:"CustomTopPanel",style:Ce({opacity:p.value?1:0})},[wt(ky,{ref_key:"customTopPanelRef",ref:f,currentCustomPanelIndex:h.value},null,8,["currentCustomPanelIndex"])],4),w("div",ob,[wt(Py)])])],2),Tt.value?(H(),j("div",sb,[w("div",lb,[w("img",{onClick:Wt,class:"close-btn",src:al}),wt(Ky)])])):ne("",!0)],64))}}),ub=Te(cb,[["__scopeId","data-v-4e382288"]]),db={class:"about"},gb=Ne({__name:"ChartsView",setup(t){return(r,e)=>(H(),j("div",db,[wt(ub)]))}});export{gb as default};
