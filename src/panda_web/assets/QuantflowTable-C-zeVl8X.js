import{d as w,c as i,a as h,o as s,b as e,t as f,_ as V,u as W,r as g,e as Z,f as U,g as A,h as o,w as p,n as R,i as $,j as C,p as G,F as T,k as X,l as z,m as ee,q as ue,E as te,s as b}from"./main-Dt6CueBW.js";import{H as ae}from"./Header-DjDYApCI.js";import{g as le,r as ne,d as oe}from"./index-CU8Z6Hiu.js";const se="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAUCAYAAACAl21KAAAAAXNSR0IArs4c6QAAAWZJREFUOE/tlLFLAzEYxd872m46u+jg4iTuOtgiCqKbg7P+A06l0OTK0SSF4uQ/oLODowiKWAfdRXBwcNDFWbe23GdTpNij1x6dvSW8fMkv+R65R6R8zrkygP1E+VwpdTxqC9NA1to9ADuJ+qXW+mIkyBizS3IxDZhlXkTe6Jx7AbAEIAAQA5AsmxNrXvutGWOKQRDcxXFcCsOwNQUIU4OiKJrN5/NHnU7nJIqir6lBjUZjXURaJIvVavU+FWSMOfAthmF49tt+Ug/ZkQrqneg9g9a65EFJnfR1LMgDetcegP7qfxBgrb31nmitN/yY1Jk9qtfryx5Qq9We/ZjUmUGTfpORIGvtKskHAFtKqZtJEF93zm0CuBaRNa31Y/8dNZvNmW63+yEiTyLig6s9AVYgWSa5ksvl5iuVyvcg2Jxz2yJySnIuy41E5JPkoVLqyq8fSkgR8fm0ICKFcTCSbaXUO8lBdv0AzkZJXg8QSYAAAAAASUVORK5CYII=",ie="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAAAXNSR0IArs4c6QAAAMxJREFUKFOdkkEKwjAQRf9Qqjv1BN7EiuAdvIA3cBkLs0hKNt5AL+ARLKh4DE8hrtSkjQSshkKrmNUE3p/5PxnSWg+ttRsiGgHo4nOUEGJZXaWUCRFNSUqZE9HeGLNi5kcgeJdZlo3Lstw652aklLoZY3q/wGmaHr3ACSHoW2cPe6ZRENqo4EaB1rpvrT17zyGslJKNE5h5wMyX0Kq335qhnutvQeuzVlOYuRPH8dVb2gE4tH3cC14AmJBfjaIo1gCS2mqEEe4ATlEUzZ/h35MqCrgdiwAAAABJRU5ErkJggg==",re="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAsAAAALCAYAAACprHcmAAAAAXNSR0IArs4c6QAAAQRJREFUKFON0aFLBFEQBvDvk31WjWIVxHRBFMVg12Iy2gwGi0Vk5y08lnn7FwjnH2DSIFpMVw02tZnEohdEzQ92ZOEObu84cNIw/MLMfFTVe5JbAAxTyswI4IExxl8RmZsGh/PGNfhHROb/gb8ncAghy7LsCMA2yS8AXRF5LstyvYUHsEfyva7ra5KLAE4BnHjvb1tYVY9JbojIwXCtqqqWzayXUloax1dmdlkUxd3oDTHGRwCHLRxj7JrZk/f+YohDCDPOubeU0to47gC4IbmT5/nrAJYAVkVkd+LPqrpH8hzAh5ktAHgh2TGzfY4k+CkiK02SIYRZ51zT90Wkr6qbJM/+APl6h+T46od8AAAAAElFTkSuQmCC",de="/quantflow/assets/no-data-BTYpxJgk.png";/*! Element Plus v2.10.2 */var ce={name:"zh-cn",el:{breadcrumb:{label:"面包屑"},colorpicker:{confirm:"确定",clear:"清空",defaultLabel:"颜色选择器",description:"当前颜色 {color}，按 Enter 键选择新颜色",alphaLabel:"选择透明度的值"},datepicker:{now:"此刻",today:"今天",cancel:"取消",clear:"清空",confirm:"确定",dateTablePrompt:"使用方向键与 Enter 键可选择日期",monthTablePrompt:"使用方向键与 Enter 键可选择月份",yearTablePrompt:"使用方向键与 Enter 键可选择年份",selectedDate:"已选日期",selectDate:"选择日期",selectTime:"选择时间",startDate:"开始日期",startTime:"开始时间",endDate:"结束日期",endTime:"结束时间",prevYear:"前一年",nextYear:"后一年",prevMonth:"上个月",nextMonth:"下个月",year:"年",month1:"1 月",month2:"2 月",month3:"3 月",month4:"4 月",month5:"5 月",month6:"6 月",month7:"7 月",month8:"8 月",month9:"9 月",month10:"10 月",month11:"11 月",month12:"12 月",weeks:{sun:"日",mon:"一",tue:"二",wed:"三",thu:"四",fri:"五",sat:"六"},weeksFull:{sun:"星期日",mon:"星期一",tue:"星期二",wed:"星期三",thu:"星期四",fri:"星期五",sat:"星期六"},months:{jan:"一月",feb:"二月",mar:"三月",apr:"四月",may:"五月",jun:"六月",jul:"七月",aug:"八月",sep:"九月",oct:"十月",nov:"十一月",dec:"十二月"}},inputNumber:{decrease:"减少数值",increase:"增加数值"},select:{loading:"加载中",noMatch:"无匹配数据",noData:"无数据",placeholder:"请选择"},dropdown:{toggleDropdown:"切换下拉选项"},mention:{loading:"加载中"},cascader:{noMatch:"无匹配数据",loading:"加载中",placeholder:"请选择",noData:"暂无数据"},pagination:{goto:"前往",pagesize:"条/页",total:"共 {total} 条",pageClassifier:"页",page:"页",prev:"上一页",next:"下一页",currentPage:"第 {pager} 页",prevPages:"向前 {pager} 页",nextPages:"向后 {pager} 页",deprecationWarning:"你使用了一些已被废弃的用法，请参考 el-pagination 的官方文档"},dialog:{close:"关闭此对话框"},drawer:{close:"关闭此对话框"},messagebox:{title:"提示",confirm:"确定",cancel:"取消",error:"输入的数据不合法!",close:"关闭此对话框"},upload:{deleteTip:"按 Delete 键可删除",delete:"删除",preview:"查看图片",continue:"继续上传"},slider:{defaultLabel:"滑块介于 {min} 至 {max}",defaultRangeStartLabel:"选择起始值",defaultRangeEndLabel:"选择结束值"},table:{emptyText:"暂无数据",confirmFilter:"筛选",resetFilter:"重置",clearFilter:"全部",sumText:"合计"},tour:{next:"下一步",previous:"上一步",finish:"结束导览"},tree:{emptyText:"暂无数据"},transfer:{noMatch:"无匹配数据",noData:"无数据",titles:["列表 1","列表 2"],filterPlaceholder:"请输入搜索内容",noCheckedFormat:"共 {total} 项",hasCheckedFormat:"已选 {checked}/{total} 项"},image:{error:"加载失败"},pageHeader:{title:"返回"},popconfirm:{confirmButtonText:"确定",cancelButtonText:"取消"},carousel:{leftArrow:"上一张幻灯片",rightArrow:"下一张幻灯片",indicator:"幻灯片切换至索引 {index}"}}};const Ee={key:0,class:"confirm-dialog-overlay"},ge={class:"confirm-dialog"},pe={class:"dialog-content"},me={class:"message"},Ae={class:"buttons"},Fe=w({__name:"ConfirmDialog",props:{modelValue:{type:Boolean,required:!0},message:{type:String,default:"您确定要删除选中的项目吗？"}},emits:["update:modelValue","confirm"],setup(D,{emit:c}){const r=c,F=()=>{r("confirm"),r("update:modelValue",!1)};return(l,n)=>D.modelValue?(s(),i("div",Ee,[e("div",ge,[e("div",{class:"close-button",onClick:n[0]||(n[0]=B=>l.$emit("update:modelValue",!1))},n[2]||(n[2]=[e("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2"},[e("path",{d:"M18 6L6 18M6 6l12 12","stroke-linecap":"round","stroke-linejoin":"round"})],-1)])),e("div",pe,[n[3]||(n[3]=e("div",{class:"icon"},[e("svg",{width:"34",height:"34",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2"},[e("circle",{cx:"12",cy:"12",r:"10"}),e("path",{d:"M12 8v4M12 16h.01","stroke-linecap":"round","stroke-linejoin":"round"})])],-1)),e("div",me,f(D.message),1),e("div",Ae,[e("button",{class:"cancel-btn",onClick:n[1]||(n[1]=B=>l.$emit("update:modelValue",!1))},"取消"),e("button",{class:"confirm-btn",onClick:F},"删除")])])])])):h("",!0)}}),ve=V(Fe,[["__scopeId","data-v-466a3a98"]]),he={class:"quantflow-table_container"},fe={class:"content-container"},De={class:"content-wrapper"},Ce={class:"table-container"},Be={class:"table-header"},_e={class:"header-row"},ke={key:0,class:"header-cell selection-cell"},be={class:"table-body-container"},we={class:"table-body"},ye=["onMouseenter"],xe={class:"table-cell id-cell",style:{"padding-left":"50px"}},Se={class:"table-cell name-cell"},Re={key:0,class:"table-name-text"},Te=["onClick"],Ve={key:3,class:"template-tag"},Ue={class:"table-cell update-cell"},ze={class:"header-cell operate-cell"},Ie=["onClick"],Ne={class:"table-cell selection-cell"},Me={class:"table-footer"},qe={class:"total-items"},Je={key:1,class:"no-data_container"},Le=w({__name:"Table",setup(D){const c=W(),r=g([]),F=Z(),l=g({currentPage:1,pageSize:10,total:0}),n=g(-1),B=g("linear-gradient( 270deg, rgba(54,54,54,0) 0%, #2F2F2F 18%, rgba(47,47,47,0.9) 86%, rgba(66,66,66,0) 100%)"),I=g("linear-gradient(270deg, rgba(19,33,72,0) 0%, #132148 17%, rgba(19,33,72,0.9) 86%, rgba(19,33,72,0) 100%)"),N=g(!1),m=g([]),_=g(!1);U(()=>{v()});async function v(){const a=await le(l.value.currentPage,l.value.pageSize);Object.keys(a).length>0&&a.workflows.length>0&&(r.value=a.workflows.map(u=>({id:u._id,name:u.name,updateTime:u.update_at,selected:!1,editing:!1,owner:u.owner})),l.value.total=a.total_count)}const M=a=>{l.value.pageSize=a,v()},q=a=>{l.value.currentPage=a,v()},J=()=>{m.value=r.value.filter(a=>a.selected),N.value=m.value.length===r.value.length},L=a=>{a.editing=!a.editing},y=async a=>{a.editing=!1,await ne(a.id,a.name),await v(),b.success("名称修改成功！")},x=()=>{c.owner="",c.workflow_run_id="",c.workflow_id="",c.title="",F.push("/editor")},K=a=>{F.push(`/editor?workflow_id=${a}`)},P=()=>{m.value.length!==0&&(_.value=!0)},Y=async()=>{if(m.value.length===0){b.warning("请先选择当前所需删除的项目！");return}const u=r.value.filter(E=>E.selected).map(E=>E.id);await oe(u),await v(),b.success("删除成功！")};return(a,u)=>{const E=A("el-col"),j=A("el-icon"),S=A("el-row"),H=A("el-input"),O=A("el-checkbox"),Q=A("el-pagination");return s(),i("div",he,[o(ae),e("div",fe,[e("div",De,[e("div",Ce,[e("div",Be,[o(S,{justify:"end"},{default:p(()=>[o(E,{span:18},{default:p(()=>[e("div",_e,[u[5]||(u[5]=e("div",{class:"header-cell id-cell"},"ID",-1)),u[6]||(u[6]=e("div",{class:"header-cell name-cell"},"名称",-1)),u[7]||(u[7]=e("div",{class:"header-cell update-cell"},"最后更新时间",-1)),u[8]||(u[8]=e("div",{class:"header-cell operate-cell"},"操作",-1)),l.value.total>0?(s(),i("div",ke,[e("div",{class:$(["delete-btn",{"delete-btn-disabled":m.value.length===0}]),onClick:P,style:R({cursor:m.value.length===0?"not-allowed":"pointer"})},u[4]||(u[4]=[e("img",{src:se,alt:"delete",width:"13px",height:"15px",style:{"margin-right":"5px"}},null,-1),e("span",null,"删除",-1)]),6)])):h("",!0)])]),_:1}),o(E,{span:3,class:"create-col"},{default:p(()=>[l.value.total>0?(s(),i("div",{key:0,class:"button-wrapper",onClick:x},[o(j,null,{default:p(()=>[o(C(G))]),_:1}),u[9]||(u[9]=e("span",null,"新建",-1))])):h("",!0)]),_:1})]),_:1})]),l.value.total>0?(s(),i(T,{key:0},[o(S,{justify:"end"},{default:p(()=>[o(E,{span:18},{default:p(()=>[e("div",be,[e("div",we,[(s(!0),i(T,null,X(r.value,(t,k)=>(s(),i("div",{key:t.id,class:"table-row",onMouseenter:d=>n.value=k,onMouseleave:u[0]||(u[0]=d=>n.value=-1),style:R({background:n.value===k?I.value:B.value,opacity:n.value===k?.8:1})},[e("div",xe,f(t.id),1),e("div",Se,[t.editing?(s(),z(H,{key:1,modelValue:t.name,"onUpdate:modelValue":d=>t.name=d,size:"small",onBlur:d=>y(t),onKeyup:ee(d=>y(t),["enter"])},null,8,["modelValue","onUpdate:modelValue","onBlur","onKeyup"])):(s(),i("span",Re,f(t.name),1)),t.owner!=="*"?(s(),i("img",{key:2,onClick:d=>L(t),src:ie,alt:"delete",width:"10px",height:"10px",class:"edit-icon"},null,8,Te)):h("",!0),t.owner==="*"?(s(),i("span",Ve,"模版")):h("",!0)]),e("div",Ue,f(t.updateTime&&C(ue)(t.updateTime).format("YYYY-MM-DD HH:mm:ss")),1),e("div",ze,[e("div",{class:"view-btn",onClick:d=>K(t.id)},u[10]||(u[10]=[e("img",{src:re,alt:"delete",width:"10px",height:"10px",style:{"margin-right":"5px"}},null,-1),e("span",{style:{"font-weight":"400"}},"查看",-1)]),8,Ie)]),e("div",Ne,[o(O,{modelValue:t.selected,"onUpdate:modelValue":d=>t.selected=d,onChange:J,disabled:t.owner==="*"},null,8,["modelValue","onUpdate:modelValue","disabled"])])],44,ye))),128))])])]),_:1}),o(E,{span:3})]),_:1}),e("div",Me,[e("div",qe,"共 "+f(l.value.total)+" 条",1),o(C(te),{locale:C(ce)},{default:p(()=>[o(Q,{"current-page":l.value.currentPage,"onUpdate:currentPage":u[1]||(u[1]=t=>l.value.currentPage=t),"page-size":l.value.pageSize,"onUpdate:pageSize":u[2]||(u[2]=t=>l.value.pageSize=t),"page-sizes":[10,20,50,100],background:!0,layout:"sizes, prev, pager, next, jumper",total:l.value.total,onSizeChange:M,onCurrentChange:q},null,8,["current-page","page-size","total"])]),_:1},8,["locale"])])],64)):(s(),i("div",Je,[u[12]||(u[12]=e("img",{src:de,alt:"delete"},null,-1)),u[13]||(u[13]=e("p",{class:"no-data_info"},"暂无数据，快创建自己的工作流吧。",-1)),e("div",{class:"button-wrapper",onClick:x,style:{width:"150px"}},u[11]||(u[11]=[e("span",null,"创建工作流",-1)]))]))])])]),o(ve,{modelValue:_.value,"onUpdate:modelValue":u[3]||(u[3]=t=>_.value=t),onConfirm:Y},null,8,["modelValue"])])}}}),Ke=V(Le,[["__scopeId","data-v-ac2cea1e"]]),He=w({__name:"QuantflowTable",setup(D){U(()=>{c()});function c(){localStorage.getItem("token")}return(r,F)=>(s(),z(Ke))}});export{He as default};
