import{H as Yt}from"./Header-B2xyBrwd.js";import{d as mt,v as wt,g as Pt,c as x,o as w,F as Ve,k as Je,i as Ke,b as e,l as Et,a as Ne,n as Ze,x as vt,t as P,_ as nt,r as c,f as ht,y as st,z as Ft,A as bt,h as De,u as Ct,B as Wt,C as yt,D as Ot,G as Zt,e as Qt,H as je,I as Ge,J as at,K as Vt,j as $e,L as Jt,M as Nt,N as At,O as Ht,P as Bt,Q as Xt,R as $t,S as jt,m as Kt,T as ea,U as ft,V as Oe,W as ta,X as aa,w as oa,Y as sa,Z as na}from"./main-BMJQvnQN.js";import{a as la}from"./index-C81Kkeck.js";import{l as ot,G as ia,a as ra,b as ca,c as da,d as ua,e as fa,f as ha,g as ga,h as pa,i as va,j as Aa,k as ma,m as ya,n as wa,o as ba,p as Gt,_ as qt,s as xa}from"./index-CiWTbwln.js";const ka="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAhNJREFUOE+Nkz2IU0EQx2c2TxJ5CHKihWcqfdl9hBgMAcVO7MUqCAqiJ2Ihp2IhaBPsUgg2lhaHV5gcaKdYaHFXqBA0CSRvkmcX5U5E1HydIewb80ISnsGPbLXLzv83s7P/QZhZ9Xr9NDOfBYBjALAXAL4xc0UIsdZut1fS6fQgKMHJoVQqLUYikccAcJiZHwkhXgohtgaDwQIiHkfE88y8bRhGxrKs9xPdCDAWv0HEcrfbvZBKpb7MVtZsNnd2Op0cIl7UWp+Mx+Nv/ZgRgIg2EPFHLBY7hYjerDh4JqIcAJwDAKWUauP4zSu9Xu/QnzLPwgqFQiiRSPg9yUsp7yIRrTHzZ9u2r/4rc/DOcZzLiHhTKSV9QBMRl6WUT+cFuK57UGv9QWu9xwf8DIVCJyzLej0voFwum+FwuOP3wQdsIuKSlPLZvADXdQ9orZue5+1Hx3FeAEDRtu078wJqtdoZIcQ9pdSi/wuXmDlrmqYVjUa354EQ0ToAvFNKXcdisbjDNM0aADy3bXv5fwAiugIAuX6/H08mkx9HRnJd98jQXRtD7z+oVCq3M5mM/huIiL4CwKpS6trUif6m0Wgc9TzvCTN/F0LcF0K8Mk1zs9Vq7WbmWD6fX89msx4R+S58CABLSqnV6TCNLb1raOkb42mMBarYQsS0lPLTOG4K+Q0QLLtarS4YhrEPANoT4cxM+JBbvwBBieor6fEbMAAAAABJRU5ErkJggg==",_a="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAATCAYAAAByUDbMAAAAAXNSR0IArs4c6QAAAwxJREFUOE+VVE1oHVUU/r47M4lN/YmCiFAQ6qbiokVQBEGooC66svRnJQgqQle+/LRm5r3kZmbuKOlLpAlSqtiNq9KiLkRQlIggiLpQFyIIRUGQKJSYUqK58+7xnUde0Pja0Fnec+53v59zhrj+x7IsD5OcBHAAwLcicrrZbL4LQAZd4/ZDEaFz7giAVhfoGoAyjuPP67p+HECzC7gbQJFl2SWS/wHdArPWmiRJeiAArgKYzbLso+2POeee7jKbAXCbgnrvL1lrg/ZRQeI4PmqMaYUQ/uwym221Wh/fQH6vVBTFU8aYGZJ3hBCKuq4vqi/nSe4zxkxPTU19shPIAKZPqgoAP6k/V0nuTdP0j5sF6vdXVXW3iPzCqqqciBzT1NI0ff9mAYuiOGyMmSN5oReAc+6giLxO8kqn02lMT09/txOoc+6A3gFwJ8lGlmXLXFhY2DU2NrauQQwNDT2vAYjIB8PDw83JycnfB3h0j44LgEMhhJlOp/O2ptloNHZpAD8A+JJklmXZb9ba25MkaQJ4TkTaKysrZ5aWlv5eXFwcXltba5AcA3Dee19Za9ecc/eKiAPwqI7GrXEcT5F8sUv5jPd+3lr7V1EU95M8TfIhAMsADgL4xnt/0lp72Vp7S5Ik4yLyMoA367p+dWtoy7K8j+QcgEdE5JVms3lBJeZ5vj+Kov26TlmWfa9nZVkeJ/kagK+896estT/3htY59xmAi977c9bauqqqxzaN3dgM4+u+b3mePxxFkZo+pKanafqFtTaOouilKIqOMM/zB6Momie5R0TGN1dIH3lWgxaR5RDCG1EUnQDwBAD19h1ddl0tkvMi8qt6uSXTOXcIQFtELhtjxtM0/XFiYmL36OjoSWPMMyGE91ZXV+fa7fa1qqr2hRCUwF6S2vthT+a/o1fKcRyfIJnqEG5sbMxaa6/0e6y1d8VxrPt4XFl778+qNf36/35BWtBLSZJYAMdEpBoZGXlrfX39BZU46JEbgvWLRVE8QLJNUr36lOSEyr/edgxktr1ZZ0pnb6cV+wccUGjIODRIkAAAAABJRU5ErkJggg==",Ca="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACYAAAAgCAYAAAB+ZAqzAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAJqADAAQAAAABAAAAIAAAAAALstX7AAAEgUlEQVRYCc2YWWiUVxTHO0uiVhP3qi20oZIggoK+1FICxaTSjULzkEJ9sNWHSMTRYGfLMk2zTGactmnGVhKopH2opXmwUE0LDaUREVREcCFqwIWCGtHWMhO7JDOZ/s7nvWEy5svWzCQXzpx7zzn3nP937j6WJ6ZYGhoanrFYLCWJROJN+GrcrFSu+pBdQ3YMfqS6uvrWVEJYJtuprq5und1udxP0Hfrax+kfA+B3sVgs6PP5Lo5jO0I9YWB+v/+loaEhD4HewIPud4r6EUCehu6KZ/QroBeolkCbREZBnei0Wq2BysrKk49EY//qAKZWAHodpx4MCpVRAn4UWRPDJMBMC8O9CZDS9y1IxzqBTAD+aNoxyXiETUdHh623t7dUOV2vlDHAfKu+umdEh3EafNxa+roxexcyhp/2eerBgoKCjtLS0niqC/0Vhry2tnYu8+c9ADkRPK+M/4IfstlsH3s8nt+UbEosEAg8G4/HP6DzDuhJ5eQ6IEPMw6+I/492bAATQNnZ2Q7mUAWgVirlAzp8QYbCpP2e7jAdnAwuJ5aDWLvwt1h8EquPWM0DAwNhAWhRX9GFrkAMKLfp0EyG2txud/SRKD2/wWAwhwyWAaqCCE+rKFeJvcXS2Nj4M4JXoDsA+hDEX4N4QBllhIXD4TnRaHQbAD8Cg4xYlwDrpzIf2lxVVfVrRpCYBKmvr9/McP6C+qGtqKjoZSqyc28pLi6OFBYWXuru7n5slaBPW5GMEXc7AT4nYwvgx03nGEPaypBKNtNWxpxjEhUApquSZdyC/v50opvQqkwOKADTuY9xEjzHUO0j5sT2sWRwUjfb+VEdxnGQfe1/7/z4usAqDExo5xdQqcXkrPxBnGb8rEwFJ20Azq7bRSrIWXcfSwXIRE7/DZZhkvOqion9JRO7LhVEJtqcQH7iy2r1gaHNjmAPoD6T4PBlmQAxWgxiP4VcqBVMc60I5IYppT4/P19O+RkpbBtlBG6U4ILJSvqMOxmNRE9Pj1ybZ6So2EM6uIVJvBdszUpwhXqAc/IbToGYNkonJ46dS+pWNXJrJBb1CiNbgCsHkB/ZQgXiJu39AGyn4/B1V+mmhcmNor+/fzsgXDjMU04jtL1s3AcNYCIEwKKsrKzdVPdAS0VGuQN9Mjg42IZ+Wm4a+FlAHJlPcmaugqT8DrUQ5wD6P0UwDEwaUkKh0HwytZNqcsc/yGALdMDr9T4wDCf509TUtJhs7Ibkw5eo7saHM5StTqfzYbLLx4BppaQ6Eom8Dxh5duUpeRR+EGrmtms8cJXclLH0V6CU1V4O5SjDGwDcn5ub2+5wOP5VshHMFJi2IrV2Ui/vQS9kTE743wA+xEPi05qamhvaNpnLI4eXkBMAsmnOU7rL8ABDdhi/Yy6ucYHpYDiycld7m7acEBu0HH4WOgPJ68oGXw6YF+EbIe3/HDI/l87v8TO8JaA3LbqjqcFoCobnNeSSQf23wWhmAuA4FGLYfxrNYCzZlIBphwBcRZZeZchkiGUl22lL5q6R3U6Xy9WnbSfL/wNxuVVL2KmRUgAAAABJRU5ErkJggg==",La={class:"category-items"},Ea=["onClick","onDragstart","onDragend","draggable"],Ia={class:"node-icon"},Sa={key:0,src:Ca,alt:"icon",width:"11px",height:"11px"},Ra={key:0,class:"item-count"},_t=30,Da=mt({__name:"CategoryItem",props:{item:{type:Object,required:!0},level:{type:Number,default:0},selectedNodeId:{type:String,default:null},expandedCategories:{type:Array,default:()=>[]}},emits:["toggle-category","drag-start","drag-end"],setup(H,{emit:M}){const N=H,v=M,I=wt(()=>{var W;return((W=N.item.children)==null?void 0:W.filter(X=>X.object_type==="group"||X.object_type==="plugin"))||[]}),n=W=>W===I.value.length-1,be=W=>W<I.value.length-1,k=W=>{if(!W.children)return 0;let X=0;for(const Be of W.children)Be.object_type==="plugin"?X++:Be.object_type==="group"&&Be.children&&(X+=k(Be));return X},G=()=>({left:`${N.level*_t+8}px`}),o=()=>({left:`${N.level*_t+9.5}px`,width:`${_t-2}px`}),ae=()=>({marginLeft:`${N.level*_t+30}px`}),ye=W=>{v("toggle-category",W)},Ee=(W,X)=>{W.dataTransfer&&(W.dataTransfer.setData("node-type",X.name),W.dataTransfer.effectAllowed="copy"),v("drag-start",X)};return(W,X)=>{const Be=Pt("category-item",!0);return w(),x("div",La,[(w(!0),x(Ve,null,Je(I.value,(T,Ae)=>{var Ie,_e;return w(),x("div",{key:T.id,class:Ke(["node-container",{"is-group":T.object_type==="group","is-plugin":T.object_type==="plugin","is-last":n(Ae),"has-children":T.object_type==="group"&&((Ie=T.children)==null?void 0:Ie.length)}])},[e("div",{class:Ke(["vertical-line",{full:be(Ae),partial:!be(Ae)}]),style:Ze(G())},null,6),e("div",{class:"horizontal-line",style:Ze(o())},null,4),e("div",{class:Ke(T.object_type==="group"?"group-content":"plugin-content"),style:Ze(ae()),onClick:vt(q=>T.object_type==="group"?ye(T.id):null,["stop"]),onDragstart:q=>T.object_type==="plugin"?Ee(q,T):null,onDragend:q=>T.object_type==="plugin"?W.$emit("drag-end"):null,draggable:T.object_type==="plugin"},[e("div",Ia,[T.object_type==="group"?(w(),x("img",Sa)):(w(),x("span",{key:1,class:Ke(["node-icon-indicator",{"is-selected":H.selectedNodeId===T.id}])},null,2))]),e("span",{class:"node-name",style:Ze({color:H.selectedNodeId===T.id?"#fff":"#858585"})},P(T.display_name||T.name),5),T.object_type==="group"&&((_e=T.children)!=null&&_e.length)?(w(),x("span",Ra,P(k(T)),1)):Ne("",!0)],46,Ea),T.object_type==="group"&&H.expandedCategories.includes(T.id)?(w(),Et(Be,{key:0,item:T,level:H.level+1,"selected-node-id":H.selectedNodeId,"expanded-categories":H.expandedCategories,onToggleCategory:X[0]||(X[0]=q=>W.$emit("toggle-category",q)),onDragStart:X[1]||(X[1]=q=>W.$emit("drag-start",q)),onDragEnd:X[2]||(X[2]=q=>W.$emit("drag-end"))},null,8,["item","level","selected-node-id","expanded-categories"])):Ne("",!0)],2)}),128))])}}}),Na=nt(Da,[["__scopeId","data-v-ac1a95ba"]]),Ba={class:"side-nav-container"},Ma={class:"search-container"},Ta={class:"search-input"},Ua={class:"nav-content"},Fa={key:0,class:"empty-state"},Wa=["onClick"],Oa={class:"category-name"},Va={key:0,class:"item-count"},Ga=mt({__name:"Aside",props:{defaultExpanded:{type:Boolean,default:!0}},emits:["categories-loading-state"],setup(H,{emit:M}){const N=H,v=M,I=c(!0),n=c(!1),k=c([]),G=c(null),o=c([]),ae=q=>{if(!k.value.includes(q.id))return 0;let g=0;const i=(B,Q)=>{B.object_type==="group"&&k.value.includes(B.id)&&(g=Math.max(g,Q),B.children&&B.children.forEach(b=>i(b,Q+1)))};return i(q,1),g},ye=q=>{if(!q.children)return 0;let g=0;for(const i of q.children)i.object_type==="plugin"?g++:i.object_type==="group"&&i.children&&(g+=ye(i));return g};ht(async()=>{v("categories-loading-state",!0),I.value=!0;let q=!1;try{const g=await la();g&&g.length>0?(o.value=g,k.value=g.map(i=>i.id),q=!0):g&&g.length===0&&(o.value=[],k.value=[],q=!0)}catch(g){console.error("加载节点分类失败 (Aside.vue):",g)}finally{I.value=!1,q&&v("categories-loading-state",!1)}N.defaultExpanded&&(k.value.length===0&&!q||k.value.length===0&&q&&o.value.length)});const Ee=()=>{n.value=!n.value},W=q=>{const g=k.value.indexOf(q);g===-1?k.value.push(q):k.value.splice(g,1)},X=q=>{q&&(G.value=q.id)},Be=()=>{G.value=null},T=c(""),Ae=wt(()=>{if(!T.value.trim())return o.value;const q=T.value.toLowerCase(),g=i=>i.map(B=>{if(B.object_type==="group"){const Q=g(B.children||[]);return Q.length>0||B.name.toLowerCase().includes(q)?{...B,children:Q}:null}else if(B.object_type==="plugin")return B.name.toLowerCase().includes(q)||B.display_name&&B.display_name.toLowerCase().includes(q)?B:null;return null}).filter(B=>B!==null);return g(o.value)}),Ie=()=>{T.value.trim()},_e=()=>{T.value=""};return(q,g)=>(w(),x("div",Ba,[e("div",{class:Ke(["side-nav",{collapsed:n.value}])},[n.value?Ne("",!0):(w(),x(Ve,{key:0},[e("div",Ma,[e("div",Ta,[g[2]||(g[2]=e("img",{src:ka,alt:"icon",class:"search-icon",width:"14px",height:"14px"},null,-1)),st(e("input",{type:"text","onUpdate:modelValue":g[0]||(g[0]=i=>T.value=i),onInput:Ie,placeholder:"搜索目录"},null,544),[[Ft,T.value]]),T.value?(w(),x("button",{key:0,class:"clear-button",onClick:_e},g[1]||(g[1]=[e("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none"},[e("path",{d:"M18 6L6 18M6 6l12 12",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round"})],-1)]))):Ne("",!0)])]),e("div",Ua,[T.value&&Ae.value.length===0?(w(),x("div",Fa,[g[3]||(g[3]=e("svg",{class:"empty-icon",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none"},[e("path",{d:"M15.5 15.5L19 19",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round"}),e("path",{d:"M5 11a6 6 0 1012 0 6 6 0 00-12 0z",stroke:"currentColor","stroke-width":"2"})],-1)),g[4]||(g[4]=e("p",null,"未找到匹配的节点或目录",-1)),e("button",{class:"clear-search",onClick:_e},"清除搜索")])):(w(!0),x(Ve,{key:1},Je(T.value?Ae.value:o.value,(i,B)=>{var Q;return w(),x("div",{key:i.id,class:"nav-category"},[e("div",{class:"category-header",onClick:b=>W(i.id)},[g[5]||(g[5]=e("span",{class:"category-icon"},[e("img",{src:_a,alt:"icon"})],-1)),e("span",Oa,P(i.name),1),(Q=i.children)!=null&&Q.length?(w(),x("span",Va,P(ye(i)),1)):Ne("",!0)],8,Wa),st(De(Na,{item:i,level:0,"selected-node-id":G.value,"expanded-categories":k.value,"max-expanded-level":ae(i),onToggleCategory:W,onDragStart:X,onDragEnd:Be},null,8,["item","selected-node-id","expanded-categories","max-expanded-level"]),[[bt,k.value.includes(i.id)]])])}),128))])],64))],2),e("div",{class:"fixed-fold_arrow",onClick:Ee},[(w(),x("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",style:Ze({transform:n.value?"rotate(90deg)":"rotate(270deg)"})},g[6]||(g[6]=[e("path",{d:"M7 14l5-5 5 5",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)]),4))])]))}}),qa=nt(Ga,[["__scopeId","data-v-24627af2"]]),Mt="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAAYtJREFUWEfV2dFxgzAMAFDZS2SHbEG26D8cPx2nP9zx3y2aLbIDGcLOqcW9lGBLlgym+cQGPxQsccIAALRtezLGfFpr34dhuOGxWr++78/OuQ/v/ds4jpOZcV8AcAaAu7W2qYWccWg5AcDNe38xXdfhgeYpYlWQC1zgXE1kYFdkxDB57xuD1JrIGM5ae8FH7RtYC0nh0PULTCCncDcldzYH9wLcC8nFrQK3RubgosCtkLm4JLA0UoIjgaWQUhwLqEVqcGygFKnFZQFzkSVw2UAushROBKSQOO6cC69MofiIq9GfUpdTymJRmq+B73NqnDiCYeUI8vk+xZELFxFHkIFU49QRTDyPOFQfeOi/+NCbJJXnqqcZThLmzOGmtKxdnLNwztwUlg2ULCg5Z4llATULac5l5UHtAlTtptosyQiWwBEVh0zmUWBJnAa5CtwCJ0W+ALfESZD/p/WxR+SWOY6zZqr9Ru4wbrlKzaOQsQbmLjjOM7nWAt4VRyCvyyZ6FVwE+dNEx8Ejf4Z4AOhffFtCFuoVAAAAAElFTkSuQmCC",za=["onClick"],Ya=["src","alt"],Pa={class:"toolbar-text"},Tt=1.1,Za=mt({__name:"Toolbar",props:{isRunning:{type:Boolean,default:!1},progress:{type:Number,default:0},graph:{type:Object,required:!0,default:null},isGraphReady:{type:Boolean,default:!1},canvas:{type:null,required:!1}},setup(H){function M(b,K=!1){b||(b=window.location.href);const O={},oe=b.indexOf("?");if(oe!==-1){const y=b.indexOf("#",oe),S=y!==-1?b.substring(oe+1,y):b.substring(oe+1);N(S,O)}if(K){const y=b.indexOf("#");if(y!==-1&&y<b.length-1){const S=b.substring(y+1),r=S.indexOf("?");if(r!==-1){const d=S.substring(r+1);N(d,O)}}}return O}function N(b,K){if(!b)return;const O=b.split("&");for(const oe of O){const[y,S]=oe.split("=",2);y&&(K[decodeURIComponent(y)]=S!==void 0?decodeURIComponent(S):"")}}const v=Ct(),I=Wt(),n=H,be=[{id:1,src:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAUCAYAAACAl21KAAAAAXNSR0IArs4c6QAAAQ9JREFUOE/llKFOxEAURe+j00AQfAceFjTBEAwWAX9AQtDTZGdJp7UIVBUJCDyiCjQsfAC+P4Agm5DOzGWbVCC6Jaliw/P35L1zJyNpmt6IyCmAFQybQPJWrLWe5FhEXgFcAXgDcPcL84TkSETOAWzPl7hsQARwqLUurbXPAEqt9aQPNM+M28xunucHIYTyH4GeAGy0wvs0bZH8TJJkr9NRnucj7/2FiKz2UUh+NQ0nSTLtBA15RksCIilZlp21whdeGkKYVVV1XRRF3XmaMWZdKfUgIk1zC4fkzDl3ZIz5WBJHf6d+ksfOucchGyml9kXkvvlGXgDsDIH8yEzFGLMWx/Gm9z4aAouiyNd1/f4N+DkenOMZoNsAAAAASUVORK5CYII=",import.meta.url).href,alt:"save",text:"保存"},{id:2,src:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAATCAYAAACdkl3yAAAAAXNSR0IArs4c6QAAAU1JREFUOE+d1DFLw1AQB/D/JRFEcHZxdNbVVRB0dXJ1cxHXgi8v2DQ3pDjpVlc3v4E4SBc/gLoJIgT3Uhyq9M48oRpLapO8KSR3P97dvTxi5hUiYlVdB+Dhd4mIhNbam8K7mY/EzC5wU1X7RCRFaDwep1EU3VeFPgCcGWNOqiTMinE70vzjqTGmXREiAC7nz6oNMfMTgDtjzFERbALZfCixql6GYXg4wWpDrp4kSdr5YKIi1ggqw0qhTqez63nedoXm7wNYJaLrUoiZzwEczINUdYGIFgEMG5cWx/GG7/u3AN5FZKsRNI1Ya19qQ2WIa0FtiJmf8xMQuHLcTiZ9bALtBEHw0Gq13orDqA3999O+AshEJPV9/+caUVUdDAb9brc7nHcMvnuUJMkegCsiWppOEJFja+1FJcgFpWm6PBqN1qZvyCzLHnu93mcV6Au5hdZ8w5yWBAAAAABJRU5ErkJggg==",import.meta.url).href,alt:"export",text:"导出"},{id:3,src:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAATCAYAAACdkl3yAAAAAXNSR0IArs4c6QAAAUZJREFUOE+l1LFKxEAQBuB/JsgVclyv1wh2ng9gc1iIb2Bn63VayaWYJEo2G7hWbOxs9QkUBFtbrS19AgURTDJeCkO8M9yS23KZ/WY2mR2C4zLG7DNzAoBrRwoielFVIUcHcRzveJ7n1yFVZSIaAnhyhpoSWmtTAKdNULmvLtVaa8+mceezEFlrLwHsishWW4iSJLkioiMAkYiYNlCFTD9gHARBWa7Tql+tNVJmqqA0TW9V9QDAG4CbRWUURfEQhuHdb1wFWWvfAXRV9YuIvhdBAK5F5GQOMsZsMPMjgNU8z/eiKHp2wKqQP79/GWyuj9pi/zZkDctEZNPlik2djclkspZl2baI3C8FuRyuxzRW1ASNx+Nur9cbElH1PvM8Z2YuR8u68xgxxhwz88VsIlX9BHDoDI1Go5V+vz+YnZCdTufV9/2PHyuOmQ4RpZomAAAAAElFTkSuQmCC",import.meta.url).href,alt:"import",text:"导入"},{id:4,src:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAUCAYAAACAl21KAAAAAXNSR0IArs4c6QAAAWZJREFUOE/tlLFLAzEYxd872m46u+jg4iTuOtgiCqKbg7P+A06l0OTK0SSF4uQ/oLODowiKWAfdRXBwcNDFWbe23GdTpNij1x6dvSW8fMkv+R65R6R8zrkygP1E+VwpdTxqC9NA1to9ADuJ+qXW+mIkyBizS3IxDZhlXkTe6Jx7AbAEIAAQA5AsmxNrXvutGWOKQRDcxXFcCsOwNQUIU4OiKJrN5/NHnU7nJIqir6lBjUZjXURaJIvVavU+FWSMOfAthmF49tt+Ug/ZkQrqneg9g9a65EFJnfR1LMgDetcegP7qfxBgrb31nmitN/yY1Jk9qtfryx5Qq9We/ZjUmUGTfpORIGvtKskHAFtKqZtJEF93zm0CuBaRNa31Y/8dNZvNmW63+yEiTyLig6s9AVYgWSa5ksvl5iuVyvcg2Jxz2yJySnIuy41E5JPkoVLqyq8fSkgR8fm0ICKFcTCSbaXUO8lBdv0AzkZJXg8QSYAAAAAASUVORK5CYII=",import.meta.url).href,alt:"delete",text:"清空"},{id:5,src:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAATCAYAAACdkl3yAAAAAXNSR0IArs4c6QAAAq9JREFUOE+VlF2oTUEUx/9r9t6lxFXIffEV+Ug3H0+IB99SyleEkkIePAnlnNl3t9z9cepe3ngg9+ESykcSijwoIlKkKDc8E4mEOLP3LOZ2jo7jIPM2s9b81pq1/msILVaWZXOstWsAzAAwiog+isgLAFfzPL/MzF+br1HjQZZlU6y1R4hoIYBnInILwGsiaiOiDhGZD+BVURT7oig623j3JyhN0yUAzgHoF5HdYRjebY7a3d3dbow5AGAHgG6t9f66zwCoq6urw/O8OwAuGWO2MXO11ZPrZ2mabgLQB2C/1vqQO3cgStP0AYBPxpjFzJz/DdIA2wOgYq2d1tnZ+ZyyLFslIueLopgeRdHTZkiapldqkZ802phZBUHwWEQehWG4xWVzSkTawzBc1CITZ7cisi4MwwstguwSkSzP85HO8SWA41rryv+C4jierJR69qO2Mx3oCxHtLJfLJxvefw3AhNp+oms5gM8iYqy1m6MoeuRsPT09g6vV6icAKxzoHYCS1vpYU1fG1vYZgNMAnjiQ53m9pVLpvbMx84ggCN5aaxdQkiQPlVI3y+Wy68Jvgv1bjZIkmUdEt40x411GLuJ6Y8wkZrYtClrUin2xhe0ggJVa68lUK5hr7Tat9Ylm5yRJ1uZ5foOZPza1v933/edExE6UA8pO0/Twj9HYaK2d7cT1L0Eys+/7/nUiGmOM6XBDPABi5kFBENwEMFoptbpUKjmlt1zMPMz3/TNEtLQoillRFD2uj8jABWYeWncQkT6lVG+1Wr1fr1ulUhlnrd0gInuJ6BuA4SJyJs/z7c7nl2+kNncbRSQkoqkA3PC+AdAGYIiIfCCio8aYzPO8uUqpi3VYM+jnc+I4nkpEM5RSo5wYrbX9eZ7fa/wZ4jhe7mDuJ/gj6F8FbxDvMgBbvwM3PmOICE4pBgAAAABJRU5ErkJggg==",import.meta.url).href,alt:"zoom-in",text:"放大"},{id:6,src:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAUCAYAAACAl21KAAAAAXNSR0IArs4c6QAAAplJREFUOE+dlE1IVFEUx//nzXtJIIgyjQsLCVoJrVqEK7PaBC5Moq9FC11Glm2Sd+80N9+7zmQRFLWwiKKIomlTqwwypI9VC1u0CGpRWYhGjEmRvY+TV2ZkHM2P7vZ/zu+d87/3/wjLnGw2WzszM1PvOM7U6OjoRD6fj/5VTpWCUmqj4zgnmbmDiBrL9Gkiegzgiuu6I5V9C0Ba6+MAsgDGmfk2Mz+3LGs8iqIay7K2EtF+AK1ElC8UCl0DAwPTJeA8SGt9AUA3M58Ow/C8UurPUmt4nrfbsqxbzDxh23ZLb2/vlKmbA3me12VZ1lVmPiilzC/nm9F8399ERK8AvBFCtM2BcrlcTRRFH5j5upTy1EqQkt7X19ecSCReElG767qPyPf9EwAyYRg2KqV+rBZk6rTWZvqUEKKFtNbDzDwmpTyyFoip7e/vb2fmB0RUb0BfmTknpbxUAvm+3xDHcaoSnEgkAiHE29kN2GjFp/KZmZsN6Dczd0kp75QatdZjABqWmjAMw52ZTOaZ0Xp6etYnk8lfANoMyDSdE0JcLDUqpeqqqqrqKkFRFAVSyo9lk5vb+1Sa6AkRfXNd9/B/eLSPme8GQbDBTHTU+BYEwWal1Pe1wLTWD4mo2nXdXaSUqnYc5z2A+0KI7tWCtNatAJ4C2COEGJp72b7vHyIiY3anEOLmSrBsNrsljuMXs7kbEUIcmI9IEXaGiNLMfDYMQ08pZW5j0fF9fy8RXQNQG8dxUzqdfrcAVHypnQBMeA3kXmX6AZj0bwNwg5m3E9E6Zm6VUn5Z6n+UtG37GBF1zPrWBMAyH2HmcQBDAC5LKV8rpVK2bQ+XYItA5bsMDg46k5OTqTAMfyqlCpV7lsGiZUErmV7M24Y4jnf8BdONMaMxI63zAAAAAElFTkSuQmCC",import.meta.url).href,alt:"zoom-out",text:"缩小"},{id:7,src:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAAAXNSR0IArs4c6QAAAfxJREFUOE+Vk7+LE0EUx79v3d1OEEEQQYsTLI3VidilUVCwipwKeoV/gcURM7NxSGY2iURFQQ4CCt5ZCHYWNlqLhRaCFjbKKiJIIIU/kE12nswRwxKSvcuUM+995vu+7z3CNscYcyrLsn69Xn9bFEpFj8aYiwA2APxh5tNSylfz4ueCxpAHAL4D8ADsLYLNBDWbzcOe571n5sue551j5i/M/IuI1pIk2dfr9YbTymaClFKe7/sHpZRJHMePHUgIUVNKLSmlPs0qr9Ajl5AH7chsrfUdItovhLiQT5gH0lr3iKjvlLr4LUVxHN+11q74vl+uVqsf8iBjzCoR/ajVas+n7o8BeAngoRBijbTWt4joGhGtM/MEYq1NsyzbVEr9dYB2u31oNBqdJaK8HQ52lZnbZIx5DeA4gDcAfud+TQFcEUK49sMNJoDrUz6FAE4w8ztSSu0JguCFgwwGgzPdbjcPc4Cj1tqfURR9zkOUUmEQBE8BHAFQ3pL5H+ZKk1KuTnmx6dovpRT5e631fSIqO4hTPam30+nsTtP0QBRFH3fStUajUbLWflNK9SddK5qPhecoD2u1WkvW2ifD4fB8GIZ6vCLPXGeTJFledEU2iOgkgK8A3AgsM/NtKWVjoRWpVCq7SqXSIyK65BKZ+cY8yLYejWH3ACRSyptFXv4DBbYHOWLuLHQAAAAASUVORK5CYII=",import.meta.url).href,alt:"move",text:"移动"},{id:8,src:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAATCAYAAAByUDbMAAAAAXNSR0IArs4c6QAAAldJREFUOE+tlDtoFFEUhv+zswMGk3WDiFoIoo0YhMVGhVhEURGNqLC9KYQo2CQI456bcLOZGbZSUTE++qCFRiRNQCtT+EC0EbERH51aaBpBduZ377IJmxCXjXrLuf/55p7HfwQtDkkZGxtb53nemiRJvlprv7fSy3KXcRwfIzkA4ACA1U2a9wCmPM+7EgTBp6Wxi2BhGG4CMCkivSS/iMg0ybciMiciG0juB7CX5E8Aw8aYG83ABVilUumpVquPRKQrTVNNkmTCWvtr6d/L5XKP53lXAfQBuKmqg9baHMl8HRbH8do0TV+ISCeAg6r6ulVtisWiVygULgE4R7IsIq4cWodFUXQNwKCI9JVKpSetQPN31tqM7/vTAA43vu2TKIrWA/hMctIYc6odkNO41LLZ7IyI7F6AjY+Pn85kMrdI7jLGPG8HZq1d5fv+XQB75vUkT0oYhrdFpKiq3QDYDuxPGpema/8WY8z2fwG5WAe7B6Cgqlv/B+yy62RHR0f30NCQG8a/Pq5m/SLyUEROlEqlByslubFK0/TNyMjIhDQ68wHAR1XdtRJYGIa9IuLm0qhqNO+AMySvk7xgjKm0A7TW5n3ff0bSzdu2IAh+1GFu1cRxPAWgn+R5Y8zFVsAoijbWmuZKshPAUVWdqXezyR6dvu/fAXCkthkeO89Vq9VZa23apMlns1nnEm34eEBVXUz9LFpBDb8NkyyJSL6xht4BmKttCPeaHTUr+wCeJklydnR09FVzBssuxyAIunO53PE0TQ+JyGYAXQC+1crwkuR9Y8zscmX4DWS98ucU6bk+AAAAAElFTkSuQmCC",import.meta.url).href,alt:"refresh",text:"刷新"}],k=wt(()=>n.isGraphReady?be:be.filter(b=>b.id<=4)),G=c(null),o=c(!1),ae=b=>!!((b===3||b===4)&&n.isRunning);function ye(b,K){if(ae(b)){b===3?I.warning("工作流运行时无法导入 JSON 文件"):b===4&&I.warning("工作流运行时无法删除节点");return}switch(b){case 1:W();break;case 2:Be();break;case 3:T();break;case 4:Ie();break;case 5:_e();break;case 6:q();break;case 7:g();break;case 8:i();break;default:console.warn(`No action defined for id: ${b}`)}}const Ee=()=>{const K=(n.graph.graph||n.graph).serialize(),O={format_version:"2.0",name:v.title?v.title:`workflow-${new Date().toISOString().replace(/[:]/g,"-").slice(0,19)}`,description:"",litegraph:K,nodes:[],links:[]};v.title=O.name;const oe=M(window.location.href);return oe.workflow_id&&(O.id=oe.workflow_id),console.log("graphData.nodes:",K.nodes),K.nodes&&Array.isArray(K.nodes)&&(O.nodes=K.nodes.map(y=>{const S=Object.keys(y.properties),r={};return S.forEach(d=>{var me;const A=(me=y.inputs.find(ke=>ke.name===d))==null?void 0:me.fieldName,J=y.properties[d];r[A]=J}),K.links.find(d=>y.id===d[1]),{uuid:y.flags.uuid,title:y.title||"",name:y.type||"",type:y.type||"",litegraph_id:y.id||0,positionX:y.pos?y.pos[0]:0,positionY:y.pos?y.pos[1]:0,width:y.size?y.size[0]:0,height:y.size?y.size[1]:0,static_input_data:r||{},output_db_id:null}})),K.links&&Array.isArray(K.links)&&(O.links=K.links.map(y=>{var He,Se;const[S,r,d,A,J,me]=y,ke=K.nodes.find(ze=>ze.id===r),Te=K.nodes.find(ze=>ze.id===A);return console.log("sourceNode,targetNode:",ke,Te),{uuid:Ot(),litegraph_id:r,status:1,previous_node_uuid:ke.flags.uuid,next_node_uuid:Te.flags.uuid,output_field_name:(He=Te.inputs[J])==null?void 0:He.fieldName,input_field_name:(Se=ke.outputs[d])==null?void 0:Se.fieldName}})),O};async function W(){try{const b=JSON.stringify(Ee());if(console.log("jsonString:",Ee()),Ee().nodes.length===0){I.warning("当前工作区域为空，不可保存空白工作流！");return}const K=localStorage.getItem("token"),O=await fetch("http://localhost:8000/api/workflow/save",{method:"POST",headers:{uid:"0","quantflow-auth":"2","Content-Type":"application/json",Authorization:`${K}`},body:b});if(O.ok){localStorage.setItem("savedGraph",JSON.stringify(b));const oe=await O.json();if(console.log("data:",oe),I.success(v.owner==="*"?"模版工作流克隆成功！":"保存成功！"),v.workflow_id=oe.data.workflow_id,v.owner="",v.workflow_id){const r=new URL(window.location.href);r.searchParams.set("workflow_id",v.workflow_id),window.history.replaceState({},"",r)}const y={uid:"0","quantflow-auth":"2","Content-Type":"application/json",Authorization:`${K}`},S=await fetch(`http://localhost:8000/api/workflow/query?workflow_id=${oe.data.workflow_id}`,{headers:y});if(S.status===404)return console.log("工作流不存在!"),null;if(S.ok){const r=await S.json();console.log("data:",r),v.title=r.name}}else throw new Error(`请求失败: ${O.status} ${O.statusText}`)}catch(b){console.error("保存图表时出错:",b),I.error("保存失败！")}}const X=b=>{(b.metaKey||b.ctrlKey)&&b.key==="s"&&(b.preventDefault(),W())};function Be(){const b=JSON.stringify(Ee()),K=new Blob([b],{type:"application/json"}),O=URL.createObjectURL(K),oe=document.createElement("a");oe.href=O,oe.download=`graph-export-${new Date().toISOString().slice(0,10)}.json`,document.body.appendChild(oe),oe.click(),document.body.removeChild(oe),URL.revokeObjectURL(O),console.log("图表已成功导出",b)}function T(){var b;if(v.owner==="*"){I.warning("当前为模版,不支持导入覆盖，请先点击保存按钮创建个人工作流后，再进行导入!");return}if(n.isRunning){I.warning("工作流运行时无法导入 JSON 文件");return}(b=G.value)==null||b.click()}async function Ae(b){var oe;if(n.isRunning){I.warning("工作流运行时无法导入 JSON 文件");return}console.log("开始导入图形");const K=b.target;if(!((oe=K.files)!=null&&oe.length))return;const O=K.files[0];try{const y=await O.text(),S=JSON.parse(y).litegraph,r=n.graph.graph||n.graph,d=n.canvas;if(!r||!d)return console.error("Graph or Canvas not initialized"),!1;r.clear();try{r.configure(S),r.nodes.forEach(Z=>{Z._fullTitle?Z.title=Z.truncateTitle(Z._fullTitle):(Z._fullTitle=Z.title||"",Z.title=Z.truncateTitle(Z.title||""))}),d.setZoom(1),d.ds.offset=[0,0];let A=1/0,J=-1/0,me=1/0,ke=-1/0;r.nodes.forEach(Z=>{A=Math.min(A,Z.pos[0]),J=Math.max(J,Z.pos[0]+Z.size[0]),me=Math.min(me,Z.pos[1]),ke=Math.max(ke,Z.pos[1]+Z.size[1])});const Te=J-A,He=ke-me,Se=A+Te/2,ze=me+He/2,rt=d.canvas.getBoundingClientRect(),qe=rt.width,lt=rt.height;return d.ds.offset=[qe/2-Se,lt/2-ze],r.setDirtyCanvas(!0),d.draw(!0,!0),I.success("导入成功！"),K.value="",!0}catch(A){return console.error("Error configuring graph:",A),I.error("导入失败：图形数据格式不正确"),!1}}catch(y){return console.error("Import failed:",y),I.error("导入失败，请检查文件格式是否正确"),!1}}function Ie(){if(v.owner==="*"){I.warning("当前为模版,不支持清空!");return}if(n.isRunning){I.warning("工作流运行时无法删除节点");return}try{const b=n.graph.graph||n.graph;confirm("确定要清空当前图表吗？此操作不可撤销。")&&(b.clear(),b.setDirtyCanvas(!0,!0),console.log("图表已清空"),I.success("图表已清空"))}catch(b){console.error("清空图表时出错:",b)}}function _e(){if(n.canvas)try{const b=n.canvas.ds.scale||1;n.canvas.setZoom(b*Tt),n.canvas.draw(!0,!0)}catch(b){console.error("Zoom in error:",b)}}function q(){if(n.canvas)try{const b=n.canvas.ds.scale||1;n.canvas.setZoom(b/Tt),n.canvas.draw(!0,!0)}catch(b){console.error("Zoom out error:",b)}}function g(){n.canvas&&(o.value=!o.value,n.canvas.drag_mode=o.value,o.value?n.canvas.canvas.style.cursor="grab":n.canvas.canvas.style.cursor="default")}function i(){if(n.canvas)try{n.canvas.setZoom(1),n.canvas.ds.offset=[0,0],n.canvas.draw(!0,!0)}catch(b){console.error("Reset view error:",b)}}const B=()=>{o.value&&(n.canvas.canvas.style.cursor="grabbing")},Q=()=>{o.value&&(n.canvas.canvas.style.cursor="grab")};return ht(()=>{window.addEventListener("keydown",X),n.canvas&&(n.canvas.canvas.addEventListener("mousedown",B),n.canvas.canvas.addEventListener("mouseup",Q))}),yt(()=>{window.removeEventListener("keydown",X),n.canvas&&(n.canvas.canvas.removeEventListener("mousedown",B),n.canvas.canvas.removeEventListener("mouseup",Q))}),(b,K)=>(w(),x(Ve,null,[(w(!0),x(Ve,null,Je(k.value,O=>(w(),x("div",{class:Ke(["toolbar-item",{disabled:ae(O.id)}]),key:O.id,onClick:oe=>ye(O.id,O.text)},[e("img",{src:O.src,alt:O.alt,width:"17px",height:"19px"},null,8,Ya),e("div",Pa,P(O.text),1)],10,za))),128)),e("input",{type:"file",ref_key:"fileInput",ref:G,style:{display:"none"},accept:".json",onChange:Ae},null,544)],64))}}),Qa=nt(Za,[["__scopeId","data-v-d3f84579"]]),Ja={class:"table-container"},Ha={class:"table-wrapper"},Xa={key:0,class:"fixed-column"},$a={class:"scroll-area"},ja={style:{display:"flex",position:"sticky",top:"0","z-index":"99",background:"#292C38",width:"max-content"}},Ka=["title"],eo={__name:"TablesScroll",props:{tableData:{type:Array,required:!0},headers:{type:Array,required:!0},isFixed:{type:Boolean,default:!0},columnWidth:{type:[Number,String],default:0},customHeader:{type:Array,default:[]}},setup(H){Zt(k=>({"25f58ff8":`${H.columnWidth}px`}));const M=H,N=c(null),v=c(null),I=k=>!isNaN(k)&&Number.isFinite(Number(k))&&String(k).includes(".")?Number(k).toFixed(2):k,n=k=>k?k.replace(/(\d{4})(\d{2})(\d{2})/g,"$1-$2-$3"):"-",be=()=>{N.value&&v.value&&M.isFixed&&(console.log("-------------"),N.value.addEventListener("scroll",()=>{console.log(N.value.scrollTop),v.value.style.top=`-${N.value.scrollTop}px`}))};return ht(()=>{be()}),(k,G)=>(w(),x("div",Ja,[e("div",Ha,[M.isFixed?(w(),x("div",Xa,[e("div",{class:"fixed-header",style:Ze({width:`${H.customHeader.length>0?H.customHeader[0].width:H.columnWidth}px`})},P(H.customHeader.length>0?H.customHeader[0].key:M.headers[0]),5),e("div",{class:"fixed-body",ref_key:"fixedBody",ref:v},[(w(!0),x(Ve,null,Je(M.tableData,(o,ae)=>(w(),x("div",{key:ae,class:"fixed-cell",style:Ze({width:`${H.customHeader.length>0?H.customHeader[0].width:H.columnWidth}px`})},P(n(o[M.headers[0]])),5))),128))],512)])):Ne("",!0),e("div",$a,[e("div",{class:"scroll-body",ref_key:"scrollBody",ref:N},[e("div",ja,[(w(!0),x(Ve,null,Je([...M.headers].splice(M.isFixed?1:0),(o,ae)=>(w(),x("div",{key:ae,class:"header-cell",style:Ze({width:`${H.customHeader.length>0?H.customHeader[ae+1].width:H.columnWidth}px`}),title:o},P(H.customHeader.length>0?H.customHeader[ae+1].key:o),13,Ka))),128))]),(w(!0),x(Ve,null,Je(M.tableData,(o,ae)=>(w(),x("div",{key:ae,class:"table-row"},[(w(!0),x(Ve,null,Je([...M.headers].splice(M.isFixed?1:0),(ye,Ee)=>(w(),x("div",{key:Ee,class:"table-cell",style:Ze({width:`${H.customHeader.length>0?H.customHeader[Ee+1].width:H.columnWidth}px`})},P(I(o[ye])),5))),128))]))),128))],512)])])]))}},Ut=nt(eo,[["__scopeId","data-v-f22c5882"]]),to={class:"factor-deep"},ao={class:"factor-deep-header"},oo={class:"factor-item"},so={class:"factor-item-title"},no={class:"factor-item-content"},lo={style:{display:"flex","justify-content":"space-between"}},io={class:"factor-deep-content",style:{"flex-shrink":"0","flex-basis":"368px","margin-top":"20px"}},ro={class:"data-card",style:{height:"100%"}},co={class:"data-item"},uo={class:"data-item"},fo={class:"data-item"},ho={class:"data-item"},go={class:"value"},po={key:0,class:"value"},vo={key:1,class:"label"},Ao={class:"factor-item",style:{margin:"0","margin-top":"20px",width:"auto","flex-shrink":"0","flex-grow":"1","flex-basis":"460px","margin-left":"20px"}},mo={class:"factor-item-title"},yo={class:"factor-item-content"},wo={style:{width:"100%",height:"400px"}},bo={class:"factor-item",style:{"margin-left":"20px"}},xo={class:"factor-item-title"},ko={class:"factor-item-content"},_o={style:{width:"100%",height:"400px"}},Co={class:"factor-item-container"},Lo={class:"factor-item"},Eo={class:"factor-item-title"},Io={class:"factor-item-content"},So={class:"factor-item"},Ro={class:"factor-item-title"},Do={class:"factor-item-content"},No={class:"factor-item"},Bo={class:"factor-item-title"},Mo={class:"factor-item-content"},To={class:"factor-item"},Uo={class:"factor-item-title"},Fo={class:"factor-item-content"},Wo={class:"factor-item"},Oo={class:"factor-item-title"},Vo={class:"factor-item-content"},Go={class:"factor-item"},qo={class:"factor-item-title"},zo={class:"factor-item-content"},Yo={class:"factor-item"},Po={class:"factor-item-title"},Zo={class:"factor-item-content"},Qo={class:"factor-item"},Jo={class:"factor-item-title"},Ho={class:"factor-item-content"},Xo={class:"factor-item"},$o={class:"factor-item-title"},jo={class:"factor-item-content"},Ko={class:"factor-item"},es={class:"factor-item-title"},ts={class:"factor-item-content"},Re="#7d7d7d",as=mt({__name:"FactorDeep",props:{factorId:{type:String,required:!0},taskId:{type:String,requfactorIdired:!0},into:{type:Boolean,required:!0,default:!1}},setup(H){const M=t=>"",N=t=>t,v=(t,we)=>{if(typeof t=="number")return parseFloat(t.toFixed(we));if(typeof t=="string"&&!isNaN(t)&&t.includes("."))return parseFloat(Number(t).toFixed(we));if(Array.isArray(t))return t.map(U=>v(U));if(typeof t=="object"&&t!==null){const U={};for(const V in t)t.hasOwnProperty(V)&&(U[V]=v(t[V]));return U}return t},I=t=>!isNaN(t)&&Number.isFinite(Number(t))&&String(t).includes(".")?Number(t).toFixed(2):t,n=H;Qt();const be=c(!1),k=t=>{const we=parseFloat(t);return we>0?"#ff4851":we<0?"#2fae34":"#333"},G=c({annualized_ratio:0,maximum_drawdown:0,return_ratio:0,sharpe_ratio:0}),o=async()=>{var U,V,le,te,ce,de,ue,fe;const{json:t,httpController:we}=await ia(n.taskId||M());G.value={annualized_ratio:N((V=(U=t==null?void 0:t.data)==null?void 0:U.one_group_data)==null?void 0:V.annualized_ratio),maximum_drawdown:N((te=(le=t==null?void 0:t.data)==null?void 0:le.one_group_data)==null?void 0:te.maximum_drawdown),return_ratio:N((de=(ce=t==null?void 0:t.data)==null?void 0:ce.one_group_data)==null?void 0:de.return_ratio),sharpe_ratio:N((fe=(ue=t==null?void 0:t.data)==null?void 0:ue.one_group_data)==null?void 0:fe.sharpe_ratio)}};let ae=null;const ye=c(null),Ee=c(""),W=c({}),X=async()=>{var V,le,te,ce,de,ue,fe,pe,ve,j,z,C,R,p,h,u;const{json:t,httpController:we}=await ra((n==null?void 0:n.taskId)||M());if(t.code==="200"&&(Ee.value=(le=(V=t==null?void 0:t.data)==null?void 0:V.return_chart)==null?void 0:le.title,W.value=(te=t==null?void 0:t.data)==null?void 0:te.return_chart,ye.value)){if(ae=at(ye.value),Object.keys(W.value).length==0)return;var U={code:"200",message:"查询成功",data:{task_id:"bbf3bbc030a94849bea4810058eec027",return_chart:{title:"python 5 groups return",x:[{name:"date",data:(de=(ce=W.value)==null?void 0:ce.x[0])==null?void 0:de.data}],y:(ue=W.value)==null?void 0:ue.y}}};const m=(j=(ve=(pe=(fe=U==null?void 0:U.data)==null?void 0:fe.return_chart)==null?void 0:pe.x[0])==null?void 0:ve.data)==null?void 0:j.map(F=>F.split(" ")[0]),D=(R=(C=(z=U==null?void 0:U.data)==null?void 0:z.return_chart)==null?void 0:C.y)==null?void 0:R.map(F=>F.name),L=["#FF6B6B","#4ECDC4","#45B7D1","#96CEB4","#FFCC5C","#FF6F69","#88D8B0","#6C88C4","#FFA07A","#98FB98","#87CEEB","#DDA0DD"].sort(()=>Math.random()-.5),ge=(u=(h=(p=U==null?void 0:U.data)==null?void 0:p.return_chart)==null?void 0:h.y)==null?void 0:u.map((F,re)=>{var Qe;return(Qe=F==null?void 0:F.data)==null?void 0:Qe.map((gt,pt)=>({value:[pt,re,Number(gt).toFixed(2)],itemStyle:{color:L[re%L.length]}}))}).flat(),ie={title:{left:"center"},tooltip:{show:!1,axisPointer:{show:!1}},grid3D:{viewControl:{projection:"orthographic",autoRotate:!0,distance:200,beta:45,alpha:25},boxWidth:70,boxHeight:70,boxDepth:200,light:{main:{intensity:1.2}},top:"0%",bottom:"10%"},xAxis3D:{type:"category",data:m==null?void 0:m.map(F=>F.split(" ")[0]),name:"",axisLabel:{interval:Math.floor((m==null?void 0:m.length)/10),rotate:45,margin:20,textStyle:{fontSize:10,color:Re}},nameTextStyle:{fontSize:14,margin:30}},yAxis3D:{type:"category",data:D,name:"",axisLabel:{color:Re}},zAxis3D:{type:"value",name:"",axisLabel:{color:Re}},dataZoom:[{type:"slider",xAxisIndex:0,start:0,end:100},{type:"inside",xAxisIndex:0,start:0,end:100}],series:[{type:"bar3D",data:ge,shading:"lambert",label:{show:!1}}]};ae.setOption(ie)}},Be=c("分组收益"),T=c([]),Ae=c({}),Ie=async()=>{var U,V;const{json:t,httpController:we}=await ca((n==null?void 0:n.taskId)||M());t.code==="200"&&(Ae.value=(U=t==null?void 0:t.data)==null?void 0:U.group_return_analysis,T.value=Object.keys((V=t==null?void 0:t.data)==null?void 0:V.group_return_analysis[0]))},_e=c("最新数据"),q=c([]),g=c({}),i=c([{key:"时间",width:130},{key:"股票代码",width:100},{key:"名称",width:100},{key:"因子值",width:80}]),B=async()=>{var U,V;const{json:t,httpController:we}=await da((n==null?void 0:n.taskId)||M());t.code==="200"&&(g.value=(U=t==null?void 0:t.data)==null?void 0:U.last_date_top_factor,q.value=Object.keys((V=t==null?void 0:t.data)==null?void 0:V.last_date_top_factor[0]))};let Q=null;const b=c(null),K=c(""),O=c({}),oe=async()=>{var V,le,te,ce,de,ue,fe,pe,ve;const{json:t,httpController:we}=await ua((n==null?void 0:n.taskId)||M());if(t.code==="200"&&(K.value=(le=(V=t==null?void 0:t.data)==null?void 0:V.ic_decay_chart)==null?void 0:le.title,console.log("还需处理-----------------",v((te=t==null?void 0:t.data)==null?void 0:te.ic_decay_chart,3)),O.value=(ce=t==null?void 0:t.data)==null?void 0:ce.ic_decay_chart,b.value)){if(Q=at(b.value),Object.keys(O.value).length==0)return;const j=(ue=(de=O.value)==null?void 0:de.x[0])==null?void 0:ue.data,z=(ve=(pe=(fe=O.value)==null?void 0:fe.y[0])==null?void 0:pe.data)==null?void 0:ve.map((C,R)=>({value:C,itemStyle:{color:C>0?"#ff0000":"#3498db"}}));var U={title:{},tooltip:{trigger:"axis",formatter:function(C){return C.map(R=>{let p=R.value;const h=R.color;if(Array.isArray(p)){const u=p.map(m=>m==null||isNaN(m)?"--":Number(m).toFixed(4));return`<span style="color:${h}">${R.seriesName}</span>: ${u[0]}, ${u[1]}`}return p=p==null||isNaN(p)?"--":Number(p).toFixed(4),`<span style="color:${h}">${R.seriesName}</span>: ${p}`}).join("<br/>")}},grid:{left:"3%",right:"4%",bottom:"15%",top:"15%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:0,start:0,end:100}],xAxis:[{type:"category",data:j,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(C,R){const p=j.length,h=40,u=Q.getWidth(),m=Math.floor(u/h),D=Math.floor((p-1)/(m-1));return(p-1-C)%D===0}},show:!1},{type:"category",data:j,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(C,R){const p=j.length,h=40,u=Q.getWidth(),m=Math.floor(u/h),D=Math.floor((p-1)/(m-1));return(p-1-C)%D===0}},position:"bottom"}],yAxis:[{type:"value",splitLine:{show:!0,lineStyle:{type:"dashed",color:Re}}}],series:[{name:"IC值",type:"bar",data:z,label:{show:!1,position:"bottom",formatter:function(C){return C.value?C.value.toFixed(2):""}}},{name:"IC趋势",type:"line",smooth:!1,showSymbol:!1,data:O.value.y[0].data,lineStyle:{color:"#ff0000",width:0}}]};Q.setOption(U)}};let y=null;const S=c(null),r=c(""),d=c({}),A=async()=>{var V,le,te,ce,de,ue,fe,pe,ve;const{json:t,httpController:we}=await fa((n==null?void 0:n.taskId)||M());if(t.code==="200"&&(console.log("json",t),r.value=(le=(V=t==null?void 0:t.data)==null?void 0:V.ic_den_chart)==null?void 0:le.title,d.value=(te=t==null?void 0:t.data)==null?void 0:te.ic_den_chart,S.value)){if(y=at(S.value),Object.keys(d.value).length==0)return;var U={title:{},tooltip:{trigger:"axis",formatter:function(j){return j.map(z=>{let C=z.value;const R=z.color;if(Array.isArray(C)){const p=C.map(h=>h==null||isNaN(h)?"--":Number(h).toFixed(4));return`<span style="color:${R}">${z.seriesName}</span>: ${p[0]}, ${p[1]}`}return C=C==null||isNaN(C)?"--":Number(C).toFixed(4),`<span style="color:${R}">${z.seriesName}</span>: ${C}`}).join("<br/>")}},legend:{data:["Histogram","Density Curve"],top:"0px",textStyle:{color:Re}},grid:{left:"3%",right:"4%",bottom:"15%",top:"15%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:0,start:0,end:100},{type:"inside",xAxisIndex:0,start:0}],xAxis:{type:"value",boundaryGap:!1,axisLine:{onZero:!1},splitLine:{show:!0,lineStyle:{type:"dashed",color:Re}}},yAxis:{type:"value",axisLine:{onZero:!1,show:!1},axisTick:{show:!1},position:"left",splitLine:{show:!0,lineStyle:{type:"dashed",color:Re}}},series:[{name:"Histogram",type:"bar",data:(ue=(de=(ce=d.value)==null?void 0:ce.y[0])==null?void 0:de.data)==null?void 0:ue.map((j,z)=>{var C,R;return[(R=(C=d.value)==null?void 0:C.x[0])==null?void 0:R.data[z],j]}),itemStyle:{color:"rgba(135, 206, 235, 0.6)"}},{name:"Density Curve",type:"line",smooth:!0,data:(ve=(pe=(fe=d.value)==null?void 0:fe.y[1])==null?void 0:pe.data)==null?void 0:ve.map((j,z)=>{var C,R;return[(R=(C=d.value)==null?void 0:C.x[0])==null?void 0:R.data[z],j]}),itemStyle:{color:"#1E90FF"},lineStyle:{width:2}}]};y.setOption(U)}};let J=null;const me=c(null),ke=c(""),Te=c({}),He=async()=>{var V,le,te,ce,de,ue,fe,pe,ve,j,z,C,R;const{json:t,httpController:we}=await ha((n==null?void 0:n.taskId)||M());if(t.code==="200"&&(ke.value=(le=(V=t==null?void 0:t.data)==null?void 0:V.ic_seq_chart)==null?void 0:le.title,Te.value=(te=t==null?void 0:t.data)==null?void 0:te.ic_seq_chart,console.log("echartsRefICSequenceData",Te.value),me.value)){if(J=at(me.value),Object.keys(Te.value).length==0)return;var U={title:{left:"center"},tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter:function(p){return p.map(h=>{let u=h.value;const m=h.color;if(Array.isArray(u)){const D=u.map(L=>L==null||isNaN(L)?"--":Number(L).toFixed(4));return`<span style="color:${m}">${h.seriesName}</span>: ${D[0]}, ${D[1]}`}return u=u==null||isNaN(u)?"--":Number(u).toFixed(4),`<span style="color:${m}">${h.seriesName}</span>: ${u}`}).join("<br/>")}},legend:{data:(de=(ce=Te.value)==null?void 0:ce.y)==null?void 0:de.map(p=>p.name),top:"0px",selected:{IC:!0,Cum_IC:!1},textStyle:{color:Re}},grid:{left:"3%",right:"4%",bottom:"2%",top:"15%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:[0,1],start:0,end:100}],xAxis:[{type:"category",data:(pe=(fe=(ue=Te.value)==null?void 0:ue.x[0])==null?void 0:fe.data)==null?void 0:pe.map(p=>p.split(" ")[0]),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(p,h){var ie,F,re;const u=(re=(F=(ie=Te.value)==null?void 0:ie.x[0])==null?void 0:F.data)==null?void 0:re.length,m=40,D=J.getWidth(),L=Math.floor(D/m),ge=Math.floor((u-1)/(L-1));return(u-1-p)%ge===0}},show:!1},{type:"category",data:(z=(j=(ve=Te.value)==null?void 0:ve.x[0])==null?void 0:j.data)==null?void 0:z.map(p=>p.split(" ")[0]),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(p,h){var ie,F,re;const u=(re=(F=(ie=Te.value)==null?void 0:ie.x[0])==null?void 0:F.data)==null?void 0:re.length,m=40,D=J.getWidth(),L=Math.floor(D/m),ge=Math.floor((u-1)/(L-1));return(u-1-p)%ge===0}},position:"bottom"}],yAxis:{type:"value",axisLabel:{interval:1e3},splitLine:{show:!0,lineStyle:{type:"dashed",color:Re}}},series:(R=(C=Te.value)==null?void 0:C.y)==null?void 0:R.map(p=>({name:p.name,data:p.data,type:p.name==="IC"?"bar":"line",itemStyle:{color:p.name==="IC"?"#3498db":"#e74c3c"},label:{show:!1,position:"top",formatter:function(h){return h.data.toFixed(3)}}}))};J.setOption(U)}};let Se=null;const ze=c(null),rt=c(""),qe=c({}),lt=async()=>{var V,le,te,ce,de,ue,fe,pe,ve,j,z,C,R;const{json:t,httpController:we}=await ga((n==null?void 0:n.taskId)||M());if(t.code==="200"&&(rt.value=(le=(V=t==null?void 0:t.data)==null?void 0:V.ic_self_correlation_chart)==null?void 0:le.title,qe.value=(te=t==null?void 0:t.data)==null?void 0:te.ic_self_correlation_chart,ze.value)){if(Se=at(ze.value),Object.keys(qe.value).length==0)return;var U={title:{},tooltip:{trigger:"axis",formatter:function(p){return p.map(h=>{let u=h.value;const m=h.color;if(Array.isArray(u)){const D=u.map(L=>L==null||isNaN(L)?"--":Number(L).toFixed(4));return`<span style="color:${m}">${h.seriesName}</span>: ${D[0]}, ${D[1]}`}return u=u==null||isNaN(u)?"--":Number(u).toFixed(4),`<span style="color:${m}">${h.seriesName}</span>: ${u}`}).join("<br/>")}},legend:{data:["自相关系数","下限(95%置信区间)","上限(95%置信区间)"],top:"0px",textStyle:{color:Re}},grid:{left:"3%",right:"4%",bottom:"15%",top:"15%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:[0,1],start:0,end:100}],xAxis:[{type:"category",data:(de=(ce=qe.value)==null?void 0:ce.x[0])==null?void 0:de.data,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(p,h){var ie,F,re;const u=(re=(F=(ie=qe.value)==null?void 0:ie.x[0])==null?void 0:F.data)==null?void 0:re.length,m=40,D=Se.getWidth(),L=Math.floor(D/m),ge=Math.floor((u-1)/(L-1));return(u-1-p)%ge===0}},show:!1},{type:"category",data:(fe=(ue=qe.value)==null?void 0:ue.x[0])==null?void 0:fe.data,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(p,h){var ie,F,re;const u=(re=(F=(ie=qe.value)==null?void 0:ie.x[0])==null?void 0:F.data)==null?void 0:re.length,m=40,D=Se.getWidth(),L=Math.floor(D/m),ge=Math.floor((u-1)/(L-1));return(u-1-p)%ge===0}},position:"bottom"}],yAxis:{type:"value",min:-1,max:1,interval:.2,splitLine:{show:!0,lineStyle:{type:"dashed",color:Re}}},series:[{name:"自相关系数",type:"line",data:(ve=(pe=qe.value)==null?void 0:pe.y[0])==null?void 0:ve.data,symbol:"circle",symbolSize:8,lineStyle:{width:2},itemStyle:{color:"#3498db"},label:{show:!1,position:"top",formatter:function(p){return p.value.toFixed(3)}}},{name:"下限(95%置信区间)",type:"line",data:(z=(j=qe.value)==null?void 0:j.y[1])==null?void 0:z.data,lineStyle:{type:"dashed",width:1},itemStyle:{color:"#95a5a6"},symbol:"none"},{name:"上限(95%置信区间)",type:"line",data:(R=(C=qe.value)==null?void 0:C.y[2])==null?void 0:R.data,lineStyle:{type:"dashed",width:1},itemStyle:{color:"#95a5a6"},symbol:"none"}]};Se.setOption(U)}};let Z=null;const Xe=c(null),et=c(""),Ue=c({}),it=async()=>{var V,le,te,ce,de,ue,fe,pe,ve,j;const{json:t,httpController:we}=await pa((n==null?void 0:n.taskId)||M());if(t.code==="200"&&(et.value=(le=(V=t==null?void 0:t.data)==null?void 0:V.rank_ic_decay_chart)==null?void 0:le.title,Ue.value=(te=t==null?void 0:t.data)==null?void 0:te.rank_ic_decay_chart,Xe.value)){if(Z=at(Xe.value),Object.keys(Ue.value).length==0)return;const z=(de=(ce=Ue.value)==null?void 0:ce.x[0])==null?void 0:de.data,C=(pe=(fe=(ue=Ue.value)==null?void 0:ue.y[0])==null?void 0:fe.data)==null?void 0:pe.map((R,p)=>({value:R,itemStyle:{color:R>0?"#ff0000":"#3498db"}}));var U={title:{},tooltip:{trigger:"axis",formatter:function(R){return R.map(p=>{let h=p.value;const u=p.color;if(Array.isArray(h)){const m=h.map(D=>D==null||isNaN(D)?"--":Number(D).toFixed(4));return`<span style="color:${u}">${p.seriesName}</span>: ${m[0]}, ${m[1]}`}return h=h==null||isNaN(h)?"--":Number(h).toFixed(4),`<span style="color:${u}">${p.seriesName}</span>: ${h}`}).join("<br/>")}},grid:{left:"3%",right:"4%",bottom:"15%",top:"15%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:0,start:0,end:100}],xAxis:[{type:"category",data:z,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(R,p){const h=z.length,u=40,m=Z.getWidth(),D=Math.floor(m/u),L=Math.floor((h-1)/(D-1));return(h-1-R)%L===0}},show:!1},{type:"category",data:z,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(R,p){const h=z.length,u=40,m=Z.getWidth(),D=Math.floor(m/u),L=Math.floor((h-1)/(D-1));return(h-1-R)%L===0}},position:"bottom"}],yAxis:[{type:"value",splitLine:{show:!0,lineStyle:{type:"dashed",color:Re}}}],series:[{name:"IC值",type:"bar",data:C,label:{show:!1,position:"bottom",formatter:function(R){return R.value?R.value.toFixed(3):""}}},{name:"IC趋势",type:"line",smooth:!1,showSymbol:!1,data:(j=(ve=Ue.value)==null?void 0:ve.y[0])==null?void 0:j.data,lineStyle:{color:"#000000",width:0}}]};Z.setOption(U)}};let Ye=null;const a=c(null),l=c(""),s=c({}),f=async()=>{var V,le,te,ce,de,ue,fe,pe,ve;const{json:t,httpController:we}=await va((n==null?void 0:n.taskId)||M());if(t.code==="200"&&(l.value=(le=(V=t==null?void 0:t.data)==null?void 0:V.rank_ic_den_chart)==null?void 0:le.title,s.value=(te=t==null?void 0:t.data)==null?void 0:te.rank_ic_den_chart,a.value)){if(Ye=at(a.value),Object.keys(s.value).length==0)return;var U={title:{},tooltip:{trigger:"axis",formatter:function(j){return j.map(z=>{let C=z.value;const R=z.color;if(Array.isArray(C)){const p=C.map(h=>h==null||isNaN(h)?"--":Number(h).toFixed(4));return`<span style="color:${R}">${z.seriesName}</span>: ${p[0]}, ${p[1]}`}return C=C==null||isNaN(C)?"--":Number(C).toFixed(4),`<span style="color:${R}">${z.seriesName}</span>: ${C}`}).join("<br/>")}},legend:{data:["Histogram","Density Curve"],top:"0px",textStyle:{color:Re}},grid:{left:"3%",right:"4%",bottom:"15%",top:"15%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:0,start:0,end:100},{type:"inside",xAxisIndex:0,zoomOnMouseWheel:!1,start:0}],xAxis:{type:"value",boundaryGap:!1,axisLine:{onZero:!1},axisLabel:{formatter:"{value}"},splitLine:{show:!0,lineStyle:{type:"dashed",color:Re}}},yAxis:{type:"value",axisLine:{onZero:!1,show:!1},axisTick:{show:!1},position:"left",splitLine:{show:!0,lineStyle:{type:"dashed",color:Re}}},series:[{name:"Histogram",type:"bar",data:(ue=(de=(ce=s.value)==null?void 0:ce.y[0])==null?void 0:de.data)==null?void 0:ue.map((j,z)=>{var C,R;return[(R=(C=s.value)==null?void 0:C.x[0])==null?void 0:R.data[z],j]}),itemStyle:{color:"rgba(135, 206, 235, 0.6)"}},{name:"Density Curve",type:"line",smooth:!0,data:(ve=(pe=(fe=s.value)==null?void 0:fe.y[1])==null?void 0:pe.data)==null?void 0:ve.map((j,z)=>{var C,R;return[(R=(C=s.value)==null?void 0:C.x[0])==null?void 0:R.data[z],j]}),itemStyle:{color:"#1E90FF"},lineStyle:{width:2}}]};Ye.setOption(U)}};let _=null;const E=c(null),se=c(""),$=c({}),he=async()=>{var V,le,te,ce,de,ue,fe,pe,ve,j,z,C,R;const{json:t,httpController:we}=await Aa(n.taskId||M());if(t.code==="200"&&(se.value=(le=(V=t==null?void 0:t.data)==null?void 0:V.rank_ic_seq_chart)==null?void 0:le.title,$.value=(te=t==null?void 0:t.data)==null?void 0:te.rank_ic_seq_chart,E.value)){if(_=at(E.value),Object.keys($.value).length==0)return;var U={title:{left:"center"},tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter:function(p){return p.map(h=>{let u=h.value;const m=h.color;if(Array.isArray(u)){const D=u.map(L=>L==null||isNaN(L)?"--":Number(L).toFixed(4));return`<span style="color:${m}">${h.seriesName}</span>: ${D[0]}, ${D[1]}`}return u=u==null||isNaN(u)?"--":Number(u).toFixed(4),`<span style="color:${m}">${h.seriesName}</span>: ${u}`}).join("<br/>")}},legend:{data:(de=(ce=$.value)==null?void 0:ce.y)==null?void 0:de.map(p=>p.name),top:"0px",selected:{IC:!0,Cum_IC:!1},textStyle:{color:Re}},grid:{left:"3%",right:"4%",bottom:"1%",top:"15%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:[0,1],start:0,end:100}],xAxis:[{type:"category",data:(pe=(fe=(ue=$.value)==null?void 0:ue.x[0])==null?void 0:fe.data)==null?void 0:pe.map(p=>p.split(" ")[0]),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(p,h){var ie,F,re;const u=(re=(F=(ie=$.value)==null?void 0:ie.x[0])==null?void 0:F.data)==null?void 0:re.length,m=40,D=_.getWidth(),L=Math.floor(D/m),ge=Math.floor((u-1)/(L-1));return(u-1-p)%ge===0}},show:!1},{type:"category",data:(z=(j=(ve=$.value)==null?void 0:ve.x[0])==null?void 0:j.data)==null?void 0:z.map(p=>p.split(" ")[0]),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(p,h){var ie,F,re;const u=(re=(F=(ie=$.value)==null?void 0:ie.x[0])==null?void 0:F.data)==null?void 0:re.length,m=40,D=_.getWidth(),L=Math.floor(D/m),ge=Math.floor((u-1)/(L-1));return(u-1-p)%ge===0}},position:"bottom"}],yAxis:{type:"value",splitLine:{show:!0,lineStyle:{type:"dashed",color:Re}}},series:(R=(C=$.value)==null?void 0:C.y)==null?void 0:R.map(p=>({name:p.name,data:p.data,type:p.name==="Rank_IC"?"bar":"line",itemStyle:{color:p.name==="IC"?"#3498db":"#e74c3c"},showSymbol:!1,label:{show:!1,position:"top",formatter:function(h){return h.data.toFixed(3)}}}))};_.setOption(U)}};let ee=null;const Ce=c(null),Me=c(""),xe=c({}),Y=async()=>{var V,le,te,ce,de,ue,fe,pe,ve,j,z,C,R;const{json:t,httpController:we}=await ma((n==null?void 0:n.taskId)||M());if(t.code==="200"&&(Me.value=(le=(V=t==null?void 0:t.data)==null?void 0:V.rank_ic_self_correlation_chart)==null?void 0:le.title,xe.value=(te=t==null?void 0:t.data)==null?void 0:te.rank_ic_self_correlation_chart,Ce.value)){if(ee=at(Ce.value),Object.keys(xe.value).length==0)return;var U={title:{},tooltip:{trigger:"axis",formatter:function(p){return p.map(h=>{let u=h.value;const m=h.color;if(Array.isArray(u)){const D=u.map(L=>L==null||isNaN(L)?"--":Number(L).toFixed(4));return`<span style="color:${m}">${h.seriesName}</span>: ${D[0]}, ${D[1]}`}return u=u==null||isNaN(u)?"--":Number(u).toFixed(4),`<span style="color:${m}">${h.seriesName}</span>: ${u}`}).join("<br/>")}},legend:{data:["自相关系数","下限(95%置信区间)","上限(95%置信区间)"],top:"0px",textStyle:{color:Re}},grid:{left:"3%",right:"4%",bottom:"10%",top:"10%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:0,start:0,end:100}],xAxis:[{type:"category",data:(de=(ce=xe.value)==null?void 0:ce.x[0])==null?void 0:de.data,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(p,h){var ie,F,re;const u=(re=(F=(ie=xe.value)==null?void 0:ie.x[0])==null?void 0:F.data)==null?void 0:re.length,m=40,D=ee.getWidth(),L=Math.floor(D/m),ge=Math.floor((u-1)/(L-1));return(u-1-p)%ge===0}},show:!1},{type:"category",data:(fe=(ue=xe.value)==null?void 0:ue.x[0])==null?void 0:fe.data,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(p,h){var ie,F,re;const u=(re=(F=(ie=xe.value)==null?void 0:ie.x[0])==null?void 0:F.data)==null?void 0:re.length,m=40,D=ee.getWidth(),L=Math.floor(D/m),ge=Math.floor((u-1)/(L-1));return(u-1-p)%ge===0}},position:"bottom"}],yAxis:{type:"value",min:-1,max:1,interval:.2,splitLine:{show:!0,lineStyle:{type:"dashed",color:Re}}},series:[{name:"自相关系数",type:"line",data:(ve=(pe=xe.value)==null?void 0:pe.y[0])==null?void 0:ve.data,symbol:"circle",symbolSize:8,lineStyle:{width:2},itemStyle:{color:"#3498db"},label:{show:!1,position:"top",formatter:function(p){return p.value.toFixed(3)}}},{name:"下限(95%置信区间)",type:"line",data:(z=(j=xe.value)==null?void 0:j.y[1])==null?void 0:z.data,lineStyle:{type:"dashed",width:1},itemStyle:{color:"#95a5a6"},symbol:"none"},{name:"上限(95%置信区间)",type:"line",data:(R=(C=xe.value)==null?void 0:C.y[2])==null?void 0:R.data,lineStyle:{type:"dashed",width:1},itemStyle:{color:"#95a5a6"},symbol:"none"}]};ee.setOption(U)}};let ne=null;const tt=c(null),We=c(""),Le=c({}),Fe=async()=>{var U,V,le,te,ce,de,ue,fe,pe,ve,j,z,C,R,p,h;const{json:t,httpController:we}=await ya((n==null?void 0:n.taskId)||M());if(t.code==="200"&&((le=(V=(U=t==null?void 0:t.data)==null?void 0:U.return_chart)==null?void 0:V.y)==null||le.forEach(u=>{u.data=u.data.map(m=>I(m*100))}),We.value=(ce=(te=t==null?void 0:t.data)==null?void 0:te.return_chart)==null?void 0:ce.title,Le.value=(de=t==null?void 0:t.data)==null?void 0:de.return_chart,tt.value)){if(ne=at(tt.value),Object.keys(Le.value).length==0)return;const u={title:{},tooltip:{trigger:"axis",formatter:function(m){return m.map(D=>{let L=D.value;const ge=D.color;if(Array.isArray(L)){const ie=L.map(F=>F==null||isNaN(F)?"--":Number(F).toFixed(4));return`<span style="color:${ge}">${D.seriesName}</span>: ${ie[0]}, ${ie[1]}`}return L=L==null||isNaN(L)?"--":Number(L).toFixed(4),`<span style="color:${ge}">${D.seriesName}</span>: ${L}%`}).join("<br/>")}},legend:{data:(fe=(ue=Le.value)==null?void 0:ue.y)==null?void 0:fe.map(m=>m.name),textStyle:{color:Re}},xAxis:[{type:"category",data:(j=(ve=(pe=Le.value)==null?void 0:pe.x[0])==null?void 0:ve.data)==null?void 0:j.map(m=>Dt(m)),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(m,D){var Qe,gt,pt;const L=(pt=(gt=(Qe=Le.value)==null?void 0:Qe.x[0])==null?void 0:gt.data)==null?void 0:pt.length,ge=60,ie=ne.getWidth(),F=Math.floor(ie/ge),re=Math.floor((L-1)/(F-1));return(L-1-m)%re===0}},show:!1},{type:"category",data:(R=(C=(z=Le.value)==null?void 0:z.x[0])==null?void 0:C.data)==null?void 0:R.map(m=>Dt(m)),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(m,D){var Qe,gt,pt;const L=(pt=(gt=(Qe=Le.value)==null?void 0:Qe.x[0])==null?void 0:gt.data)==null?void 0:pt.length,ge=60,ie=ne.getWidth(),F=Math.floor(ie/ge),re=Math.floor((L-1)/(F-1));return(L-1-m)%re===0}},position:"bottom"}],yAxis:{type:"value",axisLabel:{formatter:"{value}%"},splitLine:{show:!0,lineStyle:{type:"dashed",color:Re}}},grid:{left:"3%",right:"4%",bottom:"0%",top:"10%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:[0,1],start:0,end:100}],series:(h=(p=Le.value)==null?void 0:p.y)==null?void 0:h.map(m=>({name:m.name,type:"line",data:m.data,showSymbol:!1}))};ne.setOption(u)}};let Pe=null;const ct=c(null),It=c(""),dt=c({}),xt=async()=>{var V,le,te,ce,de,ue,fe,pe,ve,j,z,C,R;const{json:t,httpController:we}=await wa((n==null?void 0:n.taskId)||M());if(t.code==="200"&&(It.value=(le=(V=t==null?void 0:t.data)==null?void 0:V.excess_chart)==null?void 0:le.title,dt.value=(te=t==null?void 0:t.data)==null?void 0:te.excess_chart,ct.value)){if(Pe=at(ct.value),Object.keys(dt.value).length==0)return;const p=["#1f77b4","#ff7f0e","#2ca02c","#d62728","#9467bd","#0099cc","#ff00ff"];var U={title:{},tooltip:{trigger:"axis",formatter:function(h){return h.map(u=>{let m=u.value;const D=u.color;if(Array.isArray(m)){const L=m.map(ge=>ge==null||isNaN(ge)?"--":Number(ge).toFixed(4));return`<span style="color:${D}">${u.seriesName}</span>: ${L[0]}, ${L[1]}`}return m=m==null||isNaN(m)?"--":Number(m).toFixed(4),`<span style="color:${D}">${u.seriesName}</span>: ${m}`}).join("<br/>")}},legend:{data:(de=(ce=dt.value)==null?void 0:ce.y)==null?void 0:de.map(h=>h.name),top:"0px",textStyle:{color:Re,fontSize:10}},grid:{left:"3%",right:"4%",bottom:"0%",top:"10%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:[0,1],start:0,end:100}],xAxis:[{type:"category",data:(pe=(fe=(ue=dt.value)==null?void 0:ue.x[0])==null?void 0:fe.data)==null?void 0:pe.map(h=>h.split(" ")[0]),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(h,u){var F,re,Qe;const m=(Qe=(re=(F=dt.value)==null?void 0:F.x[0])==null?void 0:re.data)==null?void 0:Qe.length,D=40,L=Pe.getWidth(),ge=Math.floor(L/D),ie=Math.floor((m-1)/(ge-1));return(m-1-h)%ie===0}},show:!1},{type:"category",data:(z=(j=(ve=dt.value)==null?void 0:ve.x[0])==null?void 0:j.data)==null?void 0:z.map(h=>h.split(" ")[0]),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(h,u){var F,re,Qe;const m=(Qe=(re=(F=dt.value)==null?void 0:F.x[0])==null?void 0:re.data)==null?void 0:Qe.length,D=40,L=Pe.getWidth(),ge=Math.floor(L/D),ie=Math.floor((m-1)/(ge-1));return(m-1-h)%ie===0}},position:"bottom"}],yAxis:{type:"value",axisLabel:{formatter:h=>(h*100).toFixed(1)+"%"}},series:(R=(C=dt.value)==null?void 0:C.y)==null?void 0:R.map((h,u)=>({name:h.name,type:"line",data:h.data,symbol:"circle",symbolSize:6,showSymbol:!1,lineStyle:{width:(h.name.includes("多空组合"),1)},itemStyle:{color:p[u]}}))};Pe.setOption(U)}};je(()=>n.factorId,async()=>{n.into&&(console.log("加载mc3"),await kt(),await o(),await Ie(),await B(),await X(),await oe(),await A(),await He(),await lt(),await it(),await f(),await he(),await Y(),await Fe(),await xt(),await Ge(),ut())}),je(()=>n.taskId,async()=>{n.into&&(console.log("加载mc2"),await Ge(),ut(),await kt(),await o(),await Ge(),await Ie(),await B(),await X(),await oe(),await A(),await He(),await lt(),await it(),await f(),await he(),await Y(),await Fe(),await xt(),await Ge(),setTimeout(()=>{ut()},1200))}),je(()=>n.into,async()=>{n.into&&(console.log("加载mc1"),await kt(),await o(),await Ie(),await B(),await X(),await oe(),await A(),await He(),await lt(),await it(),await f(),await he(),await Y(),await Fe(),await xt(),await Ge(),ut())}),je(()=>n.into,async()=>{await Ge(),ut()});const ut=()=>{ae==null||ae.resize(),Q==null||Q.resize(),y==null||y.resize(),J==null||J.resize(),Se==null||Se.resize(),Z==null||Z.resize(),Ye==null||Ye.resize(),_==null||_.resize(),ee==null||ee.resize(),ne==null||ne.resize(),Pe==null||Pe.resize()},zt=()=>{ae==null||ae.dispose(),Q==null||Q.dispose(),y==null||y.dispose(),J==null||J.dispose(),Se==null||Se.dispose(),Z==null||Z.dispose(),Ye==null||Ye.dispose(),_==null||_.dispose(),ee==null||ee.dispose(),ne==null||ne.dispose(),Pe==null||Pe.dispose()},St=c([]),kt=async()=>{var U;const{json:t,httpController:we}=await ba((n==null?void 0:n.taskId)||M());St.value=(U=t==null?void 0:t.data)==null?void 0:U.factor_data_analysis},Rt=c(!0);ht(async()=>{try{n.into&&(await kt(),await o(),await Ie(),await B(),await X(),await oe(),await A(),await He(),await lt(),await it(),await f(),await he(),await Y(),await Fe(),await xt(),Rt.value=!1,await Ge(),ut())}catch{be.value,Rt.value=!0}window.addEventListener("resize",ut)}),yt(()=>{window.removeEventListener("resize",ut),zt()});const Dt=t=>t&&t.split(" ")[0];return(t,we)=>(w(),x("div",to,[e("div",ao,[e("div",oo,[e("div",so,P(Ee.value),1),e("div",no,[e("div",{ref_key:"echartsRefEarningsBigChart",ref:tt,style:{width:"100%",height:"400px"}},[De(ot,{visible:!0})],512)])]),e("div",lo,[e("div",io,[e("div",ro,[e("div",co,[e("div",{class:"value",style:Ze({color:k(G.value.return_ratio)})},P(G.value.return_ratio),5),we[0]||(we[0]=e("div",{class:"label"},"因子收益",-1))]),e("div",uo,[e("div",{class:"value",style:Ze({color:k(G.value.sharpe_ratio)})},P(G.value.sharpe_ratio),5),we[1]||(we[1]=e("div",{class:"label"},"夏普比率",-1))]),e("div",fo,[e("div",{class:"value",style:Ze({color:k(G.value.annualized_ratio)})},P(G.value.annualized_ratio),5),we[2]||(we[2]=e("div",{class:"label"},"年化收益",-1))]),e("div",ho,[e("div",go,P(G.value.maximum_drawdown),1),we[3]||(we[3]=e("div",{class:"label"},"最大回撤",-1))]),(w(!0),x(Ve,null,Je(St.value,(U,V)=>(w(),x("div",{key:V,class:"data-item"},[(w(!0),x(Ve,null,Je(Object.values(U),(le,te)=>(w(),x(Ve,{key:te},[te==1?(w(),x("div",po,P(le),1)):Ne("",!0),te==0?(w(),x("div",vo,P(le),1)):Ne("",!0)],64))),128))]))),128))])]),e("div",Ao,[e("div",mo,P(_e.value),1),e("div",yo,[e("div",wo,[De(Ut,{tableData:g.value,headers:q.value,isFixed:!0,"custom-header":i.value},null,8,["tableData","headers","custom-header"])])])]),e("div",bo,[e("div",xo,P(Be.value),1),e("div",ko,[e("div",_o,[De(Ut,{tableData:Ae.value,headers:T.value,isFixed:!0,"column-width":110},null,8,["tableData","headers"])])])])]),e("div",Co,[e("div",Lo,[e("div",Eo,P(K.value),1),e("div",Io,[e("div",{ref_key:"echartsRefSimDietrich",ref:b,style:{width:"100%",height:"400px"}},[De(ot,{visible:!0})],512)])]),e("div",So,[e("div",Ro,P(r.value),1),e("div",Do,[e("div",{ref_key:"echartsRefICDistribution",ref:S,style:{width:"100%",height:"400px"}},[De(ot,{visible:!0})],512)])]),e("div",No,[e("div",Bo,P(ke.value),1),e("div",Mo,[e("div",{ref_key:"echartsRefICSequence",ref:me,style:{width:"100%",height:"400px"}},[De(ot,{visible:!0})],512)])]),e("div",To,[e("div",Uo,P(rt.value),1),e("div",Fo,[e("div",{ref_key:"echartsRefICCcorrelation",ref:ze,style:{width:"100%",height:"400px"}},[De(ot,{visible:!0})],512)])]),e("div",Wo,[e("div",Oo,P(et.value),1),e("div",Vo,[e("div",{ref_key:"echartsRefRankICDecay",ref:Xe,style:{width:"100%",height:"400px"}},[De(ot,{visible:!0})],512)])]),e("div",Go,[e("div",qo,P(l.value),1),e("div",zo,[e("div",{ref_key:"echartsRefRankICDistribution",ref:a,style:{width:"100%",height:"400px"}},[De(ot,{visible:!0})],512)])]),e("div",Yo,[e("div",Po,P(se.value),1),e("div",Zo,[e("div",{ref_key:"echartsRefRankICSequence",ref:E,style:{width:"100%",height:"400px"}},[De(ot,{visible:!0})],512)])]),e("div",Qo,[e("div",Jo,P(Me.value),1),e("div",Ho,[e("div",{ref_key:"echartsRefRankICCcorrelation",ref:Ce,style:{width:"100%",height:"400px"}},[De(ot,{visible:!0})],512)])]),e("div",Xo,[e("div",$o,P(We.value),1),e("div",jo,[e("div",{ref_key:"echartsRefCurrentDeepAnalysis",ref:ye,style:{width:"100%",height:"400px"}},[De(ot,{visible:!0})],512)])]),e("div",Ko,[e("div",es,P(It.value),1),e("div",ts,[e("div",{ref_key:"echartsRefExcessReturnBigChart",ref:ct,style:{width:"100%",height:"400px"}},[De(ot,{visible:!0})],512)])])])])]))}}),os=nt(as,[["__scopeId","data-v-62815ad4"]]),ss={class:"logs-header"},ns={class:"controls"},ls={class:"filter-group"},is=["value"],rs={class:"filter-group"},cs={width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},ds={key:0,d:"M3 4h13M3 8h9M3 12h5M19 20V8M15 16l4 4 4-4"},us={key:1,d:"M3 4h13M3 8h9M3 12h5M19 20V8M15 12l4-4 4 4"},fs={class:"filter-group"},hs={class:"logs-content-area"},gs={class:"logs-table"},ps={class:"log-message"},vs={key:0,class:"metadata-container"},As=["onClick"],ms={class:"toggle-text"},ys={class:"log-metadata"},ws={class:"logs-footer"},bs={class:"pagination-info"},xs={key:0},ks={class:"pagination-controls"},_s=["disabled"],Cs={width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",style:{transform:"rotate(270deg)"}},Ls=["disabled"],Es={width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",style:{transform:"rotate(90deg)"}},Is=mt({__name:"Logs",props:{isCollapsed:{type:Boolean,default:!0},logCount:{type:Number,default:0}},setup(H){Vt();const M=Ct(),N=H,v=c([]),I=c(!1),n=c("ALL"),be=c("asc"),k=c(50),G=c(1),o=c([void 0]),ae=c(void 0),ye=c(!1),Ee=c(null),W=c(N.isCollapsed);je(()=>N.isCollapsed,r=>{r||(W.value=r)});const X=c(new Set),Be=c(50);let T=!1;const Ae=r=>{var A;T=!0,document.body.style.cursor="ns-resize",document.body.style.userSelect="none";const d=(A=r.target.closest(".logs-container"))==null?void 0:A.getBoundingClientRect();d&&(d.height,r.clientY,document.addEventListener("mousemove",Ie),document.addEventListener("mouseup",_e))},Ie=r=>{if(!T)return;const d=window.innerHeight,A=r.clientY,me=(d-A)/d*100;Be.value=Math.min(Math.max(me,20),80)},_e=()=>{T=!1,document.body.style.cursor="",document.body.style.userSelect="",document.removeEventListener("mousemove",Ie),document.removeEventListener("mouseup",_e)},q=async(r,d=!1)=>{if(!M.workflow_run_id||!M.workflow_id)return v.value=[],d&&(G.value=1,o.value=[void 0],ae.value=void 0,ye.value=!1,Ee.value=null),!1;I.value=!0;try{const A=localStorage.getItem("token"),J={workflow_run_id:M.workflow_run_id,workflow_id:M.workflow_id,limit:Number(k.value)};r&&(J.last_sequence=r),n.value!=="ALL"&&(J.log_level=n.value);const me=await Gt.get("http://localhost:8000/api/workflow/run/log",{params:J,headers:{uid:"0","quantflow-auth":"2","Content-Type":"application/json",Authorization:`${A}`}});if(me.status===200&&me.data&&me.data.data){const ke=me.data.data;return v.value=ke.logs||[],ae.value=ke.next_sequence,ye.value=ke.has_more??!1,Ee.value=ke.total_count,d&&(G.value=1,o.value=[void 0]),!0}else return console.error("Failed to fetch logs:",me),v.value=[],d&&(G.value=1,o.value=[void 0],ae.value=void 0),ye.value=!1,!1}catch(A){return console.error("Error fetching logs:",A),v.value=[],d&&(G.value=1,o.value=[void 0],ae.value=void 0),ye.value=!1,!1}finally{I.value=!1}},g=wt(()=>{let r=[...v.value];return n.value!=="ALL"&&(r=r.filter(d=>d.level===n.value)),r.sort((d,A)=>{const J=new Date(d.timestamp).getTime(),me=new Date(A.timestamp).getTime();return be.value==="asc"?J-me:me-J}),r}),i=wt(()=>["ALL","INFO","ERROR","WARNING","DEBUG"]),B=async()=>{if(ye.value&&ae.value!=null&&!I.value){const r=ae.value;await q(r,!1)&&(G.value++,o.value.length<G.value?o.value.push(r):o.value[G.value-1]=r)}},Q=async()=>{if(G.value>1&&!I.value){const r=G.value-1,d=o.value[r-1];await q(d,!1)&&(G.value=r)}},b=()=>{be.value=be.value==="asc"?"desc":"asc",Ge(()=>{y()})},K=r=>{if(!r)return"N/A";try{return new Date(r).toLocaleString()}catch{return r}},O=()=>{q(void 0,!0)},oe=()=>{W.value=!W.value,W.value||(Be.value=50)},y=()=>{const r=document.querySelector(".logs-content-area");r&&(be.value==="asc"?r.scrollTop=r.scrollHeight:r.scrollTop=0)};je(()=>v.value,()=>{Ge(()=>{y()})},{deep:!0}),je(()=>N.logCount,async(r,d)=>{r!==d&&M.workflow_run_id&&await q(void 0,!0)},{immediate:!1}),ht(async()=>{await q(void 0,!0)});const S=r=>{X.value.has(r)?X.value.delete(r):X.value.add(r)};return(r,d)=>(w(),x("div",{class:Ke(["logs-container",{collapsed:W.value,dragging:$e(T)}]),style:Ze({height:Be.value+"%"})},[e("div",{class:"drag-handle",onMousedown:Ae},null,32),e("div",{class:"toggle-button",onClick:oe},[(w(),x("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",style:Ze({transform:W.value?"rotate(0deg)":"rotate(180deg)"})},d[2]||(d[2]=[e("path",{d:"M7 14l5-5 5 5",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)]),4))]),e("div",ss,[d[7]||(d[7]=Jt('<div class="title" data-v-5903b4a6><svg width="24" height="24" viewBox="0 0 24 24" fill="none" data-v-5903b4a6><circle cx="12" cy="12" r="9" stroke="currentColor" stroke-width="2" data-v-5903b4a6></circle><path d="M8 9H16" stroke="currentColor" stroke-width="2" stroke-linecap="round" data-v-5903b4a6></path><path d="M8 12H14" stroke="currentColor" stroke-width="2" stroke-linecap="round" data-v-5903b4a6></path><path d="M8 15H12" stroke="currentColor" stroke-width="2" stroke-linecap="round" data-v-5903b4a6></path></svg><span data-v-5903b4a6>日志内容</span></div>',1)),e("div",ns,[e("div",ls,[d[3]||(d[3]=e("label",{for:"level-filter"},"级别:",-1)),st(e("select",{id:"level-filter","onUpdate:modelValue":d[0]||(d[0]=A=>n.value=A),onChange:O},[(w(!0),x(Ve,null,Je(i.value,A=>(w(),x("option",{key:A,value:A},P(A),9,is))),128))],544),[[Nt,n.value]])]),e("div",rs,[d[4]||(d[4]=e("label",{for:"sort-order"},"时间:",-1)),e("button",{onClick:b,class:"sort-button"},[(w(),x("svg",cs,[be.value==="desc"?(w(),x("path",ds)):(w(),x("path",us))]))])]),e("div",fs,[d[6]||(d[6]=e("label",{for:"limit-per-page"},"每页显示:",-1)),st(e("select",{id:"limit-per-page","onUpdate:modelValue":d[1]||(d[1]=A=>k.value=A),onChange:O},d[5]||(d[5]=[e("option",{value:"50"},"50",-1),e("option",{value:"100"},"100",-1)]),544),[[Nt,k.value]])])])]),e("div",hs,[e("table",gs,[d[9]||(d[9]=e("thead",null,[e("tr",null,[e("th",{class:"timestamp-col"},"时间"),e("th",{class:"level-col"},"级别"),e("th",{class:"message-col"},"消息")])],-1)),e("tbody",null,[(w(!0),x(Ve,null,Je(g.value,A=>(w(),x("tr",{key:A._id,class:Ke(["log-entry",`log-level-${A.level.toLowerCase()}`])},[e("td",null,P(K(A.timestamp)),1),e("td",null,[e("span",{class:Ke(["log-level-badge",`log-level-badge-${A.level.toLowerCase()}`])},P(A.level),3)]),e("td",ps,[At(P(A.message)+" ",1),A.metadata&&Object.keys(A.metadata).length>0?(w(),x("div",vs,[e("button",{class:"metadata-toggle",onClick:J=>S(A._id)},[e("span",ms,P(X.value.has(A._id)?"收起详情":"查看详情"),1),(w(),x("svg",{width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",class:Ke({rotate:X.value.has(A._id)})},d[8]||(d[8]=[e("path",{d:"M6 9l6 6 6-6",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)]),2))],8,As),st(e("div",ys,[e("pre",null,P(JSON.stringify(A.metadata,null,2)),1)],512),[[bt,X.value.has(A._id)]])])):Ne("",!0)])],2))),128))])])]),e("div",ws,[e("div",bs,[e("span",null,"第 "+P(G.value)+" 页",1),Ee.value!==null?(w(),x("span",xs," / 总 "+P(Ee.value)+" 条记录",1)):Ne("",!0),e("span",null," (已加载 "+P(v.value.length)+" 条)",1)]),e("div",ks,[e("button",{onClick:Q,disabled:G.value===1||I.value,class:"pagination-button pagination-button-prev"},[(w(),x("svg",Cs,d[10]||(d[10]=[e("path",{d:"M7 14l5-5 5 5",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)]))),d[11]||(d[11]=At(" 上一页 "))],8,_s),e("button",{onClick:B,disabled:!ye.value||I.value,class:"pagination-button pagination-button-next"},[d[13]||(d[13]=At(" 下一页 ")),(w(),x("svg",Es,d[12]||(d[12]=[e("path",{d:"M7 14l5-5 5 5",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)])))],8,Ls)])])],6))}}),Ss=nt(Is,[["__scopeId","data-v-5903b4a6"]]),Rs={class:"alert"},Ds={class:"box"},Ns={__name:"alert",props:{show:{type:Boolean,default:!1}},emits:["close"],setup(H,{emit:M}){const N=H,v=M,I=()=>{v("close")};return(n,be)=>st((w(),x("div",Rs,[e("div",Ds,[e("img",{onClick:I,class:"close-btn",src:qt}),Ht(n.$slots,"default",{},void 0,!0)])],512)),[[bt,N.show]])}},Lt=nt(Ns,[["__scopeId","data-v-34167be4"]]),Bs="/quantflow/assets/chat-dI4p2fsV.png",Ms="data:image/png;base64,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",Ts="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAAAXNSR0IArs4c6QAAAvdJREFUWEfNmEtoFEEQhv/qhXWJ2XiRgAHxICRHkdxEwT3N1qwQMYL4OKlnQRIUjHrxAT6C4NncfCAYY8DtmTkloHgL4tGABxEiiJeYGNaF7dIOPZKsmzibDZtpmNNUV31dXV1V3YSUDWqWJ4qi7cYYD4APYC+AHvdZVXPu+wRAK6Uiz/N+NmMjMZDWej8RXQPAAHIJjVQABCJyw/f990nm/BeoXC7vUUrdAnAKgJU3AN4R0SsRmTHGzHV0dFjPYGlpqUcp1UNE/SJyFMABAAqAAHhqjBkplUqf1wNbF0hrzUT0DMAOABUReZjJZEY9z/uWZLVRFHXXarUhIrrgvDovIid93w/Wmr8mUBiGQyJy165QRMYzmcxFz/O+JAGpl4miaHetVntARIPWw0R0qVgsjjbS1RDIwdy3riaiq8Vi8fZGQOrnhGF4RURu2q0nouFGUP8AuW16bSc59z7fDJhYh9b6hAsDEZEj9du3CsgF8AcbM0Q0slmeqV+Q1nqEiKyn5o0x+1YG+iqgIAgeAzhtY8b3/eOb6ZkGUC9cTD1h5jPx/79ALs/MAPillOrdaAAnXYQNdGPMLIBtItIf56m/QGEYTtjcYU+W7/uXkypuRU5rfceeOAATzHzM6loGcuXgO4CsUmpX0jzTCoyz222M+QqgqpTaacvMMlAQBJZuHMBbZj7UqqFm5gdB8AbAQQCDzPxyGSgMwzERObtWbmjGQLOyK3LeGDOfjz00BeAwgAIzTzertBX5IAisXWt/mpkLMdDHP8Wv1xjTVyqVbOS3bZTL5V6llLU/y8x9MdACgM5cLpcvFAqLbaOxrpma6qxUKtb+IjPnY6AfAPLVarVrYGDA/mzbmJyczGezWWt/gZm7UrtlqQvqRwDObcWx11oPE9E9AKuOfboSY+pKh8vW6SmuFih17Ycrsulp0CxQ6lpYt3X2LpaOJj+uF6m6BtVBpeOiGEOl6iodQ6XqsWFlD2LzlFLquogUt/Q5pr4xSs2DVbs6tt8B3hFDpWW0GwAAAABJRU5ErkJggg==",Us="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAYCAYAAADkgu3FAAAAAXNSR0IArs4c6QAAAydJREFUSEu11UtoE0EYB/Dv221VpAejXutBXT3I4kHBbhSxtvWFiA8Si9hLqRZfVeoDn1hFsT7RKooP9CIqqagItphNrIJQtW2QVEGxerB60UMDvjA7830ym6b2mUZLBkIg2Znf/L/5hkX4j2GFggeANGicV7g/3emY7oPJ57xh+xAL3sMSAAgPP1tctDedNf4JssIPj4DEnSARWDKob5RQ3bi8aNdgWNqQFbKPAcF2Fgpw04CbKvE5/ry4aEcqLC3IGwqeJAmV7uIC3iLBZAWwhFaWYCZgPvWiZN7WgbBBISscPM0CNiOhWiwIcdpHrD9Xi0tJpk76CZYw303KeKaptHBLf1hKyBt+WMMCNyWScL1HF8ti8exJ5EAUCECLS+NrB7V7crLuImsLVSlZ4Nnm9QUVvbH+IWbMC4fOIcF6tVMkfDBKc1bUL1r02wrYJhFE1aI6SaOxZEHbxJq64Z5h2beZcXFnSc+3VBRsBEROgn0hZrQa7AvgQHlnC9//9rPD99rvj6tJCpKdiXTIMhpL8tvU71MCgWEjv3hqmbQlIBlIwMXItoJ1SawnxIzesH2JJJSpMyGCe+KTx99SPt1J7sy6bpuSVOkQhBBGpGyBC6kx7WJzNnyPBYBgqfofBFxp2T13rcL+QlVVmjVr5mWQXMrqIcl3nM+ji7sjbiIFCYyqEgnuCSUx7ojdRIIVLN11rkbg6ZouKC8UPAMSKlQSFlw7Iid71eP8fNH7UK1rtilUIgEgRZYR2ZAoXfcxp6oh65tON1iCzz0zwTVdkDcY/EgCckHCrdxfsdW1fr+6HX2GCznoNgOR3i+kJvkCAf1965jrQFjMEtq7oBl1dh4wG+N+xG4MhCRKY5tIEEWJIOO6Eansmyi5O58voL8bP3YlEb8Z9ML2juRCDrrNQEJLCXWf++/QWZUoUTqG1ImGDiUTOZoR2TVw6YYGnbJNkIn2ZtYzDDlaVN1+Vs1QlalE1bbJEqPufZNahiE3kTqBTEIHbZOpE1LNUJ2h0k09+GiK7vArdY90SROaqgs/DPRWHVLXqdveZox5whL55dH82QB/3zmpwD/qfMD8dywLsQAAAABJRU5ErkJggg==",Fs={key:0,class:"factor-chat"},Ws={class:"container"},Os={class:"chat-header",style:{padding:"5px 0",display:"flex","justify-content":"space-between","align-items":"center"}},Vs={class:"assistant-type-select",style:{"margin-right":"16px"}},Gs={style:{"margin-left":"4px","vertical-align":"middle"},width:"12",height:"12",viewBox:"0 0 24 24"},qs={key:0,class:"dropdown-list",style:{position:"absolute",right:"0",top:"100%",background:"#222",border:"1px solid #333","border-radius":"4px","min-width":"100px","z-index":"100"}},zs=["onClick"],Ys={class:"chat-item-content"},Ps={class:"chat-item-header"},Zs={class:"chat-item-header-time"},Qs={key:0,src:Bs,alt:"chat",width:"16",height:"16",style:{"margin-left":"10px"}},Js={class:"chat-item-content-text"},Hs=["innerHTML"],Xs={class:"chat-input"},$s={key:0,src:Ms,alt:"checkbox",width:"16",height:"16",style:{"margin-right":"3px"}},js={key:1,src:Ts,alt:"checkbox-unselected",width:"16",height:"16",style:{"margin-right":"3px"}},Ks={class:"dropdown-arrow",width:"14",height:"14",viewBox:"0 0 24 24",style:{"vertical-align":"middle",position:"relative",top:"2px"}},en={key:0,class:"modes-select-dropdown"},tn=["onClick"],an={key:0,width:"16",height:"16",viewBox:"0 0 24 24",style:{"margin-right":"6px"}},on={key:0,src:Us,alt:"send",width:"14",height:"14"},sn={key:1,xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"currentColor"},nn={__name:"Chat",props:{visible:{type:Boolean,default:!0}},emits:["update:visible","apply-code","close-chat"],setup(H,{emit:M}){const N=Ct(),v=c(!0),I=c(""),n=c(""),be=c([{type:"receive",content:"你好，我是PandaAI Chat，很高兴认识你！"}]),k=c(null),G=c(null),o=c(!1),ae=c(null);Bt({start:!1,end:!1});const ye=[{value:"code",label:"通用代码助手"},{value:"backtest",label:"回测代码助手"},{value:"factor",label:"因子构建代码助手"}];console.log("msms:",N.assistantTypeMap);const Ee=wt(()=>{const y=ye.find(S=>S.value===N.assistantTypeMap.get(N.id));return y?y.label:""}),W=c(!1),X=c(null);function Be(y){N.assistantTypeMap.set(N.id,y),W.value=!1}const T=async()=>{if(n.value.trim()==""&&!o.value){G.value.className="sendEmpty",await new Promise(y=>setTimeout(y,300)),G.value.className="";return}if(o.value){o.value=!1,G.value.disabled=!1,console.log("点击了暂停"),ae.value&&ae.value.abort(),be.value.pop(),be.value.pop();return}if(n.value.trim()){o.value=!0,G.value.disabled=!0;const y={type:"send",content:n.value};be.value.push(y),n.value="",Ge(()=>{k.value&&(k.value.scrollTop=k.value.scrollHeight),k.value&&k.value.dispatchEvent(new Event("refresh-highlight"))});const S={message:v.value?I.value+`
`+y.content:y.content};Ie.value&&(S.session_id=Ie.value),v.value&&(S.original_code=N.original_code),S.model=B.value,await _e(S),o.value=!1,G.value.disabled=!1,Ge(()=>{k.value&&(k.value.scrollTop=k.value.scrollHeight),k.value&&k.value.dispatchEvent(new Event("refresh-highlight"))})}},Ae=c(null),Ie=c(""),_e=async y=>{clearInterval(Ae.value),ae.value=new AbortController;const S=setTimeout(()=>ae.value.abort(),1e3*50),r=Bt({type:"receive",content:"正在思考..."});be.value.push(r),Ge(()=>{k.value&&(k.value.scrollTop=k.value.scrollHeight),k.value&&k.value.dispatchEvent(new Event("refresh-highlight"))});try{let d=!1;const A=c(".");console.log("------1------"),Ae.value=setInterval(()=>{A.value=A.value==="..."?".":A.value+".",r.content="正在处理"+A.value,d||(Ge(()=>{k.value&&(k.value.scrollTop=k.value.scrollHeight),k.value&&k.value.dispatchEvent(new Event("refresh-highlight"))}),d=!0)},300);let J="";N.assistantTypeMap.get(N.id)==="backtest"?J="/pandaApi/quantflow/api/chat/backtest-assistant-stream":N.assistantTypeMap.get(N.id)==="factor"?J="/pandaApi/quantflow/api/chat/factor-assistant-stream":N.assistantTypeMap.get(N.id)==="code"&&(J="/pandaApi/quantflow/api/chat/code-assistant-stream");const me=localStorage.getItem("token"),ke=await fetch(J,{method:"POST",headers:{"Content-Type":"application/json",uid:"0",Authorization:`${me}`},body:JSON.stringify(y),signal:ae.value.signal});if(clearInterval(S),ke.status!==200)throw new Error("服务器错误，请稍后再试！");const Te=ke.body.getReader(),He=new TextDecoder;let Se=!1,ze=!0,rt=0,qe=1,lt="",Z="";for(;!Se;){const Xe=new Promise((it,Ye)=>{setTimeout(()=>{Ye(new Error("Read-timed-out"))},36e5)}),et=await Promise.race([Te.read(),Xe]);ze&&(ze=!1,r.content="",clearInterval(Ae.value));const{value:Ue}=et;if(Se=et.done,Ue){const it=He.decode(Ue,{stream:!0}),Ye=/^data:\s*(\{.*}$)/gm,a=it.match(Ye);if(a){for(const l of a)try{const s=JSON.parse(l.replace("data: ",""));if(console.log("---m---",s),console.log("---typeChange---",Z),Object.keys(s).includes("status")&&(Z&&Z==="reasoning"&&(r.content+="</pre>"),Z&&Z==="content"&&(r.content+="</pre>"),Z="status",r.content+=`<li style='font-size:12px;color:#ddd'>${s.status}</li>`),Object.keys(s).includes("reasoning")&&(Z!=="reasoning"&&(r.content+=`<pre class='reasoning' style="color:#aaa; white-space: pre-wrap;font-family: sans-serif;">`,Z="reasoning"),r.content+=s.reasoning),Object.keys(s).includes("content")&&(Z!=="content"&&(r.content+=`<pre class='content' style="color:#aaa; white-space: pre-wrap;font-family: sans-serif;">`,Z="content"),r.content+=s.content),Object.keys(s).includes("code")&&s.code){const f="codeId"+Date.now();Z!=="code"&&(r.content+="<div class='final_result_code'>",Z="code");const _=s.code;N.codeMap.set(f,_),r.content+=`<pre><code class='language-python'>${_}</code></pre>
                                 <span class='codeApply' data-id='${f}'>应用</span>
                                 <span class='codeCopy'  data-id='${f}'>复制</span>
                                 </div>`}Object.keys(s).includes("explanation")&&(r.content+=`<pre  style='font-size:12px;color:#ddd;white-space: pre-wrap;'>${s.explanation}</pre>`),Object.keys(s).includes("session_id")&&(Ie.value=s.session_id)}catch(s){console.log(s)}Ge(()=>{k.value&&(k.value.scrollTop=k.value.scrollHeight),k.value&&k.value.dispatchEvent(new Event("refresh-highlight"))})}}r.content=r.content.replace(/<span class=['"]lod['"][^>]*><\/span>/g,""),r.content+="<span class='lod'></span>"}Se&&(r.content=r.content.replace(/<span class=['"]lod['"][^>]*><\/span>/g,""),Ge(()=>{const Xe=document.querySelectorAll(".reasoning");for(let Ue=0;Ue<Xe.length;Ue++)Xe[Ue].style.display="none";const et=document.querySelectorAll(".content");for(let Ue=0;Ue<et.length;Ue++)et[Ue].style.display="none"}))}catch(d){d.name==="AbortError"||d.name==="Read-timed-out"?r.content="网络超时，请稍后再试！":(console.log("error",(d==null?void 0:d.message)||d),r.content="网络超时，请稍后再试!"),clearInterval(Ae.value),o.value=!1,G.value.disabled=!1}};ht(()=>{});const q=y=>{const S=document.querySelector(".factor-chat"),r=y.clientX,d=parseInt(document.defaultView.getComputedStyle(S).width,10);document.body.style.userSelect="none";const A=me=>{const ke=d-(me.clientX-r);ke>=350&&ke<=600&&(S.style.width=ke+"px")},J=()=>{document.body.style.userSelect="",document.documentElement.removeEventListener("mousemove",A,!1),document.documentElement.removeEventListener("mouseup",J,!1)};document.documentElement.addEventListener("mousemove",A,!1),document.documentElement.addEventListener("mouseup",J,!1)};function g(y){W.value&&X.value&&!X.value.contains(y.target)&&(W.value=!1)}Xt(()=>{setTimeout(()=>{const y=document.querySelectorAll(".reasoning");for(let r=0;r<y.length;r++)y[r].style.display="block";const S=document.querySelectorAll(".content");for(let r=0;r<S.length;r++)S[r].style.display="none";k.value&&(k.value.scrollTop=k.value.scrollHeight)},60)}),ht(()=>{document.addEventListener("click",g)}),yt(()=>{document.removeEventListener("click",g)});const i=[{item:"DeepSeek-R1",value:"DeepSeek-R1"},{item:"DeepSeek-V3",value:"DeepSeek-V3"}],B=c(i[1].value),Q=c(!1),b=c(null);function K(y){B.value=y,Q.value=!1}function O(y){Q.value=!Q.value,Ge(()=>{Q.value&&document.addEventListener("click",oe,!0)})}function oe(y){b.value&&!b.value.contains(y.target)&&(Q.value=!1,document.removeEventListener("click",oe,!0))}return je(Q,y=>{y||document.removeEventListener("click",oe,!0)}),(y,S)=>{var d;const r=$t("highlight");return H.visible?(w(),x("div",Fs,[e("div",Ws,[e("div",Os,[S[4]||(S[4]=e("span",null,"AI 助手",-1)),e("div",Vs,[e("div",{class:"dropdown",ref_key:"dropdownRef",ref:X,onClick:S[0]||(S[0]=A=>W.value=!W.value),style:{position:"relative",cursor:"pointer"}},[e("span",null,P(Ee.value),1),(w(),x("svg",Gs,S[3]||(S[3]=[e("path",{d:"M7 10l5 5 5-5z",fill:"#aaa"},null,-1)]))),W.value?(w(),x("div",qs,[(w(),x(Ve,null,Je(ye,A=>e("div",{key:A.value,onClick:vt(J=>Be(A.value),["stop"]),style:{padding:"6px 16px","text-align":"center","text-indent":"0",color:"#fff",cursor:"pointer","white-space":"nowrap"}},P(A.label),9,zs)),64))])):Ne("",!0)],512)])]),st((w(),x("div",{class:"chat-content",ref_key:"chatItemContent",ref:k},[(w(!0),x(Ve,null,Je(be.value,(A,J)=>(w(),x("div",{class:Ke(["chat-item",{user:A.type==="send",ai:A.type==="receive"}]),key:J},[e("div",Ys,[e("div",Ps,[e("span",Zs,[A.type==="receive"?(w(),x("img",Qs)):Ne("",!0),At(" "+P(A.type==="send"?"你":"PandaAI 助手"),1)])]),e("div",Js,[e("div",{innerHTML:A.content},null,8,Hs)])])],2))),128))])),[[r]]),e("div",Xs,[e("label",null,[st(e("input",{type:"checkbox","onUpdate:modelValue":S[1]||(S[1]=A=>v.value=A)},null,512),[[bt,!1],[jt,v.value]]),v.value?(w(),x("img",$s)):(w(),x("img",js)),S[5]||(S[5]=e("span",null,"引用当前编辑器代码",-1))]),e("div",{class:"modes-select",ref_key:"modelDropdownRef",ref:b},[e("div",{class:"modes-select-label",onClick:O},[e("span",null,P(((d=i.find(A=>A.value===B.value))==null?void 0:d.item)||"请选择模型"),1),(w(),x("svg",Ks,S[6]||(S[6]=[e("path",{d:"M7 10l5 5 5-5",stroke:"#52BBFE","stroke-width":"2",fill:"none","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)])))]),Q.value?(w(),x("div",en,[(w(),x(Ve,null,Je(i,A=>e("div",{key:A.value,onClick:vt(J=>K(A.value),["stop"]),class:Ke(["modes-select-option",{selected:B.value===A.value}])},[B.value===A.value?(w(),x("svg",an,S[7]||(S[7]=[e("path",{d:"M5 13l4 4L19 7",stroke:"#52BBFE","stroke-width":"2",fill:"none","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)]))):Ne("",!0),e("span",null,P(A.item),1)],10,tn)),64))])):Ne("",!0)],512),st(e("textarea",{ref_key:"textarea",ref:G,"onUpdate:modelValue":S[2]||(S[2]=A=>n.value=A),placeholder:"请在这里输入提问...",onKeydown:Kt(T,["enter"])},null,544),[[Ft,n.value]]),e("button",{onClick:T,class:Ke({send:!o.value})},[o.value?(w(),x("svg",sn,S[8]||(S[8]=[e("rect",{x:"6",y:"6",width:"12",height:"12",rx:"1"},null,-1)]))):(w(),x("img",on))],2)]),e("div",{class:"resize-handle",onMousedown:q},null,32)])])):Ne("",!0)}}},ln=nt(nn,[["__scopeId","data-v-0720e869"]]),rn={key:0,class:"alert-container"},cn={class:"alert-header"},dn={class:"alert-content"},un={key:1,class:"alert-container"},fn={class:"alert-header"},hn={class:"alert-content"},gn={class:"content-container"},pn={class:"toolbar-container"},vn={class:"run-container"},An={key:0,style:{"font-size":"12px",color:"#fff",position:"absolute",top:"-26px",transform:"scale(0.8)"}},mn={key:0,class:"drag-overlay"},yn={class:"monaco_ed"},wn={class:"alert"},bn={class:"box"},xn={style:{width:"100%",height:"calc(100% - 100px)",display:"flex",position:"relative"}},kn={key:0,class:"diff-action-buttons"},_n={class:"chartCode",style:{width:"35%",height:"100%","background-color":"#222",position:"relative"}},Cn=mt({__name:"Editor",setup(H){const M=c(null),N=c(!1);let v=null,I=null,n=null;const be=c(null),k=Vt(),G=ea(),o=Ct(),ae=()=>{o.codeStatus=!1,o.id="",N.value=!1};je(()=>o.codeStatus,()=>{setTimeout(()=>{I==null||I.setValue(o.code.get(o.id)||""),n==null||n.setValue(o.code.get(o.id)||"")},30)}),yt(()=>{v&&v.dispose(),I&&I.dispose(),n&&n.dispose()});const ye=c(!1);function Ee(){if(N.value&&v&&"getModel"in v&&I&&n){const a=v.getModel(),l=a&&a.modified?a.modified.getValue():"";setTimeout(()=>{o.code.set(o.id,l),I&&I.setValue(l)},60),ye.value=!0,N.value=!1}}function W(){if(N.value&&v&&"getModel"in v&&I&&n){const a=v.getModel(),l=a&&a.original?a.original.getValue():"";a&&a.modified&&a.modified.setValue(l),ye.value=!1,N.value=!1}}const X=()=>{if(o.node.node){let a="";if(N.value&&v&&"getModel"in v){const s=v.getModel();ye.value?a=s&&s.modified?s.modified.getValue():"":a=s&&s.original?s.original.getValue():""}else v&&"getValue"in v&&(a=v.getValue());o.node.node.properties[o.node.title]=a,o.code.set(o.id,a);const l=o.node.node.widgets.find(s=>s.name===o.node.title);l.value=a,console.log("workflowStore.node.widget",l),o.codeStatus=!1,ye.value=!1,N.value=!1}};je(()=>G.isBoxShow,async()=>{console.log("---2333-----"),G.isBoxShow&&(await Ge(),console.log("strategyAnalysisRef.value",be.value),be.value&&(console.log("---23334-----"),be.value.handleApply({workflow_id:G.id,title:o.title,feature_tag:"backtest",locator:"backtest_id",last_run_id:o.workflow_run_id},!1)))});function Be(a,l=!1){a||(a=window.location.href);const s={},f=a.indexOf("?");if(f!==-1){const _=a.indexOf("#",f),E=_!==-1?a.substring(f+1,_):a.substring(f+1);T(E,s)}if(l){const _=a.indexOf("#");if(_!==-1&&_<a.length-1){const E=a.substring(_+1),se=E.indexOf("?");if(se!==-1){const $=E.substring(se+1);T($,s)}}}return s}function T(a,l){if(!a)return;const s=a.split("&");for(const f of s){const[_,E]=f.split("=",2);_&&(l[decodeURIComponent(_)]=E!==void 0?decodeURIComponent(E):"")}}const Ae=Wt(),Ie=c(!1),_e=c(null);let q=c(null),g,i;const B=c(!1),Q=c(0),b=c(!1);let K=0,O=0,oe=1;const y=()=>{O+=.03*oe,O>=1?(O=1,oe=-1):O<=0&&(O=0,oe=1),i&&i.draw(!0,!0),requestAnimationFrame(y)};y();const S=()=>{const a=_e.value,l=Math.max(window.devicePixelRatio,1),{width:s,height:f}=a.getBoundingClientRect();a.width=Math.round(s*l),a.height=Math.round(f*l),a.getContext("2d").scale(l,l),i.scale_offset=[1,1],i.ds.scale=1,i.dirty_canvas=!0,i.dirty_bgcanvas=!0,i==null||i.draw(!0,!0)},r=a=>{var l;if(B.value){a.preventDefault();return}(l=a.dataTransfer)!=null&&l.types.includes("Files")&&(a.preventDefault(),K++,b.value=!0)},d=a=>{var s,f;if(B.value){a.preventDefault();return}(((s=a.dataTransfer)==null?void 0:s.types.includes("node-type"))||(f=a.dataTransfer)!=null&&f.types.includes("Files"))&&(a.preventDefault(),a.dataTransfer.dropEffect="copy")},A=a=>{var l;if(B.value){a.preventDefault();return}(l=a.dataTransfer)!=null&&l.types.includes("Files")&&(a.preventDefault(),K--,K<=0&&(b.value=!1,K=0))},J=async a=>{var s,f,_,E,se,$,he;if(o.owner==="*"){Ae.warning("当前为模版,不支持导入覆盖，请先点击保存按钮创建个人工作流后，再进行导入!");return}if(B.value){if(a.preventDefault(),(s=a.dataTransfer)!=null&&s.types.includes("Files")){const ee=a.dataTransfer.files;ee!=null&&ee.length&&ee[0].name.toLowerCase().endsWith(".json")&&Ae.warning("工作流运行时无法导入 JSON 文件")}return}if((f=a.dataTransfer)==null?void 0:f.types.includes("node-type")){const ee=(_=a.dataTransfer)==null?void 0:_.getData("node-type");if(console.log("nodeType:",ee),ee&&g){const Ce=(E=_e.value)==null?void 0:E.getBoundingClientRect();if(Ce){const Me=a.clientX-Ce.left,xe=a.clientY-Ce.top,Y=i.ds.scale||1,ne=i.ds.offset||[0,0],tt=[(Me-ne[0])/Y,(xe-ne[1])/Y],We=Oe.createNode(ee);if(We){We.pos=tt;let Le=0;for(const Fe of g.nodes)Fe.order&&Fe.order>Le&&(Le=Fe.order);We.order=Le+1e3,g.add(We),g.setDirtyCanvas(!0),i.draw(!0,!0)}}}return}if((se=a.dataTransfer)!=null&&se.types.includes("Files")){a.preventDefault(),b.value=!1,K=0;const ee=a.dataTransfer.files;if(!(ee!=null&&ee.length))return;const Ce=ee[0];if(!Ce.name.toLowerCase().endsWith(".json")){Lt("请拖入 JSON 文件");return}try{const Me=await Ce.text(),xe=JSON.parse(Me).litegraph;if(!g||!i){console.error("Graph or GraphCanvas not initialized");return}g.clear();try{if(g.configure(xe),g.nodes.forEach(Y=>{Y._fullTitle?Y.title=Y.truncateTitle(Y._fullTitle):(Y._fullTitle=Y.title||"",Y.title=Y.truncateTitle(Y.title||""))}),g.nodes.length>0){const Y=g.nodes[0],ne=_e.value.getBoundingClientRect(),tt=ne.width,We=ne.height;i.setZoom(1);const Le=Y.pos[0],Fe=Y.pos[1],Pe=(($=Y.size)==null?void 0:$[0])||0,ct=((he=Y.size)==null?void 0:he[1])||0;i.ds.offset=[tt/2-(Le+Pe/2),We/2-(Fe+ct/2)],i.setDirty(!0,!0)}else i.setZoom(1),i.ds.offset=[0,0];i.draw(!0,!0),console.log("Graph imported successfully")}catch(Y){console.error("Error configuring graph:",Y),Lt("导入失败：图形数据格式不正确")}}catch(Me){console.error("Import failed:",Me),Lt("导入失败，请检查文件格式是否正确")}}},me=()=>{const a=getComputedStyle(document.documentElement),l=f=>a.getPropertyValue(f).trim();Oe.NODE_DEFAULT_BGCOLOR=l("--node-bg"),Oe.NODE_TEXT_COLOR=l("--node-text"),Oe.BACKGROUND_IMAGE_COLOR=l("--node-bg"),Oe.GRID_COLOR=l("--grid-color");const s=document.documentElement.classList.contains("dark");Oe.LGraphCanvas.DEFAULT_BACKGROUND_IMAGE,Oe.LGraphCanvas.link_type_colors={tezheng:"#339966",mubiao:"#CC3366",hangqing:"#3366CC",out1:"#CC6600",out2:"#0ff",target1:"#0ff",target2:"#f56c6c"}},ke=c(!1);je(ke,a=>{a&&i.visible_nodes.forEach(l=>{l.inputs.forEach((s,f)=>{})})}),je(B,a=>{!g||!i||(a?(i.allow_dragnodes=!1,i.allow_reconnect_links=!1,i.allow_linking=!1,i.allow_select_nodes=!1,g.nodes.forEach(l=>{l.inputs&&l.inputs.forEach(s=>{s.locked=!0}),l.outputs&&l.outputs.forEach(s=>{s.locked=!0}),l.widgets&&l.widgets.forEach(s=>{s.disabled=!0,s.element&&(s.element.style.opacity="0.5",s.element.style.cursor="crosshair",s.element.disabled=!0),console.log("widget:",s)})})):(i.allow_dragnodes=!0,i.allow_dragcanvas=!0,i.allow_reconnect_links=!0,i.allow_linking=!0,i.allow_select_nodes=!0,g.nodes.forEach(l=>{l.inputs&&l.inputs.forEach(s=>{s.locked=!1}),l.outputs&&l.outputs.forEach(s=>{s.locked=!1}),l.widgets&&l.widgets.forEach(s=>{s.disabled=!1,s.element&&(s.element.style.opacity="1",s.element.style.cursor="auto",s.element.disabled=!1)})})),i.draw(!0,!0))});const Te=async a=>{try{const s={uid:"0","quantflow-auth":"2","Content-Type":"application/json",Authorization:`${localStorage.getItem("token")}`},f=await fetch(`http://localhost:8000/api/workflow/query?workflow_id=${a}`,{headers:s});if(f.status===404)return Ae.error("工作流不存在!"),null;if(f.ok){const _=await f.json();return o.title=_.name,o.owner=_.owner,console.log("获取workflow数据成功:",_),_.last_run_id&&(o.workflow_run_id=_.last_run_id,Ue(null,"auto")),_}else throw new Error(`请求失败: ${f.status} ${f.statusText}`)}catch(l){return console.error("获取workflow数据失败:",l),null}},He=a=>{try{if(!a){console.error("获取workflow数据为空:",a);return}const l=a.litegraph;if(!g||!i){console.error("Graph or GraphCanvas not initialized");return}g.clear();try{if(g.configure(l),g.nodes.forEach(s=>{s._fullTitle?s.title=s.truncateTitle(s._fullTitle):(s._fullTitle=s.title||"",s.title=s.truncateTitle(s.title||""))}),g.nodes.length>0){let s=1/0,f=-1/0,_=1/0,E=-1/0;g.nodes.forEach(Fe=>{var Pe,ct;s=Math.min(s,Fe.pos[0]),f=Math.max(f,Fe.pos[0]+(((Pe=Fe.size)==null?void 0:Pe[0])||0)),_=Math.min(_,Fe.pos[1]),E=Math.max(E,Fe.pos[1]+(((ct=Fe.size)==null?void 0:ct[1])||0))});const se=f-s,$=E-_,he=_e.value.getBoundingClientRect(),ee=he.width,Ce=he.height,Me=100,xe=(ee-Me*2)/se,Y=(Ce-Me*2)/$,ne=Math.min(xe,Y,1);i.setZoom(ne);const tt=(s+f)/2,We=(_+E)/2,Le=window.devicePixelRatio||1;i.ds.offset=[ee/2/Le-tt*ne+350,Ce/2/Le-We*ne+100],i.setDirty(!0,!0)}else i.setZoom(1),i.ds.offset=[0,0];i.draw(!0,!0),console.log("Graph imported successfully")}catch(s){console.error("Error configuring graph:",s),console.log("api工作流数据导入失败：图形数据格式不正确",s)}}catch(l){console.error("Import failed:",l),console.log("api工作流数据导入失败，请检查文件格式是否正确",l)}};ht(async()=>{var _;(_=document.querySelector(".chartCode"))==null||_.addEventListener("click",E=>{try{const se=E.target.closest(".codeApply"),$=E.target.closest(".codeCopy");if(se){console.log("应用");const he=o.codeMap.get(se.dataset.id);o.original_code=he,console.log("应用code:",he),N.value=!0,setTimeout(()=>{n==null||n.setValue(he)},400)}if($){console.log("复制");const he=o.codeMap.get($.dataset.id);console.log("复制code:",he),navigator.clipboard.writeText(he)}}catch(se){console.log("e1:",se)}}),N.value?(v=ft.createDiffEditor(M.value,{automaticLayout:!0,theme:"vs-dark",readOnly:!1,renderSideBySide:!1,ignoreTrimWhitespace:!1}),I.onDidChangeModelContent(()=>{console.log("内容改变2"),o.original_code=v.getValue()}),setTimeout(()=>{v&&o.code.get(o.id)&&(I=ft.createModel(o.code.get(o.id),"python"),n=ft.createModel(o.code.get(o.id),"python"),I&&n&&v.setModel({original:I,modified:n}))},60)):(v=ft.create(M.value,{automaticLayout:!0,theme:"vs-dark",readOnly:!1,language:"python",value:o.code.get(o.id)||""}),I=v.getModel(),v&&v.onDidChangeModelContent(()=>{console.log("内容改变：",v.getValue()),o.original_code=v.getValue(),o.code.set(o.id,v.getValue())}));const a=Be(window.location.href);if(console.log("params:",a),console.log("params.workflow_id:",a.workflow_id),a.workflow_id===""||!a.workflow_id?(o.workflow_id="",o.owner="",o.workflow_run_id="",o.title=""):o.workflow_id=a.workflow_id,console.log("onMounted"),!_e.value)return;me(),Oe.LGraphCanvas.DEFAULT_BACKGROUND_IMAGE="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAIAAAD/gAIDAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAQBJREFUeNrs1rEKwjAUhlETUkj3vP9rdmr1Ysammk2w5wdxuLgcMHyptfawuZX4pJSWZTnfnu/lnIe/jNNxHHGNn//HNbbv+4dr6V+11uF527arU7+u63qfa/bnmh8sWLBgwYJlqRf8MEptXPBXJXa37BSl3ixYsGDBMliwFLyCV/DeLIMFCxYsWLBMwSt4Be/NggXLYMGCBUvBK3iNruC9WbBgwYJlsGApeAWv4L1ZBgsWLFiwYJmCV/AK3psFC5bBggULloJX8BpdwXuzYMGCBctgwVLwCl7Be7MMFixYsGDBsu8FH1FaSmExVfAxBa/gvVmwYMGCZbBg/W4vAQYA5tRF9QYlv/QAAAAASUVORK5CYII=",Oe.pointerListenerAdd(_e.value,"move",E=>{ke.value=i.pointer.dragStarted}),g=new Oe.LGraph,i=new Oe.LGraphCanvas(_e.value,g),q.value=g,i.high_quality=!0,i.render_connections_border=!0,i.render_curved_connections=!0,i.links_render_mode=Oe.SPLINE_LINK,i.render_canvas_border=!1,i.render_connection_arrows=!1,i.render_curved_connections=!1,i.links_render_mode=Oe.SPLINE_LINK,i.render_shadows=!0,i.zoom_modify_alpha=!0;const l={canvas:i,graph:g,canvasContainer:_e.value.parentElement};ta(l);const s=()=>{i&&me()},f=new MutationObserver(()=>{s()});if(f.observe(document.documentElement,{attributes:!0,attributeFilter:["class"]}),i.setZoom(Math.max(window.devicePixelRatio,1)),i.ds.offset=[0,0],i.allow_dragcanvas=!0,i.allow_dragnodes=!0,i.allow_interaction=!0,i.allow_searchbox=!1,i.drag_mode=!1,i.allow_reconnect_links=!0,i.allow_zoom=!0,i.onShowNodeCreationDialog=()=>!1,i.onDrawForeground=E=>{for(const se of i.visible_nodes){const $=se._pos,he=se.size,ee=Oe.NODE_TITLE_HEIGHT,Ce=se.flags.collapsed?ee:he[1]+ee,Me=6,xe=15;if(E.strokeStyle="rgba(39,103,238,0)",E.lineWidth=0,se.status==="success")E.strokeStyle="#33FF00",E.lineWidth=2;else if(se.status==="failed")E.strokeStyle="#FF0033",E.lineWidth=7;else if(se.status==="running"){const Fe=0+O*.6;E.strokeStyle=`rgba(51, 102, 255,${Fe})`,E.lineWidth=4}E.beginPath();const Y=$[0]-Me,ne=$[1]-ee-Me;let tt=he[0];se.flags.collapsed&&(E.save(),E.restore(),tt=Math.max(se.width));const We=tt+Me*2,Le=Ce+Me*2;E.moveTo(Y+xe,ne),E.lineTo(Y+We-xe,ne),E.quadraticCurveTo(Y+We,ne,Y+We,ne+xe),E.lineTo(Y+We,ne+Le-xe),E.quadraticCurveTo(Y+We,ne+Le,Y+We-xe,ne+Le),E.lineTo(Y+xe,ne+Le),E.quadraticCurveTo(Y,ne+Le,Y,ne+Le-xe),E.lineTo(Y,ne+xe),E.quadraticCurveTo(Y,ne,Y+xe,ne),E.stroke()}},S(),window.addEventListener("resize",S),g.runStep(),s(),g.start(),Ie.value=!0,g.change(),i.draw(!0,!0),Oe.NODE_BOX_OUTLINE_COLOR="rgba(39,103,238,0.65)",Oe.WIDGET_BGCOLOR="rgba(16,18,19,0.8)",Oe.WIDGET_SECONDARY_TEXT_COLOR="#858585",Oe.NODE_WIDGET_HEIGHT=25,await new Promise(E=>setTimeout(E,500)),a.workflow_id){const E=await Te(a.workflow_id);He(E)}yt(()=>{f.disconnect(),window.removeEventListener("resize",S),g&&(g.stop(),Ie.value=!1)}),window.addEventListener("keydown",Se)}),yt(()=>{window.removeEventListener("keydown",Se)});function Se(a){(a.ctrlKey||a.metaKey)&&a.key==="s"&&(a.preventDefault(),X())}const ze=()=>{const a=g.serialize(),l={format_version:"2.0",name:o.title?o.title:`workflow-${new Date().toISOString().replace(/[:]/g,"-").slice(0,19)}`,description:"",litegraph:a,nodes:[],links:[]};o.title=l.name;const s=Be(window.location.href);return s.workflow_id&&(l.id=s.workflow_id),console.log("graphData.nodes:",a.nodes),a.nodes&&Array.isArray(a.nodes)&&(l.nodes=a.nodes.map(f=>{const _=Object.keys(f.properties),E={};return _.forEach(se=>{var ee;const $=(ee=f.inputs.find(Ce=>Ce.name===se))==null?void 0:ee.fieldName,he=f.properties[se];E[$]=he}),a.links.find(se=>f.id===se[1]),{uuid:f.flags.uuid,title:f.title||"",name:f.type||"",type:f.type||"",litegraph_id:f.id||0,positionX:f.pos?f.pos[0]:0,positionY:f.pos?f.pos[1]:0,width:f.size?f.size[0]:0,height:f.size?f.size[1]:0,static_input_data:E||{},output_db_id:null}})),a.links&&Array.isArray(a.links)&&(l.links=a.links.map(f=>{var xe,Y;const[_,E,se,$,he,ee]=f,Ce=a.nodes.find(ne=>ne.id===E),Me=a.nodes.find(ne=>ne.id===$);return console.log("sourceNode,targetNode:",Ce,Me),{uuid:Ot(),litegraph_id:E,status:1,previous_node_uuid:Ce.flags.uuid,next_node_uuid:Me.flags.uuid,output_field_name:(xe=Me.inputs[he])==null?void 0:xe.fieldName,input_field_name:(Y=Ce.outputs[se])==null?void 0:Y.fieldName}})),l},rt=async()=>{if(ze().nodes.length===0){Ae.warning("当前工作区域为空，不可保存空白工作流！");return}const a=JSON.stringify(ze());try{const l=localStorage.getItem("token"),s=await fetch("http://localhost:8000/api/workflow/save",{method:"POST",headers:{uid:"0","quantflow-auth":"2","Content-Type":"application/json",Authorization:`${l}`},body:a});if(s.ok){const f=await s.json();console.log("保存的workflow_id:",f);const _=f.data&&f.data.workflow_id;o.workflow_id=_;const E=new URL(window.location.href);if(E.searchParams.set("workflow_id",_),window.history.replaceState({},"",E),console.log("保存的的workflow_id:",_),o.owner==="*"&&Ae.success("模版工作流克隆成功！正在运行中...",{timeout:2e3}),o.owner="",o.workflow_id){const he=new URL(window.location.href);he.searchParams.set("workflow_id",o.workflow_id),window.history.replaceState({},"",he)}const se={uid:"0","quantflow-auth":"2","Content-Type":"application/json",Authorization:`${l}`},$=await fetch(`http://localhost:8000/api/workflow/query?workflow_id=${f.data.workflow_id}`,{headers:se});if($.status===404)return console.log("工作流不存在!"),null;if($.ok){const he=await $.json();console.log("data:",he),o.title=he.name}return _}else throw new Error(`请求失败: ${s.status} ${s.statusText}`)}catch(l){return console.error("保存图表时出错:",l),Ae.error("运行失败，请重试！"),null}},qe=c(0),lt=async a=>{try{qe.value=-1;const l=localStorage.getItem("token"),s=await fetch("http://localhost:8000/api/workflow/run",{method:"POST",headers:{uid:"0","quantflow-auth":"2","Content-Type":"application/json",Authorization:`${l}`},body:JSON.stringify({workflow_id:a})});if(console.log("运行模式:","LOCAL"),s.ok){const f=await s.json();console.log("运行结果:",f);const _=f.data&&f.data.workflow_run_id;return o.workflow_run_id=_,_}else throw new Error(`请求失败: ${s.status} ${s.statusText}`)}catch(l){return console.error("运行图表时出错:",l),Ae.error("运行失败，请重试！"),null}},Z=async a=>{console.log("nodeStatus:",a),g.nodes.forEach(l=>{l.status=null}),a.success_node_ids&&a.success_node_ids.forEach(l=>{const s=g.nodes.find(f=>f.flags.uuid===l);s&&(s.status="success",i.draw(!0,!0))}),a.running_node_ids&&a.running_node_ids.forEach(l=>{const s=g.nodes.find(f=>f.flags.uuid===l);s&&(s.status="running",i.draw(!0,!0))}),a.failed_node_ids&&a.failed_node_ids.forEach(l=>{const s=g.nodes.find(f=>f.flags.uuid===l);s&&(s.status="failed",i.draw(!0,!0))})},Xe=c(!0),et=async(a,l,s,f,_)=>{try{qe.value++;const E=localStorage.getItem("token"),se=await Gt.get("http://localhost:8000/api/workflow/run",{params:{workflow_run_id:a,last_log_id:l},headers:{uid:"0","quantflow-auth":"2","Content-Type":"application/json",Authorization:`${E}`}});if(se.status===200){const $=se.data;console.log("轮询结果:",$),Z($.data);const he=$.data&&$.data.progress;Q.value=he,he<100&&B.value?o.pollTimer=setTimeout(et.bind(this,a,l,s,f,_),f):(Xe.value=!0,_==="click"?Ae.success("运行完成！"):_==="auto"&&Ae.success("获取运行结果成功！"),console.log("轮询结束:",$.data),o.pollData={...$.data},B.value=!1,clearTimeout(o.pollTimer));const ee=$.data&&$.data.status;if(ee>=2&&(B.value=!1,Q.value=he,clearTimeout(o.pollTimer),ee==3)){const Ce=$.data.last_error_message+`
`+$.data.last_error_stacktrace;_!=="auto"&&(Xe.value=!1),setTimeout(()=>{Xe.value=!0},300)}ee>2&&_==="auto"&&g.nodes.forEach(Ce=>{Ce.status=null})}else throw new Error(`请求失败: ${se.status} ${se.statusText}`)}catch(E){console.error("轮询工作流状态时出错:",E),B.value=!1,clearTimeout(o.pollTimer)}},Ue=async(a,l)=>{if(a&&Ye(a),a){if(o.workflow_id=await rt(),console.log("workflow_id:",o.workflow_id),o.workflow_id){const s=await lt(o.workflow_id);o.workflow_run_id=s,console.log("workflow_run_id11:",o.workflow_run_id),et(s,0,o.user_id,2e3,"click"),B.value=!0,Q.value=0,o.pollData=null}}else et(o.workflow_run_id,0,o.user_id,2e3,"auto"),B.value=!0,Q.value=0,o.pollData=null},it=async a=>{a&&Ye(a);try{B.value=!1,Q.value=0,clearTimeout(o.pollTimer),await new Promise(f=>setTimeout(f,500)),g.nodes.forEach(f=>{f.status=null}),i.allow_dragnodes=!0,i.allow_dragcanvas=!0,i.allow_reconnect_links=!0,i.allow_linking=!0,i.allow_select_nodes=!0,g.nodes.forEach(f=>{f.inputs&&f.inputs.forEach(_=>{_.locked=!1}),f.outputs&&f.outputs.forEach(_=>{_.locked=!1}),f.widgets&&f.widgets.forEach(_=>{_.disabled=!1})}),g.setDirtyCanvas(!0),i.draw(!0,!0);const l=localStorage.getItem("token"),s=await fetch("http://localhost:8000/api/workflow/run/terminate",{method:"POST",headers:{uid:"0","quantflow-auth":"2","Content-Type":"application/json",Authorization:`${l}`},body:JSON.stringify({workflow_run_id:o.workflow_run_id})});if(s.ok)console.log("停止运行成功"),Ae.success("停止运行成功");else throw new Error(`请求失败: ${s.status} ${s.statusText}`)}catch(l){console.error("停止运行时出错:",l),Ae.error("停止运行失败")}},Ye=a=>{const l=a.currentTarget;l.classList.remove("animating-ripple"),l.offsetWidth,l.classList.add("animating-ripple"),setTimeout(()=>{l.classList.remove("animating-ripple")},600)};return je(N,async a=>{try{v&&(v.dispose(),v=null),I&&(I.dispose(),I=null),n&&(n.dispose(),n=null)}catch(l){console.log(l)}try{a?(v=ft.createDiffEditor(M.value,{automaticLayout:!0,theme:"vs-dark",readOnly:!1,renderSideBySide:!1,ignoreTrimWhitespace:!1}),console.log("workflowStore.code:",o.code),I=ft.createModel(o.code.get(o.id)||"","python"),n=ft.createModel(o.code.get(o.id)||"","python"),v.setModel({original:I,modified:n})):(v=ft.create(M.value,{automaticLayout:!0,theme:"vs-dark",readOnly:!1,language:"python",value:o.code.get(o.id)||""}),I=v.getModel(),setTimeout(()=>{v.setValue(o.code.get(o.id))},30))}catch(l){console.log("e2：",l)}try{v&&v.onDidChangeModelContent(()=>{console.log("内容改成3"),o.original_code=v.getValue(),o.code.set(o.id,v.getValue())})}catch(l){console.log(l)}}),(a,l)=>(w(),x(Ve,null,[$e(k).isBoxShow?(w(),x("div",rn,[e("div",cn,[e("img",{class:"close-btn",src:Mt,alt:"close",style:{width:"16px",height:"16px",cursor:"pointer"},onClick:l[0]||(l[0]=(...s)=>$e(k).closeBox&&$e(k).closeBox(...s))})]),e("div",dn,[De(os,{into:!0,factorId:"",taskId:$e(k).id},null,8,["taskId"])])])):Ne("",!0),$e(G).isBoxShow?(w(),x("div",un,[e("div",fn,[e("img",{class:"close-btn",src:Mt,alt:"close",style:{width:"16px",height:"16px",cursor:"pointer"},onClick:l[1]||(l[1]=(...s)=>$e(G).closeBox&&$e(G).closeBox(...s))})]),e("div",hn,[De(xa,{ref_key:"strategyAnalysisRef",ref:be},null,512)])])):Ne("",!0),e("div",{class:"graph-editor litegraph .litegraph-editor",onDragenter:vt(r,["prevent"]),onDragover:vt(d,["prevent"]),onDragleave:vt(A,["prevent"]),onDrop:vt(J,["prevent"])},[e("div",gn,[e("canvas",{ref_key:"canvas",ref:_e},null,512),e("div",pn,[De(Qa,{"is-running":B.value,progress:Q.value,graph:$e(g),"is-graph-ready":Ie.value,canvas:$e(i)},null,8,["is-running","progress","graph","is-graph-ready","canvas"])]),e("div",vn,[B.value?(w(),x("div",An,P(Q.value.toFixed(2))+"%",1)):Ne("",!0),B.value?(w(),x("div",{key:1,class:"progress-bar",style:Ze({width:Q.value+"%"})},null,4)):Ne("",!0),B.value?(w(),x("button",{key:3,class:"run-button stop-run-button",onClick:l[3]||(l[3]=s=>it(s))},l[5]||(l[5]=[e("svg",{class:"loading-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[e("path",{d:"M6 19h4V5H6v14zm8-14v14h4V5h-4z",fill:"currentColor"})],-1)]))):(w(),x("button",{key:2,class:"run-button",onClick:l[2]||(l[2]=s=>Ue(s))},l[4]||(l[4]=[e("svg",{class:"play-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[e("path",{d:"M8 5.14v14.72a1 1 0 001.5.86l11-7.36a1 1 0 000-1.72l-11-7.36a1 1 0 00-1.5.86z",fill:"currentColor"})],-1)])))]),De(Ss,{logCount:qe.value,isCollapsed:Xe.value},null,8,["logCount","isCollapsed"]),De(aa,{name:"fade"},{default:oa(()=>[b.value?(w(),x("div",mn,l[6]||(l[6]=[e("div",{class:"drag-message"},[e("svg",{class:"upload-icon",width:"48",height:"48",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[e("path",{d:"M12 3L20 10H16.5V19H7.5V10H4L12 3Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})]),e("p",null,"释放以导入 JSON 文件")],-1)]))):Ne("",!0)]),_:1})])],32),st(e("div",yn,[e("div",wn,[e("div",bn,[e("img",{onClick:ae,class:"close-btn",src:qt}),l[9]||(l[9]=e("div",{class:"editorMonaco-head",style:{height:"40px","line-height":"40px","text-indent":"20px",color:"#fff","font-size":"14px"}},"编辑代码",-1)),e("div",xn,[e("div",{class:"editorMonaco",style:{height:"100%",width:"65%",position:"relative"},ref_key:"editorDom",ref:M},[N.value?(w(),x("div",kn,[e("button",{class:"apply-btn",onClick:Ee},l[7]||(l[7]=[e("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[e("path",{d:"M4 8.5L7 11.5L12 5.5",stroke:"white","stroke-width":"1.3","stroke-linecap":"round","stroke-linejoin":"round"})],-1),At(" 应用 ")])),e("button",{class:"reject-btn",onClick:W},l[8]||(l[8]=[e("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[e("path",{d:"M4 4L12 12M12 4L4 12",stroke:"white","stroke-width":"1.3","stroke-linecap":"round"})],-1),At(" 拒绝 ")]))])):Ne("",!0)],512),e("div",_n,[(w(),Et(na,null,[$e(o).codeStatus?(w(),Et(sa(ln),{key:$e(o).id})):Ne("",!0)],1024))])]),e("div",{class:"editorMonaco-foot",style:{height:"40px","padding-top":"15px"}},[e("div",{class:"content-btn active",onClick:X,style:{display:"inline-flex",float:"left","margin-left":"20px"}},"保存")])])])],512),[[bt,$e(o).codeStatus]])],64))}}),Ln=nt(Cn,[["__scopeId","data-v-4bed71e3"]]),En={class:"quantflow-container"},In={class:"content-container"},Sn={class:"aside-container"},Rn=mt({__name:"Quantflow",setup(H){const M=c(!0),N=v=>{M.value=v};return(v,I)=>(w(),x("div",En,[De(Yt),I[0]||(I[0]=At()),e("div",In,[e("div",Sn,[De(qa,{onCategoriesLoadingState:N})]),De(Ln)])]))}}),Un=nt(Rn,[["__scopeId","data-v-36d9fec6"]]);export{Un as default};
