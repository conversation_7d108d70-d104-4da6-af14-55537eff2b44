import{_ as ft,c as F,a as tt,o as O,b as r,d as Pt,r as C,H as lt,F as et,k as ht,i as M,t as B,a3 as Je,P as Jt,O as kn,B as We,e as En,v as pt,y as Et,z as Cn,m as De,L as Sn,N as Wt,j as U,h as rt,w as Tn,I as An,S as xn,A as Ut,x as Rn,f as Lt,J as Pe,C as $n,g as Dn,a8 as Mt,l as Pn}from"./main-CZkS48P-.js";import{r as Ln}from"./index-DjmxrE12.js";const xr="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAAYtJREFUWEfV2dFxgzAMAFDZS2SHbEG26D8cPx2nP9zx3y2aLbIDGcLOqcW9lGBLlgym+cQGPxQsccIAALRtezLGfFpr34dhuOGxWr++78/OuQ/v/ds4jpOZcV8AcAaAu7W2qYWccWg5AcDNe38xXdfhgeYpYlWQC1zgXE1kYFdkxDB57xuD1JrIGM5ae8FH7RtYC0nh0PULTCCncDcldzYH9wLcC8nFrQK3RubgosCtkLm4JLA0UoIjgaWQUhwLqEVqcGygFKnFZQFzkSVw2UAushROBKSQOO6cC69MofiIq9GfUpdTymJRmq+B73NqnDiCYeUI8vk+xZELFxFHkIFU49QRTDyPOFQfeOi/+NCbJJXnqqcZThLmzOGmtKxdnLNwztwUlg2ULCg5Z4llATULac5l5UHtAlTtptosyQiWwBEVh0zmUWBJnAa5CtwCJ0W+ALfESZD/p/WxR+SWOY6zZqr9Ru4wbrlKzaOQsQbmLjjOM7nWAt4VRyCvyyZ6FVwE+dNEx8Ejf4Z4AOhffFtCFuoVAAAAAElFTkSuQmCC",On={name:"Loading",props:{visible:{type:Boolean,default:!1}}},Fn={key:0,class:"loading-mask"};function Nn(e,t,n,s,a,o){return n.visible?(O(),F("div",Fn,t[0]||(t[0]=[r("div",{class:"simple-spinner"},null,-1),r("span",null,"loading...",-1)]))):tt("",!0)}const ee=ft(On,[["render",Nn],["__scopeId","data-v-62b7c2f0"]]),jn="http://localhost:8111",Bn=1e4;function In(e){var t;throw(e==null?void 0:e.name)==="AbortError"?new Error("请求超时,超过10秒"):(t=e==null?void 0:e.message)!=null&&t.includes("Failed to fetch")?new Error("网络连接失败，请检查网络是否正常！"):(console.log("请求错误:",e),e)}async function X(e,t){const n={},s=localStorage.getItem("token"),a=localStorage.getItem("userid");s&&a&&(n.Authorization=`${s}`,n.userId=`${a}`);const o=new AbortController,i=setTimeout(()=>o.abort(),Bn);try{const l=await fetch(jn+"/"+e,{headers:n,...t,signal:o.signal});if(!l.ok)throw new Error(`HTTP error! status: ${l.status}`);return Promise.resolve({response:l,httpController:o})}catch(l){In(l)}finally{clearTimeout(i)}}async function Rr(e){try{const t=await X(`api/v1/query_one_group_data?task_id=${e}`);if(!(t!=null&&t.response))throw new Error("请求失败：未收到响应");return{json:await t.response.json(),httpController:t.httpController}}catch(t){throw t}}async function $r(e){try{const t=await X(`api/v1/query_factor_analysis_data?task_id=${e}`);if(!(t!=null&&t.response))throw new Error("请求失败：未收到响应");return{json:await t.response.json(),httpController:t.httpController}}catch(t){throw t}}async function Dr(e){try{const t=await X(`api/v1/query_ic_decay_chart?task_id=${e}`);if(!(t!=null&&t.response))throw new Error("请求失败：未收到响应");return{json:await t.response.json(),httpController:t.httpController}}catch(t){throw t}}async function Pr(e){try{const t=await X(`api/v1/query_ic_density_chart?task_id=${e}`);if(!(t!=null&&t.response))throw new Error("请求失败：未收到响应");return{json:await t.response.json(),httpController:t.httpController}}catch(t){throw t}}async function Lr(e){try{const t=await X(`api/v1/query_ic_sequence_chart?task_id=${e}`);if(!(t!=null&&t.response))throw new Error("请求失败：未收到响应");return{json:await t.response.json(),httpController:t.httpController}}catch(t){throw t}}async function Or(e){try{const t=await X(`api/v1/query_ic_self_correlation_chart?task_id=${e}`);if(!(t!=null&&t.response))throw new Error("请求失败：未收到响应");return{json:await t.response.json(),httpController:t.httpController}}catch(t){throw t}}async function Fr(e){try{const t=await X(`api/v1/query_rank_ic_decay_chart?task_id=${e}`);if(!(t!=null&&t.response))throw new Error("请求失败：未收到响应");return{json:await t.response.json(),httpController:t.httpController}}catch(t){throw t}}async function Nr(e){try{const t=await X(`api/v1/query_rank_ic_density_chart?task_id=${e}`);if(!(t!=null&&t.response))throw new Error("请求失败：未收到响应");return{json:await t.response.json(),httpController:t.httpController}}catch(t){throw t}}async function jr(e){try{const t=await X(`api/v1/query_rank_ic_sequence_chart?task_id=${e}`);if(!(t!=null&&t.response))throw new Error("请求失败：未收到响应");return{json:await t.response.json(),httpController:t.httpController}}catch(t){throw t}}async function Br(e){try{const t=await X(`api/v1/query_rank_ic_self_correlation_chart?task_id=${e}`);if(!(t!=null&&t.response))throw new Error("请求失败：未收到响应");return{json:await t.response.json(),httpController:t.httpController}}catch(t){throw t}}async function Ir(e){try{const t=await X(`api/v1/query_return_chart?task_id=${e}`);if(!(t!=null&&t.response))throw new Error("请求失败：未收到响应");return{json:await t.response.json(),httpController:t.httpController}}catch(t){throw t}}async function qr(e){try{const t=await X(`api/v1/query_factor_excess_chart?task_id=${e}`);if(!(t!=null&&t.response))throw new Error("请求失败：未收到响应");return{json:await t.response.json(),httpController:t.httpController}}catch(t){throw t}}async function Ur(e){try{const t=await X(`api/v1/query_group_return_analysis?task_id=${e}`);if(!(t!=null&&t.response))throw new Error("请求失败：未收到响应");return{json:await t.response.json(),httpController:t.httpController}}catch(t){throw t}}async function Mr(e){try{const t=await X(`api/v1/query_last_date_top_factor?task_id=${e}`);if(!(t!=null&&t.response))throw new Error("请求失败：未收到响应");return{json:await t.response.json(),httpController:t.httpController}}catch(t){throw t}}async function zr(e){try{const t=await X(`api/v1/query_return_chart?task_id=${e}`);if(!(t!=null&&t.response))throw new Error("请求失败：未收到响应");return{json:await t.response.json(),httpController:t.httpController}}catch(t){throw t}}async function Hr(e){try{const t=await X(`api/v1/query_factor_correlation_data?task_id=${e}`);if(!(t!=null&&t.response))throw new Error("请求失败：未收到响应");return{json:await t.response.json(),httpController:t.httpController}}catch(t){throw t}}function Ye(e,t){return function(){return e.apply(t,arguments)}}const{toString:qn}=Object.prototype,{getPrototypeOf:ne}=Object,{iterator:Ot,toStringTag:Ke}=Symbol,Ft=(e=>t=>{const n=qn.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),ct=e=>(e=e.toLowerCase(),t=>Ft(t)===e),Nt=e=>t=>typeof t===e,{isArray:mt}=Array,Ct=Nt("undefined");function Un(e){return e!==null&&!Ct(e)&&e.constructor!==null&&!Ct(e.constructor)&&nt(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const Qe=ct("ArrayBuffer");function Mn(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&Qe(e.buffer),t}const zn=Nt("string"),nt=Nt("function"),Ze=Nt("number"),jt=e=>e!==null&&typeof e=="object",Hn=e=>e===!0||e===!1,At=e=>{if(Ft(e)!=="object")return!1;const t=ne(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Ke in e)&&!(Ot in e)},Vn=ct("Date"),Gn=ct("File"),Jn=ct("Blob"),Wn=ct("FileList"),Yn=e=>jt(e)&&nt(e.pipe),Kn=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||nt(e.append)&&((t=Ft(e))==="formdata"||t==="object"&&nt(e.toString)&&e.toString()==="[object FormData]"))},Qn=ct("URLSearchParams"),[Zn,Xn,ts,es]=["ReadableStream","Request","Response","Headers"].map(ct),ns=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function St(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let s,a;if(typeof e!="object"&&(e=[e]),mt(e))for(s=0,a=e.length;s<a;s++)t.call(null,e[s],s,e);else{const o=n?Object.getOwnPropertyNames(e):Object.keys(e),i=o.length;let l;for(s=0;s<i;s++)l=o[s],t.call(null,e[l],l,e)}}function Xe(e,t){t=t.toLowerCase();const n=Object.keys(e);let s=n.length,a;for(;s-- >0;)if(a=n[s],t===a.toLowerCase())return a;return null}const bt=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,tn=e=>!Ct(e)&&e!==bt;function Yt(){const{caseless:e}=tn(this)&&this||{},t={},n=(s,a)=>{const o=e&&Xe(t,a)||a;At(t[o])&&At(s)?t[o]=Yt(t[o],s):At(s)?t[o]=Yt({},s):mt(s)?t[o]=s.slice():t[o]=s};for(let s=0,a=arguments.length;s<a;s++)arguments[s]&&St(arguments[s],n);return t}const ss=(e,t,n,{allOwnKeys:s}={})=>(St(t,(a,o)=>{n&&nt(a)?e[o]=Ye(a,n):e[o]=a},{allOwnKeys:s}),e),as=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),os=(e,t,n,s)=>{e.prototype=Object.create(t.prototype,s),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},rs=(e,t,n,s)=>{let a,o,i;const l={};if(t=t||{},e==null)return t;do{for(a=Object.getOwnPropertyNames(e),o=a.length;o-- >0;)i=a[o],(!s||s(i,e,t))&&!l[i]&&(t[i]=e[i],l[i]=!0);e=n!==!1&&ne(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},is=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const s=e.indexOf(t,n);return s!==-1&&s===n},ls=e=>{if(!e)return null;if(mt(e))return e;let t=e.length;if(!Ze(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},cs=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&ne(Uint8Array)),us=(e,t)=>{const s=(e&&e[Ot]).call(e);let a;for(;(a=s.next())&&!a.done;){const o=a.value;t.call(e,o[0],o[1])}},ds=(e,t)=>{let n;const s=[];for(;(n=e.exec(t))!==null;)s.push(n);return s},fs=ct("HTMLFormElement"),ps=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,s,a){return s.toUpperCase()+a}),Le=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),hs=ct("RegExp"),en=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),s={};St(n,(a,o)=>{let i;(i=t(a,o,e))!==!1&&(s[o]=i||a)}),Object.defineProperties(e,s)},bs=e=>{en(e,(t,n)=>{if(nt(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const s=e[n];if(nt(s)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},gs=(e,t)=>{const n={},s=a=>{a.forEach(o=>{n[o]=!0})};return mt(e)?s(e):s(String(e).split(t)),n},ys=()=>{},ms=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function vs(e){return!!(e&&nt(e.append)&&e[Ke]==="FormData"&&e[Ot])}const _s=e=>{const t=new Array(10),n=(s,a)=>{if(jt(s)){if(t.indexOf(s)>=0)return;if(!("toJSON"in s)){t[a]=s;const o=mt(s)?[]:{};return St(s,(i,l)=>{const h=n(i,a+1);!Ct(h)&&(o[l]=h)}),t[a]=void 0,o}}return s};return n(e,0)},ws=ct("AsyncFunction"),ks=e=>e&&(jt(e)||nt(e))&&nt(e.then)&&nt(e.catch),nn=((e,t)=>e?setImmediate:t?((n,s)=>(bt.addEventListener("message",({source:a,data:o})=>{a===bt&&o===n&&s.length&&s.shift()()},!1),a=>{s.push(a),bt.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",nt(bt.postMessage)),Es=typeof queueMicrotask<"u"?queueMicrotask.bind(bt):typeof process<"u"&&process.nextTick||nn,Cs=e=>e!=null&&nt(e[Ot]),c={isArray:mt,isArrayBuffer:Qe,isBuffer:Un,isFormData:Kn,isArrayBufferView:Mn,isString:zn,isNumber:Ze,isBoolean:Hn,isObject:jt,isPlainObject:At,isReadableStream:Zn,isRequest:Xn,isResponse:ts,isHeaders:es,isUndefined:Ct,isDate:Vn,isFile:Gn,isBlob:Jn,isRegExp:hs,isFunction:nt,isStream:Yn,isURLSearchParams:Qn,isTypedArray:cs,isFileList:Wn,forEach:St,merge:Yt,extend:ss,trim:ns,stripBOM:as,inherits:os,toFlatObject:rs,kindOf:Ft,kindOfTest:ct,endsWith:is,toArray:ls,forEachEntry:us,matchAll:ds,isHTMLForm:fs,hasOwnProperty:Le,hasOwnProp:Le,reduceDescriptors:en,freezeMethods:bs,toObjectSet:gs,toCamelCase:ps,noop:ys,toFiniteNumber:ms,findKey:Xe,global:bt,isContextDefined:tn,isSpecCompliantForm:vs,toJSONObject:_s,isAsyncFn:ws,isThenable:ks,setImmediate:nn,asap:Es,isIterable:Cs};function I(e,t,n,s,a){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),s&&(this.request=s),a&&(this.response=a,this.status=a.status?a.status:null)}c.inherits(I,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:c.toJSONObject(this.config),code:this.code,status:this.status}}});const sn=I.prototype,an={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{an[e]={value:e}});Object.defineProperties(I,an);Object.defineProperty(sn,"isAxiosError",{value:!0});I.from=(e,t,n,s,a,o)=>{const i=Object.create(sn);return c.toFlatObject(e,i,function(h){return h!==Error.prototype},l=>l!=="isAxiosError"),I.call(i,e.message,t,n,s,a),i.cause=e,i.name=e.name,o&&Object.assign(i,o),i};const Ss=null;function Kt(e){return c.isPlainObject(e)||c.isArray(e)}function on(e){return c.endsWith(e,"[]")?e.slice(0,-2):e}function Oe(e,t,n){return e?e.concat(t).map(function(a,o){return a=on(a),!n&&o?"["+a+"]":a}).join(n?".":""):t}function Ts(e){return c.isArray(e)&&!e.some(Kt)}const As=c.toFlatObject(c,{},null,function(t){return/^is[A-Z]/.test(t)});function Bt(e,t,n){if(!c.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=c.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(m,k){return!c.isUndefined(k[m])});const s=n.metaTokens,a=n.visitor||p,o=n.dots,i=n.indexes,h=(n.Blob||typeof Blob<"u"&&Blob)&&c.isSpecCompliantForm(t);if(!c.isFunction(a))throw new TypeError("visitor must be a function");function u(f){if(f===null)return"";if(c.isDate(f))return f.toISOString();if(!h&&c.isBlob(f))throw new I("Blob is not supported. Use a Buffer instead.");return c.isArrayBuffer(f)||c.isTypedArray(f)?h&&typeof Blob=="function"?new Blob([f]):Buffer.from(f):f}function p(f,m,k){let j=f;if(f&&!k&&typeof f=="object"){if(c.endsWith(m,"{}"))m=s?m:m.slice(0,-2),f=JSON.stringify(f);else if(c.isArray(f)&&Ts(f)||(c.isFileList(f)||c.endsWith(m,"[]"))&&(j=c.toArray(f)))return m=on(m),j.forEach(function(A,H){!(c.isUndefined(A)||A===null)&&t.append(i===!0?Oe([m],H,o):i===null?m:m+"[]",u(A))}),!1}return Kt(f)?!0:(t.append(Oe(k,m,o),u(f)),!1)}const b=[],v=Object.assign(As,{defaultVisitor:p,convertValue:u,isVisitable:Kt});function w(f,m){if(!c.isUndefined(f)){if(b.indexOf(f)!==-1)throw Error("Circular reference detected in "+m.join("."));b.push(f),c.forEach(f,function(j,$){(!(c.isUndefined(j)||j===null)&&a.call(t,j,c.isString($)?$.trim():$,m,v))===!0&&w(j,m?m.concat($):[$])}),b.pop()}}if(!c.isObject(e))throw new TypeError("data must be an object");return w(e),t}function Fe(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(s){return t[s]})}function se(e,t){this._pairs=[],e&&Bt(e,this,t)}const rn=se.prototype;rn.append=function(t,n){this._pairs.push([t,n])};rn.toString=function(t){const n=t?function(s){return t.call(this,s,Fe)}:Fe;return this._pairs.map(function(a){return n(a[0])+"="+n(a[1])},"").join("&")};function xs(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function ln(e,t,n){if(!t)return e;const s=n&&n.encode||xs;c.isFunction(n)&&(n={serialize:n});const a=n&&n.serialize;let o;if(a?o=a(t,n):o=c.isURLSearchParams(t)?t.toString():new se(t,n).toString(s),o){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class Ne{constructor(){this.handlers=[]}use(t,n,s){return this.handlers.push({fulfilled:t,rejected:n,synchronous:s?s.synchronous:!1,runWhen:s?s.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){c.forEach(this.handlers,function(s){s!==null&&t(s)})}}const cn={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Rs=typeof URLSearchParams<"u"?URLSearchParams:se,$s=typeof FormData<"u"?FormData:null,Ds=typeof Blob<"u"?Blob:null,Ps={isBrowser:!0,classes:{URLSearchParams:Rs,FormData:$s,Blob:Ds},protocols:["http","https","file","blob","url","data"]},ae=typeof window<"u"&&typeof document<"u",Qt=typeof navigator=="object"&&navigator||void 0,Ls=ae&&(!Qt||["ReactNative","NativeScript","NS"].indexOf(Qt.product)<0),Os=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Fs=ae&&window.location.href||"http://localhost",Ns=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:ae,hasStandardBrowserEnv:Ls,hasStandardBrowserWebWorkerEnv:Os,navigator:Qt,origin:Fs},Symbol.toStringTag,{value:"Module"})),Z={...Ns,...Ps};function js(e,t){return Bt(e,new Z.classes.URLSearchParams,Object.assign({visitor:function(n,s,a,o){return Z.isNode&&c.isBuffer(n)?(this.append(s,n.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},t))}function Bs(e){return c.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Is(e){const t={},n=Object.keys(e);let s;const a=n.length;let o;for(s=0;s<a;s++)o=n[s],t[o]=e[o];return t}function un(e){function t(n,s,a,o){let i=n[o++];if(i==="__proto__")return!0;const l=Number.isFinite(+i),h=o>=n.length;return i=!i&&c.isArray(a)?a.length:i,h?(c.hasOwnProp(a,i)?a[i]=[a[i],s]:a[i]=s,!l):((!a[i]||!c.isObject(a[i]))&&(a[i]=[]),t(n,s,a[i],o)&&c.isArray(a[i])&&(a[i]=Is(a[i])),!l)}if(c.isFormData(e)&&c.isFunction(e.entries)){const n={};return c.forEachEntry(e,(s,a)=>{t(Bs(s),a,n,0)}),n}return null}function qs(e,t,n){if(c.isString(e))try{return(t||JSON.parse)(e),c.trim(e)}catch(s){if(s.name!=="SyntaxError")throw s}return(n||JSON.stringify)(e)}const Tt={transitional:cn,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const s=n.getContentType()||"",a=s.indexOf("application/json")>-1,o=c.isObject(t);if(o&&c.isHTMLForm(t)&&(t=new FormData(t)),c.isFormData(t))return a?JSON.stringify(un(t)):t;if(c.isArrayBuffer(t)||c.isBuffer(t)||c.isStream(t)||c.isFile(t)||c.isBlob(t)||c.isReadableStream(t))return t;if(c.isArrayBufferView(t))return t.buffer;if(c.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let l;if(o){if(s.indexOf("application/x-www-form-urlencoded")>-1)return js(t,this.formSerializer).toString();if((l=c.isFileList(t))||s.indexOf("multipart/form-data")>-1){const h=this.env&&this.env.FormData;return Bt(l?{"files[]":t}:t,h&&new h,this.formSerializer)}}return o||a?(n.setContentType("application/json",!1),qs(t)):t}],transformResponse:[function(t){const n=this.transitional||Tt.transitional,s=n&&n.forcedJSONParsing,a=this.responseType==="json";if(c.isResponse(t)||c.isReadableStream(t))return t;if(t&&c.isString(t)&&(s&&!this.responseType||a)){const i=!(n&&n.silentJSONParsing)&&a;try{return JSON.parse(t)}catch(l){if(i)throw l.name==="SyntaxError"?I.from(l,I.ERR_BAD_RESPONSE,this,null,this.response):l}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Z.classes.FormData,Blob:Z.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};c.forEach(["delete","get","head","post","put","patch"],e=>{Tt.headers[e]={}});const Us=c.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Ms=e=>{const t={};let n,s,a;return e&&e.split(`
`).forEach(function(i){a=i.indexOf(":"),n=i.substring(0,a).trim().toLowerCase(),s=i.substring(a+1).trim(),!(!n||t[n]&&Us[n])&&(n==="set-cookie"?t[n]?t[n].push(s):t[n]=[s]:t[n]=t[n]?t[n]+", "+s:s)}),t},je=Symbol("internals");function kt(e){return e&&String(e).trim().toLowerCase()}function xt(e){return e===!1||e==null?e:c.isArray(e)?e.map(xt):String(e)}function zs(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let s;for(;s=n.exec(e);)t[s[1]]=s[2];return t}const Hs=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function zt(e,t,n,s,a){if(c.isFunction(s))return s.call(this,t,n);if(a&&(t=n),!!c.isString(t)){if(c.isString(s))return t.indexOf(s)!==-1;if(c.isRegExp(s))return s.test(t)}}function Vs(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,s)=>n.toUpperCase()+s)}function Gs(e,t){const n=c.toCamelCase(" "+t);["get","set","has"].forEach(s=>{Object.defineProperty(e,s+n,{value:function(a,o,i){return this[s].call(this,t,a,o,i)},configurable:!0})})}let st=class{constructor(t){t&&this.set(t)}set(t,n,s){const a=this;function o(l,h,u){const p=kt(h);if(!p)throw new Error("header name must be a non-empty string");const b=c.findKey(a,p);(!b||a[b]===void 0||u===!0||u===void 0&&a[b]!==!1)&&(a[b||h]=xt(l))}const i=(l,h)=>c.forEach(l,(u,p)=>o(u,p,h));if(c.isPlainObject(t)||t instanceof this.constructor)i(t,n);else if(c.isString(t)&&(t=t.trim())&&!Hs(t))i(Ms(t),n);else if(c.isObject(t)&&c.isIterable(t)){let l={},h,u;for(const p of t){if(!c.isArray(p))throw TypeError("Object iterator must return a key-value pair");l[u=p[0]]=(h=l[u])?c.isArray(h)?[...h,p[1]]:[h,p[1]]:p[1]}i(l,n)}else t!=null&&o(n,t,s);return this}get(t,n){if(t=kt(t),t){const s=c.findKey(this,t);if(s){const a=this[s];if(!n)return a;if(n===!0)return zs(a);if(c.isFunction(n))return n.call(this,a,s);if(c.isRegExp(n))return n.exec(a);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=kt(t),t){const s=c.findKey(this,t);return!!(s&&this[s]!==void 0&&(!n||zt(this,this[s],s,n)))}return!1}delete(t,n){const s=this;let a=!1;function o(i){if(i=kt(i),i){const l=c.findKey(s,i);l&&(!n||zt(s,s[l],l,n))&&(delete s[l],a=!0)}}return c.isArray(t)?t.forEach(o):o(t),a}clear(t){const n=Object.keys(this);let s=n.length,a=!1;for(;s--;){const o=n[s];(!t||zt(this,this[o],o,t,!0))&&(delete this[o],a=!0)}return a}normalize(t){const n=this,s={};return c.forEach(this,(a,o)=>{const i=c.findKey(s,o);if(i){n[i]=xt(a),delete n[o];return}const l=t?Vs(o):String(o).trim();l!==o&&delete n[o],n[l]=xt(a),s[l]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return c.forEach(this,(s,a)=>{s!=null&&s!==!1&&(n[a]=t&&c.isArray(s)?s.join(", "):s)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const s=new this(t);return n.forEach(a=>s.set(a)),s}static accessor(t){const s=(this[je]=this[je]={accessors:{}}).accessors,a=this.prototype;function o(i){const l=kt(i);s[l]||(Gs(a,i),s[l]=!0)}return c.isArray(t)?t.forEach(o):o(t),this}};st.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);c.reduceDescriptors(st.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(s){this[n]=s}}});c.freezeMethods(st);function Ht(e,t){const n=this||Tt,s=t||n,a=st.from(s.headers);let o=s.data;return c.forEach(e,function(l){o=l.call(n,o,a.normalize(),t?t.status:void 0)}),a.normalize(),o}function dn(e){return!!(e&&e.__CANCEL__)}function vt(e,t,n){I.call(this,e??"canceled",I.ERR_CANCELED,t,n),this.name="CanceledError"}c.inherits(vt,I,{__CANCEL__:!0});function fn(e,t,n){const s=n.config.validateStatus;!n.status||!s||s(n.status)?e(n):t(new I("Request failed with status code "+n.status,[I.ERR_BAD_REQUEST,I.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function Js(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function Ws(e,t){e=e||10;const n=new Array(e),s=new Array(e);let a=0,o=0,i;return t=t!==void 0?t:1e3,function(h){const u=Date.now(),p=s[o];i||(i=u),n[a]=h,s[a]=u;let b=o,v=0;for(;b!==a;)v+=n[b++],b=b%e;if(a=(a+1)%e,a===o&&(o=(o+1)%e),u-i<t)return;const w=p&&u-p;return w?Math.round(v*1e3/w):void 0}}function Ys(e,t){let n=0,s=1e3/t,a,o;const i=(u,p=Date.now())=>{n=p,a=null,o&&(clearTimeout(o),o=null),e.apply(null,u)};return[(...u)=>{const p=Date.now(),b=p-n;b>=s?i(u,p):(a=u,o||(o=setTimeout(()=>{o=null,i(a)},s-b)))},()=>a&&i(a)]}const $t=(e,t,n=3)=>{let s=0;const a=Ws(50,250);return Ys(o=>{const i=o.loaded,l=o.lengthComputable?o.total:void 0,h=i-s,u=a(h),p=i<=l;s=i;const b={loaded:i,total:l,progress:l?i/l:void 0,bytes:h,rate:u||void 0,estimated:u&&l&&p?(l-i)/u:void 0,event:o,lengthComputable:l!=null,[t?"download":"upload"]:!0};e(b)},n)},Be=(e,t)=>{const n=e!=null;return[s=>t[0]({lengthComputable:n,total:e,loaded:s}),t[1]]},Ie=e=>(...t)=>c.asap(()=>e(...t)),Ks=Z.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,Z.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(Z.origin),Z.navigator&&/(msie|trident)/i.test(Z.navigator.userAgent)):()=>!0,Qs=Z.hasStandardBrowserEnv?{write(e,t,n,s,a,o){const i=[e+"="+encodeURIComponent(t)];c.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),c.isString(s)&&i.push("path="+s),c.isString(a)&&i.push("domain="+a),o===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Zs(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Xs(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function pn(e,t,n){let s=!Zs(t);return e&&(s||n==!1)?Xs(e,t):t}const qe=e=>e instanceof st?{...e}:e;function yt(e,t){t=t||{};const n={};function s(u,p,b,v){return c.isPlainObject(u)&&c.isPlainObject(p)?c.merge.call({caseless:v},u,p):c.isPlainObject(p)?c.merge({},p):c.isArray(p)?p.slice():p}function a(u,p,b,v){if(c.isUndefined(p)){if(!c.isUndefined(u))return s(void 0,u,b,v)}else return s(u,p,b,v)}function o(u,p){if(!c.isUndefined(p))return s(void 0,p)}function i(u,p){if(c.isUndefined(p)){if(!c.isUndefined(u))return s(void 0,u)}else return s(void 0,p)}function l(u,p,b){if(b in t)return s(u,p);if(b in e)return s(void 0,u)}const h={url:o,method:o,data:o,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:l,headers:(u,p,b)=>a(qe(u),qe(p),b,!0)};return c.forEach(Object.keys(Object.assign({},e,t)),function(p){const b=h[p]||a,v=b(e[p],t[p],p);c.isUndefined(v)&&b!==l||(n[p]=v)}),n}const hn=e=>{const t=yt({},e);let{data:n,withXSRFToken:s,xsrfHeaderName:a,xsrfCookieName:o,headers:i,auth:l}=t;t.headers=i=st.from(i),t.url=ln(pn(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),l&&i.set("Authorization","Basic "+btoa((l.username||"")+":"+(l.password?unescape(encodeURIComponent(l.password)):"")));let h;if(c.isFormData(n)){if(Z.hasStandardBrowserEnv||Z.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((h=i.getContentType())!==!1){const[u,...p]=h?h.split(";").map(b=>b.trim()).filter(Boolean):[];i.setContentType([u||"multipart/form-data",...p].join("; "))}}if(Z.hasStandardBrowserEnv&&(s&&c.isFunction(s)&&(s=s(t)),s||s!==!1&&Ks(t.url))){const u=a&&o&&Qs.read(o);u&&i.set(a,u)}return t},ta=typeof XMLHttpRequest<"u",ea=ta&&function(e){return new Promise(function(n,s){const a=hn(e);let o=a.data;const i=st.from(a.headers).normalize();let{responseType:l,onUploadProgress:h,onDownloadProgress:u}=a,p,b,v,w,f;function m(){w&&w(),f&&f(),a.cancelToken&&a.cancelToken.unsubscribe(p),a.signal&&a.signal.removeEventListener("abort",p)}let k=new XMLHttpRequest;k.open(a.method.toUpperCase(),a.url,!0),k.timeout=a.timeout;function j(){if(!k)return;const A=st.from("getAllResponseHeaders"in k&&k.getAllResponseHeaders()),V={data:!l||l==="text"||l==="json"?k.responseText:k.response,status:k.status,statusText:k.statusText,headers:A,config:e,request:k};fn(function(Q){n(Q),m()},function(Q){s(Q),m()},V),k=null}"onloadend"in k?k.onloadend=j:k.onreadystatechange=function(){!k||k.readyState!==4||k.status===0&&!(k.responseURL&&k.responseURL.indexOf("file:")===0)||setTimeout(j)},k.onabort=function(){k&&(s(new I("Request aborted",I.ECONNABORTED,e,k)),k=null)},k.onerror=function(){s(new I("Network Error",I.ERR_NETWORK,e,k)),k=null},k.ontimeout=function(){let H=a.timeout?"timeout of "+a.timeout+"ms exceeded":"timeout exceeded";const V=a.transitional||cn;a.timeoutErrorMessage&&(H=a.timeoutErrorMessage),s(new I(H,V.clarifyTimeoutError?I.ETIMEDOUT:I.ECONNABORTED,e,k)),k=null},o===void 0&&i.setContentType(null),"setRequestHeader"in k&&c.forEach(i.toJSON(),function(H,V){k.setRequestHeader(V,H)}),c.isUndefined(a.withCredentials)||(k.withCredentials=!!a.withCredentials),l&&l!=="json"&&(k.responseType=a.responseType),u&&([v,f]=$t(u,!0),k.addEventListener("progress",v)),h&&k.upload&&([b,w]=$t(h),k.upload.addEventListener("progress",b),k.upload.addEventListener("loadend",w)),(a.cancelToken||a.signal)&&(p=A=>{k&&(s(!A||A.type?new vt(null,e,k):A),k.abort(),k=null)},a.cancelToken&&a.cancelToken.subscribe(p),a.signal&&(a.signal.aborted?p():a.signal.addEventListener("abort",p)));const $=Js(a.url);if($&&Z.protocols.indexOf($)===-1){s(new I("Unsupported protocol "+$+":",I.ERR_BAD_REQUEST,e));return}k.send(o||null)})},na=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let s=new AbortController,a;const o=function(u){if(!a){a=!0,l();const p=u instanceof Error?u:this.reason;s.abort(p instanceof I?p:new vt(p instanceof Error?p.message:p))}};let i=t&&setTimeout(()=>{i=null,o(new I(`timeout ${t} of ms exceeded`,I.ETIMEDOUT))},t);const l=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(u=>{u.unsubscribe?u.unsubscribe(o):u.removeEventListener("abort",o)}),e=null)};e.forEach(u=>u.addEventListener("abort",o));const{signal:h}=s;return h.unsubscribe=()=>c.asap(l),h}},sa=function*(e,t){let n=e.byteLength;if(n<t){yield e;return}let s=0,a;for(;s<n;)a=s+t,yield e.slice(s,a),s=a},aa=async function*(e,t){for await(const n of oa(e))yield*sa(n,t)},oa=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:s}=await t.read();if(n)break;yield s}}finally{await t.cancel()}},Ue=(e,t,n,s)=>{const a=aa(e,t);let o=0,i,l=h=>{i||(i=!0,s&&s(h))};return new ReadableStream({async pull(h){try{const{done:u,value:p}=await a.next();if(u){l(),h.close();return}let b=p.byteLength;if(n){let v=o+=b;n(v)}h.enqueue(new Uint8Array(p))}catch(u){throw l(u),u}},cancel(h){return l(h),a.return()}},{highWaterMark:2})},It=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",bn=It&&typeof ReadableStream=="function",ra=It&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),gn=(e,...t)=>{try{return!!e(...t)}catch{return!1}},ia=bn&&gn(()=>{let e=!1;const t=new Request(Z.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Me=64*1024,Zt=bn&&gn(()=>c.isReadableStream(new Response("").body)),Dt={stream:Zt&&(e=>e.body)};It&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!Dt[t]&&(Dt[t]=c.isFunction(e[t])?n=>n[t]():(n,s)=>{throw new I(`Response type '${t}' is not supported`,I.ERR_NOT_SUPPORT,s)})})})(new Response);const la=async e=>{if(e==null)return 0;if(c.isBlob(e))return e.size;if(c.isSpecCompliantForm(e))return(await new Request(Z.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(c.isArrayBufferView(e)||c.isArrayBuffer(e))return e.byteLength;if(c.isURLSearchParams(e)&&(e=e+""),c.isString(e))return(await ra(e)).byteLength},ca=async(e,t)=>{const n=c.toFiniteNumber(e.getContentLength());return n??la(t)},ua=It&&(async e=>{let{url:t,method:n,data:s,signal:a,cancelToken:o,timeout:i,onDownloadProgress:l,onUploadProgress:h,responseType:u,headers:p,withCredentials:b="same-origin",fetchOptions:v}=hn(e);u=u?(u+"").toLowerCase():"text";let w=na([a,o&&o.toAbortSignal()],i),f;const m=w&&w.unsubscribe&&(()=>{w.unsubscribe()});let k;try{if(h&&ia&&n!=="get"&&n!=="head"&&(k=await ca(p,s))!==0){let V=new Request(t,{method:"POST",body:s,duplex:"half"}),J;if(c.isFormData(s)&&(J=V.headers.get("content-type"))&&p.setContentType(J),V.body){const[Q,E]=Be(k,$t(Ie(h)));s=Ue(V.body,Me,Q,E)}}c.isString(b)||(b=b?"include":"omit");const j="credentials"in Request.prototype;f=new Request(t,{...v,signal:w,method:n.toUpperCase(),headers:p.normalize().toJSON(),body:s,duplex:"half",credentials:j?b:void 0});let $=await fetch(f);const A=Zt&&(u==="stream"||u==="response");if(Zt&&(l||A&&m)){const V={};["status","statusText","headers"].forEach(d=>{V[d]=$[d]});const J=c.toFiniteNumber($.headers.get("content-length")),[Q,E]=l&&Be(J,$t(Ie(l),!0))||[];$=new Response(Ue($.body,Me,Q,()=>{E&&E(),m&&m()}),V)}u=u||"text";let H=await Dt[c.findKey(Dt,u)||"text"]($,e);return!A&&m&&m(),await new Promise((V,J)=>{fn(V,J,{data:H,headers:st.from($.headers),status:$.status,statusText:$.statusText,config:e,request:f})})}catch(j){throw m&&m(),j&&j.name==="TypeError"&&/Load failed|fetch/i.test(j.message)?Object.assign(new I("Network Error",I.ERR_NETWORK,e,f),{cause:j.cause||j}):I.from(j,j&&j.code,e,f)}}),Xt={http:Ss,xhr:ea,fetch:ua};c.forEach(Xt,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const ze=e=>`- ${e}`,da=e=>c.isFunction(e)||e===null||e===!1,yn={getAdapter:e=>{e=c.isArray(e)?e:[e];const{length:t}=e;let n,s;const a={};for(let o=0;o<t;o++){n=e[o];let i;if(s=n,!da(n)&&(s=Xt[(i=String(n)).toLowerCase()],s===void 0))throw new I(`Unknown adapter '${i}'`);if(s)break;a[i||"#"+o]=s}if(!s){const o=Object.entries(a).map(([l,h])=>`adapter ${l} `+(h===!1?"is not supported by the environment":"is not available in the build"));let i=t?o.length>1?`since :
`+o.map(ze).join(`
`):" "+ze(o[0]):"as no adapter specified";throw new I("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return s},adapters:Xt};function Vt(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new vt(null,e)}function He(e){return Vt(e),e.headers=st.from(e.headers),e.data=Ht.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),yn.getAdapter(e.adapter||Tt.adapter)(e).then(function(s){return Vt(e),s.data=Ht.call(e,e.transformResponse,s),s.headers=st.from(s.headers),s},function(s){return dn(s)||(Vt(e),s&&s.response&&(s.response.data=Ht.call(e,e.transformResponse,s.response),s.response.headers=st.from(s.response.headers))),Promise.reject(s)})}const mn="1.9.0",qt={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{qt[e]=function(s){return typeof s===e||"a"+(t<1?"n ":" ")+e}});const Ve={};qt.transitional=function(t,n,s){function a(o,i){return"[Axios v"+mn+"] Transitional option '"+o+"'"+i+(s?". "+s:"")}return(o,i,l)=>{if(t===!1)throw new I(a(i," has been removed"+(n?" in "+n:"")),I.ERR_DEPRECATED);return n&&!Ve[i]&&(Ve[i]=!0,console.warn(a(i," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(o,i,l):!0}};qt.spelling=function(t){return(n,s)=>(console.warn(`${s} is likely a misspelling of ${t}`),!0)};function fa(e,t,n){if(typeof e!="object")throw new I("options must be an object",I.ERR_BAD_OPTION_VALUE);const s=Object.keys(e);let a=s.length;for(;a-- >0;){const o=s[a],i=t[o];if(i){const l=e[o],h=l===void 0||i(l,o,e);if(h!==!0)throw new I("option "+o+" must be "+h,I.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new I("Unknown option "+o,I.ERR_BAD_OPTION)}}const Rt={assertOptions:fa,validators:qt},ut=Rt.validators;let gt=class{constructor(t){this.defaults=t||{},this.interceptors={request:new Ne,response:new Ne}}async request(t,n){try{return await this._request(t,n)}catch(s){if(s instanceof Error){let a={};Error.captureStackTrace?Error.captureStackTrace(a):a=new Error;const o=a.stack?a.stack.replace(/^.+\n/,""):"";try{s.stack?o&&!String(s.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(s.stack+=`
`+o):s.stack=o}catch{}}throw s}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=yt(this.defaults,n);const{transitional:s,paramsSerializer:a,headers:o}=n;s!==void 0&&Rt.assertOptions(s,{silentJSONParsing:ut.transitional(ut.boolean),forcedJSONParsing:ut.transitional(ut.boolean),clarifyTimeoutError:ut.transitional(ut.boolean)},!1),a!=null&&(c.isFunction(a)?n.paramsSerializer={serialize:a}:Rt.assertOptions(a,{encode:ut.function,serialize:ut.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),Rt.assertOptions(n,{baseUrl:ut.spelling("baseURL"),withXsrfToken:ut.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let i=o&&c.merge(o.common,o[n.method]);o&&c.forEach(["delete","get","head","post","put","patch","common"],f=>{delete o[f]}),n.headers=st.concat(i,o);const l=[];let h=!0;this.interceptors.request.forEach(function(m){typeof m.runWhen=="function"&&m.runWhen(n)===!1||(h=h&&m.synchronous,l.unshift(m.fulfilled,m.rejected))});const u=[];this.interceptors.response.forEach(function(m){u.push(m.fulfilled,m.rejected)});let p,b=0,v;if(!h){const f=[He.bind(this),void 0];for(f.unshift.apply(f,l),f.push.apply(f,u),v=f.length,p=Promise.resolve(n);b<v;)p=p.then(f[b++],f[b++]);return p}v=l.length;let w=n;for(b=0;b<v;){const f=l[b++],m=l[b++];try{w=f(w)}catch(k){m.call(this,k);break}}try{p=He.call(this,w)}catch(f){return Promise.reject(f)}for(b=0,v=u.length;b<v;)p=p.then(u[b++],u[b++]);return p}getUri(t){t=yt(this.defaults,t);const n=pn(t.baseURL,t.url,t.allowAbsoluteUrls);return ln(n,t.params,t.paramsSerializer)}};c.forEach(["delete","get","head","options"],function(t){gt.prototype[t]=function(n,s){return this.request(yt(s||{},{method:t,url:n,data:(s||{}).data}))}});c.forEach(["post","put","patch"],function(t){function n(s){return function(o,i,l){return this.request(yt(l||{},{method:t,headers:s?{"Content-Type":"multipart/form-data"}:{},url:o,data:i}))}}gt.prototype[t]=n(),gt.prototype[t+"Form"]=n(!0)});let pa=class vn{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(o){n=o});const s=this;this.promise.then(a=>{if(!s._listeners)return;let o=s._listeners.length;for(;o-- >0;)s._listeners[o](a);s._listeners=null}),this.promise.then=a=>{let o;const i=new Promise(l=>{s.subscribe(l),o=l}).then(a);return i.cancel=function(){s.unsubscribe(o)},i},t(function(o,i,l){s.reason||(s.reason=new vt(o,i,l),n(s.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=s=>{t.abort(s)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new vn(function(a){t=a}),cancel:t}}};function ha(e){return function(n){return e.apply(null,n)}}function ba(e){return c.isObject(e)&&e.isAxiosError===!0}const te={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(te).forEach(([e,t])=>{te[t]=e});function _n(e){const t=new gt(e),n=Ye(gt.prototype.request,t);return c.extend(n,gt.prototype,t,{allOwnKeys:!0}),c.extend(n,t,null,{allOwnKeys:!0}),n.create=function(a){return _n(yt(e,a))},n}const Y=_n(Tt);Y.Axios=gt;Y.CanceledError=vt;Y.CancelToken=pa;Y.isCancel=dn;Y.VERSION=mn;Y.toFormData=Bt;Y.AxiosError=I;Y.Cancel=Y.CanceledError;Y.all=function(t){return Promise.all(t)};Y.spread=ha;Y.isAxiosError=ba;Y.mergeConfig=yt;Y.AxiosHeaders=st;Y.formToJSON=e=>un(c.isHTMLForm(e)?new FormData(e):e);Y.getAdapter=yn.getAdapter;Y.HttpStatusCode=te;Y.default=Y;const{Axios:Jr,AxiosError:Wr,CanceledError:Yr,isCancel:Kr,CancelToken:Qr,VERSION:Zr,all:Xr,Cancel:ti,isAxiosError:ei,spread:ni,toFormData:si,AxiosHeaders:ai,HttpStatusCode:oi,formToJSON:ri,getAdapter:ii,mergeConfig:li}=Y,ga={class:"tab-container"},ya={class:"tab-list"},ma=["onClick"],va=Pt({__name:"tab",props:{tabList:{},defaultTab:{}},emits:["update:tab"],setup(e,{emit:t}){var i;const n=e,s=t,a=C(n.defaultTab||((i=n.tabList[0])==null?void 0:i.value)||"");lt(()=>n.defaultTab,l=>{l&&(a.value=l)});const o=l=>{a.value=l.value,s("update:tab",l.value)};return(l,h)=>(O(),F("div",ga,[r("div",ya,[(O(!0),F(et,null,ht(l.tabList,(u,p)=>(O(),F("div",{key:p,class:M(["tab-item",{active:a.value===u.value}]),onClick:b=>o(u)},B(u.label),11,ma))),128))])]))}}),wn=ft(va,[["__scopeId","data-v-bdec3bfa"]]),_a={class:"title"},wa={style:{transform:"rotate(180deg)"},viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"32302",width:"16",height:"16"},ka={class:"title-text"},Ea=Pt({__name:"title",props:{title:{},backs:{}},emits:["back"],setup(e,{emit:t}){const n=t,s=()=>{console.log("返回上一个"),n("back")};return(a,o)=>(O(),F("div",_a,[a.backs?(O(),F("div",{key:0,class:"back-btn",onClick:s},[(O(),F("svg",wa,o[0]||(o[0]=[r("path",{"data-v-da8ede72":"",d:"M722.438 510.815c-0.246-0.246-0.513-0.454-0.766-0.69l-299.893-299.892c-14.231-14.24-37.291-14.24-51.529 0-14.241 14.231-14.241 37.291 0 51.529l274.851 274.859-274.849 274.851c-14.241 14.237-14.241 37.301 0 51.532 7.116 7.119 16.436 10.682 25.764 10.682 9.321 0 18.644-3.563 25.763-10.682l299.905-299.901c0.246-0.234 0.51-0.438 0.751-0.678 7.129-7.13 10.685-16.469 10.674-25.804 0.006-9.337-3.55-18.676-10.674-25.804z",fill:"#ffffff","p-id":"32303"},null,-1)])))])):tt("",!0),r("div",ka,B(a.title),1)]))}}),Ca=ft(Ea,[["__scopeId","data-v-607b620a"]]),Ge=e=>new Date(e).toLocaleString().replace(/\//g,"-"),ci=e=>!isNaN(e)&&Number.isFinite(Number(e))&&String(e).includes(".")?Number(e).toFixed(2):e,ui=(e=new Date)=>{const t=e.getFullYear(),n=String(e.getMonth()+1).padStart(2,"0"),s=String(e.getDate()).padStart(2,"0");return`${t}-${n}-${s}`},dt="http://localhost:8000",Gt="http://localhost:8111",_t=Je("api",()=>{const e=C(""),t=C(""),n=C(null);C(null);const s=C("strategy"),a=C("strategy"),o=C(null),i=C("month"),l=C(""),h=C(""),u=C(null),p=C(null),b=C(null),v=C(null),w=C(null),f=C(null),m=C(null),k=C(null),j=C(null),$=Jt({backtestTrade:!1,backtestAccount:!1,backtestProfit:!1,backtestPosition:!1,backtestData:!1,factorList:!1,factorAnalysisResult:!1,backtestList:!1,lastRunId:!1,backtestLastRunId:!1,factorLastDateData:!1}),A=Jt({backtestTrade:null,backtestAccount:null,backtestProfit:null,backtestPosition:null,backtestData:null,factorList:null,factorAnalysisResult:null,backtestList:null,lastRunId:null,backtestLastRunId:null,factorLastDateData:null}),H=()=>{const _=localStorage.getItem("token");if(!_)throw new Error("认证令牌未找到");return _};return{log_workflow_id:e,log_workflow_run_id:t,klineChartRef:n,currentFootTab:s,child_currentFootTab:a,rightTableData:o,backtestTrade:u,backtestAccount:p,backtestProfit:b,backtestPosition:v,backtestData:w,factorList:f,factorAnalysisResult:m,backtestList:k,factorLastDateData:j,loading:$,error:A,timeRange:i,startDate:l,endDate:h,getToken:H,fetchBacktestTrade:async(_,g=1,P=10)=>{$.backtestTrade=!0,A.backtestTrade=null;try{const y=H(),q=`${dt}/api/backtest/trade?back_id=${_}&page=${g}&page_size=${P}`,L=await fetch(q,{method:"GET",headers:{"Content-Type":"application/json",Authorization:`${y}`}});if(!L.ok)throw new Error(`HTTP 错误! 状态: ${L.status}`);return u.value=await L.json(),u.value}catch(y){throw console.error("获取回测交易数据失败:",y),A.backtestTrade=y instanceof Error?y:new Error(String(y)),y}finally{$.backtestTrade=!1}},fetchBacktestAccount:async(_,g=1,P=10)=>{$.backtestAccount=!0,A.backtestAccount=null;try{const y=H(),q=`${dt}/api/backtest/account?back_id=${_}&page=${g}&page_size=${P}`,L=await fetch(q,{method:"GET",headers:{"Content-Type":"application/json",Authorization:`${y}`}});if(!L.ok)throw new Error(`HTTP 错误! 状态: ${L.status}`);return p.value=await L.json(),p.value}catch(y){throw console.error("获取回测账户数据失败:",y),A.backtestAccount=y instanceof Error?y:new Error(String(y)),y}finally{$.backtestAccount=!1}},fetchBacktestProfit:async _=>{$.backtestProfit=!0,A.backtestProfit=null;try{const g=H(),P=`${dt}/api/backtest/profit?back_id=${_}&page_size=10000`,y=await fetch(P,{method:"GET",headers:{"Content-Type":"application/json",Authorization:`${g}`}});if(!y.ok)throw new Error(`HTTP 错误! 状态: ${y.status}`);return b.value=await y.json(),b.value}catch(g){throw console.error("获取回测收益数据失败:",g),A.backtestProfit=g instanceof Error?g:new Error(String(g)),g}finally{$.backtestProfit=!1}},fetchBacktestPosition:async(_,g=1,P=10,y)=>{$.backtestPosition=!0,A.backtestPosition=null;try{const q=H(),L=`${dt}/api/backtest/position/?back_id=${_}&page=${g}&page_size=${P}&date=${y}`,W=await fetch(L,{method:"GET",headers:{"Content-Type":"application/json",Authorization:`${q}`}});if(!W.ok)throw new Error(`HTTP 错误! 状态: ${W.status}`);return v.value=await W.json(),v.value}catch(q){throw console.error("获取回测持仓数据失败:",q),A.backtestPosition=q instanceof Error?q:new Error(String(q)),q}finally{$.backtestPosition=!1}},fetchBacktestData:async _=>{$.backtestData=!0,A.backtestData=null;try{const g=H(),P=`${dt}/api/backtest/backtest?back_id=${_}`,y=await fetch(P,{method:"GET",headers:{"Content-Type":"application/json",Authorization:`${g}`}});if(!y.ok)throw new Error(`HTTP 错误! 状态: ${y.status}`);return w.value=await y.json(),w.value}catch(g){throw console.error("获取回测单一数据失败:",g),A.backtestData=g instanceof Error?g:new Error(String(g)),g}finally{$.backtestData=!1}},fetchFactorList:async(_=1,g=100,P="factor")=>{$.factorList=!0,A.factorList=null;try{const y=H(),q=`${dt}/api/workflow/all?page=${_}&limit=${g}&filter=${P}`,L=await fetch(q,{method:"GET",headers:{uid:"0","quantflow-auth":"2","Content-Type":"application/json",Authorization:`${y}`}});if(!L.ok)throw new Error(`HTTP 错误! 状态: ${L.status}`);return f.value=await L.json(),f.value}catch(y){throw console.error("获取因子列表失败:",y),A.factorList=y instanceof Error?y:new Error(String(y)),y}finally{$.factorList=!1}},fetchFactorAnalysisResult:async _=>{$.factorAnalysisResult=!0,A.factorAnalysisResult=null;try{const g=H(),P=`${dt}/api/workflow/run/output?output_obj_id=${_}`,y=await fetch(P,{method:"GET",headers:{uid:"0","quantflow-auth":"2","Content-Type":"application/json",Authorization:`${g}`}});if(!y.ok)throw new Error(`HTTP 错误! 状态: ${y.status}`);return m.value=await y.json(),m.value}catch(g){throw console.error("获取因子分析结果失败:",g),A.factorAnalysisResult=g instanceof Error?g:new Error(String(g)),g}finally{$.factorAnalysisResult=!1}},fetchBacktestList:async(_=1,g=100,P="backtest")=>{$.backtestList=!0,A.backtestList=null;try{const y=H(),q=`${dt}/api/workflow/all?page=${_}&limit=${g}&filter=${P}`,L=await fetch(q,{method:"GET",headers:{uid:"0","quantflow-auth":"2","Content-Type":"application/json",Authorization:`${y}`}});if(!L.ok)throw new Error(`HTTP 错误! 状态: ${L.status}`);return k.value=await L.json(),k.value}catch(y){throw console.error("获取策略回测列表失败:",y),A.backtestList=y instanceof Error?y:new Error(String(y)),y}finally{$.backtestList=!1}},fetchLastRunId:async(_,g,P)=>{$.lastRunId=!0,A.lastRunId=null;try{const y=H(),q=`${dt}/api/workflow/run/output/by-last-run?workflow_id=${_}&feature_tag=${g}&locator=${P}`,L=await fetch(q.toString(),{method:"GET",headers:{uid:"0","quantflow-auth":"2","Content-Type":"application/json",Authorization:`${y}`}});if(!L.ok)throw new Error(`HTTP 错误! 状态: ${L.status}`);return await L.json()}catch(y){throw console.error("获取因子列表最后运行ID失败:",y),A.lastRunId=y instanceof Error?y:new Error(String(y)),y}finally{$.lastRunId=!1}},fetchBacktestLastRunId:async(_,g,P)=>{$.backtestLastRunId=!0,A.backtestLastRunId=null;try{const y=H(),q=`${dt}/api/workflow/run/output/by-last-run?workflow_id=${_}&feature_tag=${g}&locator=${P}`,L=await fetch(q.toString(),{method:"GET",headers:{uid:"0","quantflow-auth":"2","Content-Type":"application/json",Authorization:`${y}`}});if(!L.ok)throw new Error(`HTTP 错误! 状态: ${L.status}`);return await L.json()}catch(y){throw console.error("获取回测列表最后运行ID失败:",y),A.backtestLastRunId=y instanceof Error?y:new Error(String(y)),y}finally{$.backtestLastRunId=!1}},getFactorEarnings:async _=>{try{const g=await fetch(`${Gt}/api/v1/query_one_group_data?task_id=${_}`);if(!(g!=null&&g.ok))throw new Error("请求失败：未收到响应");return await g.json()}catch(g){throw g}},getFactorEarningsChart:async _=>{try{const g=await fetch(`${Gt}/api/v1/query_simple_return_chart?task_id=${_}`);if(!(g!=null&&g.ok))throw new Error("请求失败：未收到响应");return await g.json()}catch(g){throw g}},getFactorLastDateData:async _=>{var g,P;$.factorLastDateData=!0,A.factorLastDateData=null;try{const y=localStorage.getItem("token"),q=localStorage.getItem("userid"),L=await fetch(`${Gt}/api/v1/query_last_date_top_factor?task_id=${_}`,{headers:{Authorization:`${y}`,userId:`${q}`,"Content-Type":"application/json"}});if(!(L!=null&&L.ok))throw new Error("请求失败：未收到响应");const W=await L.json();return(P=(g=W.data)==null?void 0:g.last_date_top_factor)==null||P.forEach(it=>{it.code=it.symbol.split(".")[1]}),j.value=W,W}catch(y){throw console.error("获取因子最新日期数据失败:",y),A.factorLastDateData=y instanceof Error?y:new Error(String(y)),y}finally{$.factorLastDateData=!1}},updateTimeRange:(_,g,P)=>{i.value=_,l.value=g,h.value=P}}}),di=Je("kData",()=>{const e=C(void 0),t=C("future"),n=Jt({exchange:"DCE",market:"stocks",name:"pp2509",shortName:"pp2509",sourceTicker:"pp2509",ticker:"pp2509",priceCurrency:"CNY",type:"future"});return{type:e,quotationType:t,symbol:n}}),Sa={class:"no-use-container"},Ta={key:0,class:"no-use-container-img"},Aa={key:1,class:"no-data-container-img"},xa=Pt({__name:"tip",props:{type:{type:String,default:"no-use"},title:{type:String,default:"该功能暂未开放，敬请期待"}},setup(e){return(t,n)=>(O(),F("div",Sa,[e.type==="no-use"?(O(),F("div",Ta)):tt("",!0),e.type==="no-data"?(O(),F("div",Aa)):tt("",!0),r("p",null,B(e.title),1),kn(t.$slots,"default",{},void 0,!0)]))}}),Ra=ft(xa,[["__scopeId","data-v-40772042"]]),$a={key:0,class:"table-container"},Da={class:"table-wrapper"},Pa={class:"custom-table"},La=["title"],Oa={class:"name-cell table-cell"},Fa=["onBlur","onKeyup"],Na=["title"],ja=["onClick"],Ba={class:"table-cell"},Ia={class:"table-cell"},qa={style:{"text-align":"right",width:"190px"}},Ua=["onClick"],Ma=["onClick"],za={key:0,class:"loading-spinner"},Ha={class:"pagination"},Va={class:"page-nav"},Ga=["disabled"],Ja={style:{transform:"rotate(180deg)"},viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"32302",width:"16",height:"16"},Wa={key:0,class:"dots"},Ya=["onClick"],Ka=["disabled"],Qa={style:{transform:"rotate(0deg)"},viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"32302",width:"16",height:"16"},Za={key:1,class:"table-container"},Xa={key:2,class:"table-container"},to={__name:"tablea",props:{tableData:{type:Array,default:()=>[]}},emits:["apply"],setup(e,{expose:t,emit:n}){const s=We(),a=n,o=_t(),i=En(),l=S=>{S?i.push(`/editor?workflow_id=${S}`):i.push("/editor")};C(!1);const h=C(null),u=C(""),p=e,b=pt(()=>!p.tableData||!p.tableData.data||!p.tableData.data.workflows?[]:p.tableData.data.workflows.map(S=>({id:S._id,name:S.name,createTime:Ge(S.create_at),modifyTime:Ge(S.update_at),item:S}))),v=C([]);lt(b,S=>{v.value=S},{immediate:!0});const w=C(1),f=C(10),m=pt(()=>v.value.length),k=pt(()=>Math.ceil(m.value/f.value)),j=pt(()=>{const S=(w.value-1)*f.value,D=S+f.value;return v.value.slice(S,D)}),$=pt(()=>{const S=[],D=k.value,R=w.value;if(D<=5){for(let z=1;z<=D;z++)S.push(z);return S}if(S.push(1),R>3&&S.push("..."),R===1||R===2)S.includes(2)||S.push(2),S.includes(3)||S.push(3);else if(R===D||R===D-1)for(let z=D-2;z<D;z++)S.includes(z)||S.push(z);else S.push(R-1,R,R+1);return R<D-2&&S.push("..."),S.includes(D)||S.push(D),[...new Set(S)]}),A=S=>{S!=="..."&&(w.value=S)},H=()=>{w.value>1&&w.value--},V=()=>{w.value<k.value&&w.value++},J=C(null),Q=async S=>{var D;h.value=S.id,u.value=S.name,await An(),(D=J.value)==null||D.focus()},E=async S=>{if(!u.value.trim()){s.error("名称不能为空"),u.value=S.name;return}if(u.value===S.name){h.value=null;return}try{await Ln(S.id,u.value);const D=v.value.findIndex(R=>R.id===S.id);D!==-1&&(v.value[D].name=u.value),h.value=null,s.success("名称修改成功")}catch(D){console.error("重命名失败:",D),u.value=S.name,s.error("名称修改失败，请稍后重试")}},d=()=>{h.value=null,u.value=""},T=C({}),x=(S,D)=>{T.value[D]=!0,o.log_workflow_id=S._id,o.log_workflow_run_id=S.last_run_id,a("apply",{workflow_id:S._id,title:S.name,feature_tag:"backtest",locator:"backtest_id",last_run_id:S.last_run_id}),setTimeout(()=>{T.value[D]=!1},5e3),console.log("应用项目1：",S)},G=S=>{S?T.value[S]=!1:Object.keys(T.value).forEach(D=>{T.value[D]=!1})};return lt(b,()=>{G()},{deep:!0}),t({resetLoadingState:G}),(S,D)=>v.value.length>0?(O(),F("div",$a,[r("div",Da,[r("table",Pa,[D[3]||(D[3]=r("thead",null,[r("tr",null,[r("th",null,"ID"),r("th",null,"名称"),r("th",null,"创建时间"),r("th",null,"修改时间"),r("th")])],-1)),r("tbody",null,[(O(!0),F(et,null,ht(j.value,(R,z)=>(O(),F("tr",{key:R.id},[r("td",{class:"table-cell",title:R.id},B(R.id),9,La),r("td",Oa,[h.value===R.id?Et((O(),F("input",{key:0,"onUpdate:modelValue":D[0]||(D[0]=K=>u.value=K),type:"text",class:"edit-input",onBlur:K=>E(R),onKeyup:[De(K=>E(R),["enter"]),De(d,["esc"])],ref_for:!0,ref_key:"editInput",ref:J},null,40,Fa)),[[Cn,u.value]]):(O(),F(et,{key:1},[r("span",{class:"table-cell",title:R.name},B(R.name),9,Na),R.owner!=="*"?(O(),F("span",{key:0,class:"copy-icon",onClick:K=>Q(R)},D[2]||(D[2]=[Sn('<svg width="12px" height="12px" viewBox="0 0 12 12" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" data-v-173070d8><title data-v-173070d8>修改名称</title><g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" data-v-173070d8><g id="超级图表-工作流" transform="translate(-364, -729)" fill="#2767EE" fill-rule="nonzero" data-v-173070d8><g id="编辑" transform="translate(364, 729)" data-v-173070d8><path d="M5.98554217,5.31036145 L10.9359036,0.361445783 C11.1310843,0.16626506 11.4477108,0.16626506 11.6428916,0.361445783 C11.8380723,0.556626506 11.8380723,0.873253012 11.6428916,1.06843373 L6.69253012,6.0173494 C6.4973494,6.21253012 6.18072289,6.21253012 5.98554217,6.0173494 C5.79036145,5.82216867 5.79036145,5.50554217 5.98554217,5.31036145 Z M10.9995181,5.83373494 C10.9995181,5.55759036 11.2236145,5.33349398 11.499759,5.33349398 C11.7759036,5.33349398 12,5.55759036 12,5.83373494 L12,9.50024096 C12,10.8809639 10.8809639,12 9.50024096,12 L2.49975904,12 C1.11903614,12 0,10.8809639 0,9.50024096 L0,2.49975904 C0,1.11903614 1.11903614,0 2.49975904,0 L6.16626506,0 C6.44240964,0 6.66650602,0.224096386 6.66650602,0.500240964 C6.66650602,0.776385542 6.44240964,1.00048193 6.16626506,1.00048193 L2.49975904,1.00048193 C1.6713253,1.00048193 1.00048193,1.67277108 1.00048193,2.49975904 L1.00048193,9.50024096 C1.00048193,10.3286747 1.67277108,11.0009639 2.49975904,11.0009639 L9.50024096,11.0009639 C10.3286747,11.0009639 11.0009639,10.3286747 11.0009639,9.50024096 L11.0009639,5.83373494 L10.9995181,5.83373494 Z" id="形状" data-v-173070d8></path></g></g></g></svg>',1)]),8,ja)):tt("",!0)],64))]),r("td",Ba,B(R.createTime),1),r("td",Ia,B(R.modifyTime),1),r("td",qa,[r("button",{class:M(["apply-btn"]),onClick:K=>l(R.id),style:{"margin-right":"10px"}}," 编辑 ",8,Ua),r("button",{style:{width:"100px"},class:M(["apply-btn",{loading:T.value[R.id]}]),onClick:K=>x(R.item,R.id)},[Wt(B(T.value[R.id]?"正在应用":"应用")+" ",1),T.value[R.id]?(O(),F("span",za)):tt("",!0)],10,Ma)])]))),128))])])]),r("div",Ha,[r("div",Va,[r("button",{disabled:w.value===1,onClick:H},[(O(),F("svg",Ja,D[4]||(D[4]=[r("path",{"data-v-da8ede72":"",d:"M722.438 510.815c-0.246-0.246-0.513-0.454-0.766-0.69l-299.893-299.892c-14.231-14.24-37.291-14.24-51.529 0-14.241 14.231-14.241 37.291 0 51.529l274.851 274.859-274.849 274.851c-14.241 14.237-14.241 37.301 0 51.532 7.116 7.119 16.436 10.682 25.764 10.682 9.321 0 18.644-3.563 25.763-10.682l299.905-299.901c0.246-0.234 0.51-0.438 0.751-0.678 7.129-7.13 10.685-16.469 10.674-25.804 0.006-9.337-3.55-18.676-10.674-25.804z",fill:"#ffffff","p-id":"32303"},null,-1)])))],8,Ga),(O(!0),F(et,null,ht($.value,(R,z)=>(O(),F(et,{key:z},[R==="..."?(O(),F("span",Wa,"...")):(O(),F("button",{key:1,class:M({active:R===w.value}),onClick:K=>A(R)},B(R),11,Ya))],64))),128)),r("button",{disabled:w.value===k.value,onClick:V},[(O(),F("svg",Qa,D[5]||(D[5]=[r("path",{"data-v-da8ede72":"",d:"M722.438 510.815c-0.246-0.246-0.513-0.454-0.766-0.69l-299.893-299.892c-14.231-14.24-37.291-14.24-51.529 0-14.241 14.231-14.241 37.291 0 51.529l274.851 274.859-274.849 274.851c-14.241 14.237-14.241 37.301 0 51.532 7.116 7.119 16.436 10.682 25.764 10.682 9.321 0 18.644-3.563 25.763-10.682l299.905-299.901c0.246-0.234 0.51-0.438 0.751-0.678 7.129-7.13 10.685-16.469 10.674-25.804 0.006-9.337-3.55-18.676-10.674-25.804z",fill:"#ffffff","p-id":"32303"},null,-1)])))],8,Ka)])])])):v.value.length===0&&!U(o).loading.backtestList?(O(),F("div",Za,[rt(Ra,{type:"no-data",title:"暂无数据，快创建自己的工作流吧。"},{default:Tn(()=>[r("div",{class:"content-btn active",onClick:D[1]||(D[1]=R=>l())},"创建工作流")]),_:1})])):(O(),F("div",Xa,[rt(ee,{visible:U(o).loading.backtestList},null,8,["visible"])]))}},eo=ft(to,[["__scopeId","data-v-173070d8"]]),no={class:"modal-content"},so={class:"modal-header"},ao={class:"tabs"},oo={class:"toolbar"},ro={class:"checkbox-group"},io=["value"],lo={class:"log-viewer"},co={class:"timestamp"},uo={class:"level"},fo=["innerHTML"],po={key:0,class:"no-logs"},ho={key:1,class:"loading-container"},bo={key:2,class:"end-message"},go={class:"log-count"},yo={class:"placeholder-content"},mo={class:"placeholder-content"},vo={__name:"logs",props:{back_id:{type:String,required:!0}},setup(e,{expose:t}){const n=e;_t();const s=C(!1),a=C("logs"),o=C(["1","2","3","4"]),i=C(70),l=C(null),h=C([]),u=C(!0),p=C(!1),b=C(!1),v=[{label:"INFO",value:"1"},{label:"WARN",value:"2"},{label:"DEBUG",value:"3"},{label:"ERROR",value:"4"}],w=[{label:"日志",value:"logs"}],f=async(d=!1)=>{if(!(!u.value||p.value))try{p.value=!0;const T=localStorage.getItem("token"),x={relation_id:n.back_id,limit:i.value};d&&l.value&&(x.last_sort=l.value);const G=await Y.get("http://localhost:8000/api/backtest/userstrategylog",{params:x,headers:{"Content-Type":"application/json",Authorization:T}});if(G.data&&G.data.code===0){const D=G.data.data.items||[];d?h.value=[...h.value,...D]:h.value=D,b.value=h.value.some(R=>R.level==="-1"),u.value=!b.value&&D.length>0,D.length>0&&(l.value=D[D.length-1].sort)}}catch(T){console.error("Failed to fetch logs:",T)}finally{p.value=!1}},m=pt(()=>h.value.filter(d=>o.value.includes(d.level))),k=async d=>{const{scrollTop:T,scrollHeight:x,clientHeight:G}=d.target;x-T-G<30&&!p.value&&u.value&&await f(!0)},j=d=>d?new Date(d).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}):"",$=d=>({1:"INFO",2:"WARN",3:"DEBUG",4:"ERROR","-1":"END"})[d]||d,A=d=>({1:"info",2:"warn",3:"debug",4:"error","-1":"info"})[d]||"info",H=d=>{a.value=d},V=()=>{o.value.length===0&&(o.value=v.map(d=>d.value))},J=()=>{l.value=null,u.value=!0,f()},Q=()=>{s.value=!0,f()},E=()=>{s.value=!1};return t({openDialog:Q,closeDialog:E}),(d,T)=>s.value?(O(),F("div",{key:0,class:"modal-overlay",onClick:Rn(E,["self"])},[r("div",no,[r("div",so,[r("div",ao,[rt(wn,{"tab-list":w,tab:a.value,"onUpdate:tab":[T[0]||(T[0]=x=>a.value=x),H]},null,8,["tab"])]),r("div",oo,[r("div",ro,[(O(),F(et,null,ht(v,x=>r("label",{class:"custom-checkbox",key:x.value},[Et(r("input",{type:"checkbox",value:x.value,"onUpdate:modelValue":T[1]||(T[1]=G=>o.value=G),onChange:V},null,40,io),[[xn,o.value]]),T[2]||(T[2]=r("span",{class:"checkmark"},null,-1)),Wt(" "+B(x.label),1)])),64))]),r("button",{class:"button",onClick:J,style:{border:"0"}},T[3]||(T[3]=[r("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2.5","stroke-linecap":"round","stroke-linejoin":"round",class:"refresh-icon"},[r("path",{d:"M23 4v6h-6"}),r("path",{d:"M20.49 15a9 9 0 1 1-2.12-9.36L23 10"})],-1),Wt(" 刷新 ")]))]),r("button",{class:"close-button",onClick:E},"×")]),Et(r("div",lo,[r("div",{class:"log-content",onScroll:k},[(O(!0),F(et,null,ht(m.value,x=>(O(),F("div",{key:x._id,class:M(["log-entry",A(x.level)])},[r("span",co,"["+B(j(x.exhibit_time))+"]",1),r("span",uo,"-"+B($(x.level))+"-",1),r("span",{class:"message",innerHTML:x.run_info},null,8,fo)],2))),128)),m.value.length===0?(O(),F("div",po," 暂无日志记录 ")):tt("",!0),p.value?(O(),F("div",ho,T[4]||(T[4]=[r("div",{class:"loading-spinner"},null,-1),r("span",null,"加载中...",-1)]))):tt("",!0),!u.value&&m.value.length>0?(O(),F("div",bo,[r("span",null,B(b.value?"日志记录已结束":"已加载全部历史日志"),1),r("span",go,"(已加载: "+B(m.value.length)+"/"+B(h.value.length)+"条)",1)])):tt("",!0)],32)],512),[[Ut,a.value==="logs"]]),Et(r("div",yo," 风控日志内容... ",512),[[Ut,a.value==="risk"]]),Et(r("div",mo," 错误日志内容... ",512),[[Ut,a.value==="errors"]])])])):tt("",!0)}},_o=ft(vo,[["__scopeId","data-v-e1a68fd4"]]),wo={class:"chart-container"},ko={class:"header"},Eo={class:"metrics"},Co={class:"metric"},So={class:"metric"},To={class:"metric"},Ao={class:"metric"},xo={class:"metric"},Ro={class:"metric"},$o={class:"metric"},Do={class:"metric"},Po={class:"metric"},Lo={class:"metric"},Oo={class:"metric"},Fo={class:"chart-box"},No={class:"time-controls"},jo={class:"time-buttons"},Bo={class:"date-picker"},Io={class:"date-picker"},qo={class:"chart-legend"},Uo={__name:"inclusiveIncomeConcept",props:{back_id:{type:String,required:!0}},setup(e){const t=_t(),n=e,s=C(null);let a=null,o=null;const i=C("month"),l=C(""),h=C(""),u=C(1),p=C(10),b=C([]),v=C(new Set),w=C({strategyProfit:!0,benchmarkProfit:!0,excessProfit:!0,dayProfit:!0,dayTrade:!0}),f=E=>{w.value[E]=!w.value[E],J()};pt(()=>{const E=parseInt(u.value);return E===1?"日":E===2?"2日":E===3?"3日":E===5?"周":E===10?"2周":`${E}日`});const m=E=>{if(typeof E=="string"&&E.length===8)return`${E.slice(0,4)}-${E.slice(4,6)}-${E.slice(6,8)}`;if(E instanceof Date){const d=E.getFullYear(),T=String(E.getMonth()+1).padStart(2,"0"),x=String(E.getDate()).padStart(2,"0");return`${d}-${T}-${x}`}if(typeof E=="number"){const d=new Date(E);return m(d)}return""},k=E=>{if(!E||!v.value.size)return!1;const d=m(E);return console.log("Checking date:",d,"Available:",v.value.has(d)),!v.value.has(d)};lt(()=>n.back_id,async()=>{var E,d;if(n.back_id&&(console.log("回测数据back_id：",n.back_id),await t.fetchBacktestData(n.back_id),console.log("回测数据：",t.backtestData),await t.fetchBacktestProfit(n.back_id),console.log("回测收益数据：",t.backtestProfit),(d=(E=t.backtestProfit)==null?void 0:E.data)!=null&&d.items)){if(b.value=t.backtestProfit.data.items,console.log("Sample date format:",{firstItem:b.value[0],dateType:typeof b.value[0].gmt_create,dateValue:b.value[0].gmt_create}),v.value=new Set(b.value.map(T=>m(T.gmt_create))),console.log("Available dates:",Array.from(v.value)),b.value.length>0){const T=Array.from(v.value).sort();l.value=T[0],h.value=T[T.length-1],console.log("Set date range:",{start:l.value,end:h.value})}J()}},{immediate:!0}),lt([l,h],([E,d])=>{E&&d&&(t.updateTimeRange(i.value,E,d),J())},{immediate:!0});const j=(E,d=!1,T=2)=>E===null||Number(E)===void 0||Number(E)===null?d?"0.00%":"0.0000":d?(Number(E)*100).toFixed(T)+"%":Number(E).toFixed(T),$=E=>E===null?"":Number(E)>0?"positive":Number(E)<0?"negative":"",A=E=>{if(i.value=E,!b.value||b.value.length===0)return;const d=b.value.map(S=>m(S.gmt_create)),T=d[d.length-1],x=new Date(T);let G=new Date(T);switch(E){case"month":G.setMonth(x.getMonth()-1);break;case"quarter":G.setMonth(x.getMonth()-3);break;case"halfYear":G.setMonth(x.getMonth()-6);break;case"year":G.setFullYear(x.getFullYear()-1);break;case"ytd":G=new Date(x.getFullYear(),0,1);break;case"all":G=new Date(d[0]);break}l.value=G.toISOString().split("T")[0],h.value=T,t.updateTimeRange(E,l.value,h.value),J()},H=()=>{t.updateTimeRange(i.value,l.value,h.value),J()},V=(E,d)=>{if(d<=1||!E||E.length===0)return E;const T=[];let x=[];return E.forEach((G,S)=>{if(S%d===0&&S!==0){const D={gmt_create:x[0].gmt_create,gmt_create_time:x[0].gmt_create_time,strategy_profit:x[x.length-1].strategy_profit,csi_stock:x[x.length-1].csi_stock,overful_profit:x[x.length-1].overful_profit,day_profit:x.reduce((R,z)=>R+z.day_profit,0),day_purchase:x.reduce((R,z)=>R+z.day_purchase,0),day_put:x.reduce((R,z)=>R+z.day_put,0)};T.push(D),x=[]}if(x.push(G),S===E.length-1&&x.length>0){const D={gmt_create:x[0].gmt_create,gmt_create_time:x[0].gmt_create_time,strategy_profit:x[x.length-1].strategy_profit,csi_stock:x[x.length-1].csi_stock,overful_profit:x[x.length-1].overful_profit,day_profit:x.reduce((R,z)=>R+z.day_profit,0),day_purchase:x.reduce((R,z)=>R+z.day_purchase,0),day_put:x.reduce((R,z)=>R+z.day_put,0)};T.push(D)}}),T},J=()=>{if(!s.value||!b.value||b.value.length===0)return;let E=b.value;l.value&&h.value&&(E=b.value.filter(_=>{const g=m(_.gmt_create);return g>=l.value&&g<=h.value}));const d=parseInt(u.value),T=V(E,d),x=T.map(_=>m(_.gmt_create)),G=T.map(_=>_.strategy_profit*100),S=T.map(_=>_.csi_stock*100),D=T.map(_=>_.overful_profit*100),R=T.map(_=>({value:Math.abs(_.day_profit),itemStyle:{color:_.day_profit>=0?"#2dc08e":"#f92855"}})),z=T.map(_=>({value:Math.max(Math.abs(_.day_purchase),Math.abs(_.day_put)),itemStyle:{color:_.day_purchase>0?"#2dc08e":"#f92855"}})),K=[];w.value.strategyProfit&&K.push({name:"策略收益",type:"line",symbol:"none",data:G,lineStyle:{color:"#29a5ff"},itemStyle:{color:"#29a5ff"},areaStyle:{color:new Mt(0,0,0,1,[{offset:0,color:"rgba(141,165,255, 0)"},{offset:1,color:"rgba(141,165,255, 0.3)"}])}}),w.value.benchmarkProfit&&K.push({name:"基准收益",type:"line",symbol:"none",data:S,lineStyle:{color:"#38B6B6"},itemStyle:{color:"#38B6B6"},areaStyle:{color:new Mt(0,0,0,1,[{offset:0,color:"rgba(56,182,182, 0.3)"},{offset:1,color:"rgba(56,182,182, 0)"}])}}),w.value.excessProfit&&K.push({name:"超额收益",type:"line",symbol:"none",data:D,lineStyle:{color:"#9D4EDD"},itemStyle:{color:"#9D4EDD"},areaStyle:{color:new Mt(0,0,0,1,[{offset:0,color:"rgba(157,78,221, 0)"},{offset:1,color:"rgba(157,78,221, 0.3)"}])}}),w.value.dayProfit&&K.push({name:"当日盈亏",type:"bar",xAxisIndex:1,yAxisIndex:1,data:R}),w.value.dayTrade&&K.push({name:"当日买卖",type:"bar",xAxisIndex:1,yAxisIndex:1,barGap:"30%",data:z});const N={backgroundColor:"#14151A",tooltip:{trigger:"axis",axisPointer:{type:"cross"},formatter:function(_){let g=_[0].axisValue+"<br/>";return _.forEach(P=>{let y=P.color;typeof y!="string"&&(y=P.color.colorStops?P.color.colorStops[0].color:"#fff");let q=P.value;if(P.seriesName==="策略收益"||P.seriesName==="基准收益"||P.seriesName==="超额收益")q=q.toFixed(4)+"%";else if(P.seriesName==="当日盈亏"){const L=P.dataIndex;q=T[L].day_profit.toFixed(2)}else if(P.seriesName==="当日买卖"){const L=P.dataIndex,W=T[L].day_purchase,it=T[L].day_put;q=`买入: ${W}, 卖出: ${it}`}g+=`<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${y};"></span> ${P.seriesName}: ${q}<br/>`}),g}},grid:[{left:"4%",right:"2%",height:"50%",top:"5%"},{left:"4%",right:"2%",top:"65%",height:"25%",bottom:"10%"}],dataZoom:[{type:"inside",xAxisIndex:[0,1],start:0,end:100},{show:!0,xAxisIndex:[0,1],type:"slider",bottom:"2%",height:15,start:0,end:100,handleSize:10,borderColor:"#666",backgroundColor:"rgba(134, 27, 206, 0.3)"}],xAxis:[{type:"category",data:x,axisLine:{lineStyle:{color:"#333"}},axisLabel:{color:"#999"}},{gridIndex:1,type:"category",data:x,axisLine:{lineStyle:{color:"#333"}},axisLabel:{color:"#999",showMaxLabel:!0}}],yAxis:[{type:"value",axisLine:{lineStyle:{color:"#333"}},axisLabel:{color:"#999",formatter:"{value}%"},splitLine:{lineStyle:{color:"#222"}}},{gridIndex:1,type:"value",axisLine:{lineStyle:{color:"#333"}},axisLabel:{color:"#999",formatter:_=>(_/1e4).toFixed(0)+"w"},splitLine:{lineStyle:{color:"#222"}}}],series:K};a||(a=Pe(s.value),window.addEventListener("resize",()=>{a&&a.resize()})),a.setOption(N,!0)},Q=()=>{const E=document.querySelectorAll('[id^="el-popper-container-"]');console.log("找到popper元素数量:",E.length),E.forEach(d=>{d.style.position="relative",d.style.zIndex="99999999999999999",console.log(`已设置元素 ${d.id} 的样式`)})};return Lt(async()=>{var E,d;if(n.back_id&&(await t.fetchBacktestData(n.back_id),await t.fetchBacktestProfit(n.back_id),(d=(E=t.backtestProfit)==null?void 0:E.data)!=null&&d.items)){if(b.value=t.backtestProfit.data.items,b.value.length>0){const T=b.value.map(x=>m(x.gmt_create));l.value=T[0],h.value=T[T.length-1],p.value=Math.min(Math.floor(b.value.length/2),20)}A("all")}Q(),a=Pe(s.value),o=new ResizeObserver(()=>{a==null||a.resize()}),s.value&&o.observe(s.value)}),$n(()=>{o&&o.disconnect(),a&&(a.dispose(),a=null)}),(E,d)=>{var x,G,S,D,R,z,K,N,_,g,P,y,q,L,W,it,wt,at,oe,re,ie,le,ce,ue,de,fe,pe,he,be,ge,ye,me,ve,_e,we,ke,Ee,Ce,Se,Te,Ae,xe,Re,$e;const T=Dn("el-date-picker");return O(),F("div",wo,[r("div",ko,[r("div",Eo,[r("div",Co,[d[12]||(d[12]=r("span",{class:"label"},"收益",-1)),r("span",{class:M(["value",$((G=(x=U(t).backtestData)==null?void 0:x.data)==null?void 0:G.back_profit)])},B(j((D=(S=U(t).backtestData)==null?void 0:S.data)==null?void 0:D.back_profit,!0)),3)]),r("div",So,[d[13]||(d[13]=r("span",{class:"label"},"年化收益",-1)),r("span",{class:M(["value",$((z=(R=U(t).backtestData)==null?void 0:R.data)==null?void 0:z.back_profit_year)])},B(j((N=(K=U(t).backtestData)==null?void 0:K.data)==null?void 0:N.back_profit_year,!0)),3)]),r("div",To,[d[14]||(d[14]=r("span",{class:"label"},"基准收益",-1)),r("span",{class:M(["value",$((g=(_=U(t).backtestData)==null?void 0:_.data)==null?void 0:g.benchmark_profit)])},B(j((y=(P=U(t).backtestData)==null?void 0:P.data)==null?void 0:y.benchmark_profit,!0)),3)]),r("div",Ao,[d[15]||(d[15]=r("span",{class:"label"},"信息比率",-1)),r("span",{class:M(["value",$((L=(q=U(t).backtestData)==null?void 0:q.data)==null?void 0:L.information_ratio)])},B(j((it=(W=U(t).backtestData)==null?void 0:W.data)==null?void 0:it.information_ratio,!1,4)),3)]),r("div",xo,[d[16]||(d[16]=r("span",{class:"label"},"夏普比率",-1)),r("span",{class:M(["value",$((at=(wt=U(t).backtestData)==null?void 0:wt.data)==null?void 0:at.sharpe)])},B(j((re=(oe=U(t).backtestData)==null?void 0:oe.data)==null?void 0:re.sharpe,!1,4)),3)]),r("div",Ro,[d[17]||(d[17]=r("span",{class:"label"},"Alpha",-1)),r("span",{class:M(["value",$((le=(ie=U(t).backtestData)==null?void 0:ie.data)==null?void 0:le.alpha)])},B(j((ue=(ce=U(t).backtestData)==null?void 0:ce.data)==null?void 0:ue.alpha,!1,4)),3)]),r("div",$o,[d[18]||(d[18]=r("span",{class:"label"},"Beta",-1)),r("span",{class:M(["value",$((fe=(de=U(t).backtestData)==null?void 0:de.data)==null?void 0:fe.beta)])},B(j((he=(pe=U(t).backtestData)==null?void 0:pe.data)==null?void 0:he.beta,!1,4)),3)]),r("div",Do,[d[19]||(d[19]=r("span",{class:"label"},"Sortino(索提诺比率)",-1)),r("span",{class:M(["value",$((ge=(be=U(t).backtestData)==null?void 0:be.data)==null?void 0:ge.sortino)])},B(j((me=(ye=U(t).backtestData)==null?void 0:ye.data)==null?void 0:me.sortino,!1,4)),3)]),r("div",Po,[d[20]||(d[20]=r("span",{class:"label"},"收益波动率",-1)),r("span",{class:M(["value",$((_e=(ve=U(t).backtestData)==null?void 0:ve.data)==null?void 0:_e.volatility)])},B(j((ke=(we=U(t).backtestData)==null?void 0:we.data)==null?void 0:ke.volatility,!0,2)),3)]),r("div",Lo,[d[21]||(d[21]=r("span",{class:"label"},"最大回撤",-1)),r("span",{class:M(["value",$((Ce=(Ee=U(t).backtestData)==null?void 0:Ee.data)==null?void 0:Ce.max_drawdown)])},B(j((Te=(Se=U(t).backtestData)==null?void 0:Se.data)==null?void 0:Te.max_drawdown,!0,2)),3)]),r("div",Oo,[d[22]||(d[22]=r("span",{class:"label"},"下行风险",-1)),r("span",{class:M(["value",$((xe=(Ae=U(t).backtestData)==null?void 0:Ae.data)==null?void 0:xe.downside_risk)])},B(j(($e=(Re=U(t).backtestData)==null?void 0:Re.data)==null?void 0:$e.downside_risk,!0,2)),3)])])]),r("div",Fo,[r("div",No,[r("div",jo,[r("button",{class:M({active:i.value==="month"}),onClick:d[0]||(d[0]=ot=>A("month"))},"月",2),r("button",{class:M({active:i.value==="quarter"}),onClick:d[1]||(d[1]=ot=>A("quarter"))},"季度",2),r("button",{class:M({active:i.value==="halfYear"}),onClick:d[2]||(d[2]=ot=>A("halfYear"))},"半年",2),r("button",{class:M({active:i.value==="year"}),onClick:d[3]||(d[3]=ot=>A("year"))},"年",2),r("button",{class:M({active:i.value==="all"}),onClick:d[4]||(d[4]=ot=>A("all"))},"全部",2),r("div",Bo,[d[23]||(d[23]=r("span",null,"开始时间",-1)),rt(T,{class:"date-picker-input-ns",modelValue:l.value,"onUpdate:modelValue":d[5]||(d[5]=ot=>l.value=ot),type:"date",placeholder:"选择开始日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",onChange:H,"disabled-date":k,size:"small"},null,8,["modelValue"])]),r("div",Io,[d[24]||(d[24]=r("span",null,"结束时间",-1)),rt(T,{class:"date-picker-input-ns",modelValue:h.value,"onUpdate:modelValue":d[6]||(d[6]=ot=>h.value=ot),type:"date",placeholder:"选择结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",onChange:H,"disabled-date":k,size:"small"},null,8,["modelValue"])])]),r("div",qo,[r("div",{class:M(["legend-item",{inactive:!w.value.strategyProfit}]),onClick:d[7]||(d[7]=ot=>f("strategyProfit"))},d[25]||(d[25]=[r("span",{class:"line-icon strategy-line"},null,-1),r("span",{class:"legend-text"},"策略收益",-1)]),2),r("div",{class:M(["legend-item",{inactive:!w.value.excessProfit}]),onClick:d[8]||(d[8]=ot=>f("excessProfit"))},d[26]||(d[26]=[r("span",{class:"line-icon excess-line"},null,-1),r("span",{class:"legend-text"},"超额收益",-1)]),2),r("div",{class:M(["legend-item",{inactive:!w.value.benchmarkProfit}]),onClick:d[9]||(d[9]=ot=>f("benchmarkProfit"))},d[27]||(d[27]=[r("span",{class:"line-icon benchmark-line"},null,-1),r("span",{class:"legend-text"},"基准收益",-1)]),2),r("div",{class:M(["legend-item",{inactive:!w.value.dayProfit}]),onClick:d[10]||(d[10]=ot=>f("dayProfit"))},d[28]||(d[28]=[r("div",{class:"square-container"},[r("span",{class:"square-icon loss-square"}),r("span",{class:"square-icon profit-square"})],-1),r("span",{class:"legend-text"},"当日盈亏",-1)]),2),r("div",{class:M(["legend-item",{inactive:!w.value.dayTrade}]),onClick:d[11]||(d[11]=ot=>f("dayTrade"))},d[29]||(d[29]=[r("div",{class:"square-container"},[r("span",{class:"square-icon sell-square"}),r("span",{class:"square-icon buy-square"})],-1),r("span",{class:"legend-text"},"当日买卖",-1)]),2)])]),r("div",{ref_key:"chartRef",ref:s,class:"chart"},null,512)])])}}},Mo=ft(Uo,[["__scopeId","data-v-183f7318"]]),zo={key:0,class:"table-container"},Ho={class:"table-container-inner"},Vo={class:"data-table"},Go={key:1,class:"table-container"},Jo={__name:"tradingParticulars",props:{back_id:{type:String,required:!0},currentPage:{type:Number,default:1},pageSize:{type:Number,default:10}},emits:["update:pagination"],setup(e,{emit:t}){const n=_t(),s=e,a=t,o=C([]),i=C(1),l=C(0),h=w=>{if(!w)return"";const f=w.substring(0,4),m=w.substring(4,6),k=w.substring(6,8);return`${f}/${m}/${k}`},u=w=>{if(w==null)return"¥0.00";const f=w;return f<0?`-¥${Math.abs(f).toFixed(2)}`:`¥${f.toFixed(2)}`},p=w=>{switch(w){case 0:return"股票";case 1:return"期货";case 2:return"基金";default:return"未知"}},b=w=>{switch(w){case 0:return"stock";case 1:return"futures";case 2:return"fund";default:return""}},v=async()=>{try{await n.fetchBacktestTrade(s.back_id,s.currentPage,s.pageSize),n.backtestTrade&&n.backtestTrade.data&&(o.value=n.backtestTrade.data.items||[],n.backtestTrade.data.pagination&&(l.value=n.backtestTrade.data.pagination.total,i.value=n.backtestTrade.data.pagination.total_pages,a("update:pagination",{currentPage:n.backtestTrade.data.pagination.page,pageSize:n.backtestTrade.data.pagination.page_size,totalPages:i.value,totalItems:l.value})))}catch(w){console.error("获取回测交易数据失败",w)}};return lt(()=>s.back_id,()=>{v()}),lt(()=>s.currentPage,()=>{v()}),lt(()=>s.pageSize,()=>{v()}),Lt(async()=>{await v()}),(w,f)=>o.value.length>0?(O(),F("div",zo,[r("div",Ho,[r("table",Vo,[f[0]||(f[0]=r("thead",null,[r("tr",null,[r("th",null,"标的"),r("th",null,"交易日期"),r("th",null,"交易类型"),r("th",null,"品种"),r("th",null,"方向"),r("th",null,"成交量"),r("th",null,"成交价格"),r("th",null,"成交额"),r("th",null,"费用")])],-1)),r("tbody",null,[(O(!0),F(et,null,ht(o.value,(m,k)=>(O(),F("tr",{key:k},[r("td",null,B(m.contract_code),1),r("td",null,B(h(m.gmt_create)),1),r("td",null,[r("span",{class:M(["trade-type",m.business===0?"buy":"sell"])},B(m.business===0?"买入":"卖出"),3)]),r("td",null,[r("span",{class:M(["product-type",b(m.type)])},B(p(m.type)),3)]),r("td",null,[r("span",{class:M(["direction",m.direction===0?"long":"short"])},B(m.direction===0?"开":"平"),3)]),r("td",null,B(Math.abs(m.volume)),1),r("td",null,B(u(m.price)),1),r("td",null,B(u(m.price*Math.abs(m.volume))),1),r("td",null,B(u(m.cost)),1)]))),128))])])])])):(O(),F("div",Go,[rt(ee,{visible:U(n).loading.backtestTrade},null,8,["visible"])]))}},Wo=ft(Jo,[["__scopeId","data-v-216af27a"]]),Yo={key:0,class:"table-container"},Ko={class:"table-container-inner"},Qo={class:"data-table"},Zo={class:"sub-row"},Xo={class:"sub-row"},tr={class:"sub-row"},er={class:"sub-row"},nr={key:1,class:"table-container"},sr={__name:"accountInformation",props:{back_id:{type:String,required:!0},currentPage:{type:Number,default:1},pageSize:{type:Number,default:10}},emits:["update:pagination"],setup(e,{emit:t}){const n=_t(),s=e,a=t,o=v=>({0:"股票",1:"股票,期货",2:"期货",3:"基金"})[v]||"",i=C([]),l=C(1),h=C(0),u=v=>{if(v==null)return"¥0.00";const w=v<0,f=Math.abs(v);return f>=1e4?`${w?"-":""}¥${(f/1e4).toFixed(2)}万`:`${w?"-":""}¥${f.toFixed(2)}`},p=v=>{if(!v)return"";const w=v.substring(0,4),f=v.substring(4,6),m=v.substring(6,8);return`${w}/${f}/${m}`},b=async()=>{try{await n.fetchBacktestAccount(s.back_id,s.currentPage,s.pageSize),n.backtestAccount&&n.backtestAccount.data&&(i.value=n.backtestAccount.data.items||[],n.backtestAccount.data.pagination&&(h.value=n.backtestAccount.data.pagination.total,l.value=n.backtestAccount.data.pagination.total_pages,a("update:pagination",{currentPage:n.backtestAccount.data.pagination.page,pageSize:n.backtestAccount.data.pagination.page_size,totalPages:l.value,totalItems:h.value})))}catch(v){console.error("获取回测账户数据失败",v)}};return lt(()=>s.back_id,()=>{b()}),lt(()=>s.currentPage,()=>{b()}),lt(()=>s.pageSize,()=>{b()}),Lt(async()=>{await b()}),(v,w)=>i.value.length>0?(O(),F("div",Yo,[r("div",Ko,[r("table",Qo,[w[0]||(w[0]=r("thead",null,[r("tr",null,[r("th",null,"日期"),r("th",null,"账户类别"),r("th",null,"可用资金"),r("th",null,"总权益"),r("th",null,"累计盈亏"),r("th",null,"市值"),r("th",null,"累计费用")])],-1)),r("tbody",null,[(O(!0),F(et,null,ht(i.value,(f,m)=>(O(),F("tr",{key:m},[r("td",null,B(p(f.gmt_create)),1),r("td",null,[r("div",null,B(o(f.type)),1)]),r("td",null,[r("div",null,B(u(f.available_funds)),1),r("div",Zo,B(u(f.available_funds)),1)]),r("td",null,[r("div",null,B(u(f.start_capital)),1),r("div",Xo,B(u(f.yes_total_capital)),1)]),r("td",null,[r("div",{class:M(["profit",f.total_profit-f.start_capital<0?"positive":"",f.total_profit-f.start_capital>0?"negative":""])},B(u(f.total_profit-f.start_capital)),3),r("div",{class:M(["sub-row profit",f.add_profit<0?"positive":"",f.add_profit>0?"negative":"poitive"])},B(u(f.add_profit)),3)]),r("td",null,[r("div",null,B(u(f.market_value)),1),r("div",tr,B(u(f.market_value)),1)]),r("td",null,[r("div",null,B(u(f.cost)),1),r("div",er,B(u(f.cost)),1)])]))),128))])])])])):(O(),F("div",nr,[rt(ee,{visible:U(n).loading.backtestAccount},null,8,["visible"])]))}},ar=ft(sr,[["__scopeId","data-v-730dac61"]]),or={class:"strategy-container"},rr={key:1,class:"strategy-container-content"},ir={class:"strategy-header"},lr={class:"strategy-header-right"},cr={key:0,class:"pagination"},ur=["disabled"],dr={class:"page-numbers"},fr=["onClick","disabled"],pr={key:1,class:"ellipsis"},hr=["disabled"],br={key:1,class:"pagination"},gr=["disabled"],yr={class:"page-numbers"},mr=["onClick","disabled"],vr={key:1,class:"ellipsis"},_r=["disabled"],wr={class:"strategy-content"},kr={key:0,class:"tab-content"},Er={key:1,class:"tab-content"},Cr={key:2,class:"tab-content"},Sr=Pt({__name:"index",emits:["apply"],setup(e,{expose:t,emit:n}){const s=We(),a=_t(),o=C("我的工作流"),i=C(!0),l=C(null),h=()=>{var N;(N=l.value)==null||N.openDialog()},u=n,p=C(!0),b=[{label:"收益概览",value:"strategy_overview"},{label:"交易详情",value:"strategy_details"},{label:"账户信息",value:"strategy_account"}];a.child_currentFootTab="strategy_overview";const v=C(1),w=C(40),f=C(1),m=C(0),k=C(!1),j=C(1),$=C(40),A=C(1),H=C(0),V=C(!1),J=N=>{v.value=N.currentPage,w.value=N.pageSize,f.value=N.totalPages,m.value=N.totalItems,k.value=!1},Q=N=>{j.value=N.currentPage,$.value=N.pageSize,A.value=N.totalPages,H.value=N.totalItems,V.value=!1},E=N=>{k.value||(k.value=!0,v.value=N)},d=N=>{V.value||(V.value=!0,j.value=N)},T=pt(()=>{const N=[];if(f.value<=5)for(let g=1;g<=f.value;g++)N.push(g);else{N.push(1);let g=Math.max(2,v.value-1),P=Math.min(f.value-1,v.value+1);g===2&&(P=Math.min(f.value-1,4)),P===f.value-1&&(g=Math.max(2,f.value-3)),g>2&&N.push("left-ellipsis");for(let y=g;y<=P;y++)N.push(y);P<f.value-1&&N.push("right-ellipsis"),N.push(f.value)}return N}),x=pt(()=>{const N=[];if(A.value<=5)for(let g=1;g<=A.value;g++)N.push(g);else{N.push(1);let g=Math.max(2,j.value-1),P=Math.min(A.value-1,j.value+1);g===2&&(P=Math.min(A.value-1,4)),P===A.value-1&&(g=Math.max(2,A.value-3)),g>2&&N.push("left-ellipsis");for(let y=g;y<=P;y++)N.push(y);P<A.value-1&&N.push("right-ellipsis"),N.push(A.value)}return N}),G=N=>{console.log("当前选中的tab:",N),N==="strategy_details"&&(v.value=1),N==="strategy_account"&&(j.value=1)},S=C(null),D=C({node_id:"",node_output:"",node_title:""}),R=C(null),z=async(N,_)=>{var q,L,W,it,wt;if(_!==!1)try{const at=await a.fetchBacktestLastRunId(N.workflow_id,N.feature_tag,N.locator);if(at.code===0)S.value=at.data;else if(at.code===-1001){s.warning("工作流正在运行，请等待完成后再点击应用！"),(q=R.value)==null||q.resetLoadingState();return}else if(at.code===-1002){s.error("工作流运行错误，请重新去工作流重新运行！"),(L=R.value)==null||L.resetLoadingState();return}else if(at.code===-1003){s.warning("工作流没有运行过，请去工作流运行！"),(W=R.value)==null||W.resetLoadingState();return}console.log("applyData.value",S.value),at.data&&at.data.length>0&&(D.value=at.data[0])}catch(at){console.log("请求接口出错:",at),s.error("请求接口出错，请检查网络！"),(it=R.value)==null||it.resetLoadingState();return}else D.value.node_output=N.workflow_id;(wt=R.value)==null||wt.resetLoadingState(),_===!1&&(i.value=!1),console.log("应用项目：",N),p.value=!1,o.value=N.title;const g=N.timeRange||a.timeRange,P=N.startDate||a.startDate,y=N.endDate||a.endDate;u("apply",{type:"strategy",positionHeight:40,selectedApplyData:D.value,timeRange:g,startDate:P,endDate:y})},K=()=>{p.value=!0,a.child_currentFootTab="strategy_overview",u("apply",{type:"strategy",positionHeight:0})};return Lt(async()=>{await a.fetchBacktestList(),console.log("策略列表返回的数据:",a.backtestList)}),t({handleApply:z}),(N,_)=>{var g,P,y,q;return O(),F(et,null,[r("div",or,[p.value?(O(),Pn(eo,{key:0,ref_key:"tableaRef",ref:R,onApply:z,tableData:U(a).backtestList||[]},null,8,["tableData"])):(O(),F("div",rr,[r("div",ir,[rt(Ca,{onBack:K,backs:i.value,title:o.value},null,8,["backs","title"]),rt(wn,{"tab-list":b,tab:U(a).child_currentFootTab,"onUpdate:tab":[_[0]||(_[0]=L=>U(a).child_currentFootTab=L),G]},null,8,["tab"]),r("div",lr,[r("div",{class:"showlog-btn",onClick:h}," 查看日志 "),U(a).child_currentFootTab==="strategy_details"?(O(),F("div",cr,[r("button",{disabled:v.value===1||k.value,onClick:_[1]||(_[1]=L=>E(v.value-1)),class:"page-btn"},_[5]||(_[5]=[r("svg",{class:"arrow-icon",viewBox:"0 0 24 24",fill:"currentColor"},[r("path",{d:"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"})],-1)]),8,ur),r("div",dr,[(O(!0),F(et,null,ht(T.value,(L,W)=>(O(),F(et,{key:W},[typeof L=="number"?(O(),F("button",{key:0,class:M(["page-number",v.value===L?"active":""]),onClick:it=>E(L),disabled:k.value},B(L),11,fr)):(O(),F("span",pr,"..."))],64))),128))]),r("button",{disabled:v.value===f.value||k.value,onClick:_[2]||(_[2]=L=>E(v.value+1)),class:"page-btn"},_[6]||(_[6]=[r("svg",{class:"arrow-icon",viewBox:"0 0 24 24",fill:"currentColor"},[r("path",{d:"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"})],-1)]),8,hr)])):tt("",!0),U(a).child_currentFootTab==="strategy_account"?(O(),F("div",br,[r("button",{disabled:j.value===1||V.value,onClick:_[3]||(_[3]=L=>d(j.value-1)),class:"page-btn"},_[7]||(_[7]=[r("svg",{class:"arrow-icon",viewBox:"0 0 24 24",fill:"currentColor"},[r("path",{d:"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"})],-1)]),8,gr),r("div",yr,[(O(!0),F(et,null,ht(x.value,(L,W)=>(O(),F(et,{key:W},[typeof L=="number"?(O(),F("button",{key:0,class:M(["page-number",j.value===L?"active":""]),onClick:it=>d(L),disabled:V.value},B(L),11,mr)):(O(),F("span",vr,"..."))],64))),128))]),r("button",{disabled:j.value===A.value||V.value,onClick:_[4]||(_[4]=L=>d(j.value+1)),class:"page-btn"},_[8]||(_[8]=[r("svg",{class:"arrow-icon",viewBox:"0 0 24 24",fill:"currentColor"},[r("path",{d:"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"})],-1)]),8,_r)])):tt("",!0)])]),r("div",wr,[U(a).child_currentFootTab==="strategy_overview"?(O(),F("div",kr,[rt(Mo,{back_id:(g=D.value)==null?void 0:g.node_output},null,8,["back_id"])])):tt("",!0),U(a).child_currentFootTab==="strategy_details"?(O(),F("div",Er,[rt(Wo,{back_id:(P=D.value)==null?void 0:P.node_output,"current-page":v.value,"page-size":w.value,"onUpdate:pagination":J},null,8,["back_id","current-page","page-size"])])):tt("",!0),U(a).child_currentFootTab==="strategy_account"?(O(),F("div",Cr,[rt(ar,{back_id:(y=D.value)==null?void 0:y.node_output,"current-page":j.value,"page-size":$.value,"onUpdate:pagination":Q},null,8,["back_id","current-page","page-size"])])):tt("",!0)])]))]),rt(_o,{ref_key:"logsDialog",ref:l,back_id:(q=D.value)==null?void 0:q.node_output},null,8,["back_id"])],64)}}}),fi=ft(Sr,[["__scopeId","data-v-b9d7cee3"]]);export{Rr as G,_o as L,Ra as N,Ca as T,xr as _,zr as a,Ur as b,Mr as c,Dr as d,Pr as e,Lr as f,Or as g,Fr as h,Nr as i,jr as j,Br as k,ee as l,Ir as m,qr as n,$r as o,Hr as p,Y as q,_t as r,fi as s,Ge as t,di as u,ci as v,ui as w,wn as x};
