import{H as Yt}from"./Header-CtIjzaxe.js";import{d as mt,v as wt,g as Pt,c as w,o as y,F as qe,k as Xe,i as et,b as e,l as Et,a as Ne,n as Ze,x as vt,t as P,_ as it,r,f as ht,y as nt,z as Ft,A as bt,h as De,u as Ct,B as Wt,C as yt,D as Ot,G as Zt,e as Qt,H as Ke,I as Ge,J as ot,K as Vt,j as je,L as Jt,M as Nt,N as At,O as Ht,P as Bt,Q as Xt,R as $t,S as jt,m as Kt,T as ea,U as ta,V as ft,W as Ve,X as aa,Y as oa,w as sa,Z as la,$ as na}from"./main-BRdkwCuq.js";import{a as ia}from"./index-CVfGkOaS.js";import{l as st,G as ra,a as ca,b as da,c as ua,d as fa,e as ha,f as ga,g as pa,h as va,i as Aa,j as ma,k as ya,m as wa,n as ba,o as xa,p as Gt,_ as qt,s as ka}from"./index-CB97ykhI.js";const _a="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAhNJREFUOE+Nkz2IU0EQx2c2TxJ5CHKihWcqfdl9hBgMAcVO7MUqCAqiJ2Ihp2IhaBPsUgg2lhaHV5gcaKdYaHFXqBA0CSRvkmcX5U5E1HydIewb80ISnsGPbLXLzv83s7P/QZhZ9Xr9NDOfBYBjALAXAL4xc0UIsdZut1fS6fQgKMHJoVQqLUYikccAcJiZHwkhXgohtgaDwQIiHkfE88y8bRhGxrKs9xPdCDAWv0HEcrfbvZBKpb7MVtZsNnd2Op0cIl7UWp+Mx+Nv/ZgRgIg2EPFHLBY7hYjerDh4JqIcAJwDAKWUauP4zSu9Xu/QnzLPwgqFQiiRSPg9yUsp7yIRrTHzZ9u2r/4rc/DOcZzLiHhTKSV9QBMRl6WUT+cFuK57UGv9QWu9xwf8DIVCJyzLej0voFwum+FwuOP3wQdsIuKSlPLZvADXdQ9orZue5+1Hx3FeAEDRtu078wJqtdoZIcQ9pdSi/wuXmDlrmqYVjUa354EQ0ToAvFNKXcdisbjDNM0aADy3bXv5fwAiugIAuX6/H08mkx9HRnJd98jQXRtD7z+oVCq3M5mM/huIiL4CwKpS6trUif6m0Wgc9TzvCTN/F0LcF0K8Mk1zs9Vq7WbmWD6fX89msx4R+S58CABLSqnV6TCNLb1raOkb42mMBarYQsS0lPLTOG4K+Q0QLLtarS4YhrEPANoT4cxM+JBbvwBBieor6fEbMAAAAABJRU5ErkJggg==",Ca="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAATCAYAAAByUDbMAAAAAXNSR0IArs4c6QAAAwxJREFUOE+VVE1oHVUU/r47M4lN/YmCiFAQ6qbiokVQBEGooC66svRnJQgqQle+/LRm5r3kZmbuKOlLpAlSqtiNq9KiLkRQlIggiLpQFyIIRUGQKJSYUqK58+7xnUde0Pja0Fnec+53v59zhrj+x7IsD5OcBHAAwLcicrrZbL4LQAZd4/ZDEaFz7giAVhfoGoAyjuPP67p+HECzC7gbQJFl2SWS/wHdArPWmiRJeiAArgKYzbLso+2POeee7jKbAXCbgnrvL1lrg/ZRQeI4PmqMaYUQ/uwym221Wh/fQH6vVBTFU8aYGZJ3hBCKuq4vqi/nSe4zxkxPTU19shPIAKZPqgoAP6k/V0nuTdP0j5sF6vdXVXW3iPzCqqqciBzT1NI0ff9mAYuiOGyMmSN5oReAc+6giLxO8kqn02lMT09/txOoc+6A3gFwJ8lGlmXLXFhY2DU2NrauQQwNDT2vAYjIB8PDw83JycnfB3h0j44LgEMhhJlOp/O2ptloNHZpAD8A+JJklmXZb9ba25MkaQJ4TkTaKysrZ5aWlv5eXFwcXltba5AcA3Dee19Za9ecc/eKiAPwqI7GrXEcT5F8sUv5jPd+3lr7V1EU95M8TfIhAMsADgL4xnt/0lp72Vp7S5Ik4yLyMoA367p+dWtoy7K8j+QcgEdE5JVms3lBJeZ5vj+Kov26TlmWfa9nZVkeJ/kagK+896estT/3htY59xmAi977c9bauqqqxzaN3dgM4+u+b3mePxxFkZo+pKanafqFtTaOouilKIqOMM/zB6Momie5R0TGN1dIH3lWgxaR5RDCG1EUnQDwBAD19h1ddl0tkvMi8qt6uSXTOXcIQFtELhtjxtM0/XFiYmL36OjoSWPMMyGE91ZXV+fa7fa1qqr2hRCUwF6S2vthT+a/o1fKcRyfIJnqEG5sbMxaa6/0e6y1d8VxrPt4XFl778+qNf36/35BWtBLSZJYAMdEpBoZGXlrfX39BZU46JEbgvWLRVE8QLJNUr36lOSEyr/edgxktr1ZZ0pnb6cV+wccUGjIODRIkAAAAABJRU5ErkJggg==",La="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACYAAAAgCAYAAAB+ZAqzAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAJqADAAQAAAABAAAAIAAAAAALstX7AAAEgUlEQVRYCc2YWWiUVxTHO0uiVhP3qi20oZIggoK+1FICxaTSjULzkEJ9sNWHSMTRYGfLMk2zTGactmnGVhKopH2opXmwUE0LDaUREVREcCFqwIWCGtHWMhO7JDOZ/s7nvWEy5svWzCQXzpx7zzn3nP937j6WJ6ZYGhoanrFYLCWJROJN+GrcrFSu+pBdQ3YMfqS6uvrWVEJYJtuprq5und1udxP0Hfrax+kfA+B3sVgs6PP5Lo5jO0I9YWB+v/+loaEhD4HewIPud4r6EUCehu6KZ/QroBeolkCbREZBnei0Wq2BysrKk49EY//qAKZWAHodpx4MCpVRAn4UWRPDJMBMC8O9CZDS9y1IxzqBTAD+aNoxyXiETUdHh623t7dUOV2vlDHAfKu+umdEh3EafNxa+roxexcyhp/2eerBgoKCjtLS0niqC/0Vhry2tnYu8+c9ADkRPK+M/4IfstlsH3s8nt+UbEosEAg8G4/HP6DzDuhJ5eQ6IEPMw6+I/492bAATQNnZ2Q7mUAWgVirlAzp8QYbCpP2e7jAdnAwuJ5aDWLvwt1h8EquPWM0DAwNhAWhRX9GFrkAMKLfp0EyG2txud/SRKD2/wWAwhwyWAaqCCE+rKFeJvcXS2Nj4M4JXoDsA+hDEX4N4QBllhIXD4TnRaHQbAD8Cg4xYlwDrpzIf2lxVVfVrRpCYBKmvr9/McP6C+qGtqKjoZSqyc28pLi6OFBYWXuru7n5slaBPW5GMEXc7AT4nYwvgx03nGEPaypBKNtNWxpxjEhUApquSZdyC/v50opvQqkwOKADTuY9xEjzHUO0j5sT2sWRwUjfb+VEdxnGQfe1/7/z4usAqDExo5xdQqcXkrPxBnGb8rEwFJ20Azq7bRSrIWXcfSwXIRE7/DZZhkvOqion9JRO7LhVEJtqcQH7iy2r1gaHNjmAPoD6T4PBlmQAxWgxiP4VcqBVMc60I5IYppT4/P19O+RkpbBtlBG6U4ILJSvqMOxmNRE9Pj1ybZ6So2EM6uIVJvBdszUpwhXqAc/IbToGYNkonJ46dS+pWNXJrJBb1CiNbgCsHkB/ZQgXiJu39AGyn4/B1V+mmhcmNor+/fzsgXDjMU04jtL1s3AcNYCIEwKKsrKzdVPdAS0VGuQN9Mjg42IZ+Wm4a+FlAHJlPcmaugqT8DrUQ5wD6P0UwDEwaUkKh0HwytZNqcsc/yGALdMDr9T4wDCf509TUtJhs7Ibkw5eo7saHM5StTqfzYbLLx4BppaQ6Eom8Dxh5duUpeRR+EGrmtms8cJXclLH0V6CU1V4O5SjDGwDcn5ub2+5wOP5VshHMFJi2IrV2Ui/vQS9kTE743wA+xEPi05qamhvaNpnLI4eXkBMAsmnOU7rL8ABDdhi/Yy6ucYHpYDiycld7m7acEBu0HH4WOgPJ68oGXw6YF+EbIe3/HDI/l87v8TO8JaA3LbqjqcFoCobnNeSSQf23wWhmAuA4FGLYfxrNYCzZlIBphwBcRZZeZchkiGUl22lL5q6R3U6Xy9WnbSfL/wNxuVVL2KmRUgAAAABJRU5ErkJggg==",Ea={class:"category-items"},Ia=["onClick","onDragstart","onDragend","draggable"],Sa={class:"node-icon"},Ra={key:0,src:La,alt:"icon",width:"11px",height:"11px"},Da={key:0,class:"item-count"},_t=30,Na=mt({__name:"CategoryItem",props:{item:{type:Object,required:!0},level:{type:Number,default:0},selectedNodeId:{type:String,default:null},expandedCategories:{type:Array,default:()=>[]}},emits:["toggle-category","drag-start","drag-end"],setup(Q,{emit:O}){const N=Q,m=O,L=wt(()=>{var G;return((G=N.item.children)==null?void 0:G.filter(H=>H.object_type==="group"||H.object_type==="plugin"))||[]}),l=G=>G===L.value.length-1,we=G=>G<L.value.length-1,x=G=>{if(!G.children)return 0;let H=0;for(const Le of G.children)Le.object_type==="plugin"?H++:Le.object_type==="group"&&Le.children&&(H+=x(Le));return H},se=()=>({left:`${N.level*_t+8}px`}),o=()=>({left:`${N.level*_t+9.5}px`,width:`${_t-2}px`}),J=()=>({marginLeft:`${N.level*_t+30}px`}),ke=G=>{m("toggle-category",G)},_e=(G,H)=>{G.dataTransfer&&(G.dataTransfer.setData("node-type",H.name),G.dataTransfer.effectAllowed="copy"),m("drag-start",H)};return(G,H)=>{const Le=Pt("category-item",!0);return y(),w("div",Ea,[(y(!0),w(qe,null,Xe(L.value,(U,ge)=>{var Be,Ee;return y(),w("div",{key:U.id,class:et(["node-container",{"is-group":U.object_type==="group","is-plugin":U.object_type==="plugin","is-last":l(ge),"has-children":U.object_type==="group"&&((Be=U.children)==null?void 0:Be.length)}])},[e("div",{class:et(["vertical-line",{full:we(ge),partial:!we(ge)}]),style:Ze(se())},null,6),e("div",{class:"horizontal-line",style:Ze(o())},null,4),e("div",{class:et(U.object_type==="group"?"group-content":"plugin-content"),style:Ze(J()),onClick:vt(z=>U.object_type==="group"?ke(U.id):null,["stop"]),onDragstart:z=>U.object_type==="plugin"?_e(z,U):null,onDragend:z=>U.object_type==="plugin"?G.$emit("drag-end"):null,draggable:U.object_type==="plugin"},[e("div",Sa,[U.object_type==="group"?(y(),w("img",Ra)):(y(),w("span",{key:1,class:et(["node-icon-indicator",{"is-selected":Q.selectedNodeId===U.id}])},null,2))]),e("span",{class:"node-name",style:Ze({color:Q.selectedNodeId===U.id?"#fff":"#858585"})},P(U.display_name||U.name),5),U.object_type==="group"&&((Ee=U.children)!=null&&Ee.length)?(y(),w("span",Da,P(x(U)),1)):Ne("",!0)],46,Ia),U.object_type==="group"&&Q.expandedCategories.includes(U.id)?(y(),Et(Le,{key:0,item:U,level:Q.level+1,"selected-node-id":Q.selectedNodeId,"expanded-categories":Q.expandedCategories,onToggleCategory:H[0]||(H[0]=z=>G.$emit("toggle-category",z)),onDragStart:H[1]||(H[1]=z=>G.$emit("drag-start",z)),onDragEnd:H[2]||(H[2]=z=>G.$emit("drag-end"))},null,8,["item","level","selected-node-id","expanded-categories"])):Ne("",!0)],2)}),128))])}}}),Ba=it(Na,[["__scopeId","data-v-ac1a95ba"]]),Ta={class:"side-nav-container"},Ma={class:"search-container"},Ua={class:"search-input"},Fa={class:"nav-content"},Wa={key:0,class:"empty-state"},Oa=["onClick"],Va={class:"category-name"},Ga={key:0,class:"item-count"},qa=mt({__name:"Aside",props:{defaultExpanded:{type:Boolean,default:!0}},emits:["categories-loading-state"],setup(Q,{emit:O}){const N=Q,m=O,L=r(!0),l=r(!1),x=r([]),se=r(null),o=r([]),J=z=>{if(!x.value.includes(z.id))return 0;let c=0;const i=(T,le)=>{T.object_type==="group"&&x.value.includes(T.id)&&(c=Math.max(c,le),T.children&&T.children.forEach(b=>i(b,le+1)))};return i(z,1),c},ke=z=>{if(!z.children)return 0;let c=0;for(const i of z.children)i.object_type==="plugin"?c++:i.object_type==="group"&&i.children&&(c+=ke(i));return c};ht(async()=>{m("categories-loading-state",!0),L.value=!0;let z=!1;try{const c=await ia();c&&c.length>0?(o.value=c,x.value=c.map(i=>i.id),z=!0):c&&c.length===0&&(o.value=[],x.value=[],z=!0)}catch(c){console.error("加载节点分类失败 (Aside.vue):",c)}finally{L.value=!1,z&&m("categories-loading-state",!1)}N.defaultExpanded&&(x.value.length===0&&!z||x.value.length===0&&z&&o.value.length)});const _e=()=>{l.value=!l.value},G=z=>{const c=x.value.indexOf(z);c===-1?x.value.push(z):x.value.splice(c,1)},H=z=>{z&&(se.value=z.id)},Le=()=>{se.value=null},U=r(""),ge=wt(()=>{if(!U.value.trim())return o.value;const z=U.value.toLowerCase(),c=i=>i.map(T=>{if(T.object_type==="group"){const le=c(T.children||[]);return le.length>0||T.name.toLowerCase().includes(z)?{...T,children:le}:null}else if(T.object_type==="plugin")return T.name.toLowerCase().includes(z)||T.display_name&&T.display_name.toLowerCase().includes(z)?T:null;return null}).filter(T=>T!==null);return c(o.value)}),Be=()=>{U.value.trim()},Ee=()=>{U.value=""};return(z,c)=>(y(),w("div",Ta,[e("div",{class:et(["side-nav",{collapsed:l.value}])},[l.value?Ne("",!0):(y(),w(qe,{key:0},[e("div",Ma,[e("div",Ua,[c[2]||(c[2]=e("img",{src:_a,alt:"icon",class:"search-icon",width:"14px",height:"14px"},null,-1)),nt(e("input",{type:"text","onUpdate:modelValue":c[0]||(c[0]=i=>U.value=i),onInput:Be,placeholder:"搜索目录"},null,544),[[Ft,U.value]]),U.value?(y(),w("button",{key:0,class:"clear-button",onClick:Ee},c[1]||(c[1]=[e("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none"},[e("path",{d:"M18 6L6 18M6 6l12 12",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round"})],-1)]))):Ne("",!0)])]),e("div",Fa,[U.value&&ge.value.length===0?(y(),w("div",Wa,[c[3]||(c[3]=e("svg",{class:"empty-icon",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none"},[e("path",{d:"M15.5 15.5L19 19",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round"}),e("path",{d:"M5 11a6 6 0 1012 0 6 6 0 00-12 0z",stroke:"currentColor","stroke-width":"2"})],-1)),c[4]||(c[4]=e("p",null,"未找到匹配的节点或目录",-1)),e("button",{class:"clear-search",onClick:Ee},"清除搜索")])):(y(!0),w(qe,{key:1},Xe(U.value?ge.value:o.value,(i,T)=>{var le;return y(),w("div",{key:i.id,class:"nav-category"},[e("div",{class:"category-header",onClick:b=>G(i.id)},[c[5]||(c[5]=e("span",{class:"category-icon"},[e("img",{src:Ca,alt:"icon"})],-1)),e("span",Va,P(i.name),1),(le=i.children)!=null&&le.length?(y(),w("span",Ga,P(ke(i)),1)):Ne("",!0)],8,Oa),nt(De(Ba,{item:i,level:0,"selected-node-id":se.value,"expanded-categories":x.value,"max-expanded-level":J(i),onToggleCategory:G,onDragStart:H,onDragEnd:Le},null,8,["item","selected-node-id","expanded-categories","max-expanded-level"]),[[bt,x.value.includes(i.id)]])])}),128))])],64))],2),e("div",{class:"fixed-fold_arrow",onClick:_e},[(y(),w("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",style:Ze({transform:l.value?"rotate(90deg)":"rotate(270deg)"})},c[6]||(c[6]=[e("path",{d:"M7 14l5-5 5 5",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)]),4))])]))}}),za=it(qa,[["__scopeId","data-v-24627af2"]]),Tt="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAAYtJREFUWEfV2dFxgzAMAFDZS2SHbEG26D8cPx2nP9zx3y2aLbIDGcLOqcW9lGBLlgym+cQGPxQsccIAALRtezLGfFpr34dhuOGxWr++78/OuQ/v/ds4jpOZcV8AcAaAu7W2qYWccWg5AcDNe38xXdfhgeYpYlWQC1zgXE1kYFdkxDB57xuD1JrIGM5ae8FH7RtYC0nh0PULTCCncDcldzYH9wLcC8nFrQK3RubgosCtkLm4JLA0UoIjgaWQUhwLqEVqcGygFKnFZQFzkSVw2UAushROBKSQOO6cC69MofiIq9GfUpdTymJRmq+B73NqnDiCYeUI8vk+xZELFxFHkIFU49QRTDyPOFQfeOi/+NCbJJXnqqcZThLmzOGmtKxdnLNwztwUlg2ULCg5Z4llATULac5l5UHtAlTtptosyQiWwBEVh0zmUWBJnAa5CtwCJ0W+ALfESZD/p/WxR+SWOY6zZqr9Ru4wbrlKzaOQsQbmLjjOM7nWAt4VRyCvyyZ6FVwE+dNEx8Ejf4Z4AOhffFtCFuoVAAAAAElFTkSuQmCC",Ya=["onClick"],Pa=["src","alt"],Za={class:"toolbar-text"},Mt=1.1,Qa=mt({__name:"Toolbar",props:{isRunning:{type:Boolean,default:!1},progress:{type:Number,default:0},graph:{type:Object,required:!0,default:null},isGraphReady:{type:Boolean,default:!1},canvas:{type:null,required:!1}},setup(Q){function O(b,j=!1){b||(b=window.location.href);const F={},E=b.indexOf("?");if(E!==-1){const v=b.indexOf("#",E),S=v!==-1?b.substring(E+1,v):b.substring(E+1);N(S,F)}if(j){const v=b.indexOf("#");if(v!==-1&&v<b.length-1){const S=b.substring(v+1),Z=S.indexOf("?");if(Z!==-1){const d=S.substring(Z+1);N(d,F)}}}return F}function N(b,j){if(!b)return;const F=b.split("&");for(const E of F){const[v,S]=E.split("=",2);v&&(j[decodeURIComponent(v)]=S!==void 0?decodeURIComponent(S):"")}}const m=Ct(),L=Wt(),l=Q,we=[{id:1,src:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAUCAYAAACAl21KAAAAAXNSR0IArs4c6QAAAQ9JREFUOE/llKFOxEAURe+j00AQfAceFjTBEAwWAX9AQtDTZGdJp7UIVBUJCDyiCjQsfAC+P4Agm5DOzGWbVCC6Jaliw/P35L1zJyNpmt6IyCmAFQybQPJWrLWe5FhEXgFcAXgDcPcL84TkSETOAWzPl7hsQARwqLUurbXPAEqt9aQPNM+M28xunucHIYTyH4GeAGy0wvs0bZH8TJJkr9NRnucj7/2FiKz2UUh+NQ0nSTLtBA15RksCIilZlp21whdeGkKYVVV1XRRF3XmaMWZdKfUgIk1zC4fkzDl3ZIz5WBJHf6d+ksfOucchGyml9kXkvvlGXgDsDIH8yEzFGLMWx/Gm9z4aAouiyNd1/f4N+DkenOMZoNsAAAAASUVORK5CYII=",import.meta.url).href,alt:"save",text:"保存"},{id:2,src:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAATCAYAAACdkl3yAAAAAXNSR0IArs4c6QAAAU1JREFUOE+d1DFLw1AQB/D/JRFEcHZxdNbVVRB0dXJ1cxHXgi8v2DQ3pDjpVlc3v4E4SBc/gLoJIgT3Uhyq9M48oRpLapO8KSR3P97dvTxi5hUiYlVdB+Dhd4mIhNbam8K7mY/EzC5wU1X7RCRFaDwep1EU3VeFPgCcGWNOqiTMinE70vzjqTGmXREiAC7nz6oNMfMTgDtjzFERbALZfCixql6GYXg4wWpDrp4kSdr5YKIi1ggqw0qhTqez63nedoXm7wNYJaLrUoiZzwEczINUdYGIFgEMG5cWx/GG7/u3AN5FZKsRNI1Ya19qQ2WIa0FtiJmf8xMQuHLcTiZ9bALtBEHw0Gq13orDqA3999O+AshEJPV9/+caUVUdDAb9brc7nHcMvnuUJMkegCsiWppOEJFja+1FJcgFpWm6PBqN1qZvyCzLHnu93mcV6Au5hdZ8w5yWBAAAAABJRU5ErkJggg==",import.meta.url).href,alt:"export",text:"导出"},{id:3,src:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAATCAYAAACdkl3yAAAAAXNSR0IArs4c6QAAAUZJREFUOE+l1LFKxEAQBuB/JsgVclyv1wh2ng9gc1iIb2Bn63VayaWYJEo2G7hWbOxs9QkUBFtbrS19AgURTDJeCkO8M9yS23KZ/WY2mR2C4zLG7DNzAoBrRwoielFVIUcHcRzveJ7n1yFVZSIaAnhyhpoSWmtTAKdNULmvLtVaa8+mceezEFlrLwHsishWW4iSJLkioiMAkYiYNlCFTD9gHARBWa7Tql+tNVJmqqA0TW9V9QDAG4CbRWUURfEQhuHdb1wFWWvfAXRV9YuIvhdBAK5F5GQOMsZsMPMjgNU8z/eiKHp2wKqQP79/GWyuj9pi/zZkDctEZNPlik2djclkspZl2baI3C8FuRyuxzRW1ASNx+Nur9cbElH1PvM8Z2YuR8u68xgxxhwz88VsIlX9BHDoDI1Go5V+vz+YnZCdTufV9/2PHyuOmQ4RpZomAAAAAElFTkSuQmCC",import.meta.url).href,alt:"import",text:"导入"},{id:4,src:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAUCAYAAACAl21KAAAAAXNSR0IArs4c6QAAAWZJREFUOE/tlLFLAzEYxd872m46u+jg4iTuOtgiCqKbg7P+A06l0OTK0SSF4uQ/oLODowiKWAfdRXBwcNDFWbe23GdTpNij1x6dvSW8fMkv+R65R6R8zrkygP1E+VwpdTxqC9NA1to9ADuJ+qXW+mIkyBizS3IxDZhlXkTe6Jx7AbAEIAAQA5AsmxNrXvutGWOKQRDcxXFcCsOwNQUIU4OiKJrN5/NHnU7nJIqir6lBjUZjXURaJIvVavU+FWSMOfAthmF49tt+Ug/ZkQrqneg9g9a65EFJnfR1LMgDetcegP7qfxBgrb31nmitN/yY1Jk9qtfryx5Qq9We/ZjUmUGTfpORIGvtKskHAFtKqZtJEF93zm0CuBaRNa31Y/8dNZvNmW63+yEiTyLig6s9AVYgWSa5ksvl5iuVyvcg2Jxz2yJySnIuy41E5JPkoVLqyq8fSkgR8fm0ICKFcTCSbaXUO8lBdv0AzkZJXg8QSYAAAAAASUVORK5CYII=",import.meta.url).href,alt:"delete",text:"清空"},{id:5,src:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAATCAYAAACdkl3yAAAAAXNSR0IArs4c6QAAAq9JREFUOE+VlF2oTUEUx/9r9t6lxFXIffEV+Ug3H0+IB99SyleEkkIePAnlnNl3t9z9cepe3ngg9+ESykcSijwoIlKkKDc8E4mEOLP3LOZ2jo7jIPM2s9b81pq1/msILVaWZXOstWsAzAAwiog+isgLAFfzPL/MzF+br1HjQZZlU6y1R4hoIYBnInILwGsiaiOiDhGZD+BVURT7oig623j3JyhN0yUAzgHoF5HdYRjebY7a3d3dbow5AGAHgG6t9f66zwCoq6urw/O8OwAuGWO2MXO11ZPrZ2mabgLQB2C/1vqQO3cgStP0AYBPxpjFzJz/DdIA2wOgYq2d1tnZ+ZyyLFslIueLopgeRdHTZkiapldqkZ802phZBUHwWEQehWG4xWVzSkTawzBc1CITZ7cisi4MwwstguwSkSzP85HO8SWA41rryv+C4jierJR69qO2Mx3oCxHtLJfLJxvefw3AhNp+oms5gM8iYqy1m6MoeuRsPT09g6vV6icAKxzoHYCS1vpYU1fG1vYZgNMAnjiQ53m9pVLpvbMx84ggCN5aaxdQkiQPlVI3y+Wy68Jvgv1bjZIkmUdEt40x411GLuJ6Y8wkZrYtClrUin2xhe0ggJVa68lUK5hr7Tat9Ylm5yRJ1uZ5foOZPza1v933/edExE6UA8pO0/Twj9HYaK2d7cT1L0Eys+/7/nUiGmOM6XBDPABi5kFBENwEMFoptbpUKjmlt1zMPMz3/TNEtLQoillRFD2uj8jABWYeWncQkT6lVG+1Wr1fr1ulUhlnrd0gInuJ6BuA4SJyJs/z7c7nl2+kNncbRSQkoqkA3PC+AdAGYIiIfCCio8aYzPO8uUqpi3VYM+jnc+I4nkpEM5RSo5wYrbX9eZ7fa/wZ4jhe7mDuJ/gj6F8FbxDvMgBbvwM3PmOICE4pBgAAAABJRU5ErkJggg==",import.meta.url).href,alt:"zoom-in",text:"放大"},{id:6,src:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAUCAYAAACAl21KAAAAAXNSR0IArs4c6QAAAplJREFUOE+dlE1IVFEUx//nzXtJIIgyjQsLCVoJrVqEK7PaBC5Moq9FC11Glm2Sd+80N9+7zmQRFLWwiKKIomlTqwwypI9VC1u0CGpRWYhGjEmRvY+TV2ZkHM2P7vZ/zu+d87/3/wjLnGw2WzszM1PvOM7U6OjoRD6fj/5VTpWCUmqj4zgnmbmDiBrL9Gkiegzgiuu6I5V9C0Ba6+MAsgDGmfk2Mz+3LGs8iqIay7K2EtF+AK1ElC8UCl0DAwPTJeA8SGt9AUA3M58Ow/C8UurPUmt4nrfbsqxbzDxh23ZLb2/vlKmbA3me12VZ1lVmPiilzC/nm9F8399ERK8AvBFCtM2BcrlcTRRFH5j5upTy1EqQkt7X19ecSCReElG767qPyPf9EwAyYRg2KqV+rBZk6rTWZvqUEKKFtNbDzDwmpTyyFoip7e/vb2fmB0RUb0BfmTknpbxUAvm+3xDHcaoSnEgkAiHE29kN2GjFp/KZmZsN6Dczd0kp75QatdZjABqWmjAMw52ZTOaZ0Xp6etYnk8lfANoMyDSdE0JcLDUqpeqqqqrqKkFRFAVSyo9lk5vb+1Sa6AkRfXNd9/B/eLSPme8GQbDBTHTU+BYEwWal1Pe1wLTWD4mo2nXdXaSUqnYc5z2A+0KI7tWCtNatAJ4C2COEGJp72b7vHyIiY3anEOLmSrBsNrsljuMXs7kbEUIcmI9IEXaGiNLMfDYMQ08pZW5j0fF9fy8RXQNQG8dxUzqdfrcAVHypnQBMeA3kXmX6AZj0bwNwg5m3E9E6Zm6VUn5Z6n+UtG37GBF1zPrWBMAyH2HmcQBDAC5LKV8rpVK2bQ+XYItA5bsMDg46k5OTqTAMfyqlCpV7lsGiZUErmV7M24Y4jnf8BdONMaMxI63zAAAAAElFTkSuQmCC",import.meta.url).href,alt:"zoom-out",text:"缩小"},{id:7,src:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAAAXNSR0IArs4c6QAAAfxJREFUOE+Vk7+LE0EUx79v3d1OEEEQQYsTLI3VidilUVCwipwKeoV/gcURM7NxSGY2iURFQQ4CCt5ZCHYWNlqLhRaCFjbKKiJIIIU/kE12nswRwxKSvcuUM+995vu+7z3CNscYcyrLsn69Xn9bFEpFj8aYiwA2APxh5tNSylfz4ueCxpAHAL4D8ADsLYLNBDWbzcOe571n5sue551j5i/M/IuI1pIk2dfr9YbTymaClFKe7/sHpZRJHMePHUgIUVNKLSmlPs0qr9Ajl5AH7chsrfUdItovhLiQT5gH0lr3iKjvlLr4LUVxHN+11q74vl+uVqsf8iBjzCoR/ajVas+n7o8BeAngoRBijbTWt4joGhGtM/MEYq1NsyzbVEr9dYB2u31oNBqdJaK8HQ52lZnbZIx5DeA4gDcAfud+TQFcEUK49sMNJoDrUz6FAE4w8ztSSu0JguCFgwwGgzPdbjcPc4Cj1tqfURR9zkOUUmEQBE8BHAFQ3pL5H+ZKk1KuTnmx6dovpRT5e631fSIqO4hTPam30+nsTtP0QBRFH3fStUajUbLWflNK9SddK5qPhecoD2u1WkvW2ifD4fB8GIZ6vCLPXGeTJFledEU2iOgkgK8A3AgsM/NtKWVjoRWpVCq7SqXSIyK65BKZ+cY8yLYejWH3ACRSyptFXv4DBbYHOWLuLHQAAAAASUVORK5CYII=",import.meta.url).href,alt:"move",text:"移动"},{id:8,src:new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAATCAYAAAByUDbMAAAAAXNSR0IArs4c6QAAAldJREFUOE+tlDtoFFEUhv+zswMGk3WDiFoIoo0YhMVGhVhEURGNqLC9KYQo2CQI456bcLOZGbZSUTE++qCFRiRNQCtT+EC0EbERH51aaBpBduZ377IJmxCXjXrLuf/55p7HfwQtDkkZGxtb53nemiRJvlprv7fSy3KXcRwfIzkA4ACA1U2a9wCmPM+7EgTBp6Wxi2BhGG4CMCkivSS/iMg0ybciMiciG0juB7CX5E8Aw8aYG83ABVilUumpVquPRKQrTVNNkmTCWvtr6d/L5XKP53lXAfQBuKmqg9baHMl8HRbH8do0TV+ISCeAg6r6ulVtisWiVygULgE4R7IsIq4cWodFUXQNwKCI9JVKpSetQPN31tqM7/vTAA43vu2TKIrWA/hMctIYc6odkNO41LLZ7IyI7F6AjY+Pn85kMrdI7jLGPG8HZq1d5fv+XQB75vUkT0oYhrdFpKiq3QDYDuxPGpema/8WY8z2fwG5WAe7B6Cgqlv/B+yy62RHR0f30NCQG8a/Pq5m/SLyUEROlEqlByslubFK0/TNyMjIhDQ68wHAR1XdtRJYGIa9IuLm0qhqNO+AMySvk7xgjKm0A7TW5n3ff0bSzdu2IAh+1GFu1cRxPAWgn+R5Y8zFVsAoijbWmuZKshPAUVWdqXezyR6dvu/fAXCkthkeO89Vq9VZa23apMlns1nnEm34eEBVXUz9LFpBDb8NkyyJSL6xht4BmKttCPeaHTUr+wCeJklydnR09FVzBssuxyAIunO53PE0TQ+JyGYAXQC+1crwkuR9Y8zscmX4DWS98ucU6bk+AAAAAElFTkSuQmCC",import.meta.url).href,alt:"refresh",text:"刷新"}],x=wt(()=>l.isGraphReady?we:we.filter(b=>b.id<=4)),se=r(null),o=r(!1),J=b=>!!((b===3||b===4)&&l.isRunning);function ke(b,j){if(J(b)){b===3?L.warning("工作流运行时无法导入 JSON 文件"):b===4&&L.warning("工作流运行时无法删除节点");return}switch(b){case 1:G();break;case 2:Le();break;case 3:U();break;case 4:Be();break;case 5:Ee();break;case 6:z();break;case 7:c();break;case 8:i();break;default:console.warn(`No action defined for id: ${b}`)}}const _e=()=>{const j=(l.graph.graph||l.graph).serialize(),F={format_version:"2.0",name:m.title?m.title:`workflow-${new Date().toISOString().replace(/[:]/g,"-").slice(0,19)}`,description:"",litegraph:j,nodes:[],links:[]};m.title=F.name;const E=O(window.location.href);return E.workflow_id&&(F.id=E.workflow_id),console.log("graphData.nodes:",j.nodes),j.nodes&&Array.isArray(j.nodes)&&(F.nodes=j.nodes.map(v=>{const S=Object.keys(v.properties),Z={};return S.forEach(d=>{var me;const A=(me=v.inputs.find(ye=>ye.name===d))==null?void 0:me.fieldName,R=v.properties[d];Z[A]=R}),j.links.find(d=>v.id===d[1]),{uuid:v.flags.uuid,title:v.title||"",name:v.type||"",type:v.type||"",litegraph_id:v.id||0,positionX:v.pos?v.pos[0]:0,positionY:v.pos?v.pos[1]:0,width:v.size?v.size[0]:0,height:v.size?v.size[1]:0,static_input_data:Z||{},output_db_id:null}})),j.links&&Array.isArray(j.links)&&(F.links=j.links.map(v=>{var We,Te;const[S,Z,d,A,R,me]=v,ye=j.nodes.find($e=>$e.id===Z),Ce=j.nodes.find($e=>$e.id===A);return console.log("sourceNode,targetNode:",ye,Ce),{uuid:Ot(),litegraph_id:Z,status:1,previous_node_uuid:ye.flags.uuid,next_node_uuid:Ce.flags.uuid,output_field_name:(We=Ce.inputs[R])==null?void 0:We.fieldName,input_field_name:(Te=ye.outputs[d])==null?void 0:Te.fieldName}})),F};async function G(){try{const b=JSON.stringify(_e());if(console.log("jsonString:",_e()),_e().nodes.length===0){L.warning("当前工作区域为空，不可保存空白工作流！");return}const j=localStorage.getItem("token"),F=await fetch("http://localhost:8000/api/workflow/save",{method:"POST",headers:{uid:"0","quantflow-auth":"2","Content-Type":"application/json",Authorization:`${j}`},body:b});if(F.ok){localStorage.setItem("savedGraph",JSON.stringify(b));const E=await F.json();if(console.log("data:",E),L.success(m.owner==="*"?"模版工作流克隆成功！":"保存成功！"),m.workflow_id=E.data.workflow_id,m.owner="",m.workflow_id){const Z=new URL(window.location.href);Z.searchParams.set("workflow_id",m.workflow_id),window.history.replaceState({},"",Z)}const v={uid:"0","quantflow-auth":"2","Content-Type":"application/json",Authorization:`${j}`},S=await fetch(`http://localhost:8000/api/workflow/query?workflow_id=${E.data.workflow_id}`,{headers:v});if(S.status===404)return console.log("工作流不存在!"),null;if(S.ok){const Z=await S.json();console.log("data:",Z),m.title=Z.name}}else throw new Error(`请求失败: ${F.status} ${F.statusText}`)}catch(b){console.error("保存图表时出错:",b),L.error("保存失败！")}}const H=b=>{(b.metaKey||b.ctrlKey)&&b.key==="s"&&(b.preventDefault(),G())};function Le(){const b=JSON.stringify(_e()),j=new Blob([b],{type:"application/json"}),F=URL.createObjectURL(j),E=document.createElement("a");E.href=F,E.download=`graph-export-${new Date().toISOString().slice(0,10)}.json`,document.body.appendChild(E),E.click(),document.body.removeChild(E),URL.revokeObjectURL(F),console.log("图表已成功导出",b)}function U(){var b;if(m.owner==="*"){L.warning("当前为模版,不支持导入覆盖，请先点击保存按钮创建个人工作流后，再进行导入!");return}if(l.isRunning){L.warning("工作流运行时无法导入 JSON 文件");return}(b=se.value)==null||b.click()}async function ge(b){var E;if(l.isRunning){L.warning("工作流运行时无法导入 JSON 文件");return}console.log("开始导入图形");const j=b.target;if(!((E=j.files)!=null&&E.length))return;const F=j.files[0];try{const v=await F.text(),S=JSON.parse(v).litegraph,Z=l.graph.graph||l.graph,d=l.canvas;if(!Z||!d)return console.error("Graph or Canvas not initialized"),!1;Z.clear();try{Z.configure(S),Z.nodes.forEach(fe=>{fe._fullTitle?fe.title=fe.truncateTitle(fe._fullTitle):(fe._fullTitle=fe.title||"",fe.title=fe.truncateTitle(fe.title||""))}),d.setZoom(1),d.ds.offset=[0,0];let A=1/0,R=-1/0,me=1/0,ye=-1/0;Z.nodes.forEach(fe=>{A=Math.min(A,fe.pos[0]),R=Math.max(R,fe.pos[0]+fe.size[0]),me=Math.min(me,fe.pos[1]),ye=Math.max(ye,fe.pos[1]+fe.size[1])});const Ce=R-A,We=ye-me,Te=A+Ce/2,$e=me+We/2,rt=d.canvas.getBoundingClientRect(),Ye=rt.width,Me=rt.height;return d.ds.offset=[Ye/2-Te,Me/2-$e],Z.setDirtyCanvas(!0),d.draw(!0,!0),L.success("导入成功！"),j.value="",!0}catch(A){return console.error("Error configuring graph:",A),L.error("导入失败：图形数据格式不正确"),!1}}catch(v){return console.error("Import failed:",v),L.error("导入失败，请检查文件格式是否正确"),!1}}function Be(){if(m.owner==="*"){L.warning("当前为模版,不支持清空!");return}if(l.isRunning){L.warning("工作流运行时无法删除节点");return}try{const b=l.graph.graph||l.graph;confirm("确定要清空当前图表吗？此操作不可撤销。")&&(b.clear(),b.setDirtyCanvas(!0,!0),console.log("图表已清空"),L.success("图表已清空"))}catch(b){console.error("清空图表时出错:",b)}}function Ee(){if(l.canvas)try{const b=l.canvas.ds.scale||1;l.canvas.setZoom(b*Mt),l.canvas.draw(!0,!0)}catch(b){console.error("Zoom in error:",b)}}function z(){if(l.canvas)try{const b=l.canvas.ds.scale||1;l.canvas.setZoom(b/Mt),l.canvas.draw(!0,!0)}catch(b){console.error("Zoom out error:",b)}}function c(){l.canvas&&(o.value=!o.value,l.canvas.drag_mode=o.value,o.value?l.canvas.canvas.style.cursor="grab":l.canvas.canvas.style.cursor="default")}function i(){if(l.canvas)try{l.canvas.setZoom(1),l.canvas.ds.offset=[0,0],l.canvas.draw(!0,!0)}catch(b){console.error("Reset view error:",b)}}const T=()=>{o.value&&(l.canvas.canvas.style.cursor="grabbing")},le=()=>{o.value&&(l.canvas.canvas.style.cursor="grab")};return ht(()=>{window.addEventListener("keydown",H),l.canvas&&(l.canvas.canvas.addEventListener("mousedown",T),l.canvas.canvas.addEventListener("mouseup",le))}),yt(()=>{window.removeEventListener("keydown",H),l.canvas&&(l.canvas.canvas.removeEventListener("mousedown",T),l.canvas.canvas.removeEventListener("mouseup",le))}),(b,j)=>(y(),w(qe,null,[(y(!0),w(qe,null,Xe(x.value,F=>(y(),w("div",{class:et(["toolbar-item",{disabled:J(F.id)}]),key:F.id,onClick:E=>ke(F.id,F.text)},[e("img",{src:F.src,alt:F.alt,width:"17px",height:"19px"},null,8,Pa),e("div",Za,P(F.text),1)],10,Ya))),128)),e("input",{type:"file",ref_key:"fileInput",ref:se,style:{display:"none"},accept:".json",onChange:ge},null,544)],64))}}),Ja=it(Qa,[["__scopeId","data-v-d3f84579"]]),Ha={class:"table-container"},Xa={class:"table-wrapper"},$a={key:0,class:"fixed-column"},ja={class:"scroll-area"},Ka={style:{display:"flex",position:"sticky",top:"0","z-index":"99",background:"#292C38",width:"max-content"}},eo=["title"],to={__name:"TablesScroll",props:{tableData:{type:Array,required:!0},headers:{type:Array,required:!0},isFixed:{type:Boolean,default:!0},columnWidth:{type:[Number,String],default:0},customHeader:{type:Array,default:[]}},setup(Q){Zt(x=>({"25f58ff8":`${Q.columnWidth}px`}));const O=Q,N=r(null),m=r(null),L=x=>!isNaN(x)&&Number.isFinite(Number(x))&&String(x).includes(".")?Number(x).toFixed(2):x,l=x=>x?x.replace(/(\d{4})(\d{2})(\d{2})/g,"$1-$2-$3"):"-",we=()=>{N.value&&m.value&&O.isFixed&&(console.log("-------------"),N.value.addEventListener("scroll",()=>{console.log(N.value.scrollTop),m.value.style.top=`-${N.value.scrollTop}px`}))};return ht(()=>{we()}),(x,se)=>(y(),w("div",Ha,[e("div",Xa,[O.isFixed?(y(),w("div",$a,[e("div",{class:"fixed-header",style:Ze({width:`${Q.customHeader.length>0?Q.customHeader[0].width:Q.columnWidth}px`})},P(Q.customHeader.length>0?Q.customHeader[0].key:O.headers[0]),5),e("div",{class:"fixed-body",ref_key:"fixedBody",ref:m},[(y(!0),w(qe,null,Xe(O.tableData,(o,J)=>(y(),w("div",{key:J,class:"fixed-cell",style:Ze({width:`${Q.customHeader.length>0?Q.customHeader[0].width:Q.columnWidth}px`})},P(l(o[O.headers[0]])),5))),128))],512)])):Ne("",!0),e("div",ja,[e("div",{class:"scroll-body",ref_key:"scrollBody",ref:N},[e("div",Ka,[(y(!0),w(qe,null,Xe([...O.headers].splice(O.isFixed?1:0),(o,J)=>(y(),w("div",{key:J,class:"header-cell",style:Ze({width:`${Q.customHeader.length>0?Q.customHeader[J+1].width:Q.columnWidth}px`}),title:o},P(Q.customHeader.length>0?Q.customHeader[J+1].key:o),13,eo))),128))]),(y(!0),w(qe,null,Xe(O.tableData,(o,J)=>(y(),w("div",{key:J,class:"table-row"},[(y(!0),w(qe,null,Xe([...O.headers].splice(O.isFixed?1:0),(ke,_e)=>(y(),w("div",{key:_e,class:"table-cell",style:Ze({width:`${Q.customHeader.length>0?Q.customHeader[_e+1].width:Q.columnWidth}px`})},P(L(o[ke])),5))),128))]))),128))],512)])])]))}},Ut=it(to,[["__scopeId","data-v-f22c5882"]]),ao={class:"factor-deep"},oo={class:"factor-deep-header"},so={class:"factor-item"},lo={class:"factor-item-title"},no={class:"factor-item-content"},io={style:{display:"flex","justify-content":"space-between"}},ro={class:"factor-deep-content",style:{"flex-shrink":"0","flex-basis":"368px","margin-top":"20px"}},co={class:"data-card",style:{height:"100%"}},uo={class:"data-item"},fo={class:"data-item"},ho={class:"data-item"},go={class:"data-item"},po={class:"value"},vo={key:0,class:"value"},Ao={key:1,class:"label"},mo={class:"factor-item",style:{margin:"0","margin-top":"20px",width:"auto","flex-shrink":"0","flex-grow":"1","flex-basis":"460px","margin-left":"20px"}},yo={class:"factor-item-title"},wo={class:"factor-item-content"},bo={style:{width:"100%",height:"400px"}},xo={class:"factor-item",style:{"margin-left":"20px"}},ko={class:"factor-item-title"},_o={class:"factor-item-content"},Co={style:{width:"100%",height:"400px"}},Lo={class:"factor-item-container"},Eo={class:"factor-item"},Io={class:"factor-item-title"},So={class:"factor-item-content"},Ro={class:"factor-item"},Do={class:"factor-item-title"},No={class:"factor-item-content"},Bo={class:"factor-item"},To={class:"factor-item-title"},Mo={class:"factor-item-content"},Uo={class:"factor-item"},Fo={class:"factor-item-title"},Wo={class:"factor-item-content"},Oo={class:"factor-item"},Vo={class:"factor-item-title"},Go={class:"factor-item-content"},qo={class:"factor-item"},zo={class:"factor-item-title"},Yo={class:"factor-item-content"},Po={class:"factor-item"},Zo={class:"factor-item-title"},Qo={class:"factor-item-content"},Jo={class:"factor-item"},Ho={class:"factor-item-title"},Xo={class:"factor-item-content"},$o={class:"factor-item"},jo={class:"factor-item-title"},Ko={class:"factor-item-content"},es={class:"factor-item"},ts={class:"factor-item-title"},as={class:"factor-item-content"},Re="#7d7d7d",os=mt({__name:"FactorDeep",props:{factorId:{type:String,required:!0},taskId:{type:String,requfactorIdired:!0},into:{type:Boolean,required:!0,default:!1}},setup(Q){const O=t=>"",N=t=>t,m=(t,be)=>{if(typeof t=="number")return parseFloat(t.toFixed(be));if(typeof t=="string"&&!isNaN(t)&&t.includes("."))return parseFloat(Number(t).toFixed(be));if(Array.isArray(t))return t.map(M=>m(M));if(typeof t=="object"&&t!==null){const M={};for(const V in t)t.hasOwnProperty(V)&&(M[V]=m(t[V]));return M}return t},L=t=>!isNaN(t)&&Number.isFinite(Number(t))&&String(t).includes(".")?Number(t).toFixed(2):t,l=Q;Qt();const we=r(!1),x=t=>{const be=parseFloat(t);return be>0?"#ff4851":be<0?"#2fae34":"#333"},se=r({annualized_ratio:0,maximum_drawdown:0,return_ratio:0,sharpe_ratio:0}),o=async()=>{var M,V,oe,ee,re,ce,de,ue;const{json:t,httpController:be}=await ra(l.taskId||O());se.value={annualized_ratio:N((V=(M=t==null?void 0:t.data)==null?void 0:M.one_group_data)==null?void 0:V.annualized_ratio),maximum_drawdown:N((ee=(oe=t==null?void 0:t.data)==null?void 0:oe.one_group_data)==null?void 0:ee.maximum_drawdown),return_ratio:N((ce=(re=t==null?void 0:t.data)==null?void 0:re.one_group_data)==null?void 0:ce.return_ratio),sharpe_ratio:N((ue=(de=t==null?void 0:t.data)==null?void 0:de.one_group_data)==null?void 0:ue.sharpe_ratio)}};let J=null;const ke=r(null),_e=r(""),G=r({}),H=async()=>{var V,oe,ee,re,ce,de,ue,ve,Ae,$,q,k,D,g,h,u;const{json:t,httpController:be}=await ca((l==null?void 0:l.taskId)||O());if(t.code==="200"&&(_e.value=(oe=(V=t==null?void 0:t.data)==null?void 0:V.return_chart)==null?void 0:oe.title,G.value=(ee=t==null?void 0:t.data)==null?void 0:ee.return_chart,ke.value)){if(J=ot(ke.value),Object.keys(G.value).length==0)return;var M={code:"200",message:"查询成功",data:{task_id:"bbf3bbc030a94849bea4810058eec027",return_chart:{title:"python 5 groups return",x:[{name:"date",data:(ce=(re=G.value)==null?void 0:re.x[0])==null?void 0:ce.data}],y:(de=G.value)==null?void 0:de.y}}};const p=($=(Ae=(ve=(ue=M==null?void 0:M.data)==null?void 0:ue.return_chart)==null?void 0:ve.x[0])==null?void 0:Ae.data)==null?void 0:$.map(W=>W.split(" ")[0]),B=(D=(k=(q=M==null?void 0:M.data)==null?void 0:q.return_chart)==null?void 0:k.y)==null?void 0:D.map(W=>W.name),_=["#FF6B6B","#4ECDC4","#45B7D1","#96CEB4","#FFCC5C","#FF6F69","#88D8B0","#6C88C4","#FFA07A","#98FB98","#87CEEB","#DDA0DD"].sort(()=>Math.random()-.5),pe=(u=(h=(g=M==null?void 0:M.data)==null?void 0:g.return_chart)==null?void 0:h.y)==null?void 0:u.map((W,ie)=>{var He;return(He=W==null?void 0:W.data)==null?void 0:He.map((gt,pt)=>({value:[pt,ie,Number(gt).toFixed(2)],itemStyle:{color:_[ie%_.length]}}))}).flat(),ne={title:{left:"center"},tooltip:{show:!1,axisPointer:{show:!1}},grid3D:{viewControl:{projection:"orthographic",autoRotate:!0,distance:200,beta:45,alpha:25},boxWidth:70,boxHeight:70,boxDepth:200,light:{main:{intensity:1.2}},top:"0%",bottom:"10%"},xAxis3D:{type:"category",data:p==null?void 0:p.map(W=>W.split(" ")[0]),name:"",axisLabel:{interval:Math.floor((p==null?void 0:p.length)/10),rotate:45,margin:20,textStyle:{fontSize:10,color:Re}},nameTextStyle:{fontSize:14,margin:30}},yAxis3D:{type:"category",data:B,name:"",axisLabel:{color:Re}},zAxis3D:{type:"value",name:"",axisLabel:{color:Re}},dataZoom:[{type:"slider",xAxisIndex:0,start:0,end:100},{type:"inside",xAxisIndex:0,start:0,end:100}],series:[{type:"bar3D",data:pe,shading:"lambert",label:{show:!1}}]};J.setOption(ne)}},Le=r("分组收益"),U=r([]),ge=r({}),Be=async()=>{var M,V;const{json:t,httpController:be}=await da((l==null?void 0:l.taskId)||O());t.code==="200"&&(ge.value=(M=t==null?void 0:t.data)==null?void 0:M.group_return_analysis,U.value=Object.keys((V=t==null?void 0:t.data)==null?void 0:V.group_return_analysis[0]))},Ee=r("最新数据"),z=r([]),c=r({}),i=r([{key:"时间",width:130},{key:"股票代码",width:100},{key:"名称",width:100},{key:"因子值",width:80}]),T=async()=>{var M,V;const{json:t,httpController:be}=await ua((l==null?void 0:l.taskId)||O());t.code==="200"&&(c.value=(M=t==null?void 0:t.data)==null?void 0:M.last_date_top_factor,z.value=Object.keys((V=t==null?void 0:t.data)==null?void 0:V.last_date_top_factor[0]))};let le=null;const b=r(null),j=r(""),F=r({}),E=async()=>{var V,oe,ee,re,ce,de,ue,ve,Ae;const{json:t,httpController:be}=await fa((l==null?void 0:l.taskId)||O());if(t.code==="200"&&(j.value=(oe=(V=t==null?void 0:t.data)==null?void 0:V.ic_decay_chart)==null?void 0:oe.title,console.log("还需处理-----------------",m((ee=t==null?void 0:t.data)==null?void 0:ee.ic_decay_chart,3)),F.value=(re=t==null?void 0:t.data)==null?void 0:re.ic_decay_chart,b.value)){if(le=ot(b.value),Object.keys(F.value).length==0)return;const $=(de=(ce=F.value)==null?void 0:ce.x[0])==null?void 0:de.data,q=(Ae=(ve=(ue=F.value)==null?void 0:ue.y[0])==null?void 0:ve.data)==null?void 0:Ae.map((k,D)=>({value:k,itemStyle:{color:k>0?"#ff0000":"#3498db"}}));var M={title:{},tooltip:{trigger:"axis",formatter:function(k){return k.map(D=>{let g=D.value;const h=D.color;if(Array.isArray(g)){const u=g.map(p=>p==null||isNaN(p)?"--":Number(p).toFixed(4));return`<span style="color:${h}">${D.seriesName}</span>: ${u[0]}, ${u[1]}`}return g=g==null||isNaN(g)?"--":Number(g).toFixed(4),`<span style="color:${h}">${D.seriesName}</span>: ${g}`}).join("<br/>")}},grid:{left:"3%",right:"4%",bottom:"15%",top:"15%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:0,start:0,end:100}],xAxis:[{type:"category",data:$,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(k,D){const g=$.length,h=40,u=le.getWidth(),p=Math.floor(u/h),B=Math.floor((g-1)/(p-1));return(g-1-k)%B===0}},show:!1},{type:"category",data:$,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(k,D){const g=$.length,h=40,u=le.getWidth(),p=Math.floor(u/h),B=Math.floor((g-1)/(p-1));return(g-1-k)%B===0}},position:"bottom"}],yAxis:[{type:"value",splitLine:{show:!0,lineStyle:{type:"dashed",color:Re}}}],series:[{name:"IC值",type:"bar",data:q,label:{show:!1,position:"bottom",formatter:function(k){return k.value?k.value.toFixed(2):""}}},{name:"IC趋势",type:"line",smooth:!1,showSymbol:!1,data:F.value.y[0].data,lineStyle:{color:"#ff0000",width:0}}]};le.setOption(M)}};let v=null;const S=r(null),Z=r(""),d=r({}),A=async()=>{var V,oe,ee,re,ce,de,ue,ve,Ae;const{json:t,httpController:be}=await ha((l==null?void 0:l.taskId)||O());if(t.code==="200"&&(console.log("json",t),Z.value=(oe=(V=t==null?void 0:t.data)==null?void 0:V.ic_den_chart)==null?void 0:oe.title,d.value=(ee=t==null?void 0:t.data)==null?void 0:ee.ic_den_chart,S.value)){if(v=ot(S.value),Object.keys(d.value).length==0)return;var M={title:{},tooltip:{trigger:"axis",formatter:function($){return $.map(q=>{let k=q.value;const D=q.color;if(Array.isArray(k)){const g=k.map(h=>h==null||isNaN(h)?"--":Number(h).toFixed(4));return`<span style="color:${D}">${q.seriesName}</span>: ${g[0]}, ${g[1]}`}return k=k==null||isNaN(k)?"--":Number(k).toFixed(4),`<span style="color:${D}">${q.seriesName}</span>: ${k}`}).join("<br/>")}},legend:{data:["Histogram","Density Curve"],top:"0px",textStyle:{color:Re}},grid:{left:"3%",right:"4%",bottom:"15%",top:"15%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:0,start:0,end:100},{type:"inside",xAxisIndex:0,start:0}],xAxis:{type:"value",boundaryGap:!1,axisLine:{onZero:!1},splitLine:{show:!0,lineStyle:{type:"dashed",color:Re}}},yAxis:{type:"value",axisLine:{onZero:!1,show:!1},axisTick:{show:!1},position:"left",splitLine:{show:!0,lineStyle:{type:"dashed",color:Re}}},series:[{name:"Histogram",type:"bar",data:(de=(ce=(re=d.value)==null?void 0:re.y[0])==null?void 0:ce.data)==null?void 0:de.map(($,q)=>{var k,D;return[(D=(k=d.value)==null?void 0:k.x[0])==null?void 0:D.data[q],$]}),itemStyle:{color:"rgba(135, 206, 235, 0.6)"}},{name:"Density Curve",type:"line",smooth:!0,data:(Ae=(ve=(ue=d.value)==null?void 0:ue.y[1])==null?void 0:ve.data)==null?void 0:Ae.map(($,q)=>{var k,D;return[(D=(k=d.value)==null?void 0:k.x[0])==null?void 0:D.data[q],$]}),itemStyle:{color:"#1E90FF"},lineStyle:{width:2}}]};v.setOption(M)}};let R=null;const me=r(null),ye=r(""),Ce=r({}),We=async()=>{var V,oe,ee,re,ce,de,ue,ve,Ae,$,q,k,D;const{json:t,httpController:be}=await ga((l==null?void 0:l.taskId)||O());if(t.code==="200"&&(ye.value=(oe=(V=t==null?void 0:t.data)==null?void 0:V.ic_seq_chart)==null?void 0:oe.title,Ce.value=(ee=t==null?void 0:t.data)==null?void 0:ee.ic_seq_chart,console.log("echartsRefICSequenceData",Ce.value),me.value)){if(R=ot(me.value),Object.keys(Ce.value).length==0)return;var M={title:{left:"center"},tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter:function(g){return g.map(h=>{let u=h.value;const p=h.color;if(Array.isArray(u)){const B=u.map(_=>_==null||isNaN(_)?"--":Number(_).toFixed(4));return`<span style="color:${p}">${h.seriesName}</span>: ${B[0]}, ${B[1]}`}return u=u==null||isNaN(u)?"--":Number(u).toFixed(4),`<span style="color:${p}">${h.seriesName}</span>: ${u}`}).join("<br/>")}},legend:{data:(ce=(re=Ce.value)==null?void 0:re.y)==null?void 0:ce.map(g=>g.name),top:"0px",selected:{IC:!0,Cum_IC:!1},textStyle:{color:Re}},grid:{left:"3%",right:"4%",bottom:"2%",top:"15%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:[0,1],start:0,end:100}],xAxis:[{type:"category",data:(ve=(ue=(de=Ce.value)==null?void 0:de.x[0])==null?void 0:ue.data)==null?void 0:ve.map(g=>g.split(" ")[0]),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(g,h){var ne,W,ie;const u=(ie=(W=(ne=Ce.value)==null?void 0:ne.x[0])==null?void 0:W.data)==null?void 0:ie.length,p=40,B=R.getWidth(),_=Math.floor(B/p),pe=Math.floor((u-1)/(_-1));return(u-1-g)%pe===0}},show:!1},{type:"category",data:(q=($=(Ae=Ce.value)==null?void 0:Ae.x[0])==null?void 0:$.data)==null?void 0:q.map(g=>g.split(" ")[0]),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(g,h){var ne,W,ie;const u=(ie=(W=(ne=Ce.value)==null?void 0:ne.x[0])==null?void 0:W.data)==null?void 0:ie.length,p=40,B=R.getWidth(),_=Math.floor(B/p),pe=Math.floor((u-1)/(_-1));return(u-1-g)%pe===0}},position:"bottom"}],yAxis:{type:"value",axisLabel:{interval:1e3},splitLine:{show:!0,lineStyle:{type:"dashed",color:Re}}},series:(D=(k=Ce.value)==null?void 0:k.y)==null?void 0:D.map(g=>({name:g.name,data:g.data,type:g.name==="IC"?"bar":"line",itemStyle:{color:g.name==="IC"?"#3498db":"#e74c3c"},label:{show:!1,position:"top",formatter:function(h){return h.data.toFixed(3)}}}))};R.setOption(M)}};let Te=null;const $e=r(null),rt=r(""),Ye=r({}),Me=async()=>{var V,oe,ee,re,ce,de,ue,ve,Ae,$,q,k,D;const{json:t,httpController:be}=await pa((l==null?void 0:l.taskId)||O());if(t.code==="200"&&(rt.value=(oe=(V=t==null?void 0:t.data)==null?void 0:V.ic_self_correlation_chart)==null?void 0:oe.title,Ye.value=(ee=t==null?void 0:t.data)==null?void 0:ee.ic_self_correlation_chart,$e.value)){if(Te=ot($e.value),Object.keys(Ye.value).length==0)return;var M={title:{},tooltip:{trigger:"axis",formatter:function(g){return g.map(h=>{let u=h.value;const p=h.color;if(Array.isArray(u)){const B=u.map(_=>_==null||isNaN(_)?"--":Number(_).toFixed(4));return`<span style="color:${p}">${h.seriesName}</span>: ${B[0]}, ${B[1]}`}return u=u==null||isNaN(u)?"--":Number(u).toFixed(4),`<span style="color:${p}">${h.seriesName}</span>: ${u}`}).join("<br/>")}},legend:{data:["自相关系数","下限(95%置信区间)","上限(95%置信区间)"],top:"0px",textStyle:{color:Re}},grid:{left:"3%",right:"4%",bottom:"15%",top:"15%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:[0,1],start:0,end:100}],xAxis:[{type:"category",data:(ce=(re=Ye.value)==null?void 0:re.x[0])==null?void 0:ce.data,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(g,h){var ne,W,ie;const u=(ie=(W=(ne=Ye.value)==null?void 0:ne.x[0])==null?void 0:W.data)==null?void 0:ie.length,p=40,B=Te.getWidth(),_=Math.floor(B/p),pe=Math.floor((u-1)/(_-1));return(u-1-g)%pe===0}},show:!1},{type:"category",data:(ue=(de=Ye.value)==null?void 0:de.x[0])==null?void 0:ue.data,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(g,h){var ne,W,ie;const u=(ie=(W=(ne=Ye.value)==null?void 0:ne.x[0])==null?void 0:W.data)==null?void 0:ie.length,p=40,B=Te.getWidth(),_=Math.floor(B/p),pe=Math.floor((u-1)/(_-1));return(u-1-g)%pe===0}},position:"bottom"}],yAxis:{type:"value",min:-1,max:1,interval:.2,splitLine:{show:!0,lineStyle:{type:"dashed",color:Re}}},series:[{name:"自相关系数",type:"line",data:(Ae=(ve=Ye.value)==null?void 0:ve.y[0])==null?void 0:Ae.data,symbol:"circle",symbolSize:8,lineStyle:{width:2},itemStyle:{color:"#3498db"},label:{show:!1,position:"top",formatter:function(g){return g.value.toFixed(3)}}},{name:"下限(95%置信区间)",type:"line",data:(q=($=Ye.value)==null?void 0:$.y[1])==null?void 0:q.data,lineStyle:{type:"dashed",width:1},itemStyle:{color:"#95a5a6"},symbol:"none"},{name:"上限(95%置信区间)",type:"line",data:(D=(k=Ye.value)==null?void 0:k.y[2])==null?void 0:D.data,lineStyle:{type:"dashed",width:1},itemStyle:{color:"#95a5a6"},symbol:"none"}]};Te.setOption(M)}};let fe=null;const Qe=r(null),ze=r(""),at=r({}),lt=async()=>{var V,oe,ee,re,ce,de,ue,ve,Ae,$;const{json:t,httpController:be}=await va((l==null?void 0:l.taskId)||O());if(t.code==="200"&&(ze.value=(oe=(V=t==null?void 0:t.data)==null?void 0:V.rank_ic_decay_chart)==null?void 0:oe.title,at.value=(ee=t==null?void 0:t.data)==null?void 0:ee.rank_ic_decay_chart,Qe.value)){if(fe=ot(Qe.value),Object.keys(at.value).length==0)return;const q=(ce=(re=at.value)==null?void 0:re.x[0])==null?void 0:ce.data,k=(ve=(ue=(de=at.value)==null?void 0:de.y[0])==null?void 0:ue.data)==null?void 0:ve.map((D,g)=>({value:D,itemStyle:{color:D>0?"#ff0000":"#3498db"}}));var M={title:{},tooltip:{trigger:"axis",formatter:function(D){return D.map(g=>{let h=g.value;const u=g.color;if(Array.isArray(h)){const p=h.map(B=>B==null||isNaN(B)?"--":Number(B).toFixed(4));return`<span style="color:${u}">${g.seriesName}</span>: ${p[0]}, ${p[1]}`}return h=h==null||isNaN(h)?"--":Number(h).toFixed(4),`<span style="color:${u}">${g.seriesName}</span>: ${h}`}).join("<br/>")}},grid:{left:"3%",right:"4%",bottom:"15%",top:"15%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:0,start:0,end:100}],xAxis:[{type:"category",data:q,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(D,g){const h=q.length,u=40,p=fe.getWidth(),B=Math.floor(p/u),_=Math.floor((h-1)/(B-1));return(h-1-D)%_===0}},show:!1},{type:"category",data:q,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(D,g){const h=q.length,u=40,p=fe.getWidth(),B=Math.floor(p/u),_=Math.floor((h-1)/(B-1));return(h-1-D)%_===0}},position:"bottom"}],yAxis:[{type:"value",splitLine:{show:!0,lineStyle:{type:"dashed",color:Re}}}],series:[{name:"IC值",type:"bar",data:k,label:{show:!1,position:"bottom",formatter:function(D){return D.value?D.value.toFixed(3):""}}},{name:"IC趋势",type:"line",smooth:!1,showSymbol:!1,data:($=(Ae=at.value)==null?void 0:Ae.y[0])==null?void 0:$.data,lineStyle:{color:"#000000",width:0}}]};fe.setOption(M)}};let Je=null;const s=r(null),a=r(""),n=r({}),f=async()=>{var V,oe,ee,re,ce,de,ue,ve,Ae;const{json:t,httpController:be}=await Aa((l==null?void 0:l.taskId)||O());if(t.code==="200"&&(a.value=(oe=(V=t==null?void 0:t.data)==null?void 0:V.rank_ic_den_chart)==null?void 0:oe.title,n.value=(ee=t==null?void 0:t.data)==null?void 0:ee.rank_ic_den_chart,s.value)){if(Je=ot(s.value),Object.keys(n.value).length==0)return;var M={title:{},tooltip:{trigger:"axis",formatter:function($){return $.map(q=>{let k=q.value;const D=q.color;if(Array.isArray(k)){const g=k.map(h=>h==null||isNaN(h)?"--":Number(h).toFixed(4));return`<span style="color:${D}">${q.seriesName}</span>: ${g[0]}, ${g[1]}`}return k=k==null||isNaN(k)?"--":Number(k).toFixed(4),`<span style="color:${D}">${q.seriesName}</span>: ${k}`}).join("<br/>")}},legend:{data:["Histogram","Density Curve"],top:"0px",textStyle:{color:Re}},grid:{left:"3%",right:"4%",bottom:"15%",top:"15%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:0,start:0,end:100},{type:"inside",xAxisIndex:0,zoomOnMouseWheel:!1,start:0}],xAxis:{type:"value",boundaryGap:!1,axisLine:{onZero:!1},axisLabel:{formatter:"{value}"},splitLine:{show:!0,lineStyle:{type:"dashed",color:Re}}},yAxis:{type:"value",axisLine:{onZero:!1,show:!1},axisTick:{show:!1},position:"left",splitLine:{show:!0,lineStyle:{type:"dashed",color:Re}}},series:[{name:"Histogram",type:"bar",data:(de=(ce=(re=n.value)==null?void 0:re.y[0])==null?void 0:ce.data)==null?void 0:de.map(($,q)=>{var k,D;return[(D=(k=n.value)==null?void 0:k.x[0])==null?void 0:D.data[q],$]}),itemStyle:{color:"rgba(135, 206, 235, 0.6)"}},{name:"Density Curve",type:"line",smooth:!0,data:(Ae=(ve=(ue=n.value)==null?void 0:ue.y[1])==null?void 0:ve.data)==null?void 0:Ae.map(($,q)=>{var k,D;return[(D=(k=n.value)==null?void 0:k.x[0])==null?void 0:D.data[q],$]}),itemStyle:{color:"#1E90FF"},lineStyle:{width:2}}]};Je.setOption(M)}};let I=null;const C=r(null),te=r(""),X=r({}),he=async()=>{var V,oe,ee,re,ce,de,ue,ve,Ae,$,q,k,D;const{json:t,httpController:be}=await ma(l.taskId||O());if(t.code==="200"&&(te.value=(oe=(V=t==null?void 0:t.data)==null?void 0:V.rank_ic_seq_chart)==null?void 0:oe.title,X.value=(ee=t==null?void 0:t.data)==null?void 0:ee.rank_ic_seq_chart,C.value)){if(I=ot(C.value),Object.keys(X.value).length==0)return;var M={title:{left:"center"},tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter:function(g){return g.map(h=>{let u=h.value;const p=h.color;if(Array.isArray(u)){const B=u.map(_=>_==null||isNaN(_)?"--":Number(_).toFixed(4));return`<span style="color:${p}">${h.seriesName}</span>: ${B[0]}, ${B[1]}`}return u=u==null||isNaN(u)?"--":Number(u).toFixed(4),`<span style="color:${p}">${h.seriesName}</span>: ${u}`}).join("<br/>")}},legend:{data:(ce=(re=X.value)==null?void 0:re.y)==null?void 0:ce.map(g=>g.name),top:"0px",selected:{IC:!0,Cum_IC:!1},textStyle:{color:Re}},grid:{left:"3%",right:"4%",bottom:"1%",top:"15%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:[0,1],start:0,end:100}],xAxis:[{type:"category",data:(ve=(ue=(de=X.value)==null?void 0:de.x[0])==null?void 0:ue.data)==null?void 0:ve.map(g=>g.split(" ")[0]),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(g,h){var ne,W,ie;const u=(ie=(W=(ne=X.value)==null?void 0:ne.x[0])==null?void 0:W.data)==null?void 0:ie.length,p=40,B=I.getWidth(),_=Math.floor(B/p),pe=Math.floor((u-1)/(_-1));return(u-1-g)%pe===0}},show:!1},{type:"category",data:(q=($=(Ae=X.value)==null?void 0:Ae.x[0])==null?void 0:$.data)==null?void 0:q.map(g=>g.split(" ")[0]),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(g,h){var ne,W,ie;const u=(ie=(W=(ne=X.value)==null?void 0:ne.x[0])==null?void 0:W.data)==null?void 0:ie.length,p=40,B=I.getWidth(),_=Math.floor(B/p),pe=Math.floor((u-1)/(_-1));return(u-1-g)%pe===0}},position:"bottom"}],yAxis:{type:"value",splitLine:{show:!0,lineStyle:{type:"dashed",color:Re}}},series:(D=(k=X.value)==null?void 0:k.y)==null?void 0:D.map(g=>({name:g.name,data:g.data,type:g.name==="Rank_IC"?"bar":"line",itemStyle:{color:g.name==="IC"?"#3498db":"#e74c3c"},showSymbol:!1,label:{show:!1,position:"top",formatter:function(h){return h.data.toFixed(3)}}}))};I.setOption(M)}};let K=null;const Ie=r(null),Ue=r(""),xe=r({}),Y=async()=>{var V,oe,ee,re,ce,de,ue,ve,Ae,$,q,k,D;const{json:t,httpController:be}=await ya((l==null?void 0:l.taskId)||O());if(t.code==="200"&&(Ue.value=(oe=(V=t==null?void 0:t.data)==null?void 0:V.rank_ic_self_correlation_chart)==null?void 0:oe.title,xe.value=(ee=t==null?void 0:t.data)==null?void 0:ee.rank_ic_self_correlation_chart,Ie.value)){if(K=ot(Ie.value),Object.keys(xe.value).length==0)return;var M={title:{},tooltip:{trigger:"axis",formatter:function(g){return g.map(h=>{let u=h.value;const p=h.color;if(Array.isArray(u)){const B=u.map(_=>_==null||isNaN(_)?"--":Number(_).toFixed(4));return`<span style="color:${p}">${h.seriesName}</span>: ${B[0]}, ${B[1]}`}return u=u==null||isNaN(u)?"--":Number(u).toFixed(4),`<span style="color:${p}">${h.seriesName}</span>: ${u}`}).join("<br/>")}},legend:{data:["自相关系数","下限(95%置信区间)","上限(95%置信区间)"],top:"0px",textStyle:{color:Re}},grid:{left:"3%",right:"4%",bottom:"10%",top:"10%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:0,start:0,end:100}],xAxis:[{type:"category",data:(ce=(re=xe.value)==null?void 0:re.x[0])==null?void 0:ce.data,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(g,h){var ne,W,ie;const u=(ie=(W=(ne=xe.value)==null?void 0:ne.x[0])==null?void 0:W.data)==null?void 0:ie.length,p=40,B=K.getWidth(),_=Math.floor(B/p),pe=Math.floor((u-1)/(_-1));return(u-1-g)%pe===0}},show:!1},{type:"category",data:(ue=(de=xe.value)==null?void 0:de.x[0])==null?void 0:ue.data,axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(g,h){var ne,W,ie;const u=(ie=(W=(ne=xe.value)==null?void 0:ne.x[0])==null?void 0:W.data)==null?void 0:ie.length,p=40,B=K.getWidth(),_=Math.floor(B/p),pe=Math.floor((u-1)/(_-1));return(u-1-g)%pe===0}},position:"bottom"}],yAxis:{type:"value",min:-1,max:1,interval:.2,splitLine:{show:!0,lineStyle:{type:"dashed",color:Re}}},series:[{name:"自相关系数",type:"line",data:(Ae=(ve=xe.value)==null?void 0:ve.y[0])==null?void 0:Ae.data,symbol:"circle",symbolSize:8,lineStyle:{width:2},itemStyle:{color:"#3498db"},label:{show:!1,position:"top",formatter:function(g){return g.value.toFixed(3)}}},{name:"下限(95%置信区间)",type:"line",data:(q=($=xe.value)==null?void 0:$.y[1])==null?void 0:q.data,lineStyle:{type:"dashed",width:1},itemStyle:{color:"#95a5a6"},symbol:"none"},{name:"上限(95%置信区间)",type:"line",data:(D=(k=xe.value)==null?void 0:k.y[2])==null?void 0:D.data,lineStyle:{type:"dashed",width:1},itemStyle:{color:"#95a5a6"},symbol:"none"}]};K.setOption(M)}};let ae=null;const tt=r(null),Oe=r(""),Se=r({}),Fe=async()=>{var M,V,oe,ee,re,ce,de,ue,ve,Ae,$,q,k,D,g,h;const{json:t,httpController:be}=await wa((l==null?void 0:l.taskId)||O());if(t.code==="200"&&((oe=(V=(M=t==null?void 0:t.data)==null?void 0:M.return_chart)==null?void 0:V.y)==null||oe.forEach(u=>{u.data=u.data.map(p=>L(p*100))}),Oe.value=(re=(ee=t==null?void 0:t.data)==null?void 0:ee.return_chart)==null?void 0:re.title,Se.value=(ce=t==null?void 0:t.data)==null?void 0:ce.return_chart,tt.value)){if(ae=ot(tt.value),Object.keys(Se.value).length==0)return;const u={title:{},tooltip:{trigger:"axis",formatter:function(p){return p.map(B=>{let _=B.value;const pe=B.color;if(Array.isArray(_)){const ne=_.map(W=>W==null||isNaN(W)?"--":Number(W).toFixed(4));return`<span style="color:${pe}">${B.seriesName}</span>: ${ne[0]}, ${ne[1]}`}return _=_==null||isNaN(_)?"--":Number(_).toFixed(4),`<span style="color:${pe}">${B.seriesName}</span>: ${_}%`}).join("<br/>")}},legend:{data:(ue=(de=Se.value)==null?void 0:de.y)==null?void 0:ue.map(p=>p.name),textStyle:{color:Re}},xAxis:[{type:"category",data:($=(Ae=(ve=Se.value)==null?void 0:ve.x[0])==null?void 0:Ae.data)==null?void 0:$.map(p=>Dt(p)),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(p,B){var He,gt,pt;const _=(pt=(gt=(He=Se.value)==null?void 0:He.x[0])==null?void 0:gt.data)==null?void 0:pt.length,pe=60,ne=ae.getWidth(),W=Math.floor(ne/pe),ie=Math.floor((_-1)/(W-1));return(_-1-p)%ie===0}},show:!1},{type:"category",data:(D=(k=(q=Se.value)==null?void 0:q.x[0])==null?void 0:k.data)==null?void 0:D.map(p=>Dt(p)),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(p,B){var He,gt,pt;const _=(pt=(gt=(He=Se.value)==null?void 0:He.x[0])==null?void 0:gt.data)==null?void 0:pt.length,pe=60,ne=ae.getWidth(),W=Math.floor(ne/pe),ie=Math.floor((_-1)/(W-1));return(_-1-p)%ie===0}},position:"bottom"}],yAxis:{type:"value",axisLabel:{formatter:"{value}%"},splitLine:{show:!0,lineStyle:{type:"dashed",color:Re}}},grid:{left:"3%",right:"4%",bottom:"0%",top:"10%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:[0,1],start:0,end:100}],series:(h=(g=Se.value)==null?void 0:g.y)==null?void 0:h.map(p=>({name:p.name,type:"line",data:p.data,showSymbol:!1}))};ae.setOption(u)}};let Pe=null;const ct=r(null),It=r(""),dt=r({}),xt=async()=>{var V,oe,ee,re,ce,de,ue,ve,Ae,$,q,k,D;const{json:t,httpController:be}=await ba((l==null?void 0:l.taskId)||O());if(t.code==="200"&&(It.value=(oe=(V=t==null?void 0:t.data)==null?void 0:V.excess_chart)==null?void 0:oe.title,dt.value=(ee=t==null?void 0:t.data)==null?void 0:ee.excess_chart,ct.value)){if(Pe=ot(ct.value),Object.keys(dt.value).length==0)return;const g=["#1f77b4","#ff7f0e","#2ca02c","#d62728","#9467bd","#0099cc","#ff00ff"];var M={title:{},tooltip:{trigger:"axis",formatter:function(h){return h.map(u=>{let p=u.value;const B=u.color;if(Array.isArray(p)){const _=p.map(pe=>pe==null||isNaN(pe)?"--":Number(pe).toFixed(4));return`<span style="color:${B}">${u.seriesName}</span>: ${_[0]}, ${_[1]}`}return p=p==null||isNaN(p)?"--":Number(p).toFixed(4),`<span style="color:${B}">${u.seriesName}</span>: ${p}`}).join("<br/>")}},legend:{data:(ce=(re=dt.value)==null?void 0:re.y)==null?void 0:ce.map(h=>h.name),top:"0px",textStyle:{color:Re,fontSize:10}},grid:{left:"3%",right:"4%",bottom:"0%",top:"10%",containLabel:!0},dataZoom:[{type:"slider",xAxisIndex:[0,1],start:0,end:100}],xAxis:[{type:"category",data:(ve=(ue=(de=dt.value)==null?void 0:de.x[0])==null?void 0:ue.data)==null?void 0:ve.map(h=>h.split(" ")[0]),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(h,u){var W,ie,He;const p=(He=(ie=(W=dt.value)==null?void 0:W.x[0])==null?void 0:ie.data)==null?void 0:He.length,B=40,_=Pe.getWidth(),pe=Math.floor(_/B),ne=Math.floor((p-1)/(pe-1));return(p-1-h)%ne===0}},show:!1},{type:"category",data:(q=($=(Ae=dt.value)==null?void 0:Ae.x[0])==null?void 0:$.data)==null?void 0:q.map(h=>h.split(" ")[0]),axisTick:{show:!0,alignWithLabel:!0},axisLabel:{rotate:45,interval:function(h,u){var W,ie,He;const p=(He=(ie=(W=dt.value)==null?void 0:W.x[0])==null?void 0:ie.data)==null?void 0:He.length,B=40,_=Pe.getWidth(),pe=Math.floor(_/B),ne=Math.floor((p-1)/(pe-1));return(p-1-h)%ne===0}},position:"bottom"}],yAxis:{type:"value",axisLabel:{formatter:h=>(h*100).toFixed(1)+"%"}},series:(D=(k=dt.value)==null?void 0:k.y)==null?void 0:D.map((h,u)=>({name:h.name,type:"line",data:h.data,symbol:"circle",symbolSize:6,showSymbol:!1,lineStyle:{width:(h.name.includes("多空组合"),1)},itemStyle:{color:g[u]}}))};Pe.setOption(M)}};Ke(()=>l.factorId,async()=>{l.into&&(console.log("加载mc3"),await kt(),await o(),await Be(),await T(),await H(),await E(),await A(),await We(),await Me(),await lt(),await f(),await he(),await Y(),await Fe(),await xt(),await Ge(),ut())}),Ke(()=>l.taskId,async()=>{l.into&&(console.log("加载mc2"),await Ge(),ut(),await kt(),await o(),await Ge(),await Be(),await T(),await H(),await E(),await A(),await We(),await Me(),await lt(),await f(),await he(),await Y(),await Fe(),await xt(),await Ge(),setTimeout(()=>{ut()},1200))}),Ke(()=>l.into,async()=>{l.into&&(console.log("加载mc1"),await kt(),await o(),await Be(),await T(),await H(),await E(),await A(),await We(),await Me(),await lt(),await f(),await he(),await Y(),await Fe(),await xt(),await Ge(),ut())}),Ke(()=>l.into,async()=>{await Ge(),ut()});const ut=()=>{J==null||J.resize(),le==null||le.resize(),v==null||v.resize(),R==null||R.resize(),Te==null||Te.resize(),fe==null||fe.resize(),Je==null||Je.resize(),I==null||I.resize(),K==null||K.resize(),ae==null||ae.resize(),Pe==null||Pe.resize()},zt=()=>{J==null||J.dispose(),le==null||le.dispose(),v==null||v.dispose(),R==null||R.dispose(),Te==null||Te.dispose(),fe==null||fe.dispose(),Je==null||Je.dispose(),I==null||I.dispose(),K==null||K.dispose(),ae==null||ae.dispose(),Pe==null||Pe.dispose()},St=r([]),kt=async()=>{var M;const{json:t,httpController:be}=await xa((l==null?void 0:l.taskId)||O());St.value=(M=t==null?void 0:t.data)==null?void 0:M.factor_data_analysis},Rt=r(!0);ht(async()=>{try{l.into&&(await kt(),await o(),await Be(),await T(),await H(),await E(),await A(),await We(),await Me(),await lt(),await f(),await he(),await Y(),await Fe(),await xt(),Rt.value=!1,await Ge(),ut())}catch{we.value,Rt.value=!0}window.addEventListener("resize",ut)}),yt(()=>{window.removeEventListener("resize",ut),zt()});const Dt=t=>t&&t.split(" ")[0];return(t,be)=>(y(),w("div",ao,[e("div",oo,[e("div",so,[e("div",lo,P(_e.value),1),e("div",no,[e("div",{ref_key:"echartsRefEarningsBigChart",ref:tt,style:{width:"100%",height:"400px"}},[De(st,{visible:!0})],512)])]),e("div",io,[e("div",ro,[e("div",co,[e("div",uo,[e("div",{class:"value",style:Ze({color:x(se.value.return_ratio)})},P(se.value.return_ratio),5),be[0]||(be[0]=e("div",{class:"label"},"因子收益",-1))]),e("div",fo,[e("div",{class:"value",style:Ze({color:x(se.value.sharpe_ratio)})},P(se.value.sharpe_ratio),5),be[1]||(be[1]=e("div",{class:"label"},"夏普比率",-1))]),e("div",ho,[e("div",{class:"value",style:Ze({color:x(se.value.annualized_ratio)})},P(se.value.annualized_ratio),5),be[2]||(be[2]=e("div",{class:"label"},"年化收益",-1))]),e("div",go,[e("div",po,P(se.value.maximum_drawdown),1),be[3]||(be[3]=e("div",{class:"label"},"最大回撤",-1))]),(y(!0),w(qe,null,Xe(St.value,(M,V)=>(y(),w("div",{key:V,class:"data-item"},[(y(!0),w(qe,null,Xe(Object.values(M),(oe,ee)=>(y(),w(qe,{key:ee},[ee==1?(y(),w("div",vo,P(oe),1)):Ne("",!0),ee==0?(y(),w("div",Ao,P(oe),1)):Ne("",!0)],64))),128))]))),128))])]),e("div",mo,[e("div",yo,P(Ee.value),1),e("div",wo,[e("div",bo,[De(Ut,{tableData:c.value,headers:z.value,isFixed:!0,"custom-header":i.value},null,8,["tableData","headers","custom-header"])])])]),e("div",xo,[e("div",ko,P(Le.value),1),e("div",_o,[e("div",Co,[De(Ut,{tableData:ge.value,headers:U.value,isFixed:!0,"column-width":110},null,8,["tableData","headers"])])])])]),e("div",Lo,[e("div",Eo,[e("div",Io,P(j.value),1),e("div",So,[e("div",{ref_key:"echartsRefSimDietrich",ref:b,style:{width:"100%",height:"400px"}},[De(st,{visible:!0})],512)])]),e("div",Ro,[e("div",Do,P(Z.value),1),e("div",No,[e("div",{ref_key:"echartsRefICDistribution",ref:S,style:{width:"100%",height:"400px"}},[De(st,{visible:!0})],512)])]),e("div",Bo,[e("div",To,P(ye.value),1),e("div",Mo,[e("div",{ref_key:"echartsRefICSequence",ref:me,style:{width:"100%",height:"400px"}},[De(st,{visible:!0})],512)])]),e("div",Uo,[e("div",Fo,P(rt.value),1),e("div",Wo,[e("div",{ref_key:"echartsRefICCcorrelation",ref:$e,style:{width:"100%",height:"400px"}},[De(st,{visible:!0})],512)])]),e("div",Oo,[e("div",Vo,P(ze.value),1),e("div",Go,[e("div",{ref_key:"echartsRefRankICDecay",ref:Qe,style:{width:"100%",height:"400px"}},[De(st,{visible:!0})],512)])]),e("div",qo,[e("div",zo,P(a.value),1),e("div",Yo,[e("div",{ref_key:"echartsRefRankICDistribution",ref:s,style:{width:"100%",height:"400px"}},[De(st,{visible:!0})],512)])]),e("div",Po,[e("div",Zo,P(te.value),1),e("div",Qo,[e("div",{ref_key:"echartsRefRankICSequence",ref:C,style:{width:"100%",height:"400px"}},[De(st,{visible:!0})],512)])]),e("div",Jo,[e("div",Ho,P(Ue.value),1),e("div",Xo,[e("div",{ref_key:"echartsRefRankICCcorrelation",ref:Ie,style:{width:"100%",height:"400px"}},[De(st,{visible:!0})],512)])]),e("div",$o,[e("div",jo,P(Oe.value),1),e("div",Ko,[e("div",{ref_key:"echartsRefCurrentDeepAnalysis",ref:ke,style:{width:"100%",height:"400px"}},[De(st,{visible:!0})],512)])]),e("div",es,[e("div",ts,P(It.value),1),e("div",as,[e("div",{ref_key:"echartsRefExcessReturnBigChart",ref:ct,style:{width:"100%",height:"400px"}},[De(st,{visible:!0})],512)])])])])]))}}),ss=it(os,[["__scopeId","data-v-62815ad4"]]),ls={class:"logs-header"},ns={class:"controls"},is={class:"filter-group"},rs=["value"],cs={class:"filter-group"},ds={width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},us={key:0,d:"M3 4h13M3 8h9M3 12h5M19 20V8M15 16l4 4 4-4"},fs={key:1,d:"M3 4h13M3 8h9M3 12h5M19 20V8M15 12l4-4 4 4"},hs={class:"filter-group"},gs={class:"logs-table"},ps={class:"log-message"},vs={key:0,class:"metadata-container"},As=["onClick"],ms={class:"toggle-text"},ys={class:"log-metadata"},ws={class:"logs-footer"},bs={class:"pagination-info"},xs={key:0},ks={class:"pagination-controls"},_s=["disabled"],Cs={width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",style:{transform:"rotate(270deg)"}},Ls=["disabled"],Es={width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",style:{transform:"rotate(90deg)"}},Is=mt({__name:"Logs",props:{isCollapsed:{type:Boolean,default:!0},logCount:{type:Number,default:0}},setup(Q){const O=r(null);Vt();const N=Ct(),m=Q,L=r([]),l=r(!1),we=r("ALL"),x=r("asc"),se=r(50),o=r(1),J=r([void 0]),ke=r(void 0),_e=r(!1),G=r(null),H=r(m.isCollapsed);Ke(()=>m.isCollapsed,d=>{d||(H.value=d)});const Le=r(new Set),U=r(50);let ge=!1;const Be=d=>{var R;ge=!0,document.body.style.cursor="ns-resize",document.body.style.userSelect="none";const A=(R=d.target.closest(".logs-container"))==null?void 0:R.getBoundingClientRect();A&&(A.height,d.clientY,document.addEventListener("mousemove",Ee),document.addEventListener("mouseup",z))},Ee=d=>{if(!ge)return;const A=window.innerHeight,R=d.clientY,ye=(A-R)/A*100;U.value=Math.min(Math.max(ye,20),80)},z=()=>{ge=!1,document.body.style.cursor="",document.body.style.userSelect="",document.removeEventListener("mousemove",Ee),document.removeEventListener("mouseup",z)},c=async(d,A=!1)=>{if(!N.workflow_run_id||!N.workflow_id)return L.value=[],A&&(o.value=1,J.value=[void 0],ke.value=void 0,_e.value=!1,G.value=null),!1;l.value=!0;try{const R=localStorage.getItem("token"),me={workflow_run_id:N.workflow_run_id,workflow_id:N.workflow_id,limit:Number(se.value)};d&&(me.last_sequence=d),we.value!=="ALL"&&(me.log_level=we.value);const ye=await Gt.get("http://localhost:8000/api/workflow/run/log",{params:me,headers:{uid:"0","quantflow-auth":"2","Content-Type":"application/json",Authorization:`${R}`}});if(ye.status===200&&ye.data&&ye.data.data){const Ce=ye.data.data;return L.value=Ce.logs||[],ke.value=Ce.next_sequence,_e.value=Ce.has_more??!1,G.value=Ce.total_count,A&&(o.value=1,J.value=[void 0]),!0}else return console.error("Failed to fetch logs:",ye),L.value=[],A&&(o.value=1,J.value=[void 0],ke.value=void 0),_e.value=!1,!1}catch(R){return console.error("Error fetching logs:",R),L.value=[],A&&(o.value=1,J.value=[void 0],ke.value=void 0),_e.value=!1,!1}finally{l.value=!1}},i=wt(()=>{let d=[...L.value];return we.value!=="ALL"&&(d=d.filter(A=>A.level===we.value)),d.sort((A,R)=>{const me=new Date(A.timestamp).getTime(),ye=new Date(R.timestamp).getTime();return x.value==="asc"?me-ye:ye-me}),d}),T=wt(()=>["ALL","INFO","ERROR","WARNING","DEBUG"]),le=async()=>{if(_e.value&&ke.value!=null&&!l.value){const d=ke.value;await c(d,!1)&&(o.value++,J.value.length<o.value?J.value.push(d):J.value[o.value-1]=d)}},b=async()=>{if(o.value>1&&!l.value){const d=o.value-1,A=J.value[d-1];await c(A,!1)&&(o.value=d)}},j=()=>{x.value=x.value==="asc"?"desc":"asc",Ge(()=>{S()})},F=d=>{if(!d)return"N/A";try{return new Date(d).toLocaleString()}catch{return d}},E=()=>{c(void 0,!0)},v=()=>{H.value=!H.value,H.value||(U.value=50)},S=()=>{const d=document.querySelector(".logs-content-area");d&&(x.value==="asc"?d.scrollTop=d.scrollHeight:d.scrollTop=0)};Ke(()=>L.value,()=>{Ge(()=>{S()})},{deep:!0}),Ke(()=>m.logCount,async(d,A)=>{d!==A&&N.workflow_run_id&&await c(void 0,!0)},{immediate:!1}),ht(async()=>{await c(void 0,!0)});const Z=(d,A)=>{Le.value.has(d)?Le.value.delete(d):Le.value.add(d),Ge(()=>{var me,ye,Ce;const R=(me=A.target)==null?void 0:me.nextElementSibling;if(R){const We=R.getBoundingClientRect().height;console.log("height:",We),(Ce=O.value)==null||Ce.scrollTo({top:((ye=O.value)==null?void 0:ye.scrollHeight)+We,behavior:"smooth"})}})};return(d,A)=>(y(),w("div",{class:et(["logs-container",{collapsed:H.value,dragging:je(ge)}]),style:Ze({height:U.value+"%"})},[e("div",{class:"drag-handle",onMousedown:Be},null,32),e("div",{class:"toggle-button",onClick:v},[(y(),w("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",style:Ze({transform:H.value?"rotate(0deg)":"rotate(180deg)"})},A[2]||(A[2]=[e("path",{d:"M7 14l5-5 5 5",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)]),4))]),e("div",ls,[A[7]||(A[7]=Jt('<div class="title" data-v-cd54df2d><svg width="24" height="24" viewBox="0 0 24 24" fill="none" data-v-cd54df2d><circle cx="12" cy="12" r="9" stroke="currentColor" stroke-width="2" data-v-cd54df2d></circle><path d="M8 9H16" stroke="currentColor" stroke-width="2" stroke-linecap="round" data-v-cd54df2d></path><path d="M8 12H14" stroke="currentColor" stroke-width="2" stroke-linecap="round" data-v-cd54df2d></path><path d="M8 15H12" stroke="currentColor" stroke-width="2" stroke-linecap="round" data-v-cd54df2d></path></svg><span data-v-cd54df2d>日志内容</span></div>',1)),e("div",ns,[e("div",is,[A[3]||(A[3]=e("label",{for:"level-filter"},"级别:",-1)),nt(e("select",{id:"level-filter","onUpdate:modelValue":A[0]||(A[0]=R=>we.value=R),onChange:E},[(y(!0),w(qe,null,Xe(T.value,R=>(y(),w("option",{key:R,value:R},P(R),9,rs))),128))],544),[[Nt,we.value]])]),e("div",cs,[A[4]||(A[4]=e("label",{for:"sort-order"},"时间:",-1)),e("button",{onClick:j,class:"sort-button"},[(y(),w("svg",ds,[x.value==="desc"?(y(),w("path",us)):(y(),w("path",fs))]))])]),e("div",hs,[A[6]||(A[6]=e("label",{for:"limit-per-page"},"每页显示:",-1)),nt(e("select",{id:"limit-per-page","onUpdate:modelValue":A[1]||(A[1]=R=>se.value=R),onChange:E},A[5]||(A[5]=[e("option",{value:"50"},"50",-1),e("option",{value:"100"},"100",-1)]),544),[[Nt,se.value]])])])]),e("div",{class:"logs-content-area",ref_key:"logsContentArea",ref:O},[e("table",gs,[A[9]||(A[9]=e("thead",null,[e("tr",null,[e("th",{class:"timestamp-col"},"时间"),e("th",{class:"level-col"},"级别"),e("th",{class:"message-col"},"消息")])],-1)),e("tbody",null,[(y(!0),w(qe,null,Xe(i.value,R=>(y(),w("tr",{key:R._id,class:et(["log-entry",`log-level-${R.level.toLowerCase()}`])},[e("td",null,P(F(R.timestamp)),1),e("td",null,[e("span",{class:et(["log-level-badge",`log-level-badge-${R.level.toLowerCase()}`])},P(R.level),3)]),e("td",ps,[At(P(R.message)+" ",1),R.error_detail&&Object.keys(R.error_detail).length>0?(y(),w("div",vs,[e("button",{class:"metadata-toggle",onClick:me=>Z(R._id,me)},[e("span",ms,P(Le.value.has(R._id)?"收起详情":"查看详情"),1),(y(),w("svg",{width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",class:et({rotate:Le.value.has(R._id)})},A[8]||(A[8]=[e("path",{d:"M6 9l6 6 6-6",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)]),2))],8,As),nt(e("div",ys,[e("pre",null,P(R.error_detail),1)],512),[[bt,Le.value.has(R._id)]])])):Ne("",!0)])],2))),128))])])],512),e("div",ws,[e("div",bs,[e("span",null,"第 "+P(o.value)+" 页",1),G.value!==null?(y(),w("span",xs," / 总 "+P(G.value)+" 条记录",1)):Ne("",!0),e("span",null," (已加载 "+P(L.value.length)+" 条)",1)]),e("div",ks,[e("button",{onClick:b,disabled:o.value===1||l.value,class:"pagination-button pagination-button-prev"},[(y(),w("svg",Cs,A[10]||(A[10]=[e("path",{d:"M7 14l5-5 5 5",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)]))),A[11]||(A[11]=At(" 上一页 "))],8,_s),e("button",{onClick:le,disabled:!_e.value||l.value,class:"pagination-button pagination-button-next"},[A[13]||(A[13]=At(" 下一页 ")),(y(),w("svg",Es,A[12]||(A[12]=[e("path",{d:"M7 14l5-5 5 5",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)])))],8,Ls)])])],6))}}),Ss=it(Is,[["__scopeId","data-v-cd54df2d"]]),Rs={class:"alert"},Ds={class:"box"},Ns={__name:"alert",props:{show:{type:Boolean,default:!1}},emits:["close"],setup(Q,{emit:O}){const N=Q,m=O,L=()=>{m("close")};return(l,we)=>nt((y(),w("div",Rs,[e("div",Ds,[e("img",{onClick:L,class:"close-btn",src:qt}),Ht(l.$slots,"default",{},void 0,!0)])],512)),[[bt,N.show]])}},Lt=it(Ns,[["__scopeId","data-v-34167be4"]]),Bs="/quantflow/assets/chat-dI4p2fsV.png",Ts="data:image/png;base64,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",Ms="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAAAXNSR0IArs4c6QAAAvdJREFUWEfNmEtoFEEQhv/qhXWJ2XiRgAHxICRHkdxEwT3N1qwQMYL4OKlnQRIUjHrxAT6C4NncfCAYY8DtmTkloHgL4tGABxEiiJeYGNaF7dIOPZKsmzibDZtpmNNUV31dXV1V3YSUDWqWJ4qi7cYYD4APYC+AHvdZVXPu+wRAK6Uiz/N+NmMjMZDWej8RXQPAAHIJjVQABCJyw/f990nm/BeoXC7vUUrdAnAKgJU3AN4R0SsRmTHGzHV0dFjPYGlpqUcp1UNE/SJyFMABAAqAAHhqjBkplUqf1wNbF0hrzUT0DMAOABUReZjJZEY9z/uWZLVRFHXXarUhIrrgvDovIid93w/Wmr8mUBiGQyJy165QRMYzmcxFz/O+JAGpl4miaHetVntARIPWw0R0qVgsjjbS1RDIwdy3riaiq8Vi8fZGQOrnhGF4RURu2q0nouFGUP8AuW16bSc59z7fDJhYh9b6hAsDEZEj9du3CsgF8AcbM0Q0slmeqV+Q1nqEiKyn5o0x+1YG+iqgIAgeAzhtY8b3/eOb6ZkGUC9cTD1h5jPx/79ALs/MAPillOrdaAAnXYQNdGPMLIBtItIf56m/QGEYTtjcYU+W7/uXkypuRU5rfceeOAATzHzM6loGcuXgO4CsUmpX0jzTCoyz222M+QqgqpTaacvMMlAQBJZuHMBbZj7UqqFm5gdB8AbAQQCDzPxyGSgMwzERObtWbmjGQLOyK3LeGDOfjz00BeAwgAIzTzertBX5IAisXWt/mpkLMdDHP8Wv1xjTVyqVbOS3bZTL5V6llLU/y8x9MdACgM5cLpcvFAqLbaOxrpma6qxUKtb+IjPnY6AfAPLVarVrYGDA/mzbmJyczGezWWt/gZm7UrtlqQvqRwDObcWx11oPE9E9AKuOfboSY+pKh8vW6SmuFih17Ycrsulp0CxQ6lpYt3X2LpaOJj+uF6m6BtVBpeOiGEOl6iodQ6XqsWFlD2LzlFLquogUt/Q5pr4xSs2DVbs6tt8B3hFDpWW0GwAAAABJRU5ErkJggg==",Us="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAYCAYAAADkgu3FAAAAAXNSR0IArs4c6QAAAydJREFUSEu11UtoE0EYB/Dv221VpAejXutBXT3I4kHBbhSxtvWFiA8Si9hLqRZfVeoDn1hFsT7RKooP9CIqqagItphNrIJQtW2QVEGxerB60UMDvjA7830ym6b2mUZLBkIg2Znf/L/5hkX4j2GFggeANGicV7g/3emY7oPJ57xh+xAL3sMSAAgPP1tctDedNf4JssIPj4DEnSARWDKob5RQ3bi8aNdgWNqQFbKPAcF2Fgpw04CbKvE5/ry4aEcqLC3IGwqeJAmV7uIC3iLBZAWwhFaWYCZgPvWiZN7WgbBBISscPM0CNiOhWiwIcdpHrD9Xi0tJpk76CZYw303KeKaptHBLf1hKyBt+WMMCNyWScL1HF8ti8exJ5EAUCECLS+NrB7V7crLuImsLVSlZ4Nnm9QUVvbH+IWbMC4fOIcF6tVMkfDBKc1bUL1r02wrYJhFE1aI6SaOxZEHbxJq64Z5h2beZcXFnSc+3VBRsBEROgn0hZrQa7AvgQHlnC9//9rPD99rvj6tJCpKdiXTIMhpL8tvU71MCgWEjv3hqmbQlIBlIwMXItoJ1SawnxIzesH2JJJSpMyGCe+KTx99SPt1J7sy6bpuSVOkQhBBGpGyBC6kx7WJzNnyPBYBgqfofBFxp2T13rcL+QlVVmjVr5mWQXMrqIcl3nM+ji7sjbiIFCYyqEgnuCSUx7ojdRIIVLN11rkbg6ZouKC8UPAMSKlQSFlw7Iid71eP8fNH7UK1rtilUIgEgRZYR2ZAoXfcxp6oh65tON1iCzz0zwTVdkDcY/EgCckHCrdxfsdW1fr+6HX2GCznoNgOR3i+kJvkCAf1965jrQFjMEtq7oBl1dh4wG+N+xG4MhCRKY5tIEEWJIOO6Eansmyi5O58voL8bP3YlEb8Z9ML2juRCDrrNQEJLCXWf++/QWZUoUTqG1ImGDiUTOZoR2TVw6YYGnbJNkIn2ZtYzDDlaVN1+Vs1QlalE1bbJEqPufZNahiE3kTqBTEIHbZOpE1LNUJ2h0k09+GiK7vArdY90SROaqgs/DPRWHVLXqdveZox5whL55dH82QB/3zmpwD/qfMD8dywLsQAAAABJRU5ErkJggg==",Fs={key:0,class:"factor-chat"},Ws={class:"container"},Os={class:"chat-header",style:{padding:"5px 0",display:"flex","justify-content":"space-between","align-items":"center"}},Vs={class:"assistant-type-select",style:{"margin-right":"16px"}},Gs={style:{"margin-left":"4px","vertical-align":"middle"},width:"12",height:"12",viewBox:"0 0 24 24"},qs={key:0,class:"dropdown-list",style:{position:"absolute",right:"0",top:"100%",background:"#222",border:"1px solid #333","border-radius":"4px","min-width":"100px","z-index":"100"}},zs=["onClick"],Ys={class:"chat-item-content"},Ps={class:"chat-item-header"},Zs={class:"chat-item-header-time"},Qs={key:0,src:Bs,alt:"chat",width:"16",height:"16",style:{"margin-left":"10px"}},Js={class:"chat-item-content-text"},Hs=["innerHTML"],Xs={class:"chat-input"},$s={key:0,src:Ts,alt:"checkbox",width:"16",height:"16",style:{"margin-right":"3px"}},js={key:1,src:Ms,alt:"checkbox-unselected",width:"16",height:"16",style:{"margin-right":"3px"}},Ks={class:"dropdown-arrow",width:"14",height:"14",viewBox:"0 0 24 24",style:{"vertical-align":"middle",position:"relative",top:"2px"}},el={key:0,class:"modes-select-dropdown"},tl=["onClick"],al={key:0,width:"16",height:"16",viewBox:"0 0 24 24",style:{"margin-right":"6px"}},ol={key:0,src:Us,alt:"send",width:"14",height:"14"},sl={key:1,xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"currentColor"},ll={__name:"Chat",props:{visible:{type:Boolean,default:!0}},emits:["update:visible","apply-code","close-chat"],setup(Q,{emit:O}){const N=Ct(),m=r(!0),L=r(""),l=r(""),we=r([{type:"receive",content:"你好，我是PandaAI Chat，很高兴认识你！"}]),x=r(null),se=r(null),o=r(!1),J=r(null);Bt({start:!1,end:!1});const ke=[{value:"code",label:"通用代码助手"},{value:"backtest",label:"回测代码助手"},{value:"factor",label:"因子构建代码助手"}];console.log("msms:",N.assistantTypeMap);const _e=wt(()=>{const E=ke.find(v=>v.value===N.assistantTypeMap.get(N.id));return E?E.label:""}),G=r(!1),H=r(null);function Le(E){N.assistantTypeMap.set(N.id,E),G.value=!1}const U=async()=>{if(l.value.trim()==""&&!o.value){se.value.className="sendEmpty",await new Promise(E=>setTimeout(E,300)),se.value.className="";return}if(o.value){o.value=!1,se.value.disabled=!1,console.log("点击了暂停"),J.value&&J.value.abort(),we.value.pop(),we.value.pop();return}if(l.value.trim()){o.value=!0,se.value.disabled=!0;const E={type:"send",content:l.value};we.value.push(E),l.value="",Ge(()=>{x.value&&(x.value.scrollTop=x.value.scrollHeight),x.value&&x.value.dispatchEvent(new Event("refresh-highlight"))});const v={message:m.value?L.value+`
`+E.content:E.content};Be.value&&(v.session_id=Be.value),m.value&&(v.original_code=N.original_code),v.model=i.value,await Ee(v),o.value=!1,se.value.disabled=!1,Ge(()=>{x.value&&(x.value.scrollTop=x.value.scrollHeight),x.value&&x.value.dispatchEvent(new Event("refresh-highlight"))})}},ge=r(null),Be=r(""),Ee=async E=>{clearInterval(ge.value),J.value=new AbortController;const v=setTimeout(()=>J.value.abort(),1e3*50),S=Bt({type:"receive",content:"正在思考..."});we.value.push(S),Ge(()=>{x.value&&(x.value.scrollTop=x.value.scrollHeight),x.value&&x.value.dispatchEvent(new Event("refresh-highlight"))});try{let Z=!1;const d=r(".");console.log("------1------"),ge.value=setInterval(()=>{d.value=d.value==="..."?".":d.value+".",S.content="正在处理"+d.value,Z||(Ge(()=>{x.value&&(x.value.scrollTop=x.value.scrollHeight),x.value&&x.value.dispatchEvent(new Event("refresh-highlight"))}),Z=!0)},300);let A="";N.assistantTypeMap.get(N.id)==="backtest"?A="/pandaApi/quantflow/api/chat/backtest-assistant-stream":N.assistantTypeMap.get(N.id)==="factor"?A="/pandaApi/quantflow/api/chat/factor-assistant-stream":N.assistantTypeMap.get(N.id)==="code"&&(A="/pandaApi/quantflow/api/chat/code-assistant-stream");const R=localStorage.getItem("token"),me=await fetch(A,{method:"POST",headers:{"Content-Type":"application/json",uid:"0",Authorization:`${R}`},body:JSON.stringify(E),signal:J.value.signal});if(clearInterval(v),me.status!==200)throw new Error("服务器错误，请稍后再试！");const ye=me.body.getReader(),Ce=new TextDecoder;let We=!1,Te=!0,$e=0,rt=1,Ye="",Me="";for(;!We;){const fe=new Promise((at,lt)=>{setTimeout(()=>{lt(new Error("Read-timed-out"))},36e5)}),Qe=await Promise.race([ye.read(),fe]);Te&&(Te=!1,S.content="",clearInterval(ge.value));const{value:ze}=Qe;if(We=Qe.done,ze){const at=Ce.decode(ze,{stream:!0}),lt=/^data:\s*(\{.*}$)/gm,Je=at.match(lt);if(Je){for(const s of Je)try{const a=JSON.parse(s.replace("data: ",""));if(console.log("---m---",a),console.log("---typeChange---",Me),Object.keys(a).includes("status")&&(Me&&Me==="reasoning"&&(S.content+="</pre>"),Me&&Me==="content"&&(S.content+="</pre>"),Me="status",S.content+=`<li style='font-size:12px;color:#ddd'>${a.status}</li>`),Object.keys(a).includes("reasoning")&&(Me!=="reasoning"&&(S.content+=`<pre class='reasoning' style="color:#aaa; white-space: pre-wrap;font-family: sans-serif;">`,Me="reasoning"),S.content+=a.reasoning),Object.keys(a).includes("content")&&(Me!=="content"&&(S.content+=`<pre class='content' style="color:#aaa; white-space: pre-wrap;font-family: sans-serif;">`,Me="content"),S.content+=a.content),Object.keys(a).includes("code")&&a.code){const n="codeId"+Date.now();Me!=="code"&&(S.content+="<div class='final_result_code'>",Me="code");const f=a.code;N.codeMap.set(n,f),S.content+=`<pre><code class='language-python'>${f}</code></pre>
                                 <span class='codeApply' data-id='${n}'>应用</span>
                                 <span class='codeCopy'  data-id='${n}'>复制</span>
                                 </div>`}Object.keys(a).includes("explanation")&&(S.content+=`<pre  style='font-size:12px;color:#ddd;white-space: pre-wrap;'>${ea.parse(a.explanation)}</pre>`),Object.keys(a).includes("session_id")&&(Be.value=a.session_id)}catch(a){console.log(a)}Ge(()=>{x.value&&(x.value.scrollTop=x.value.scrollHeight),x.value&&x.value.dispatchEvent(new Event("refresh-highlight"))})}}S.content=S.content.replace(/<span class=['"]lod['"][^>]*><\/span>/g,""),S.content+="<span class='lod'></span>"}We&&(S.content=S.content.replace(/<span class=['"]lod['"][^>]*><\/span>/g,""),Ge(()=>{const fe=document.querySelectorAll(".reasoning");for(let ze=0;ze<fe.length;ze++)fe[ze].style.display="none";const Qe=document.querySelectorAll(".content");for(let ze=0;ze<Qe.length;ze++)Qe[ze].style.display="none"}))}catch(Z){Z.name==="AbortError"||Z.name==="Read-timed-out"?S.content="网络超时，请稍后再试！":(console.log("error",(Z==null?void 0:Z.message)||Z),S.content="网络超时，请稍后再试!"),clearInterval(ge.value),o.value=!1,se.value.disabled=!1}};ht(()=>{});function z(E){G.value&&H.value&&!H.value.contains(E.target)&&(G.value=!1)}Xt(()=>{setTimeout(()=>{const E=document.querySelectorAll(".reasoning");for(let S=0;S<E.length;S++)E[S].style.display="block";const v=document.querySelectorAll(".content");for(let S=0;S<v.length;S++)v[S].style.display="none";x.value&&(x.value.scrollTop=x.value.scrollHeight)},60)}),ht(()=>{document.addEventListener("click",z)}),yt(()=>{document.removeEventListener("click",z)});const c=[{item:"DeepSeek-R1",value:"DeepSeek-R1"},{item:"DeepSeek-V3",value:"DeepSeek-V3"}],i=r(c[1].value),T=r(!1),le=r(null);function b(E){i.value=E,T.value=!1}function j(E){T.value=!T.value,Ge(()=>{T.value&&document.addEventListener("click",F,!0)})}function F(E){le.value&&!le.value.contains(E.target)&&(T.value=!1,document.removeEventListener("click",F,!0))}return Ke(T,E=>{E||document.removeEventListener("click",F,!0)}),(E,v)=>{var Z;const S=$t("highlight");return Q.visible?(y(),w("div",Fs,[e("div",Ws,[e("div",Os,[v[4]||(v[4]=e("span",null,"AI 助手",-1)),e("div",Vs,[e("div",{class:"dropdown",ref_key:"dropdownRef",ref:H,onClick:v[0]||(v[0]=d=>G.value=!G.value),style:{position:"relative",cursor:"pointer"}},[e("span",null,P(_e.value),1),(y(),w("svg",Gs,v[3]||(v[3]=[e("path",{d:"M7 10l5 5 5-5z",fill:"#aaa"},null,-1)]))),G.value?(y(),w("div",qs,[(y(),w(qe,null,Xe(ke,d=>e("div",{key:d.value,onClick:vt(A=>Le(d.value),["stop"]),style:{padding:"6px 16px","text-align":"center","text-indent":"0",color:"#fff",cursor:"pointer","white-space":"nowrap"}},P(d.label),9,zs)),64))])):Ne("",!0)],512)])]),nt((y(),w("div",{class:"chat-content",ref_key:"chatItemContent",ref:x},[(y(!0),w(qe,null,Xe(we.value,(d,A)=>(y(),w("div",{class:et(["chat-item",{user:d.type==="send",ai:d.type==="receive"}]),key:A},[e("div",Ys,[e("div",Ps,[e("span",Zs,[d.type==="receive"?(y(),w("img",Qs)):Ne("",!0),At(" "+P(d.type==="send"?"你":"PandaAI 助手"),1)])]),e("div",Js,[e("div",{innerHTML:d.content},null,8,Hs)])])],2))),128))])),[[S]]),e("div",Xs,[e("label",null,[nt(e("input",{type:"checkbox","onUpdate:modelValue":v[1]||(v[1]=d=>m.value=d)},null,512),[[bt,!1],[jt,m.value]]),m.value?(y(),w("img",$s)):(y(),w("img",js)),v[5]||(v[5]=e("span",null,"引用当前编辑器代码",-1))]),e("div",{class:"modes-select",ref_key:"modelDropdownRef",ref:le},[e("div",{class:"modes-select-label",onClick:j},[e("span",null,P(((Z=c.find(d=>d.value===i.value))==null?void 0:Z.item)||"请选择模型"),1),(y(),w("svg",Ks,v[6]||(v[6]=[e("path",{d:"M7 10l5 5 5-5",stroke:"#52BBFE","stroke-width":"2",fill:"none","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)])))]),T.value?(y(),w("div",el,[(y(),w(qe,null,Xe(c,d=>e("div",{key:d.value,onClick:vt(A=>b(d.value),["stop"]),class:et(["modes-select-option",{selected:i.value===d.value}])},[i.value===d.value?(y(),w("svg",al,v[7]||(v[7]=[e("path",{d:"M5 13l4 4L19 7",stroke:"#52BBFE","stroke-width":"2",fill:"none","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)]))):Ne("",!0),e("span",null,P(d.item),1)],10,tl)),64))])):Ne("",!0)],512),nt(e("textarea",{ref_key:"textarea",ref:se,"onUpdate:modelValue":v[2]||(v[2]=d=>l.value=d),placeholder:"请在这里输入提问...",onKeydown:Kt(U,["enter"])},null,544),[[Ft,l.value]]),e("button",{onClick:U,class:et({send:!o.value})},[o.value?(y(),w("svg",sl,v[8]||(v[8]=[e("rect",{x:"6",y:"6",width:"12",height:"12",rx:"1"},null,-1)]))):(y(),w("img",ol))],2)])])])):Ne("",!0)}}},nl=it(ll,[["__scopeId","data-v-d87fb98b"]]),il={key:0,class:"alert-container"},rl={class:"alert-header"},cl={class:"alert-content"},dl={key:1,class:"alert-container"},ul={class:"alert-header"},fl={class:"alert-content"},hl={class:"content-container"},gl={class:"toolbar-container"},pl={class:"run-container"},vl={key:0,style:{"font-size":"12px",color:"#fff",position:"absolute",top:"-26px",transform:"scale(0.8)"}},Al={key:0,class:"drag-overlay"},ml={class:"monaco_ed"},yl={class:"alert"},wl={class:"box"},bl={style:{width:"100%",height:"calc(100% - 100px)",display:"flex",position:"relative"}},xl={key:0,class:"diff-action-buttons"},kl={class:"chartCode",style:{width:"35%",height:"100%","background-color":"#222",position:"relative"}},_l=mt({__name:"Editor",setup(Q){const O=r(null),N=r(!1);let m=null,L=null,l=null;const we=r(null),x=Vt(),se=ta(),o=Ct(),J=()=>{o.codeStatus=!1,o.id="",N.value=!1};Ke(()=>o.codeStatus,()=>{setTimeout(()=>{L==null||L.setValue(o.code.get(o.id)||""),l==null||l.setValue(o.code.get(o.id)||"")},30)}),yt(()=>{m&&m.dispose(),L&&L.dispose(),l&&l.dispose()});const ke=r(!1);function _e(){if(N.value&&m&&"getModel"in m&&L&&l){const s=m.getModel(),a=s&&s.modified?s.modified.getValue():"";setTimeout(()=>{o.code.set(o.id,a),L&&L.setValue(a)},60),ke.value=!0,N.value=!1}}function G(){if(N.value&&m&&"getModel"in m&&L&&l){const s=m.getModel(),a=s&&s.original?s.original.getValue():"";s&&s.modified&&s.modified.setValue(a),ke.value=!1,N.value=!1}}const H=()=>{if(o.node.node){let s="";if(N.value&&m&&"getModel"in m){const n=m.getModel();ke.value?s=n&&n.modified?n.modified.getValue():"":s=n&&n.original?n.original.getValue():""}else m&&"getValue"in m&&(s=m.getValue());o.node.node.properties[o.node.title]=s,o.code.set(o.id,s);const a=o.node.node.widgets.find(n=>n.name===o.node.title);a.value=s,console.log("workflowStore.node.widget",a),o.codeStatus=!1,ke.value=!1,N.value=!1}};Ke(()=>se.isBoxShow,async()=>{console.log("---2333-----"),se.isBoxShow&&(await Ge(),console.log("strategyAnalysisRef.value",we.value),we.value&&(console.log("---23334-----"),we.value.handleApply({workflow_id:se.id,title:o.title,feature_tag:"backtest",locator:"backtest_id",last_run_id:o.workflow_run_id},!1)))});function Le(s,a=!1){s||(s=window.location.href);const n={},f=s.indexOf("?");if(f!==-1){const I=s.indexOf("#",f),C=I!==-1?s.substring(f+1,I):s.substring(f+1);U(C,n)}if(a){const I=s.indexOf("#");if(I!==-1&&I<s.length-1){const C=s.substring(I+1),te=C.indexOf("?");if(te!==-1){const X=C.substring(te+1);U(X,n)}}}return n}function U(s,a){if(!s)return;const n=s.split("&");for(const f of n){const[I,C]=f.split("=",2);I&&(a[decodeURIComponent(I)]=C!==void 0?decodeURIComponent(C):"")}}const ge=Wt(),Be=r(!1),Ee=r(null);let z=r(null),c,i;const T=r(!1),le=r(0),b=r(!1);let j=0,F=0,E=1;const v=()=>{F+=.03*E,F>=1?(F=1,E=-1):F<=0&&(F=0,E=1),i&&i.draw(!0,!0),requestAnimationFrame(v)};v();const S=()=>{const s=Ee.value,a=Math.max(window.devicePixelRatio,1),{width:n,height:f}=s.getBoundingClientRect();s.width=Math.round(n*a),s.height=Math.round(f*a),s.getContext("2d").scale(a,a),i.scale_offset=[1,1],i.ds.scale=1,i.dirty_canvas=!0,i.dirty_bgcanvas=!0,i==null||i.draw(!0,!0)},Z=s=>{var a;if(T.value){s.preventDefault();return}(a=s.dataTransfer)!=null&&a.types.includes("Files")&&(s.preventDefault(),j++,b.value=!0)},d=s=>{var n,f;if(T.value){s.preventDefault();return}(((n=s.dataTransfer)==null?void 0:n.types.includes("node-type"))||(f=s.dataTransfer)!=null&&f.types.includes("Files"))&&(s.preventDefault(),s.dataTransfer.dropEffect="copy")},A=s=>{var a;if(T.value){s.preventDefault();return}(a=s.dataTransfer)!=null&&a.types.includes("Files")&&(s.preventDefault(),j--,j<=0&&(b.value=!1,j=0))},R=async s=>{var n,f,I,C,te,X,he;if(o.owner==="*"){ge.warning("当前为模版,不支持导入覆盖，请先点击保存按钮创建个人工作流后，再进行导入!");return}if(T.value){if(s.preventDefault(),(n=s.dataTransfer)!=null&&n.types.includes("Files")){const K=s.dataTransfer.files;K!=null&&K.length&&K[0].name.toLowerCase().endsWith(".json")&&ge.warning("工作流运行时无法导入 JSON 文件")}return}if((f=s.dataTransfer)==null?void 0:f.types.includes("node-type")){const K=(I=s.dataTransfer)==null?void 0:I.getData("node-type");if(console.log("nodeType:",K),K&&c){const Ie=(C=Ee.value)==null?void 0:C.getBoundingClientRect();if(Ie){const Ue=s.clientX-Ie.left,xe=s.clientY-Ie.top,Y=i.ds.scale||1,ae=i.ds.offset||[0,0],tt=[(Ue-ae[0])/Y,(xe-ae[1])/Y],Oe=Ve.createNode(K);if(Oe){Oe.pos=tt;let Se=0;for(const Fe of c.nodes)Fe.order&&Fe.order>Se&&(Se=Fe.order);Oe.order=Se+1e3,c.add(Oe),c.setDirtyCanvas(!0),i.draw(!0,!0)}}}return}if((te=s.dataTransfer)!=null&&te.types.includes("Files")){s.preventDefault(),b.value=!1,j=0;const K=s.dataTransfer.files;if(!(K!=null&&K.length))return;const Ie=K[0];if(!Ie.name.toLowerCase().endsWith(".json")){Lt("请拖入 JSON 文件");return}try{const Ue=await Ie.text(),xe=JSON.parse(Ue).litegraph;if(!c||!i){console.error("Graph or GraphCanvas not initialized");return}c.clear();try{if(c.configure(xe),c.nodes.forEach(Y=>{Y._fullTitle?Y.title=Y.truncateTitle(Y._fullTitle):(Y._fullTitle=Y.title||"",Y.title=Y.truncateTitle(Y.title||""))}),c.nodes.length>0){const Y=c.nodes[0],ae=Ee.value.getBoundingClientRect(),tt=ae.width,Oe=ae.height;i.setZoom(1);const Se=Y.pos[0],Fe=Y.pos[1],Pe=((X=Y.size)==null?void 0:X[0])||0,ct=((he=Y.size)==null?void 0:he[1])||0;i.ds.offset=[tt/2-(Se+Pe/2),Oe/2-(Fe+ct/2)],i.setDirty(!0,!0)}else i.setZoom(1),i.ds.offset=[0,0];i.draw(!0,!0),console.log("Graph imported successfully")}catch(Y){console.error("Error configuring graph:",Y),Lt("导入失败：图形数据格式不正确")}}catch(Ue){console.error("Import failed:",Ue),Lt("导入失败，请检查文件格式是否正确")}}},me=()=>{const s=getComputedStyle(document.documentElement),a=f=>s.getPropertyValue(f).trim();Ve.NODE_DEFAULT_BGCOLOR=a("--node-bg"),Ve.NODE_TEXT_COLOR=a("--node-text"),Ve.BACKGROUND_IMAGE_COLOR=a("--node-bg"),Ve.GRID_COLOR=a("--grid-color");const n=document.documentElement.classList.contains("dark");Ve.LGraphCanvas.DEFAULT_BACKGROUND_IMAGE,Ve.LGraphCanvas.link_type_colors={tezheng:"#339966",mubiao:"#CC3366",hangqing:"#3366CC",out1:"#CC6600",out2:"#0ff",target1:"#0ff",target2:"#f56c6c"}},ye=r(!1);Ke(ye,s=>{s&&i.visible_nodes.forEach(a=>{a.inputs.forEach((n,f)=>{})})}),Ke(T,s=>{!c||!i||(s?(i.allow_dragnodes=!1,i.allow_reconnect_links=!1,i.allow_linking=!1,i.allow_select_nodes=!1,c.nodes.forEach(a=>{a.inputs&&a.inputs.forEach(n=>{n.locked=!0}),a.outputs&&a.outputs.forEach(n=>{n.locked=!0}),a.widgets&&a.widgets.forEach(n=>{n.disabled=!0,n.element&&(n.element.style.opacity="0.5",n.element.style.cursor="crosshair",n.element.disabled=!0),console.log("widget:",n)})})):(i.allow_dragnodes=!0,i.allow_dragcanvas=!0,i.allow_reconnect_links=!0,i.allow_linking=!0,i.allow_select_nodes=!0,c.nodes.forEach(a=>{a.inputs&&a.inputs.forEach(n=>{n.locked=!1}),a.outputs&&a.outputs.forEach(n=>{n.locked=!1}),a.widgets&&a.widgets.forEach(n=>{n.disabled=!1,n.element&&(n.element.style.opacity="1",n.element.style.cursor="auto",n.element.disabled=!1)})})),i.draw(!0,!0))});const Ce=async s=>{try{const n={uid:"0","quantflow-auth":"2","Content-Type":"application/json",Authorization:`${localStorage.getItem("token")}`},f=await fetch(`http://localhost:8000/api/workflow/query?workflow_id=${s}`,{headers:n});if(f.status===404)return ge.error("工作流不存在!"),null;if(f.ok){const I=await f.json();return o.title=I.name,o.owner=I.owner,console.log("获取workflow数据成功:",I),I.last_run_id&&(o.workflow_run_id=I.last_run_id,at(null,"auto")),I}else throw new Error(`请求失败: ${f.status} ${f.statusText}`)}catch(a){return console.error("获取workflow数据失败:",a),null}},We=s=>{try{if(!s){console.error("获取workflow数据为空:",s);return}const a=s.litegraph;if(!c||!i){console.error("Graph or GraphCanvas not initialized");return}c.clear();try{if(c.configure(a),c.nodes.forEach(n=>{n._fullTitle?n.title=n.truncateTitle(n._fullTitle):(n._fullTitle=n.title||"",n.title=n.truncateTitle(n.title||""))}),c.nodes.length>0){let n=1/0,f=-1/0,I=1/0,C=-1/0;c.nodes.forEach(Fe=>{var Pe,ct;n=Math.min(n,Fe.pos[0]),f=Math.max(f,Fe.pos[0]+(((Pe=Fe.size)==null?void 0:Pe[0])||0)),I=Math.min(I,Fe.pos[1]),C=Math.max(C,Fe.pos[1]+(((ct=Fe.size)==null?void 0:ct[1])||0))});const te=f-n,X=C-I,he=Ee.value.getBoundingClientRect(),K=he.width,Ie=he.height,Ue=100,xe=(K-Ue*2)/te,Y=(Ie-Ue*2)/X,ae=Math.min(xe,Y,1);i.setZoom(ae);const tt=(n+f)/2,Oe=(I+C)/2,Se=window.devicePixelRatio||1;i.ds.offset=[K/2/Se-tt*ae+350,Ie/2/Se-Oe*ae+100],i.setDirty(!0,!0)}else i.setZoom(1),i.ds.offset=[0,0];i.draw(!0,!0),console.log("Graph imported successfully")}catch(n){console.error("Error configuring graph:",n),console.log("api工作流数据导入失败：图形数据格式不正确",n)}}catch(a){console.error("Import failed:",a),console.log("api工作流数据导入失败，请检查文件格式是否正确",a)}};ht(async()=>{var I;(I=document.querySelector(".chartCode"))==null||I.addEventListener("click",C=>{try{const te=C.target.closest(".codeApply"),X=C.target.closest(".codeCopy");if(te){console.log("应用");const he=o.codeMap.get(te.dataset.id);o.original_code=he,console.log("应用code:",he),N.value=!0,setTimeout(()=>{l==null||l.setValue(he)},400)}if(X){console.log("复制");const he=o.codeMap.get(X.dataset.id);console.log("复制code:",he),navigator.clipboard.writeText(he)}}catch(te){console.log("e1:",te)}}),N.value?(m=ft.createDiffEditor(O.value,{automaticLayout:!0,theme:"vs-dark",readOnly:!1,renderSideBySide:!1,ignoreTrimWhitespace:!1}),L.onDidChangeModelContent(()=>{console.log("内容改变2"),o.original_code=m.getValue()}),setTimeout(()=>{m&&o.code.get(o.id)&&(L=ft.createModel(o.code.get(o.id),"python"),l=ft.createModel(o.code.get(o.id),"python"),L&&l&&m.setModel({original:L,modified:l}))},60)):(m=ft.create(O.value,{automaticLayout:!0,theme:"vs-dark",readOnly:!1,language:"python",value:o.code.get(o.id)||""}),L=m.getModel(),m&&m.onDidChangeModelContent(()=>{console.log("内容改变：",m.getValue()),o.original_code=m.getValue(),o.code.set(o.id,m.getValue())}));const s=Le(window.location.href);if(console.log("params:",s),console.log("params.workflow_id:",s.workflow_id),s.workflow_id===""||!s.workflow_id?(o.workflow_id="",o.owner="",o.workflow_run_id="",o.title=""):o.workflow_id=s.workflow_id,console.log("onMounted"),!Ee.value)return;me(),Ve.LGraphCanvas.DEFAULT_BACKGROUND_IMAGE="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAIAAAD/gAIDAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAQBJREFUeNrs1rEKwjAUhlETUkj3vP9rdmr1Ysammk2w5wdxuLgcMHyptfawuZX4pJSWZTnfnu/lnIe/jNNxHHGNn//HNbbv+4dr6V+11uF527arU7+u63qfa/bnmh8sWLBgwYJlqRf8MEptXPBXJXa37BSl3ixYsGDBMliwFLyCV/DeLIMFCxYsWLBMwSt4Be/NggXLYMGCBUvBK3iNruC9WbBgwYJlsGApeAWv4L1ZBgsWLFiwYJmCV/AK3psFC5bBggULloJX8BpdwXuzYMGCBctgwVLwCl7Be7MMFixYsGDBsu8FH1FaSmExVfAxBa/gvVmwYMGCZbBg/W4vAQYA5tRF9QYlv/QAAAAASUVORK5CYII=",Ve.pointerListenerAdd(Ee.value,"move",C=>{ye.value=i.pointer.dragStarted}),c=new Ve.LGraph,i=new Ve.LGraphCanvas(Ee.value,c),z.value=c,i.high_quality=!0,i.render_connections_border=!0,i.render_curved_connections=!0,i.links_render_mode=Ve.SPLINE_LINK,i.render_canvas_border=!1,i.render_connection_arrows=!1,i.render_curved_connections=!1,i.links_render_mode=Ve.SPLINE_LINK,i.render_shadows=!0,i.zoom_modify_alpha=!0;const a={canvas:i,graph:c,canvasContainer:Ee.value.parentElement};aa(a);const n=()=>{i&&me()},f=new MutationObserver(()=>{n()});if(f.observe(document.documentElement,{attributes:!0,attributeFilter:["class"]}),i.setZoom(Math.max(window.devicePixelRatio,1)),i.ds.offset=[0,0],i.allow_dragcanvas=!0,i.allow_dragnodes=!0,i.allow_interaction=!0,i.allow_searchbox=!1,i.drag_mode=!1,i.allow_reconnect_links=!0,i.allow_zoom=!0,i.onShowNodeCreationDialog=()=>!1,i.onDrawForeground=C=>{for(const te of i.visible_nodes){const X=te._pos,he=te.size,K=Ve.NODE_TITLE_HEIGHT,Ie=te.flags.collapsed?K:he[1]+K,Ue=6,xe=15;if(C.strokeStyle="rgba(39,103,238,0)",C.lineWidth=0,te.status==="success")C.strokeStyle="#33FF00",C.lineWidth=2;else if(te.status==="failed")C.strokeStyle="#FF0033",C.lineWidth=7;else if(te.status==="running"){const Fe=0+F*.6;C.strokeStyle=`rgba(51, 102, 255,${Fe})`,C.lineWidth=4}C.beginPath();const Y=X[0]-Ue,ae=X[1]-K-Ue;let tt=he[0];te.flags.collapsed&&(C.save(),C.restore(),tt=Math.max(te.width));const Oe=tt+Ue*2,Se=Ie+Ue*2;C.moveTo(Y+xe,ae),C.lineTo(Y+Oe-xe,ae),C.quadraticCurveTo(Y+Oe,ae,Y+Oe,ae+xe),C.lineTo(Y+Oe,ae+Se-xe),C.quadraticCurveTo(Y+Oe,ae+Se,Y+Oe-xe,ae+Se),C.lineTo(Y+xe,ae+Se),C.quadraticCurveTo(Y,ae+Se,Y,ae+Se-xe),C.lineTo(Y,ae+xe),C.quadraticCurveTo(Y,ae,Y+xe,ae),C.stroke()}},S(),window.addEventListener("resize",S),c.runStep(),n(),c.start(),Be.value=!0,c.change(),i.draw(!0,!0),Ve.NODE_BOX_OUTLINE_COLOR="rgba(39,103,238,0.65)",Ve.WIDGET_BGCOLOR="rgba(16,18,19,0.8)",Ve.WIDGET_SECONDARY_TEXT_COLOR="#858585",Ve.NODE_WIDGET_HEIGHT=25,await new Promise(C=>setTimeout(C,500)),s.workflow_id){const C=await Ce(s.workflow_id);We(C)}yt(()=>{f.disconnect(),window.removeEventListener("resize",S),c&&(c.stop(),Be.value=!1)}),window.addEventListener("keydown",Te)}),yt(()=>{window.removeEventListener("keydown",Te)});function Te(s){(s.ctrlKey||s.metaKey)&&s.key==="s"&&(s.preventDefault(),H())}const $e=()=>{const s=c.serialize(),a={format_version:"2.0",name:o.title?o.title:`workflow-${new Date().toISOString().replace(/[:]/g,"-").slice(0,19)}`,description:"",litegraph:s,nodes:[],links:[]};o.title=a.name;const n=Le(window.location.href);return n.workflow_id&&(a.id=n.workflow_id),console.log("graphData.nodes:",s.nodes),s.nodes&&Array.isArray(s.nodes)&&(a.nodes=s.nodes.map(f=>{const I=Object.keys(f.properties),C={};return I.forEach(te=>{var K;const X=(K=f.inputs.find(Ie=>Ie.name===te))==null?void 0:K.fieldName,he=f.properties[te];C[X]=he}),s.links.find(te=>f.id===te[1]),{uuid:f.flags.uuid,title:f.title||"",name:f.type||"",type:f.type||"",litegraph_id:f.id||0,positionX:f.pos?f.pos[0]:0,positionY:f.pos?f.pos[1]:0,width:f.size?f.size[0]:0,height:f.size?f.size[1]:0,static_input_data:C||{},output_db_id:null}})),s.links&&Array.isArray(s.links)&&(a.links=s.links.map(f=>{var xe,Y;const[I,C,te,X,he,K]=f,Ie=s.nodes.find(ae=>ae.id===C),Ue=s.nodes.find(ae=>ae.id===X);return console.log("sourceNode,targetNode:",Ie,Ue),{uuid:Ot(),litegraph_id:C,status:1,previous_node_uuid:Ie.flags.uuid,next_node_uuid:Ue.flags.uuid,output_field_name:(xe=Ue.inputs[he])==null?void 0:xe.fieldName,input_field_name:(Y=Ie.outputs[te])==null?void 0:Y.fieldName}})),a},rt=async()=>{if($e().nodes.length===0){ge.warning("当前工作区域为空，不可保存空白工作流！");return}const s=JSON.stringify($e());try{const a=localStorage.getItem("token"),n=await fetch("http://localhost:8000/api/workflow/save",{method:"POST",headers:{uid:"0","quantflow-auth":"2","Content-Type":"application/json",Authorization:`${a}`},body:s});if(n.ok){const f=await n.json();console.log("保存的workflow_id:",f);const I=f.data&&f.data.workflow_id;o.workflow_id=I;const C=new URL(window.location.href);if(C.searchParams.set("workflow_id",I),window.history.replaceState({},"",C),console.log("保存的的workflow_id:",I),o.owner==="*"&&ge.success("模版工作流克隆成功！正在运行中...",{timeout:2e3}),o.owner="",o.workflow_id){const he=new URL(window.location.href);he.searchParams.set("workflow_id",o.workflow_id),window.history.replaceState({},"",he)}const te={uid:"0","quantflow-auth":"2","Content-Type":"application/json",Authorization:`${a}`},X=await fetch(`http://localhost:8000/api/workflow/query?workflow_id=${f.data.workflow_id}`,{headers:te});if(X.status===404)return console.log("工作流不存在!"),null;if(X.ok){const he=await X.json();console.log("data:",he),o.title=he.name}return I}else throw new Error(`请求失败: ${n.status} ${n.statusText}`)}catch(a){return console.error("保存图表时出错:",a),ge.error("运行失败，请重试！"),null}},Ye=r(0),Me=async s=>{try{Ye.value=-1;const a=localStorage.getItem("token"),n=await fetch("http://localhost:8000/api/workflow/run",{method:"POST",headers:{uid:"0","quantflow-auth":"2","Content-Type":"application/json",Authorization:`${a}`},body:JSON.stringify({workflow_id:s})});if(console.log("运行模式:","LOCAL"),n.ok){const f=await n.json();console.log("运行结果:",f);const I=f.data&&f.data.workflow_run_id;return o.workflow_run_id=I,I}else throw new Error(`请求失败: ${n.status} ${n.statusText}`)}catch(a){return console.error("运行图表时出错:",a),ge.error("运行失败，请重试！"),null}},fe=async s=>{console.log("nodeStatus:",s),c.nodes.forEach(a=>{a.status=null}),s.success_node_ids&&s.success_node_ids.forEach(a=>{const n=c.nodes.find(f=>f.flags.uuid===a);n&&(n.status="success",i.draw(!0,!0))}),s.running_node_ids&&s.running_node_ids.forEach(a=>{const n=c.nodes.find(f=>f.flags.uuid===a);n&&(n.status="running",i.draw(!0,!0))}),s.failed_node_ids&&s.failed_node_ids.forEach(a=>{const n=c.nodes.find(f=>f.flags.uuid===a);n&&(n.status="failed",i.draw(!0,!0))})},Qe=r(!0),ze=async(s,a,n,f,I)=>{try{Ye.value++;const C=localStorage.getItem("token"),te=await Gt.get("http://localhost:8000/api/workflow/run",{params:{workflow_run_id:s,last_log_id:a},headers:{uid:"0","quantflow-auth":"2","Content-Type":"application/json",Authorization:`${C}`}});if(te.status===200){const X=te.data;console.log("轮询结果:",X),fe(X.data);const he=X.data&&X.data.progress;le.value=he,he<100&&T.value?o.pollTimer=setTimeout(ze.bind(this,s,a,n,f,I),f):(Qe.value=!0,I==="click"?ge.success("运行完成！"):I==="auto"&&ge.success("获取运行结果成功！"),console.log("轮询结束:",X.data),o.pollData={...X.data},T.value=!1,clearTimeout(o.pollTimer));const K=X.data&&X.data.status;if(K>=2&&(T.value=!1,le.value=he,clearTimeout(o.pollTimer),K==3)){const Ie=X.data.last_error_message+`
`+X.data.last_error_stacktrace;I!=="auto"&&(Qe.value=!1),setTimeout(()=>{Qe.value=!0},300)}K>2&&I==="auto"&&c.nodes.forEach(Ie=>{Ie.status=null})}else throw new Error(`请求失败: ${te.status} ${te.statusText}`)}catch(C){console.error("轮询工作流状态时出错:",C),T.value=!1,clearTimeout(o.pollTimer)}},at=async(s,a)=>{if(s&&Je(s),s){if(o.workflow_id=await rt(),console.log("workflow_id:",o.workflow_id),o.workflow_id){const n=await Me(o.workflow_id);o.workflow_run_id=n,console.log("workflow_run_id11:",o.workflow_run_id),ze(n,0,o.user_id,2e3,"click"),T.value=!0,le.value=0,o.pollData=null}}else ze(o.workflow_run_id,0,o.user_id,2e3,"auto"),T.value=!0,le.value=0,o.pollData=null},lt=async s=>{s&&Je(s);try{T.value=!1,le.value=0,clearTimeout(o.pollTimer),await new Promise(f=>setTimeout(f,500)),c.nodes.forEach(f=>{f.status=null}),i.allow_dragnodes=!0,i.allow_dragcanvas=!0,i.allow_reconnect_links=!0,i.allow_linking=!0,i.allow_select_nodes=!0,c.nodes.forEach(f=>{f.inputs&&f.inputs.forEach(I=>{I.locked=!1}),f.outputs&&f.outputs.forEach(I=>{I.locked=!1}),f.widgets&&f.widgets.forEach(I=>{I.disabled=!1})}),c.setDirtyCanvas(!0),i.draw(!0,!0);const a=localStorage.getItem("token"),n=await fetch("http://localhost:8000/api/workflow/run/terminate",{method:"POST",headers:{uid:"0","quantflow-auth":"2","Content-Type":"application/json",Authorization:`${a}`},body:JSON.stringify({workflow_run_id:o.workflow_run_id})});if(n.ok)console.log("停止运行成功"),ge.success("停止运行成功");else throw new Error(`请求失败: ${n.status} ${n.statusText}`)}catch(a){console.error("停止运行时出错:",a),ge.error("停止运行失败")}},Je=s=>{const a=s.currentTarget;a.classList.remove("animating-ripple"),a.offsetWidth,a.classList.add("animating-ripple"),setTimeout(()=>{a.classList.remove("animating-ripple")},600)};return Ke(N,async s=>{try{m&&(m.dispose(),m=null),L&&(L.dispose(),L=null),l&&(l.dispose(),l=null)}catch(a){console.log(a)}try{s?(m=ft.createDiffEditor(O.value,{automaticLayout:!0,theme:"vs-dark",readOnly:!1,renderSideBySide:!1,ignoreTrimWhitespace:!1}),console.log("workflowStore.code:",o.code),L=ft.createModel(o.code.get(o.id)||"","python"),l=ft.createModel(o.code.get(o.id)||"","python"),m.setModel({original:L,modified:l})):(m=ft.create(O.value,{automaticLayout:!0,theme:"vs-dark",readOnly:!1,language:"python",value:o.code.get(o.id)||""}),L=m.getModel(),setTimeout(()=>{m.setValue(o.code.get(o.id))},30))}catch(a){console.log("e2：",a)}try{m&&m.onDidChangeModelContent(()=>{console.log("内容改成3"),o.original_code=m.getValue(),o.code.set(o.id,m.getValue())})}catch(a){console.log(a)}}),(s,a)=>(y(),w(qe,null,[je(x).isBoxShow?(y(),w("div",il,[e("div",rl,[e("img",{class:"close-btn",src:Tt,alt:"close",style:{width:"16px",height:"16px",cursor:"pointer"},onClick:a[0]||(a[0]=(...n)=>je(x).closeBox&&je(x).closeBox(...n))})]),e("div",cl,[De(ss,{into:!0,factorId:"",taskId:je(x).id},null,8,["taskId"])])])):Ne("",!0),je(se).isBoxShow?(y(),w("div",dl,[e("div",ul,[e("img",{class:"close-btn",src:Tt,alt:"close",style:{width:"16px",height:"16px",cursor:"pointer"},onClick:a[1]||(a[1]=(...n)=>je(se).closeBox&&je(se).closeBox(...n))})]),e("div",fl,[De(ka,{ref_key:"strategyAnalysisRef",ref:we},null,512)])])):Ne("",!0),e("div",{class:"graph-editor litegraph .litegraph-editor",onDragenter:vt(Z,["prevent"]),onDragover:vt(d,["prevent"]),onDragleave:vt(A,["prevent"]),onDrop:vt(R,["prevent"])},[e("div",hl,[e("canvas",{ref_key:"canvas",ref:Ee},null,512),e("div",gl,[De(Ja,{"is-running":T.value,progress:le.value,graph:je(c),"is-graph-ready":Be.value,canvas:je(i)},null,8,["is-running","progress","graph","is-graph-ready","canvas"])]),e("div",pl,[T.value?(y(),w("div",vl,P(le.value.toFixed(2))+"%",1)):Ne("",!0),T.value?(y(),w("div",{key:1,class:"progress-bar",style:Ze({width:le.value+"%"})},null,4)):Ne("",!0),T.value?(y(),w("button",{key:3,class:"run-button stop-run-button",onClick:a[3]||(a[3]=n=>lt(n))},a[5]||(a[5]=[e("svg",{class:"loading-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[e("path",{d:"M6 19h4V5H6v14zm8-14v14h4V5h-4z",fill:"currentColor"})],-1)]))):(y(),w("button",{key:2,class:"run-button",onClick:a[2]||(a[2]=n=>at(n))},a[4]||(a[4]=[e("svg",{class:"play-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[e("path",{d:"M8 5.14v14.72a1 1 0 001.5.86l11-7.36a1 1 0 000-1.72l-11-7.36a1 1 0 00-1.5.86z",fill:"currentColor"})],-1)])))]),De(Ss,{logCount:Ye.value,isCollapsed:Qe.value},null,8,["logCount","isCollapsed"]),De(oa,{name:"fade"},{default:sa(()=>[b.value?(y(),w("div",Al,a[6]||(a[6]=[e("div",{class:"drag-message"},[e("svg",{class:"upload-icon",width:"48",height:"48",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[e("path",{d:"M12 3L20 10H16.5V19H7.5V10H4L12 3Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})]),e("p",null,"释放以导入 JSON 文件")],-1)]))):Ne("",!0)]),_:1})])],32),nt(e("div",ml,[e("div",yl,[e("div",wl,[e("img",{onClick:J,class:"close-btn",src:qt}),a[9]||(a[9]=e("div",{class:"editorMonaco-head",style:{height:"40px","line-height":"40px","text-indent":"20px",color:"#fff","font-size":"14px"}},"编辑代码",-1)),e("div",bl,[e("div",{class:"editorMonaco",style:{height:"100%",width:"65%",position:"relative"},ref_key:"editorDom",ref:O},[N.value?(y(),w("div",xl,[e("button",{class:"apply-btn",onClick:_e},a[7]||(a[7]=[e("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[e("path",{d:"M4 8.5L7 11.5L12 5.5",stroke:"white","stroke-width":"1.3","stroke-linecap":"round","stroke-linejoin":"round"})],-1),At(" 应用 ")])),e("button",{class:"reject-btn",onClick:G},a[8]||(a[8]=[e("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[e("path",{d:"M4 4L12 12M12 4L4 12",stroke:"white","stroke-width":"1.3","stroke-linecap":"round"})],-1),At(" 拒绝 ")]))])):Ne("",!0)],512),e("div",kl,[(y(),Et(na,null,[je(o).codeStatus?(y(),Et(la(nl),{key:je(o).id})):Ne("",!0)],1024))])]),e("div",{class:"editorMonaco-foot",style:{height:"40px","padding-top":"15px"}},[e("div",{class:"content-btn active",onClick:H,style:{display:"inline-flex",float:"left","margin-left":"20px"}},"保存")])])])],512),[[bt,je(o).codeStatus]])],64))}}),Cl=it(_l,[["__scopeId","data-v-4bed71e3"]]),Ll={class:"quantflow-container"},El={class:"content-container"},Il={class:"aside-container"},Sl=mt({__name:"Quantflow",setup(Q){const O=r(!0),N=m=>{O.value=m};return(m,L)=>(y(),w("div",Ll,[De(Yt),L[0]||(L[0]=At()),e("div",El,[e("div",Il,[De(za,{onCategoriesLoadingState:N})]),De(Cl)])]))}}),Ml=it(Sl,[["__scopeId","data-v-36d9fec6"]]);export{Ml as default};
