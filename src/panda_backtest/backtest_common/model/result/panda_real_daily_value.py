#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time   : 18-2-25 下午5:56
# <AUTHOR> wlb
# @File   : panda_simulation_minute_value.py.py
# @desc   : 回测结果收益概览图标数据
from panda_backtest.backtest_common.constant.strategy_constant import *
import logging

class PandaRealDailyValue:
    run_id = EMPTY_STRING                           # 关联回测结果主键id
    total_value = EMPTY_FLOAT                       # 策略总权益
    strategy_profit = EMPTY_FLOAT                   # 策略累计收益（百分比）
    add_profit = EMPTY_FLOAT                      # 累计收益
    daily_pnl = EMPTY_FLOAT                # 今日收益
    date = EMPTY_STRING                             # 日期
    trade_date = EMPTY_STRING                             # 日期

