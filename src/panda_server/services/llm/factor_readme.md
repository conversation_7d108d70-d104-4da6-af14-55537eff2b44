# 量化算子工具类使用文档

本文档汇总介绍了量化算子工具类中所有函数的功能、输入/输出说明以及使用示例。所有函数均以静态方式提供，调用时直接使用函数名称，无需添加类名前缀。

示例中均采用如下调用格式，例如：

```
CORRELATION(CLOSE, VOLUME, 20)
```

> **注意**
>
> - **数据要求**：各函数要求输入的数据为带有日期和标的信息的多级索引时间序列。如果数据格式不符合要求，内部会自动将索引调整为 date 与 symbol 两级。
> - **参数说明**：下列说明中，输入输出参数采用通俗描述，不涉及具体编程语言的数据类型。

---

## 基础计算函数

### RANK
**描述**：对横截面数据进行排序，并归一化到区间 [-0.5, 0.5]，用于衡量每日各标的的相对排名。
- **输入**：series（记录各标的数值的时间序列，必须带有日期和标的信息）
- **输出**：归一化后的排名数据，数值范围为 [-0.5, 0.5]
- **示例**：
  ```
  RANK(CLOSE)  # 对收盘价进行排序归一化
  ```

### RETURNS
**描述**：计算收益率序列。
- **输入**：close（价格序列），period（收益率计算周期，默认为1）
- **输出**：收益率序列
- **示例**：
  ```
  RETURNS(CLOSE, 5)  # 计算5日收益率
  ```

### STDDEV
**描述**：计算滚动标准差。
- **输入**：series（输入序列），window（计算窗口，默认为20）
- **输出**：标准差序列
- **示例**：
  ```
  STDDEV(CLOSE, 20)  # 计算20日滚动标准差
  ```

### CORRELATION
**描述**：计算两个序列的滚动相关系数。
- **输入**：series1（第一个序列），series2（第二个序列），window（计算窗口，默认为20）
- **输出**：相关系数序列
- **示例**：
  ```
  CORRELATION(CLOSE, VOLUME, 20)  # 计算收盘价与成交量的20日相关系数
  ```

### COVARIANCE
**描述**：计算两个序列的滚动协方差。
- **输入**：series1（第一个序列），series2（第二个序列），window（计算窗口，默认为20）
- **输出**：协方差序列
- **示例**：
  ```
  COVARIANCE(CLOSE, VOLUME, 20)  # 计算收盘价与成交量的20日协方差
  ```

### VWAP
**描述**：计算成交量加权平均价格。
- **输入**：close（收盘价序列），volume（成交量序列）
- **输出**：VWAP序列
- **示例**：
  ```
  VWAP(CLOSE, VOLUME)
  ```

### CAP
**描述**：计算市值。
- **输入**：close（收盘价序列），volume（成交量）
- **输出**：市值序列
- **示例**：
  ```
  CAP(CLOSE, VOLUME)
  ```

---

## 时间序列函数

### DELAY
**描述**：计算序列的滞后值。
- **输入**：series（输入序列），period（滞后期数，默认为1）
- **输出**：滞后序列
- **示例**：
  ```
  DELAY(CLOSE, 1)  # 计算前一日收盘价
  ```

### SUM
**描述**：计算滚动求和。
- **输入**：series（输入序列），window（计算窗口，默认为20）
- **输出**：滚动求和序列
- **示例**：
  ```
  SUM(VOLUME, 20)  # 计算20日成交量总和
  ```

### TS_ARGMAX
**描述**：计算时间序列最大值位置，返回值归一化到[0, 1]区间。
- **输入**：series（输入序列），window（计算窗口）
- **输出**：最大值位置序列
- **示例**：
  ```
  TS_ARGMAX(CLOSE, 20)  # 计算20日内最高收盘价的位置
  ```

### TS_ARGMIN
**描述**：计算时间序列最小值位置。
- **输入**：series（输入序列），window（计算窗口，默认为20）
- **输出**：最小值位置序列
- **示例**：
  ```
  TS_ARGMIN(CLOSE, 20)  # 计算20日内最低收盘价的位置
  ```

### TS_MIN
**描述**：计算时间序列最小值。
- **输入**：series（输入序列），window（计算窗口，默认为20）
- **输出**：最小值序列
- **示例**：
  ```
  TS_MIN(CLOSE, 20)  # 计算20日最低价
  ```

### TS_MAX
**描述**：计算时间序列最大值。
- **输入**：series（输入序列），window（计算窗口，默认为20）
- **输出**：最大值序列
- **示例**：
  ```
  TS_MAX(CLOSE, 20)  # 计算20日最高价
  ```

### TS_RANK
**描述**：计算时间序列排名。
- **输入**：series（输入序列），window（计算窗口，默认为20）
- **输出**：排名序列
- **示例**：
  ```
  TS_RANK(CLOSE, 20)  # 计算20日价格排名
  ```

### DECAY_LINEAR
**描述**：计算线性衰减加权平均。
- **输入**：series（输入序列），window（计算窗口，默认为20）
- **输出**：加权平均序列
- **示例**：
  ```
  DECAY_LINEAR(CLOSE, 20)  # 计算20日线性衰减加权平均价格
  ```

---

## 技术指标函数

### MACD （公式方法暂不支持）
**描述**：计算MACD技术指标，需要至少120天数据以保证准确性。
- **输入**：CLOSE（收盘价序列），SHORT（短期EMA周期，默认12），LONG（长期EMA周期，默认26），M（信号线周期，默认9）
- **输出**：DIF，DEA，MACD线 (DIF - DEA) * 2
- **示例**：
  ```
  MACD(CLOSE, 12, 26, 9)
  ```

### KDJ
**描述**：计算KDJ指标，返回K线值。
- **输入**：CLOSE（收盘价序列），HIGH（最高价序列），LOW（最低价序列），N（RSV计算周期，默认9），M1（K线平滑周期，默认3），M2（D线平滑周期，默认3）
- **输出**：K线值
- **示例**：
  ```
  KDJ(CLOSE, HIGH, LOW, 9, 3, 3)
  ```

### RSI
**描述**：计算相对强弱指标(RSI)。
- **输入**：CLOSE（收盘价序列），N（计算周期，默认24）
- **输出**：RSI值
- **示例**：
  ```
  RSI(CLOSE, 24)
  ```

### BOLL
**描述**：计算布林带指标，返回中轨线。
- **输入**：CLOSE（收盘价序列），N（计算周期，默认20），P（标准差倍数，默认2）
- **输出**：布林带中轨线值
- **示例**：
  ```
  BOLL(CLOSE, 20, 2)
  ```

### ATR
**描述**：计算平均真实波幅。
- **输入**：CLOSE（收盘价序列），HIGH（最高价序列），LOW（最低价序列），N（计算周期，默认20）
- **输出**：ATR值
- **示例**：
  ```
  ATR(CLOSE, HIGH, LOW, 20)
  ```

### ROC
**描述**：计算变动率指标(ROC)。
- **输入**：CLOSE（收盘价序列），N（计算周期，默认12）
- **输出**：ROC值（百分比）
- **示例**：
  ```
  ROC(CLOSE, 12)
  ```

### OBV
**描述**：计算能量潮指标。
- **输入**：CLOSE（收盘价序列），VOL（成交量序列）
- **输出**：OBV值（除以10000）
- **示例**：
  ```
  OBV(CLOSE, VOLUME)
  ```

### MFI
**描述**：计算资金流量指标（量化版RSI）。
- **输入**：CLOSE（收盘价序列），HIGH（最高价序列），LOW（最低价序列），VOL（成交量序列），N（计算周期，默认14）
- **输出**：MFI值
- **示例**：
  ```
  MFI(CLOSE, HIGH, LOW, VOLUME, 14)
  ```

### CCI
**描述**：计算顺势指标。
- **输入**：CLOSE（收盘价序列），HIGH（最高价序列），LOW（最低价序列），N（计算周期，默认14）
- **输出**：CCI值
- **示例**：
  ```
  CCI(CLOSE, HIGH, LOW, 14)
  ```

### BBI
**描述**：计算多空指标（Bull and Bear Index）。
- **输入**：CLOSE（收盘价序列），M1（第一个MA周期，默认3），M2（第二个MA周期，默认6），M3（第三个MA周期，默认12），M4（第四个MA周期，默认20）
- **输出**：BBI值
- **示例**：
  ```
  BBI(CLOSE, 3, 6, 12, 20)
  ```

### DMI
**描述**：计算动向指标，返回ADX线。
- **输入**：CLOSE（收盘价序列），HIGH（最高价序列），LOW（最低价序列），M1（TR计算周期，默认14），M2（ADX平滑周期，默认6）
- **输出**：ADX值
- **示例**：
  ```
  DMI(CLOSE, HIGH, LOW, 14, 6)
  ```

### TRIX
**描述**：计算三重指数平滑移动平均指标。
- **输入**：CLOSE（收盘价序列），M1（EMA周期，默认12），M2（MATRIX周期，默认20）
- **输出**：TRIX值
- **示例**：
  ```
  TRIX(CLOSE, 12, 20)
  ```

### VR
**描述**：计算成交量比率指标。
- **输入**：CLOSE（收盘价序列），VOL（成交量序列），M1（计算周期，默认26）
- **输出**：VR值
- **示例**：
  ```
  VR(CLOSE, VOLUME, 26)
  ```

### EMV
**描述**：计算简易波动指标。
- **输入**：HIGH（最高价序列），LOW（最低价序列），VOL（成交量序列），N（计算周期，默认14），M（平滑周期，默认9）
- **输出**：EMV值
- **示例**：
  ```
  EMV(HIGH, LOW, VOLUME, 14, 9)
  ```

### DPO
**描述**：计算区间震荡线。
- **输入**：CLOSE（收盘价序列），M1（MA周期，默认20），M2（位移周期，默认10），M3（平滑周期，默认6）
- **输出**：DPO值
- **示例**：
  ```
  DPO(CLOSE, 20, 10, 6)
  ```

### BRAR
**描述**：计算人气意愿指标。
- **输入**：OPEN（开盘价序列），CLOSE（收盘价序列），HIGH（最高价序列），LOW（最低价序列），M1（计算周期，默认26）
- **输出**：AR值
- **示例**：
  ```
  BRAR(OPEN, CLOSE, HIGH, LOW, 26)
  ```

### MASS
**描述**：计算梅斯线。
- **输入**：HIGH（最高价序列），LOW（最低价序列），N1（EMA周期，默认9），N2（累积周期，默认25），M（平滑周期，默认6）
- **输出**：MASS值
- **示例**：
  ```
  MASS(HIGH, LOW, 9, 25, 6)
  ```

---

## 条件计算函数

### IF
**描述**：条件选择函数。
- **输入**：condition（条件序列），true_value（条件为真时的值），false_value（条件为假时的值）
- **输出**：条件选择后的序列
- **示例**：
  ```
  IF(CLOSE > OPEN, 1, -1)
  ```

### CROSS
**描述**：判断向上穿越。
- **输入**：S1（第一个序列），S2（第二个序列）
- **输出**：布尔序列，True表示发生向上穿越
- **示例**：
  ```
  CROSS(CLOSE, MA(CLOSE, 20))  # 判断收盘价是否上穿20日均线
  ```

### VALUEWHEN
**描述**：当条件满足时取值。
- **输入**：S（条件序列，布尔值），X（值序列）
- **输出**：条件满足时的值序列
- **示例**：
  ```
  VALUEWHEN(CLOSE > OPEN, VOLUME)  # 在阳线时获取成交量
  ```

---

## 辅助工具函数

### SCALE
**描述**：将序列缩放到[-1, 1]区间。
- **输入**：series（输入序列）
- **输出**：缩放后的序列
- **示例**：
  ```
  SCALE(CLOSE)  # 将收盘价缩放到[-1, 1]区间
  ```

### ABS
**描述**：计算绝对值。
- **输入**：series（输入序列）
- **输出**：绝对值序列
- **示例**：
  ```
  ABS(RETURNS(CLOSE))  # 计算收益率的绝对值
  ```

### MIN
**描述**：计算两个序列的逐元素最小值。
- **输入**：series1（第一个序列），series2（第二个序列或标量）
- **输出**：最小值序列
- **示例**：
  ```
  MIN(CLOSE, OPEN)  # 计算收盘价和开盘价的较小值
  ```

### MAX
**描述**：计算两个序列的逐元素最大值。
- **输入**：series1（第一个序列），series2（第二个序列或标量）
- **输出**：最大值序列
- **示例**：
  ```
  MAX(CLOSE, OPEN)  # 计算收盘价和开盘价的较大值
  ``` 