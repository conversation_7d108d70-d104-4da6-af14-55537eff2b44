import time
from intervaltree import Interval, IntervalTree
import pickle

# 各品种每日交易时间段节点
from common.connector.redis_client import RedisClient

IC = ['9:30:00', '11:30:00', '13:00:00', '15:00:00']
IF = ['9:30:00', '11:30:00', '13:00:00', '15:00:00']
IH = ['9:30:00', '11:30:00', '13:00:00', '15:00:00']
T = ['9:15:00',  '11:30:00', '13:00:00', '15:15:00']
TF = ['9:15:00',  '11:30:00', '13:00:00', '15:15:00']
TS = ['9:15:00',  '11:30:00', '13:00:00', '15:15:00']

AP = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00']
BB = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00']
CJ = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00']
ER = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00']
FB = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00']
JD = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00']
JR = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00']
LR = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00']
ME = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00']
PM = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00']
RI = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00']
RO = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00']
RS = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00']
SF = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00']
SM = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00']
UR = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00']
WH = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00']
WR = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00']
WS = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00']
WT = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00']

A = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:00:00']
B = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:00:00']
BU = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:00:00']
C = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:00:00']
CS = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:00:00']
EB = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:00:00']
EG = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:00:00']
FU = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:00:00']
HC = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:00:00']
I = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:00:00']
J = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:00:00']
JM = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:00:00']
L = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:00:00']
M = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:00:00']
P = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:00:00']
PP = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:00:00']
RB = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:00:00']
RR = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:00:00']
RU = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:00:00']
SP = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:00:00']
V = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:00:00']
Y = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:00:00']
NR = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:00:00']

CF = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:30:00']
CY = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:30:00']
FG = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:30:00']
MA = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:30:00']
OI = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:30:00']
RM = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:30:00']
SR = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:30:00']
TA = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:30:00']
TC = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:30:00']
ZC = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:30:00']

AL = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:59:59', '00:00:00', '1:00:00']
CU = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:59:59', '00:00:00', '1:00:00']
NI = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:59:59', '00:00:00', '1:00:00']
PB = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:59:59', '00:00:00', '1:00:00']
SN = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:59:59', '00:00:00', '1:00:00']
SS = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:59:59', '00:00:00', '1:00:00']
ZN = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:59:59', '00:00:00', '1:00:00']

SC = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:59:59', '00:00:00', '2:30:00']
AG = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:59:59', '00:00:00', '2:30:00']
AU = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:59:59', '00:00:00', '2:30:00']
SA = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:00:00']
PG = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:00:00']
LU = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:00:00']
PF = ['9:00:00', '10:15:00', '10:30:00', '11:30:00', '13:30:00', '15:00:00', '21:00:00', '23:00:00']


pz_dict = {'IC': IC, 'IF': IF, 'IH': IH, 'T': T, 'TF': TF, 'TS': TS, 'AP': AP, 'BB': BB, 'CJ': CJ,
           'ER': ER, 'FB': FB, 'JD': JD, 'JR': JR, 'LR': LR, 'ME': ME, 'PM': PM, 'RI': RI, 'RO': RO,
           'RS': RS, 'SF': SF, 'SM': SM, 'UR': UR, 'WH': WH, 'WR': WR, 'WS': WS, 'WT': WT, 'A': A,
           'B': B, 'BU': BU, 'C': C, 'CF': CF, 'CS': CS, 'CY': CY, 'EB': EB, 'EG': EG, 'FG': FG,
           'FU': FU, 'HC': HC, 'I': I, 'J': J, 'JM': JM, 'L': L, 'M': M, 'MA': MA, 'OI': OI,
           'P': P, 'PP': PP, 'RB': RB, 'RM': RM, 'RR': RR, 'RU': RU, 'SP': SP, 'SR': SR, 'TA': TA,
           'TC': TC, 'V': V, 'Y': Y, 'ZC': ZC, 'NR': NR, 'AL': AL, 'CU': CU, 'NI': NI, 'PB': PB,
           'SN': SN, 'SS': SS, 'ZN': ZN, 'SC': SC, 'AG': AG, 'AU': AU, 'SA': SA, 'PG': PG, 'LU': LU,
           'PF': PF}




def update(pz_dict):
    '''
    更新各个品种的交易时间段
    '''
    redis_client = RedisClient()
    for keys in pz_dict.keys():  # 循环品种时间段字典
        pz_dict[keys] = [time.strptime(x, "%H:%M:%S") for x in pz_dict[keys]]  # 将时间转化成strftime格式
        pz_dict[keys] = [tuple(pz_dict[keys][i:i+2]) for i in range(0, len(pz_dict[keys]), 2)]  # 将list拆分成tuple格式，一个tuple一个交易时间段
        pz_dict[keys] = IntervalTree(Interval(begin, end) for begin, end in pz_dict[keys])  # 将多个交易时间段合并成一个IntervalTree格式，用于后续判断单个时间点是否在期间
    print(pz_dict)
    res_str = pickle.dumps(pz_dict)
    redis_client.setRedis('future_trade_time_dict', res_str)












