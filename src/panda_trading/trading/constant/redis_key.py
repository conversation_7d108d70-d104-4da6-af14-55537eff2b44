#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time   : 2019/6/19 下午3:07
# <AUTHOR> wlb
# @File   : redis_key.py
# @desc   :

real_trade_account_assets = 'redefine_real_account_assets'
real_trade_account_positions = 'redefine_real_account_positions'


# 保存运行数据
real_trade_restore_data = 'redefine_real_restore_data:'
restore_strategy_context = 'redefine_real_strategy_context'
standard_symbol_result = 'redefine_real_standard_symbol_result'
trade_reverse_result = 'redefine_real_trade_reverse_result'
future_reverse_result = 'redefine_real_future_reverse_result'

real_trade_progress = 'real_trade_progress:'
